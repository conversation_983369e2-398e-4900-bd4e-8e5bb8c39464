import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Grid,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  TextField,
  CircularProgress,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepButton,
  Divider,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Check as CheckIcon,
  Flag as FlagIcon,
  Timer as TimerIcon,
  Done as DoneIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchQuizById,
  fetchQuestions,
  startQuizAttempt,
  submitQuizAttempt,
  fetchQuizAttempt,
} from '../store/slices/quizzesSlice';
import { Question } from '../services/quizzesService';

const QuizTakingInterface: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { data: quiz, loading: quizLoading, error: quizError } = useSelector(
    (state: RootState) => state.quizzes.currentQuiz
  );
  const { items: questions, loading: questionsLoading, error: questionsError } = useSelector(
    (state: RootState) => state.quizzes.questions
  );
  const { data: currentAttempt, loading: attemptLoading } = useSelector(
    (state: RootState) => state.quizzes.currentAttempt
  );
  
  const [activeStep, setActiveStep] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string | string[]>>({});
  const [flaggedQuestions, setFlaggedQuestions] = useState<number[]>([]);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFinished, setIsFinished] = useState(false);
  const [confirmSubmitOpen, setConfirmSubmitOpen] = useState(false);
  const [timeExpiredOpen, setTimeExpiredOpen] = useState(false);
  const [attemptId, setAttemptId] = useState<number | null>(null);
  const [quizResult, setQuizResult] = useState<{
    score: number;
    totalPoints: number;
    isPassed: boolean;
    correctAnswers: number;
    totalQuestions: number;
  } | null>(null);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    if (id) {
      dispatch(fetchQuizById(parseInt(id)));
      dispatch(fetchQuestions(parseInt(id)));
    }
  }, [dispatch, id]);
  
  useEffect(() => {
    if (quiz && quiz.timeLimit && !timeLeft && !isFinished) {
      setTimeLeft(quiz.timeLimit * 60); // Convert minutes to seconds
    }
  }, [quiz, timeLeft, isFinished]);
  
  useEffect(() => {
    if (timeLeft !== null && timeLeft > 0 && !isFinished) {
      timerRef.current = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0 && !isFinished) {
      setTimeExpiredOpen(true);
      handleSubmitQuiz();
    }
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeLeft, isFinished]);
  
  useEffect(() => {
    if (currentAttempt && currentAttempt.endTime && !isFinished) {
      setIsFinished(true);
      setQuizResult({
        score: currentAttempt.score,
        totalPoints: questions.reduce((sum, q) => sum + q.points, 0),
        isPassed: currentAttempt.isPassed,
        correctAnswers: currentAttempt.answers.filter((a) => a.isCorrect).length,
        totalQuestions: questions.length,
      });
    }
  }, [currentAttempt, questions, isFinished]);
  
  const startQuiz = async () => {
    if (!user || !id) return;
    
    try {
      const attempt = await dispatch(
        startQuizAttempt({ quizId: parseInt(id), userId: user.id })
      ).unwrap();
      
      setAttemptId(attempt.id);
    } catch (error) {
      console.error('Failed to start quiz:', error);
    }
  };
  
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };
  
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  
  const handleStep = (step: number) => {
    setActiveStep(step);
  };
  
  const handleAnswerChange = (questionId: number, answer: string | string[]) => {
    setAnswers({
      ...answers,
      [questionId]: answer,
    });
  };
  
  const handleFlagQuestion = (questionId: number) => {
    if (flaggedQuestions.includes(questionId)) {
      setFlaggedQuestions(flaggedQuestions.filter((id) => id !== questionId));
    } else {
      setFlaggedQuestions([...flaggedQuestions, questionId]);
    }
  };
  
  const handleOpenConfirmSubmit = () => {
    setConfirmSubmitOpen(true);
  };
  
  const handleCloseConfirmSubmit = () => {
    setConfirmSubmitOpen(false);
  };
  
  const handleSubmitQuiz = async () => {
    if (!attemptId) return;
    
    setIsSubmitting(true);
    handleCloseConfirmSubmit();
    
    try {
      const formattedAnswers = Object.entries(answers).map(([questionId, answer]) => ({
        questionId: parseInt(questionId),
        answer,
      }));
      
      await dispatch(
        submitQuizAttempt({ attemptId, answers: formattedAnswers })
      ).unwrap();
      
      await dispatch(fetchQuizAttempt(attemptId)).unwrap();
      
      setIsFinished(true);
    } catch (error) {
      console.error('Failed to submit quiz:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  const getQuestionStatus = (index: number) => {
    const question = questions[index];
    if (!question) return 'unknown';
    
    if (flaggedQuestions.includes(question.id)) {
      return 'flagged';
    }
    
    if (answers[question.id]) {
      return 'answered';
    }
    
    return 'unanswered';
  };
  
  const renderQuestion = (question: Question) => {
    switch (question.questionType) {
      case 'multiple_choice':
      case 'true_false':
        return (
          <FormControl component="fieldset" sx={{ width: '100%' }}>
            <FormLabel component="legend">{question.questionText}</FormLabel>
            <RadioGroup
              value={answers[question.id] || ''}
              onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            >
              {question.options.map((option, index) => (
                <FormControlLabel
                  key={index}
                  value={index.toString()}
                  control={<Radio />}
                  label={option}
                  disabled={isFinished}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );
      
      case 'short_answer':
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {question.questionText}
            </Typography>
            <TextField
              fullWidth
              label="Your Answer"
              value={(answers[question.id] as string) || ''}
              onChange={(e) => handleAnswerChange(question.id, e.target.value)}
              variant="outlined"
              disabled={isFinished}
              sx={{ mt: 2 }}
            />
          </Box>
        );
      
      case 'essay':
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {question.questionText}
            </Typography>
            <TextField
              fullWidth
              label="Your Answer"
              value={(answers[question.id] as string) || ''}
              onChange={(e) => handleAnswerChange(question.id, e.target.value)}
              variant="outlined"
              multiline
              rows={6}
              disabled={isFinished}
              sx={{ mt: 2 }}
            />
          </Box>
        );
      
      case 'matching':
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {question.questionText}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Matching questions will be implemented in a future update.
            </Typography>
          </Box>
        );
      
      default:
        return (
          <Typography variant="body1">
            Unsupported question type: {question.questionType}
          </Typography>
        );
    }
  };
  
  const renderQuizResults = () => {
    if (!quizResult) return null;
    
    const { score, totalPoints, isPassed, correctAnswers, totalQuestions } = quizResult;
    const percentage = Math.round((score / totalPoints) * 100);
    
    return (
      <Box>
        <Paper sx={{ p: 4, mb: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            Quiz Results
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <Chip
              icon={isPassed ? <DoneIcon /> : <CloseIcon />}
              label={isPassed ? 'PASSED' : 'FAILED'}
              color={isPassed ? 'success' : 'error'}
              variant="outlined"
              sx={{ fontSize: '1.2rem', py: 3, px: 2 }}
            />
          </Box>
          
          <Typography variant="h5" gutterBottom>
            Score: {score} / {totalPoints} ({percentage}%)
          </Typography>
          
          <Typography variant="body1" gutterBottom>
            Correct Answers: {correctAnswers} / {totalQuestions}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Passing Score: {quiz?.passingScore}%
          </Typography>
          
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/quizzes')}
            sx={{ mt: 3 }}
          >
            Return to Quizzes
          </Button>
        </Paper>
        
        <Typography variant="h6" gutterBottom>
          Review Your Answers
        </Typography>
        
        {questions.map((question, index) => {
          const userAnswer = currentAttempt?.answers.find((a) => a.questionId === question.id);
          const isCorrect = userAnswer?.isCorrect;
          
          return (
            <Paper key={question.id} sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  Question {index + 1}: {question.questionText}
                </Typography>
                <Chip
                  icon={isCorrect ? <DoneIcon /> : <CloseIcon />}
                  label={isCorrect ? 'Correct' : 'Incorrect'}
                  color={isCorrect ? 'success' : 'error'}
                  size="small"
                />
              </Box>
              
              {question.questionType === 'multiple_choice' || question.questionType === 'true_false' ? (
                <Box sx={{ ml: 3 }}>
                  {question.options.map((option, optIndex) => {
                    const isUserAnswer = userAnswer?.userAnswer === optIndex.toString();
                    const isCorrectAnswer = question.correctAnswer === optIndex.toString();
                    
                    return (
                      <Box
                        key={optIndex}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 1,
                          p: 1,
                          borderRadius: 1,
                          bgcolor: isCorrectAnswer
                            ? 'success.light'
                            : isUserAnswer && !isCorrectAnswer
                            ? 'error.light'
                            : 'background.paper',
                        }}
                      >
                        <Typography variant="body1">
                          {String.fromCharCode(65 + optIndex)}. {option}
                        </Typography>
                        {isCorrectAnswer && (
                          <Chip
                            label="Correct Answer"
                            color="success"
                            size="small"
                            sx={{ ml: 2 }}
                          />
                        )}
                        {isUserAnswer && !isCorrectAnswer && (
                          <Chip
                            label="Your Answer"
                            color="error"
                            size="small"
                            sx={{ ml: 2 }}
                          />
                        )}
                      </Box>
                    );
                  })}
                </Box>
              ) : (
                <Box sx={{ ml: 3 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Your Answer:
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {userAnswer?.userAnswer || 'No answer provided'}
                  </Typography>
                  
                  {question.questionType === 'short_answer' && (
                    <>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Correct Answer:
                      </Typography>
                      <Typography variant="body1">{question.correctAnswer}</Typography>
                    </>
                  )}
                </Box>
              )}
              
              {question.explanation && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Explanation:
                  </Typography>
                  <Typography variant="body2">{question.explanation}</Typography>
                </Box>
              )}
              
              <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                <Chip
                  label={`${question.points} ${question.points === 1 ? 'point' : 'points'}`}
                  size="small"
                  variant="outlined"
                />
              </Box>
            </Paper>
          );
        })}
      </Box>
    );
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to take quizzes. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  if (quizLoading || questionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (quizError || questionsError || !quiz) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {quizError || questionsError || 'Failed to load quiz. Please try again later.'}
        </Alert>
      </Container>
    );
  }
  
  if (!attemptId && !isFinished) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper sx={{ p: 4 }}>
          <Typography variant="h4" gutterBottom>
            {quiz.title}
          </Typography>
          <Typography variant="body1" paragraph>
            {quiz.description}
          </Typography>
          
          <Divider sx={{ my: 3 }} />
          
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TimerIcon sx={{ mr: 1 }} />
                <Typography variant="body1">
                  Time Limit: {quiz.timeLimit ? `${quiz.timeLimit} minutes` : 'No time limit'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckIcon sx={{ mr: 1 }} />
                <Typography variant="body1">
                  Passing Score: {quiz.passingScore}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <QuestionIcon sx={{ mr: 1 }} />
                <Typography variant="body1">
                  Questions: {questions.length}
                </Typography>
              </Box>
            </Grid>
          </Grid>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body1">
              Once you start the quiz, the timer will begin. You will need to complete the quiz within the time limit.
              {quiz.timeLimit ? ' If the time expires, your quiz will be automatically submitted.' : ''}
            </Typography>
          </Alert>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/quizzes')}
            >
              Back to Quizzes
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={startQuiz}
            >
              Start Quiz
            </Button>
          </Box>
        </Paper>
      </Container>
    );
  }
  
  if (isFinished) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {renderQuizResults()}
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">{quiz.title}</Typography>
          {timeLeft !== null && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TimerIcon sx={{ mr: 1 }} />
              <Typography variant="h6" color={timeLeft < 60 ? 'error.main' : 'inherit'}>
                {formatTime(timeLeft)}
              </Typography>
            </Box>
          )}
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={(Object.keys(answers).length / questions.length) * 100}
          sx={{ mb: 2 }}
        />
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="body2">
            {Object.keys(answers).length} of {questions.length} questions answered
          </Typography>
          <Typography variant="body2">
            {flaggedQuestions.length} questions flagged for review
          </Typography>
        </Box>
      </Paper>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            {questions.length > 0 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Question {activeStep + 1} of {questions.length}
                </Typography>
                <Box sx={{ my: 3 }}>
                  {renderQuestion(questions[activeStep])}
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                  <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon />}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outlined"
                    color={
                      flaggedQuestions.includes(questions[activeStep].id) ? 'warning' : 'primary'
                    }
                    startIcon={<FlagIcon />}
                    onClick={() => handleFlagQuestion(questions[activeStep].id)}
                  >
                    {flaggedQuestions.includes(questions[activeStep].id)
                      ? 'Unflag Question'
                      : 'Flag for Review'}
                  </Button>
                  {activeStep === questions.length - 1 ? (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleOpenConfirmSubmit}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? <CircularProgress size={24} /> : 'Submit Quiz'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      endIcon={<ArrowForwardIcon />}
                      onClick={handleNext}
                    >
                      Next
                    </Button>
                  )}
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Question Navigator
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {questions.map((question, index) => {
                const status = getQuestionStatus(index);
                return (
                  <Button
                    key={question.id}
                    variant={activeStep === index ? 'contained' : 'outlined'}
                    color={
                      status === 'flagged'
                        ? 'warning'
                        : status === 'answered'
                        ? 'success'
                        : 'primary'
                    }
                    onClick={() => handleStep(index)}
                    sx={{ minWidth: 40, height: 40, p: 0 }}
                  >
                    {index + 1}
                  </Button>
                );
              })}
            </Box>
            <Box sx={{ mt: 3 }}>
              <Typography variant="body2" gutterBottom>
                Legend:
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      bgcolor: 'primary.main',
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography variant="body2">Current Question</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      bgcolor: 'success.main',
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography variant="body2">Answered</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      bgcolor: 'warning.main',
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography variant="body2">Flagged for Review</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      bgcolor: 'background.paper',
                      border: '1px solid',
                      borderColor: 'primary.main',
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography variant="body2">Unanswered</Typography>
                </Box>
              </Box>
            </Box>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleOpenConfirmSubmit}
              sx={{ mt: 3 }}
              disabled={isSubmitting}
            >
              {isSubmitting ? <CircularProgress size={24} /> : 'Submit Quiz'}
            </Button>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Confirm Submit Dialog */}
      <Dialog open={confirmSubmitOpen} onClose={handleCloseConfirmSubmit}>
        <DialogTitle>Submit Quiz</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to submit your quiz?
            {Object.keys(answers).length < questions.length && (
              <>
                <br />
                <br />
                You have answered {Object.keys(answers).length} out of {questions.length} questions.
                {flaggedQuestions.length > 0 && (
                  <>
                    <br />
                    You have {flaggedQuestions.length} question(s) flagged for review.
                  </>
                )}
              </>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmSubmit}>Cancel</Button>
          <Button onClick={handleSubmitQuiz} color="primary" variant="contained">
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Time Expired Dialog */}
      <Dialog open={timeExpiredOpen}>
        <DialogTitle>Time Expired</DialogTitle>
        <DialogContent>
          <DialogContentText>
            The time limit for this quiz has expired. Your answers have been automatically submitted.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTimeExpiredOpen(false)} color="primary">
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default QuizTakingInterface;
