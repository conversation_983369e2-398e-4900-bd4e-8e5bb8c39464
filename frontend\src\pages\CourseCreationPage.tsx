import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Chip,
  InputAdornment,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import { AppDispatch, RootState } from '../store';
import { createCourse } from '../store/slices/coursesSlice';

const steps = ['Basic Information', 'Course Details', 'Pricing & Settings'];

const CourseCreationPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { loading, error } = useSelector((state: RootState) => state.courses.currentCourse);
  
  const [activeStep, setActiveStep] = useState(0);
  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    instructorID: user?.id || 0,
    instructorName: user?.name || '',
    thumbnailURL: '',
    price: 0,
    isFree: true,
    isPublic: true,
    isFeatured: false,
    status: 'draft',
    duration: 0,
    level: 'beginner',
    prerequisites: '',
    objectives: '',
    tags: [] as string[],
  });
  const [tagInput, setTagInput] = useState('');
  
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };
  
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCourseData({
      ...courseData,
      [name]: value,
    });
  };
  
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setCourseData({
      ...courseData,
      [name]: value,
    });
  };
  
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setCourseData({
      ...courseData,
      [name]: checked,
    });
  };
  
  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };
  
  const handleAddTag = () => {
    if (tagInput.trim() && !courseData.tags.includes(tagInput.trim())) {
      setCourseData({
        ...courseData,
        tags: [...courseData.tags, tagInput.trim()],
      });
      setTagInput('');
    }
  };
  
  const handleDeleteTag = (tagToDelete: string) => {
    setCourseData({
      ...courseData,
      tags: courseData.tags.filter((tag) => tag !== tagToDelete),
    });
  };
  
  const handleSubmit = async () => {
    try {
      // Generate slug from title
      const slug = courseData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      
      // Create course
      await dispatch(
        createCourse({
          ...courseData,
          slug,
          rating: 0,
          enrollmentCount: 0,
        })
      ).unwrap();
      
      // Navigate to course management
      navigate('/instructor/courses');
    } catch (error) {
      console.error('Failed to create course:', error);
    }
  };
  
  const validateStep = () => {
    switch (activeStep) {
      case 0:
        return courseData.title.trim() !== '' && courseData.description.trim() !== '';
      case 1:
        return (
          courseData.level !== '' &&
          courseData.duration > 0 &&
          courseData.objectives.trim() !== ''
        );
      case 2:
        return true;
      default:
        return false;
    }
  };
  
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                label="Course Title"
                name="title"
                value={courseData.title}
                onChange={handleInputChange}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                label="Course Description"
                name="description"
                value={courseData.description}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={6}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Thumbnail URL"
                name="thumbnailURL"
                value={courseData.thumbnailURL}
                onChange={handleInputChange}
                variant="outlined"
                helperText="Enter a URL for your course thumbnail image"
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Level</InputLabel>
                <Select
                  name="level"
                  value={courseData.level}
                  onChange={handleSelectChange}
                  label="Level"
                >
                  <MenuItem value="beginner">Beginner</MenuItem>
                  <MenuItem value="intermediate">Intermediate</MenuItem>
                  <MenuItem value="advanced">Advanced</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Duration (hours)"
                name="duration"
                type="number"
                value={courseData.duration}
                onChange={handleInputChange}
                variant="outlined"
                inputProps={{ min: 0, step: 0.5 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Prerequisites"
                name="prerequisites"
                value={courseData.prerequisites}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={3}
                helperText="What should students know before starting this course?"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                label="Learning Objectives"
                name="objectives"
                value={courseData.objectives}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={4}
                helperText="What will students learn? Enter each objective on a new line."
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ mb: 1 }}>
                <Typography variant="subtitle1">Tags</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TextField
                  fullWidth
                  label="Add a tag"
                  value={tagInput}
                  onChange={handleTagInputChange}
                  variant="outlined"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  variant="contained"
                  onClick={handleAddTag}
                  sx={{ ml: 1, height: 56 }}
                >
                  Add
                </Button>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {courseData.tags.map((tag) => (
                  <Chip
                    key={tag}
                    label={tag}
                    onDelete={() => handleDeleteTag(tag)}
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={courseData.isFree}
                    onChange={handleSwitchChange}
                    name="isFree"
                    color="primary"
                  />
                }
                label="This is a free course"
              />
            </Grid>
            {!courseData.isFree && (
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Price"
                  name="price"
                  type="number"
                  value={courseData.price}
                  onChange={handleInputChange}
                  variant="outlined"
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₦</InputAdornment>,
                  }}
                  inputProps={{ min: 0 }}
                />
              </Grid>
            )}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={courseData.isPublic}
                    onChange={handleSwitchChange}
                    name="isPublic"
                    color="primary"
                  />
                }
                label="Make this course public"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={courseData.status}
                  onChange={handleSelectChange}
                  label="Status"
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="upcoming">Upcoming</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );
      default:
        return 'Unknown step';
    }
  };
  
  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to create a course. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Create a New Course
        </Typography>
        
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ mb: 3 }}>{getStepContent(activeStep)}</Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
            variant="outlined"
          >
            Back
          </Button>
          <Box>
            <Button
              variant="outlined"
              onClick={() => navigate('/instructor/courses')}
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                color="primary"
                onClick={handleSubmit}
                disabled={!validateStep() || loading}
              >
                {loading ? <CircularProgress size={24} /> : 'Create Course'}
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
                disabled={!validateStep()}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default CourseCreationPage;
