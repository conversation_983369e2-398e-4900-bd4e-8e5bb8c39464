import api from './api';

export interface RewardPoint {
  id: number;
  userId: number;
  amount: number;
  source: string;
  sourceId?: number;
  reason: string;
  createdAt: string;
  expiresAt?: string;
}

export interface UserRewardSummary {
  userId: number;
  totalPoints: number;
  availablePoints: number;
  expiredPoints: number;
  redeemedPoints: number;
  level: number;
  nextLevelPoints: number;
  achievements: Achievement[];
  recentActivities: RewardActivity[];
}

export interface RewardActivity {
  id: number;
  userId: number;
  activityType: string;
  points: number;
  description: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface Achievement {
  id: number;
  name: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  requirements: AchievementRequirement[];
  isSecret: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AchievementRequirement {
  type: string;
  value: number;
  description: string;
}

export interface UserAchievement {
  id: number;
  userId: number;
  achievementId: number;
  achievement: Achievement;
  earnedAt: string;
  progress: number;
  isCompleted: boolean;
}

export interface Reward {
  id: number;
  name: string;
  description: string;
  image: string;
  pointsCost: number;
  category: string;
  quantity: number;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface RewardRedemption {
  id: number;
  userId: number;
  rewardId: number;
  reward: Reward;
  pointsSpent: number;
  status: 'pending' | 'approved' | 'rejected' | 'fulfilled';
  redeemedAt: string;
  fulfilledAt?: string;
  rejectionReason?: string;
}

export interface RewardRule {
  id: number;
  name: string;
  description: string;
  triggerAction: string;
  pointsAwarded: number;
  cooldownPeriod?: number; // in seconds
  maxOccurrences?: number;
  isActive: boolean;
  conditions?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface RewardTier {
  id: number;
  name: string;
  level: number;
  pointsRequired: number;
  benefits: string[];
  icon: string;
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface LeaderboardEntry {
  userId: number;
  username: string;
  profileImage?: string;
  points: number;
  level: number;
  rank: number;
  achievements: number;
}

const rewardsService = {
  // User points and summary
  getUserRewardSummary: async (userId: number) => {
    const response = await api.get(`/rewards/users/${userId}/summary`);
    return response.data;
  },

  getUserPointsHistory: async (userId: number, params?: { page?: number; limit?: number }) => {
    const response = await api.get(`/rewards/users/${userId}/points`, { params });
    return response.data;
  },

  // Activities
  getUserActivities: async (userId: number, params?: { page?: number; limit?: number }) => {
    const response = await api.get(`/rewards/users/${userId}/activities`, { params });
    return response.data;
  },

  trackActivity: async (activity: Partial<RewardActivity>) => {
    const response = await api.post('/rewards/activities', activity);
    return response.data;
  },

  // Achievements
  getAchievements: async (params?: { category?: string; isSecret?: boolean }) => {
    const response = await api.get('/rewards/achievements', { params });
    return response.data;
  },

  getAchievementById: async (id: number) => {
    const response = await api.get(`/rewards/achievements/${id}`);
    return response.data;
  },

  getUserAchievements: async (userId: number) => {
    const response = await api.get(`/rewards/users/${userId}/achievements`);
    return response.data;
  },

  // Rewards
  getRewards: async (params?: { category?: string; isActive?: boolean }) => {
    const response = await api.get('/rewards/rewards', { params });
    return response.data;
  },

  getRewardById: async (id: number) => {
    const response = await api.get(`/rewards/rewards/${id}`);
    return response.data;
  },

  redeemReward: async (userId: number, rewardId: number) => {
    const response = await api.post(`/rewards/users/${userId}/redeem`, { rewardId });
    return response.data;
  },

  getUserRedemptions: async (userId: number, params?: { status?: string }) => {
    const response = await api.get(`/rewards/users/${userId}/redemptions`, { params });
    return response.data;
  },

  // Admin endpoints
  createReward: async (reward: Partial<Reward>) => {
    const response = await api.post('/rewards/admin/rewards', reward);
    return response.data;
  },

  updateReward: async (id: number, reward: Partial<Reward>) => {
    const response = await api.put(`/rewards/admin/rewards/${id}`, reward);
    return response.data;
  },

  deleteReward: async (id: number) => {
    const response = await api.delete(`/rewards/admin/rewards/${id}`);
    return response.data;
  },

  createAchievement: async (achievement: Partial<Achievement>) => {
    const response = await api.post('/rewards/admin/achievements', achievement);
    return response.data;
  },

  updateAchievement: async (id: number, achievement: Partial<Achievement>) => {
    const response = await api.put(`/rewards/admin/achievements/${id}`, achievement);
    return response.data;
  },

  deleteAchievement: async (id: number) => {
    const response = await api.delete(`/rewards/admin/achievements/${id}`);
    return response.data;
  },

  // Rules
  getRules: async () => {
    const response = await api.get('/rewards/admin/rules');
    return response.data;
  },

  getRuleById: async (id: number) => {
    const response = await api.get(`/rewards/admin/rules/${id}`);
    return response.data;
  },

  createRule: async (rule: Partial<RewardRule>) => {
    const response = await api.post('/rewards/admin/rules', rule);
    return response.data;
  },

  updateRule: async (id: number, rule: Partial<RewardRule>) => {
    const response = await api.put(`/rewards/admin/rules/${id}`, rule);
    return response.data;
  },

  deleteRule: async (id: number) => {
    const response = await api.delete(`/rewards/admin/rules/${id}`);
    return response.data;
  },

  // Tiers
  getTiers: async () => {
    const response = await api.get('/rewards/tiers');
    return response.data;
  },

  getTierById: async (id: number) => {
    const response = await api.get(`/rewards/tiers/${id}`);
    return response.data;
  },

  createTier: async (tier: Partial<RewardTier>) => {
    const response = await api.post('/rewards/admin/tiers', tier);
    return response.data;
  },

  updateTier: async (id: number, tier: Partial<RewardTier>) => {
    const response = await api.put(`/rewards/admin/tiers/${id}`, tier);
    return response.data;
  },

  deleteTier: async (id: number) => {
    const response = await api.delete(`/rewards/admin/tiers/${id}`);
    return response.data;
  },

  // Redemption management
  getRedemptions: async (params?: { status?: string }) => {
    const response = await api.get('/rewards/admin/redemptions', { params });
    return response.data;
  },

  updateRedemptionStatus: async (id: number, status: string, notes?: string) => {
    const response = await api.put(`/rewards/admin/redemptions/${id}`, { status, notes });
    return response.data;
  },

  // Leaderboard
  getLeaderboard: async (params?: { timeframe?: 'all' | 'month' | 'week' | 'day'; limit?: number }) => {
    const response = await api.get('/rewards/leaderboard', { params });
    return response.data;
  },

  // Manual point adjustment (admin only)
  adjustUserPoints: async (userId: number, amount: number, reason: string) => {
    const response = await api.post(`/rewards/admin/users/${userId}/adjust-points`, {
      amount,
      reason,
    });
    return response.data;
  },
};

export default rewardsService;
