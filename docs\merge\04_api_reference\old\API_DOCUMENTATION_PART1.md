# Great Nigeria Platform - API Documentation (Part 1)

## Overview

This document provides comprehensive documentation for the Great Nigeria platform's API. The API follows RESTful principles and is implemented using Go with the Gin web framework. The API is organized around microservices, each responsible for specific functionality.

## Table of Contents

1. [Overview](#overview)
2. [API Gateway](#api-gateway)
3. [Authentication](#authentication)
4. [Error Handling](#error-handling)
5. [Rate Limiting](#rate-limiting)
6. [Core Services](#core-services)
   - [Auth Service](#auth-service)
   - [User Service](#user-service)
   - [Content Service](#content-service)
   - [Social Service](#social-service)
   - [Discussion Service](#discussion-service)
   - [Points Service](#points-service)
   - [Payment Service](#payment-service)
7. [Additional Services](#additional-services)
8. [Webhooks](#webhooks)
9. [API Versioning](#api-versioning)
10. [Testing](#testing)

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

### Base URL

```
https://api.greatnigeria.net/api
```

### Route Configuration

```go
// Example API Gateway route configuration in Go/Gin
func setupRoutes(r *gin.Engine) {
    // Public routes
    r.GET("/api/health", HealthCheck)
    r.GET("/api/books", ForwardToContentService)
    r.GET("/api/books/:id", ForwardToContentService)
    
    // Authentication routes
    r.POST("/api/register", ForwardToAuthService)
    r.POST("/api/login", ForwardToAuthService)
    r.POST("/api/logout", ForwardToAuthService)
    
    // Protected routes - require authentication
    authorized := r.Group("/api")
    authorized.Use(AuthMiddleware())
    {
        authorized.GET("/user", ForwardToAuthService)
        authorized.GET("/user/progress", ForwardToContentService)
        authorized.POST("/discussions", ForwardToDiscussionService)
        authorized.GET("/points", ForwardToPointsService)
        authorized.POST("/payments", ForwardToPaymentService)
    }
}
```

## Authentication

The Great Nigeria platform uses JWT (JSON Web Token) for authentication.

### JWT-Based Authentication

- Authentication header: `Authorization: Bearer {token}`
- Token structure: Header.Payload.Signature
- Claims: `sub`, `exp`, `iat`, `roles`, `permissions`
- Refresh token mechanism

### API Key Authentication (for services)

- API key header: `X-API-Key: {api_key}`
- Rate limiting by key
- Permission scoping

### Authentication Flow

1. **Registration**:
   - Client sends registration data to `/api/register`
   - Server validates data and creates user account
   - Server returns JWT token and refresh token

2. **Login**:
   - Client sends credentials to `/api/login`
   - Server validates credentials
   - Server returns JWT token and refresh token

3. **Token Refresh**:
   - Client sends refresh token to `/api/refresh-token`
   - Server validates refresh token
   - Server returns new JWT token

4. **Logout**:
   - Client sends request to `/api/logout`
   - Server invalidates refresh token
   - Client discards JWT token

### Example Authentication Requests

#### Registration

```http
POST /api/register HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "full_name": "New User"
}
```

Response:

```json
{
  "status": "success",
  "message": "User registered successfully",
  "data": {
    "user_id": "user-uuid",
    "username": "newuser",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh-token-uuid"
  }
}
```

#### Login

```http
POST /api/login HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

Response:

```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user_id": "user-uuid",
    "username": "newuser",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh-token-uuid"
  }
}
```

## Error Handling

The API uses consistent error responses across all endpoints.

### Error Response Format

```json
{
  "status": "error",
  "code": "error_code",
  "message": "Human-readable error message",
  "details": {
    "field1": "Error details for field1",
    "field2": "Error details for field2"
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `authentication_required` | Authentication is required for this endpoint |
| `invalid_credentials` | Invalid username or password |
| `invalid_token` | Invalid or expired token |
| `permission_denied` | User does not have permission for this action |
| `resource_not_found` | The requested resource was not found |
| `validation_error` | Request data failed validation |
| `rate_limit_exceeded` | Rate limit has been exceeded |
| `server_error` | Internal server error |

## Rate Limiting

The API implements rate limiting to prevent abuse and ensure fair usage.

### Rate Limit Implementation

- Token bucket algorithm with Redis backend
- Tiered rate limits based on user tier, IP address, and endpoint
- Graceful limiting with retry headers

### Rate Limit Headers

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1619194800
```

### Rate Limit Tiers

| User Tier | Requests per Minute |
|-----------|---------------------|
| Anonymous | 30 |
| Basic | 60 |
| Engaged | 100 |
| Active | 150 |
| Premium | 300 |

### Rate Limit Response

When a rate limit is exceeded, the API returns a 429 Too Many Requests response:

```json
{
  "status": "error",
  "code": "rate_limit_exceeded",
  "message": "Rate limit exceeded. Please try again later.",
  "details": {
    "retry_after": 30
  }
}
```
