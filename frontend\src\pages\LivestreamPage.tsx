import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Container, Grid, Typography, Button, Box, CircularProgress } from '@mui/material';
import { RootState } from '../store';
import { fetchActiveStreams } from '../features/livestream/livestreamSlice';
import StreamCard from '../components/livestream/StreamCard';
import CreateStreamModal from '../components/livestream/CreateStreamModal';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';
import { Stream } from '../api/livestreamService';
import { useNavigate } from 'react-router-dom';

const LivestreamPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { items: streams, loading, error } = useSelector((state: RootState) => state.livestream.streams);

  const [page, setPage] = useState(1);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  useEffect(() => {
    dispatch(fetchActiveStreams({ page, limit: 12 }) as any);
  }, [dispatch, page]);

  const handleStreamClick = (stream: Stream) => {
    navigate(`/livestream/${stream.id}`);
  };

  const handleCreateStream = () => {
    setIsCreateModalOpen(true);
  };

  const handleLoadMore = () => {
    setPage(prevPage => prevPage + 1);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Live Streaming
      </Typography>

      <LivestreamNavigation />

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h5" component="h2" gutterBottom>
          Active Streams
        </Typography>
        {user && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleCreateStream}
          >
            Create Stream
          </Button>
        )}
      </Box>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {loading && streams.length === 0 ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : streams.length > 0 ? (
        <>
          <Grid container spacing={3}>
            {streams.map((stream) => (
              <Grid item xs={12} sm={6} md={4} key={stream.id}>
                <StreamCard
                  stream={stream}
                  onClick={() => handleStreamClick(stream)}
                />
              </Grid>
            ))}
          </Grid>

          <Box display="flex" justifyContent="center" mt={4}>
            <Button
              variant="outlined"
              onClick={handleLoadMore}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Load More'}
            </Button>
          </Box>
        </>
      ) : (
        <Box textAlign="center" my={8}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No active streams at the moment
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Be the first to start streaming or check back later!
          </Typography>
        </Box>
      )}

      <CreateStreamModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />
    </Container>
  );
};

export default LivestreamPage;
