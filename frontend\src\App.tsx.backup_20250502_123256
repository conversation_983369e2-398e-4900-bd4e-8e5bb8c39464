import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from './store';

// Layout components
import MainLayout from './layouts/MainLayout';

// Page components
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ProfilePage from './pages/ProfilePage';
import BookPage from './pages/BookPage';
import ForumPage from './pages/ForumPage';
import LivestreamPage from './pages/LivestreamPage';
import LivestreamCreatePage from './pages/LivestreamCreatePage';
import LivestreamViewPage from './pages/LivestreamViewPage';
import MarketplacePage from './pages/MarketplacePage';
import ProductPage from './pages/ProductPage';
import CreateProductPage from './pages/CreateProductPage';
import WalletPage from './pages/WalletPage';
import EscrowPage from './pages/EscrowPage';
import DisputePage from './pages/DisputePage';
import CreateDisputePage from './pages/CreateDisputePage';
import AffiliatePage from './pages/AffiliatePage';

// Protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }
  
  return <>{children}</>;
};

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePage />} />
          <Route path="login" element={<LoginPage />} />
          <Route path="register" element={<RegisterPage />} />
          <Route path="profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="books/:bookId" element={<BookPage />} />
          <Route path="forum" element={<ForumPage />} />
          <Route path="livestream" element={
            <ProtectedRoute>
              <LivestreamPage />
            </ProtectedRoute>
          } />
          <Route path="livestream/create" element={
            <ProtectedRoute>
              <LivestreamCreatePage />
            </ProtectedRoute>
          } />
          <Route path="livestream/:streamId" element={<LivestreamViewPage />} />
          <Route path="marketplace" element={<MarketplacePage />} />
          <Route path="marketplace/product/:productId" element={<ProductPage />} />
          <Route path="marketplace/create" element={
            <ProtectedRoute>
              <CreateProductPage />
            </ProtectedRoute>
          } />
          <Route path="wallet" element={
            <ProtectedRoute>
              <WalletPage />
            </ProtectedRoute>
          } />
          <Route path="escrow" element={
            <ProtectedRoute>
              <EscrowPage />
            </ProtectedRoute>
          } />
          <Route path="escrow/disputes/:disputeId" element={
            <ProtectedRoute>
              <DisputePage />
            </ProtectedRoute>
          } />
          <Route path="escrow/disputes/create" element={
            <ProtectedRoute>
              <CreateDisputePage />
            </ProtectedRoute>
          } />
          <Route path="affiliate" element={
            <ProtectedRoute>
              <AffiliatePage />
            </ProtectedRoute>
          } />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
