package repository

import (
	"context"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/models"
	"gorm.io/gorm"
)

// GifterRanking represents the ranking of gifters in a stream or globally
type GifterRanking struct {
	gorm.Model
	UserID         uint      `json:"userId" gorm:"uniqueIndex:idx_gifter_ranking"`
	StreamID       *uint     `json:"streamId" gorm:"uniqueIndex:idx_gifter_ranking"` // null for global rankings
	RankingPeriod  string    `json:"rankingPeriod" gorm:"type:varchar(50);uniqueIndex:idx_gifter_ranking"` // daily, weekly, monthly, all_time
	PeriodStart    time.Time `json:"periodStart" gorm:"uniqueIndex:idx_gifter_ranking"`
	PeriodEnd      time.Time `json:"periodEnd"`
	TotalGifts     int       `json:"totalGifts" gorm:"default:0"`
	TotalCoins     float64   `json:"totalCoins" gorm:"default:0"`
	TotalNairaValue float64  `json:"totalNairaValue" gorm:"default:0"`
	Rank           int       `json:"rank" gorm:"default:0"`
	PreviousRank   *int      `json:"previousRank"`
	BadgeLevel     string    `json:"badgeLevel" gorm:"type:varchar(50);default:'bronze'"` // bronze, silver, gold, platinum, diamond
}

// GifterRankingRepository defines the interface for gifter ranking data access
type GifterRankingRepository interface {
	// Ranking CRUD operations
	CreateRanking(ctx context.Context, ranking *GifterRanking) error
	GetRankingByID(ctx context.Context, id uint) (*GifterRanking, error)
	UpdateRanking(ctx context.Context, ranking *GifterRanking) error
	
	// Ranking queries
	GetStreamRankings(ctx context.Context, streamID uint, period string, limit int) ([]GifterRanking, error)
	GetGlobalRankings(ctx context.Context, period string, limit int) ([]GifterRanking, error)
	GetUserRanking(ctx context.Context, userID uint, streamID *uint, period string) (*GifterRanking, error)
	
	// Ranking operations
	UpdateStreamRankings(ctx context.Context, streamID uint) error
	UpdateGlobalRankings(ctx context.Context) error
	CalculateBadgeLevels(ctx context.Context) error
}

// GifterRankingRepositoryImpl implements the GifterRankingRepository interface
type GifterRankingRepositoryImpl struct {
	db *gorm.DB
}

// NewGifterRankingRepository creates a new instance of the gifter ranking repository
func NewGifterRankingRepository(db *gorm.DB) GifterRankingRepository {
	return &GifterRankingRepositoryImpl{
		db: db,
	}
}

// CreateRanking creates a new gifter ranking
func (r *GifterRankingRepositoryImpl) CreateRanking(ctx context.Context, ranking *GifterRanking) error {
	return r.db.WithContext(ctx).Create(ranking).Error
}

// GetRankingByID retrieves a gifter ranking by its ID
func (r *GifterRankingRepositoryImpl) GetRankingByID(ctx context.Context, id uint) (*GifterRanking, error) {
	var ranking GifterRanking
	if err := r.db.WithContext(ctx).First(&ranking, id).Error; err != nil {
		return nil, err
	}
	return &ranking, nil
}

// UpdateRanking updates a gifter ranking
func (r *GifterRankingRepositoryImpl) UpdateRanking(ctx context.Context, ranking *GifterRanking) error {
	return r.db.WithContext(ctx).Save(ranking).Error
}

// GetStreamRankings retrieves rankings for a stream
func (r *GifterRankingRepositoryImpl) GetStreamRankings(ctx context.Context, streamID uint, period string, limit int) ([]GifterRanking, error) {
	var rankings []GifterRanking
	
	query := r.db.WithContext(ctx).
		Where("stream_id = ? AND ranking_period = ?", streamID, period).
		Order("rank ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&rankings).Error; err != nil {
		return nil, err
	}
	
	return rankings, nil
}

// GetGlobalRankings retrieves global rankings
func (r *GifterRankingRepositoryImpl) GetGlobalRankings(ctx context.Context, period string, limit int) ([]GifterRanking, error) {
	var rankings []GifterRanking
	
	query := r.db.WithContext(ctx).
		Where("stream_id IS NULL AND ranking_period = ?", period).
		Order("rank ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&rankings).Error; err != nil {
		return nil, err
	}
	
	return rankings, nil
}

// GetUserRanking retrieves a user's ranking
func (r *GifterRankingRepositoryImpl) GetUserRanking(ctx context.Context, userID uint, streamID *uint, period string) (*GifterRanking, error) {
	var ranking GifterRanking
	
	query := r.db.WithContext(ctx).
		Where("user_id = ? AND ranking_period = ?", userID, period)
	
	if streamID != nil {
		query = query.Where("stream_id = ?", *streamID)
	} else {
		query = query.Where("stream_id IS NULL")
	}
	
	if err := query.First(&ranking).Error; err != nil {
		return nil, err
	}
	
	return &ranking, nil
}

// UpdateStreamRankings updates rankings for a stream
func (r *GifterRankingRepositoryImpl) UpdateStreamRankings(ctx context.Context, streamID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get current time
		now := time.Now()
		
		// Define periods
		periods := []struct {
			Name  string
			Start time.Time
			End   time.Time
		}{
			{
				Name:  "daily",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location()),
			},
			{
				Name:  "weekly",
				Start: now.AddDate(0, 0, -int(now.Weekday())),
				End:   now.AddDate(0, 0, 6-int(now.Weekday())),
			},
			{
				Name:  "monthly",
				Start: time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month()+1, 0, 23, 59, 59, 999999999, now.Location()),
			},
			{
				Name:  "all_time",
				Start: time.Date(2020, 1, 1, 0, 0, 0, 0, now.Location()),
				End:   time.Date(2099, 12, 31, 23, 59, 59, 999999999, now.Location()),
			},
		}
		
		// Process each period
		for _, period := range periods {
			// Get gift data for this period
			type GiftData struct {
				UserID        uint
				TotalGifts    int
				TotalCoins    float64
				TotalNairaValue float64
			}
			
			var giftData []GiftData
			
			if err := tx.WithContext(ctx).Model(&LiveStreamGift{}).
				Select("sender_id as user_id, COUNT(*) as total_gifts, SUM(coins_amount) as total_coins, SUM(naira_value) as total_naira_value").
				Where("stream_id = ? AND created_at BETWEEN ? AND ?", streamID, period.Start, period.End).
				Group("sender_id").
				Order("total_coins DESC").
				Find(&giftData).Error; err != nil {
				return err
			}
			
			// Get existing rankings for this period
			var existingRankings []GifterRanking
			if err := tx.WithContext(ctx).
				Where("stream_id = ? AND ranking_period = ? AND period_start = ?", streamID, period.Name, period.Start).
				Find(&existingRankings).Error; err != nil {
				return err
			}
			
			// Create map of existing rankings
			existingMap := make(map[uint]GifterRanking)
			for _, ranking := range existingRankings {
				existingMap[ranking.UserID] = ranking
			}
			
			// Process each user's gift data
			for rank, data := range giftData {
				// Check if ranking already exists
				if existing, ok := existingMap[data.UserID]; ok {
					// Update existing ranking
					existing.TotalGifts = data.TotalGifts
					existing.TotalCoins = data.TotalCoins
					existing.TotalNairaValue = data.TotalNairaValue
					existing.PreviousRank = &existing.Rank
					existing.Rank = rank + 1
					
					if err := tx.Save(&existing).Error; err != nil {
						return err
					}
					
					// Remove from map to track processed rankings
					delete(existingMap, data.UserID)
				} else {
					// Create new ranking
					newRanking := GifterRanking{
						UserID:         data.UserID,
						StreamID:       &streamID,
						RankingPeriod:  period.Name,
						PeriodStart:    period.Start,
						PeriodEnd:      period.End,
						TotalGifts:     data.TotalGifts,
						TotalCoins:     data.TotalCoins,
						TotalNairaValue: data.TotalNairaValue,
						Rank:           rank + 1,
						BadgeLevel:     "bronze", // Default badge level
					}
					
					if err := tx.Create(&newRanking).Error; err != nil {
						return err
					}
				}
			}
		}
		
		return nil
	})
}

// UpdateGlobalRankings updates global rankings
func (r *GifterRankingRepositoryImpl) UpdateGlobalRankings(ctx context.Context) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get current time
		now := time.Now()
		
		// Define periods
		periods := []struct {
			Name  string
			Start time.Time
			End   time.Time
		}{
			{
				Name:  "daily",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location()),
			},
			{
				Name:  "weekly",
				Start: now.AddDate(0, 0, -int(now.Weekday())),
				End:   now.AddDate(0, 0, 6-int(now.Weekday())),
			},
			{
				Name:  "monthly",
				Start: time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month()+1, 0, 23, 59, 59, 999999999, now.Location()),
			},
			{
				Name:  "all_time",
				Start: time.Date(2020, 1, 1, 0, 0, 0, 0, now.Location()),
				End:   time.Date(2099, 12, 31, 23, 59, 59, 999999999, now.Location()),
			},
		}
		
		// Process each period
		for _, period := range periods {
			// Get gift data for this period
			type GiftData struct {
				UserID        uint
				TotalGifts    int
				TotalCoins    float64
				TotalNairaValue float64
			}
			
			var giftData []GiftData
			
			if err := tx.WithContext(ctx).Model(&LiveStreamGift{}).
				Select("sender_id as user_id, COUNT(*) as total_gifts, SUM(coins_amount) as total_coins, SUM(naira_value) as total_naira_value").
				Where("created_at BETWEEN ? AND ?", period.Start, period.End).
				Group("sender_id").
				Order("total_coins DESC").
				Find(&giftData).Error; err != nil {
				return err
			}
			
			// Get existing rankings for this period
			var existingRankings []GifterRanking
			if err := tx.WithContext(ctx).
				Where("stream_id IS NULL AND ranking_period = ? AND period_start = ?", period.Name, period.Start).
				Find(&existingRankings).Error; err != nil {
				return err
			}
			
			// Create map of existing rankings
			existingMap := make(map[uint]GifterRanking)
			for _, ranking := range existingRankings {
				existingMap[ranking.UserID] = ranking
			}
			
			// Process each user's gift data
			for rank, data := range giftData {
				// Check if ranking already exists
				if existing, ok := existingMap[data.UserID]; ok {
					// Update existing ranking
					existing.TotalGifts = data.TotalGifts
					existing.TotalCoins = data.TotalCoins
					existing.TotalNairaValue = data.TotalNairaValue
					existing.PreviousRank = &existing.Rank
					existing.Rank = rank + 1
					
					if err := tx.Save(&existing).Error; err != nil {
						return err
					}
					
					// Remove from map to track processed rankings
					delete(existingMap, data.UserID)
				} else {
					// Create new ranking
					newRanking := GifterRanking{
						UserID:         data.UserID,
						StreamID:       nil, // Global ranking
						RankingPeriod:  period.Name,
						PeriodStart:    period.Start,
						PeriodEnd:      period.End,
						TotalGifts:     data.TotalGifts,
						TotalCoins:     data.TotalCoins,
						TotalNairaValue: data.TotalNairaValue,
						Rank:           rank + 1,
						BadgeLevel:     "bronze", // Default badge level
					}
					
					if err := tx.Create(&newRanking).Error; err != nil {
						return err
					}
				}
			}
		}
		
		return nil
	})
}

// CalculateBadgeLevels calculates badge levels for all rankings
func (r *GifterRankingRepositoryImpl) CalculateBadgeLevels(ctx context.Context) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Define badge level thresholds
		badgeLevels := []struct {
			Level     string
			MinCoins  float64
		}{
			{Level: "diamond", MinCoins: 100000},
			{Level: "platinum", MinCoins: 50000},
			{Level: "gold", MinCoins: 10000},
			{Level: "silver", MinCoins: 1000},
			{Level: "bronze", MinCoins: 0},
		}
		
		// Update badge levels for all rankings
		for _, level := range badgeLevels {
			if err := tx.WithContext(ctx).Model(&GifterRanking{}).
				Where("total_coins >= ?", level.MinCoins).
				Update("badge_level", level.Level).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}
