import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { tutorialsService, Tutorial, TutorialStep, TutorialCategory } from '../../services/tutorialsService';

// State types
interface TutorialsState {
  tutorials: {
    items: Tutorial[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  userTutorials: {
    items: Tutorial[];
    loading: boolean;
    error: string | null;
  };
  currentTutorial: {
    data: Tutorial | null;
    loading: boolean;
    error: string | null;
  };
  categories: {
    items: TutorialCategory[];
    loading: boolean;
    error: string | null;
  };
  tutorialSteps: {
    items: TutorialStep[];
    loading: boolean;
    error: string | null;
  };
  currentStep: {
    data: TutorialStep | null;
    loading: boolean;
    error: string | null;
  };
  progress: {
    currentStepIndex: number;
    completedSteps: number[];
    isCompleted: boolean;
  };
}

// Initial state
const initialState: TutorialsState = {
  tutorials: {
    items: [],
    total: 0,
    loading: false,
    error: null,
  },
  userTutorials: {
    items: [],
    loading: false,
    error: null,
  },
  currentTutorial: {
    data: null,
    loading: false,
    error: null,
  },
  categories: {
    items: [],
    loading: false,
    error: null,
  },
  tutorialSteps: {
    items: [],
    loading: false,
    error: null,
  },
  currentStep: {
    data: null,
    loading: false,
    error: null,
  },
  progress: {
    currentStepIndex: 0,
    completedSteps: [],
    isCompleted: false,
  },
};

// Async thunks
export const fetchTutorials = createAsyncThunk(
  'tutorials/fetchTutorials',
  async ({ page = 1, pageSize = 10, filters = {} }: { page?: number; pageSize?: number; filters?: any }, { rejectWithValue }) => {
    try {
      return await tutorialsService.getTutorials(page, pageSize, filters);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch tutorials');
    }
  }
);

export const fetchUserTutorials = createAsyncThunk(
  'tutorials/fetchUserTutorials',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await tutorialsService.getUserTutorials(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user tutorials');
    }
  }
);

export const fetchTutorialById = createAsyncThunk(
  'tutorials/fetchTutorialById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await tutorialsService.getTutorialById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch tutorial');
    }
  }
);

export const fetchTutorialBySlug = createAsyncThunk(
  'tutorials/fetchTutorialBySlug',
  async (slug: string, { rejectWithValue }) => {
    try {
      return await tutorialsService.getTutorialBySlug(slug);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch tutorial');
    }
  }
);

export const createTutorial = createAsyncThunk(
  'tutorials/createTutorial',
  async (tutorial: Partial<Tutorial>, { rejectWithValue }) => {
    try {
      return await tutorialsService.createTutorial(tutorial);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create tutorial');
    }
  }
);

export const updateTutorial = createAsyncThunk(
  'tutorials/updateTutorial',
  async ({ id, tutorial }: { id: number; tutorial: Partial<Tutorial> }, { rejectWithValue }) => {
    try {
      return await tutorialsService.updateTutorial(id, tutorial);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update tutorial');
    }
  }
);

export const deleteTutorial = createAsyncThunk(
  'tutorials/deleteTutorial',
  async (id: number, { rejectWithValue }) => {
    try {
      await tutorialsService.deleteTutorial(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete tutorial');
    }
  }
);

export const fetchTutorialSteps = createAsyncThunk(
  'tutorials/fetchTutorialSteps',
  async (tutorialId: number, { rejectWithValue }) => {
    try {
      return await tutorialsService.getTutorialSteps(tutorialId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch tutorial steps');
    }
  }
);

export const fetchStepById = createAsyncThunk(
  'tutorials/fetchStepById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await tutorialsService.getStepById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch step');
    }
  }
);

export const createStep = createAsyncThunk(
  'tutorials/createStep',
  async ({ tutorialId, step }: { tutorialId: number; step: Partial<TutorialStep> }, { rejectWithValue }) => {
    try {
      return await tutorialsService.createStep(tutorialId, step);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create step');
    }
  }
);

export const updateStep = createAsyncThunk(
  'tutorials/updateStep',
  async ({ id, step }: { id: number; step: Partial<TutorialStep> }, { rejectWithValue }) => {
    try {
      return await tutorialsService.updateStep(id, step);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update step');
    }
  }
);

export const deleteStep = createAsyncThunk(
  'tutorials/deleteStep',
  async (id: number, { rejectWithValue }) => {
    try {
      await tutorialsService.deleteStep(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete step');
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'tutorials/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await tutorialsService.getCategories();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch categories');
    }
  }
);

export const markStepCompleted = createAsyncThunk(
  'tutorials/markStepCompleted',
  async ({ userId, stepId }: { userId: number; stepId: number }, { rejectWithValue }) => {
    try {
      return await tutorialsService.markStepCompleted(userId, stepId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to mark step as completed');
    }
  }
);

export const fetchTutorialProgress = createAsyncThunk(
  'tutorials/fetchTutorialProgress',
  async ({ userId, tutorialId }: { userId: number; tutorialId: number }, { rejectWithValue }) => {
    try {
      return await tutorialsService.getTutorialProgress(userId, tutorialId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch tutorial progress');
    }
  }
);

// Slice
const tutorialsSlice = createSlice({
  name: 'tutorials',
  initialState,
  reducers: {
    resetCurrentTutorial: (state) => {
      state.currentTutorial.data = null;
      state.currentTutorial.error = null;
    },
    resetCurrentStep: (state) => {
      state.currentStep.data = null;
      state.currentStep.error = null;
    },
    setCurrentStepIndex: (state, action: PayloadAction<number>) => {
      state.progress.currentStepIndex = action.payload;
    },
    resetProgress: (state) => {
      state.progress.currentStepIndex = 0;
      state.progress.completedSteps = [];
      state.progress.isCompleted = false;
    },
  },
  extraReducers: (builder) => {
    // Fetch tutorials
    builder.addCase(fetchTutorials.pending, (state) => {
      state.tutorials.loading = true;
      state.tutorials.error = null;
    });
    builder.addCase(fetchTutorials.fulfilled, (state, action) => {
      state.tutorials.loading = false;
      state.tutorials.items = action.payload.data;
      state.tutorials.total = action.payload.meta.total;
    });
    builder.addCase(fetchTutorials.rejected, (state, action) => {
      state.tutorials.loading = false;
      state.tutorials.error = action.payload as string;
    });

    // Fetch user tutorials
    builder.addCase(fetchUserTutorials.pending, (state) => {
      state.userTutorials.loading = true;
      state.userTutorials.error = null;
    });
    builder.addCase(fetchUserTutorials.fulfilled, (state, action) => {
      state.userTutorials.loading = false;
      state.userTutorials.items = action.payload;
    });
    builder.addCase(fetchUserTutorials.rejected, (state, action) => {
      state.userTutorials.loading = false;
      state.userTutorials.error = action.payload as string;
    });

    // Fetch tutorial by ID
    builder.addCase(fetchTutorialById.pending, (state) => {
      state.currentTutorial.loading = true;
      state.currentTutorial.error = null;
    });
    builder.addCase(fetchTutorialById.fulfilled, (state, action) => {
      state.currentTutorial.loading = false;
      state.currentTutorial.data = action.payload;
    });
    builder.addCase(fetchTutorialById.rejected, (state, action) => {
      state.currentTutorial.loading = false;
      state.currentTutorial.error = action.payload as string;
    });

    // Fetch tutorial by slug
    builder.addCase(fetchTutorialBySlug.pending, (state) => {
      state.currentTutorial.loading = true;
      state.currentTutorial.error = null;
    });
    builder.addCase(fetchTutorialBySlug.fulfilled, (state, action) => {
      state.currentTutorial.loading = false;
      state.currentTutorial.data = action.payload;
    });
    builder.addCase(fetchTutorialBySlug.rejected, (state, action) => {
      state.currentTutorial.loading = false;
      state.currentTutorial.error = action.payload as string;
    });

    // Create tutorial
    builder.addCase(createTutorial.fulfilled, (state, action) => {
      state.tutorials.items.push(action.payload);
      state.currentTutorial.data = action.payload;
    });

    // Update tutorial
    builder.addCase(updateTutorial.fulfilled, (state, action) => {
      const index = state.tutorials.items.findIndex(tutorial => tutorial.id === action.payload.id);
      if (index !== -1) {
        state.tutorials.items[index] = action.payload;
      }
      state.currentTutorial.data = action.payload;
    });

    // Delete tutorial
    builder.addCase(deleteTutorial.fulfilled, (state, action) => {
      state.tutorials.items = state.tutorials.items.filter(tutorial => tutorial.id !== action.payload);
      if (state.currentTutorial.data?.id === action.payload) {
        state.currentTutorial.data = null;
      }
    });

    // Fetch tutorial steps
    builder.addCase(fetchTutorialSteps.pending, (state) => {
      state.tutorialSteps.loading = true;
      state.tutorialSteps.error = null;
    });
    builder.addCase(fetchTutorialSteps.fulfilled, (state, action) => {
      state.tutorialSteps.loading = false;
      state.tutorialSteps.items = action.payload;
    });
    builder.addCase(fetchTutorialSteps.rejected, (state, action) => {
      state.tutorialSteps.loading = false;
      state.tutorialSteps.error = action.payload as string;
    });

    // Fetch step by ID
    builder.addCase(fetchStepById.pending, (state) => {
      state.currentStep.loading = true;
      state.currentStep.error = null;
    });
    builder.addCase(fetchStepById.fulfilled, (state, action) => {
      state.currentStep.loading = false;
      state.currentStep.data = action.payload;
    });
    builder.addCase(fetchStepById.rejected, (state, action) => {
      state.currentStep.loading = false;
      state.currentStep.error = action.payload as string;
    });

    // Create step
    builder.addCase(createStep.fulfilled, (state, action) => {
      state.tutorialSteps.items.push(action.payload);
      state.currentStep.data = action.payload;
    });

    // Update step
    builder.addCase(updateStep.fulfilled, (state, action) => {
      const index = state.tutorialSteps.items.findIndex(step => step.id === action.payload.id);
      if (index !== -1) {
        state.tutorialSteps.items[index] = action.payload;
      }
      state.currentStep.data = action.payload;
    });

    // Delete step
    builder.addCase(deleteStep.fulfilled, (state, action) => {
      state.tutorialSteps.items = state.tutorialSteps.items.filter(step => step.id !== action.payload);
      if (state.currentStep.data?.id === action.payload) {
        state.currentStep.data = null;
      }
    });

    // Fetch categories
    builder.addCase(fetchCategories.pending, (state) => {
      state.categories.loading = true;
      state.categories.error = null;
    });
    builder.addCase(fetchCategories.fulfilled, (state, action) => {
      state.categories.loading = false;
      state.categories.items = action.payload;
    });
    builder.addCase(fetchCategories.rejected, (state, action) => {
      state.categories.loading = false;
      state.categories.error = action.payload as string;
    });

    // Mark step completed
    builder.addCase(markStepCompleted.fulfilled, (state, action) => {
      const stepId = action.payload.stepId;
      if (!state.progress.completedSteps.includes(stepId)) {
        state.progress.completedSteps.push(stepId);
      }
      
      // Check if all steps are completed
      if (state.tutorialSteps.items.length > 0 && 
          state.progress.completedSteps.length === state.tutorialSteps.items.length) {
        state.progress.isCompleted = true;
      }
    });

    // Fetch tutorial progress
    builder.addCase(fetchTutorialProgress.fulfilled, (state, action) => {
      state.progress.completedSteps = action.payload.completedSteps;
      state.progress.currentStepIndex = action.payload.currentStepIndex;
      state.progress.isCompleted = action.payload.isCompleted;
    });
  },
});

// Export actions and reducer
export const { 
  resetCurrentTutorial, 
  resetCurrentStep, 
  setCurrentStepIndex, 
  resetProgress 
} = tutorialsSlice.actions;

export default tutorialsSlice.reducer;
