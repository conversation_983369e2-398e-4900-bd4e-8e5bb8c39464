import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  Rating,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import { AppDispatch, RootState } from '../store';
import { fetchCourses } from '../store/slices/coursesSlice';
import { Course } from '../services/coursesService';

const CoursesPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { items: courses, total, loading, error } = useSelector((state: RootState) => state.courses.courses);
  
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    isFree: '',
    level: '',
    sortBy: 'newest',
  });
  
  useEffect(() => {
    const fetchData = () => {
      const filterParams: any = {};
      
      if (filters.status) filterParams.status = filters.status;
      if (filters.isFree) filterParams.isFree = filters.isFree === 'true';
      if (filters.level) filterParams.level = filters.level;
      if (searchQuery) filterParams.search = searchQuery;
      
      dispatch(fetchCourses({ page, pageSize, filters: filterParams }));
    };
    
    fetchData();
  }, [dispatch, page, pageSize, filters, searchQuery]);
  
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };
  
  const handleFilterChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFilters({
      ...filters,
      [name]: value,
    });
    setPage(1); // Reset to first page when filters change
  };
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  
  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setPage(1); // Reset to first page when search changes
  };
  
  const totalPages = Math.ceil(total / pageSize);
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Courses
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <form onSubmit={handleSearchSubmit}>
              <TextField
                fullWidth
                label="Search courses"
                variant="outlined"
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </form>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                    label="Status"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="published">Published</MenuItem>
                    <MenuItem value="upcoming">Upcoming</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Price</InputLabel>
                  <Select
                    name="isFree"
                    value={filters.isFree}
                    onChange={handleFilterChange}
                    label="Price"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="true">Free</MenuItem>
                    <MenuItem value="false">Paid</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Level</InputLabel>
                  <Select
                    name="level"
                    value={filters.level}
                    onChange={handleFilterChange}
                    label="Level"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="beginner">Beginner</MenuItem>
                    <MenuItem value="intermediate">Intermediate</MenuItem>
                    <MenuItem value="advanced">Advanced</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      
      <Divider sx={{ mb: 4 }} />
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      ) : courses.length === 0 ? (
        <Alert severity="info" sx={{ mb: 4 }}>
          No courses found. Try adjusting your filters.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {courses.map((course: Course) => (
              <Grid item key={course.id} xs={12} sm={6} md={4}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardMedia
                    component="img"
                    height="140"
                    image={course.thumbnailURL || '/images/course-placeholder.jpg'}
                    alt={course.title}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography gutterBottom variant="h5" component="h2">
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {course.description.length > 120
                        ? `${course.description.substring(0, 120)}...`
                        : course.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                        {course.instructorName}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Rating value={course.rating} precision={0.5} readOnly size="small" />
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        ({course.rating.toFixed(1)})
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {course.enrollmentCount} students
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      <Chip
                        label={course.level}
                        size="small"
                        color={
                          course.level === 'beginner'
                            ? 'success'
                            : course.level === 'intermediate'
                            ? 'primary'
                            : 'secondary'
                        }
                      />
                      {course.isFree ? (
                        <Chip label="Free" size="small" color="success" />
                      ) : (
                        <Chip label={`₦${course.price.toLocaleString()}`} size="small" color="default" />
                      )}
                      {course.tags.slice(0, 2).map((tag) => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      component={Link}
                      to={`/courses/${course.id}`}
                      variant="contained"
                      fullWidth
                    >
                      View Course
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        </>
      )}
    </Container>
  );
};

export default CoursesPage;
