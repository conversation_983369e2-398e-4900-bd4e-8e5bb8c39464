import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Divider,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Badge,
  Checkbox,
  FormGroup,
  FormHelperText,
  Snackbar,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Event as EventIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Public as PublicIcon,
  Videocam as VideocamIcon,
  QrCode as QrCodeIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchGroupById,
  fetchGroupEvents,
  fetchEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  fetchEventAttendees,
  addEventAttendee,
  updateEventAttendee,
  removeEventAttendee,
  checkInEventAttendee,
  LocalEvent,
  EventAttendee,
} from '../store/slices/groupsSlice';

// Helper components
const EventCard: React.FC<{
  event: LocalEvent;
  onView: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  isOwner: boolean;
}> = ({ event, onView, onEdit, onDelete, isOwner }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned':
        return '#ff9800'; // orange
      case 'confirmed':
        return '#4caf50'; // green
      case 'in_progress':
        return '#2196f3'; // blue
      case 'completed':
        return '#9e9e9e'; // grey
      case 'cancelled':
        return '#f44336'; // red
      default:
        return '#9e9e9e'; // grey
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'confirmed':
        return 'Confirmed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography variant="h6" component="div">
            {event.title}
          </Typography>
          <Chip
            label={getStatusText(event.status)}
            size="small"
            sx={{ bgcolor: getStatusColor(event.status), color: 'white' }}
          />
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
          {event.description.length > 150
            ? `${event.description.substring(0, 150)}...`
            : event.description}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {formatDate(event.startTime)} at {formatTime(event.startTime)}
          </Typography>
        </Box>

        {event.location && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              {event.location}
            </Typography>
          </Box>
        )}

        {event.isVirtual && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <VideocamIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              Virtual Participation Available
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <PeopleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {event.attendees?.length || 0} attendees
            {event.maxAttendees > 0 && ` (max: ${event.maxAttendees})`}
          </Typography>
        </Box>
      </CardContent>
      <CardActions>
        <Button size="small" onClick={onView}>
          View Details
        </Button>
        {isOwner && (
          <>
            {onEdit && (
              <Button size="small" startIcon={<EditIcon />} onClick={onEdit}>
                Edit
              </Button>
            )}
            {onDelete && (
              <Button size="small" color="error" startIcon={<DeleteIcon />} onClick={onDelete}>
                Delete
              </Button>
            )}
          </>
        )}
      </CardActions>
    </Card>
  );
};

const CreateEventDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onSubmit: (event: Partial<LocalEvent>) => void;
  loading: boolean;
  groupId: number;
  initialData?: LocalEvent;
  isEdit?: boolean;
}> = ({ open, onClose, onSubmit, loading, groupId, initialData, isEdit = false }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [isVirtual, setIsVirtual] = useState(false);
  const [virtualLink, setVirtualLink] = useState('');
  const [startTime, setStartTime] = useState<Date | null>(new Date());
  const [endTime, setEndTime] = useState<Date | null>(
    new Date(new Date().getTime() + 2 * 60 * 60 * 1000)
  ); // Default to 2 hours later
  const [status, setStatus] = useState<'planned' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'>(
    'planned'
  );
  const [maxAttendees, setMaxAttendees] = useState(0);

  useEffect(() => {
    if (initialData && open) {
      setTitle(initialData.title);
      setDescription(initialData.description);
      setLocation(initialData.location);
      setIsVirtual(initialData.isVirtual);
      setVirtualLink(initialData.virtualLink || '');
      setStartTime(new Date(initialData.startTime));
      setEndTime(new Date(initialData.endTime));
      setStatus(initialData.status as any);
      setMaxAttendees(initialData.maxAttendees);
    } else if (open) {
      // Reset form for new event
      setTitle('');
      setDescription('');
      setLocation('');
      setIsVirtual(false);
      setVirtualLink('');
      setStartTime(new Date());
      setEndTime(new Date(new Date().getTime() + 2 * 60 * 60 * 1000));
      setStatus('planned');
      setMaxAttendees(0);
    }
  }, [initialData, open]);

  const handleSubmit = () => {
    if (!startTime || !endTime) {
      return;
    }

    const event: Partial<LocalEvent> = {
      title,
      description,
      location,
      isVirtual,
      virtualLink: virtualLink || undefined,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      status,
      maxAttendees,
      groupId,
    };

    onSubmit(event);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{isEdit ? 'Edit Event' : 'Create New Event'}</DialogTitle>
      <DialogContent>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Event Title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                multiline
                rows={4}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Maximum Attendees (0 for unlimited)"
                type="number"
                value={maxAttendees}
                onChange={(e) => setMaxAttendees(parseInt(e.target.value) || 0)}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <DateTimePicker
                label="Start Time"
                value={startTime}
                onChange={(newValue) => setStartTime(newValue)}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <DateTimePicker
                label="End Time"
                value={endTime}
                onChange={(newValue) => setEndTime(newValue)}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isVirtual}
                    onChange={(e) => setIsVirtual(e.target.checked)}
                  />
                }
                label="Virtual Participation Available"
              />
            </Grid>
            {isVirtual && (
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Virtual Meeting Link"
                  value={virtualLink}
                  onChange={(e) => setVirtualLink(e.target.value)}
                  placeholder="https://..."
                />
              </Grid>
            )}
            {isEdit && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={status}
                    onChange={(e) => setStatus(e.target.value as any)}
                    label="Status"
                  >
                    <MenuItem value="planned">Planned</MenuItem>
                    <MenuItem value="confirmed">Confirmed</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>
        </LocalizationProvider>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={!title || !description || !startTime || !endTime || loading}
        >
          {loading ? <CircularProgress size={24} /> : isEdit ? 'Update Event' : 'Create Event'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Main component
const LocalEventManagement: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useSelector((state: RootState) => state.auth);
  const {
    currentGroup: { data: group, loading: groupLoading, error: groupError },
    events: { items: events, loading: eventsLoading, error: eventsError },
    currentEvent: { data: currentEvent, loading: currentEventLoading },
  } = useSelector((state: RootState) => state.groups);

  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<LocalEvent | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useEffect(() => {
    if (groupId) {
      dispatch(fetchGroupById(parseInt(groupId)));
      dispatch(fetchGroupEvents({ groupId: parseInt(groupId) }));
    }
  }, [dispatch, groupId]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCreateEvent = async (event: Partial<LocalEvent>) => {
    if (!groupId) return;

    try {
      await dispatch(createEvent({ groupId: parseInt(groupId), event })).unwrap();
      setCreateDialogOpen(false);
      dispatch(fetchGroupEvents({ groupId: parseInt(groupId) }));
      setSnackbarMessage('Event created successfully');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Failed to create event:', error);
      setSnackbarMessage('Failed to create event');
      setSnackbarOpen(true);
    }
  };

  const handleUpdateEvent = async (event: Partial<LocalEvent>) => {
    if (!selectedEvent) return;

    try {
      await dispatch(updateEvent({ eventId: selectedEvent.id, event })).unwrap();
      setEditDialogOpen(false);
      dispatch(fetchGroupEvents({ groupId: parseInt(groupId!) }));
      setSnackbarMessage('Event updated successfully');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Failed to update event:', error);
      setSnackbarMessage('Failed to update event');
      setSnackbarOpen(true);
    }
  };

  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;

    try {
      await dispatch(deleteEvent(selectedEvent.id)).unwrap();
      setDeleteDialogOpen(false);
      setSelectedEvent(null);
      dispatch(fetchGroupEvents({ groupId: parseInt(groupId!) }));
      setSnackbarMessage('Event deleted successfully');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Failed to delete event:', error);
      setSnackbarMessage('Failed to delete event');
      setSnackbarOpen(true);
    }
  };

  const handleViewEvent = (event: LocalEvent) => {
    setSelectedEvent(event);
    setDetailsDialogOpen(true);
    dispatch(fetchEventAttendees({ eventId: event.id }));
  };

  const handleEditEvent = (event: LocalEvent) => {
    setSelectedEvent(event);
    setEditDialogOpen(true);
  };

  const handleDeleteEventDialog = (event: LocalEvent) => {
    setSelectedEvent(event);
    setDeleteDialogOpen(true);
  };

  const isGroupOwner = group && user && group.createdById === user.id;
  const isGroupAdmin = group && user && group.members?.some(
    member => member.userId === user.id && (member.role === 'owner' || member.role === 'admin')
  );

  const filteredEvents = statusFilter
    ? events.filter(event => event.status === statusFilter)
    : events;

  const loading = groupLoading || eventsLoading || currentEventLoading;

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to manage events. Please log in and try again.
        </Alert>
      </Container>
    );
  }

  if (!group && !groupLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">Group not found.</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1">
            {group ? group.name : 'Loading...'}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Event Management
          </Typography>
        </Box>
        {(isGroupOwner || isGroupAdmin) && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Create Event
          </Button>
        )}
      </Box>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Upcoming Events" />
          <Tab label="Past Events" />
          <Tab label="All Events" />
        </Tabs>
      </Paper>

      {(groupError || eventsError) && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {groupError || eventsError}
        </Alert>
      )}

      <Box sx={{ mb: 3 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Filter by Status</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            label="Filter by Status"
          >
            <MenuItem value="">All Statuses</MenuItem>
            <MenuItem value="planned">Planned</MenuItem>
            <MenuItem value="confirmed">Confirmed</MenuItem>
            <MenuItem value="in_progress">In Progress</MenuItem>
            <MenuItem value="completed">Completed</MenuItem>
            <MenuItem value="cancelled">Cancelled</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {filteredEvents.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <EventIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                No events found
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                {statusFilter
                  ? `There are no ${statusFilter} events.`
                  : tabValue === 0
                  ? 'There are no upcoming events scheduled.'
                  : tabValue === 1
                  ? 'There are no past events.'
                  : 'There are no events for this group yet.'}
              </Typography>
              {(isGroupOwner || isGroupAdmin) && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => setCreateDialogOpen(true)}
                >
                  Create Event
                </Button>
              )}
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {filteredEvents
                .filter((event) => {
                  const eventDate = new Date(event.startTime);
                  const now = new Date();
                  
                  if (tabValue === 0) {
                    // Upcoming events
                    return eventDate >= now && event.status !== 'completed' && event.status !== 'cancelled';
                  } else if (tabValue === 1) {
                    // Past events
                    return eventDate < now || event.status === 'completed';
                  }
                  // All events
                  return true;
                })
                .map((event) => (
                  <Grid item key={event.id} xs={12} sm={6} md={4}>
                    <EventCard
                      event={event}
                      onView={() => handleViewEvent(event)}
                      onEdit={isGroupOwner || isGroupAdmin ? () => handleEditEvent(event) : undefined}
                      onDelete={isGroupOwner || isGroupAdmin ? () => handleDeleteEventDialog(event) : undefined}
                      isOwner={isGroupOwner || isGroupAdmin}
                    />
                  </Grid>
                ))}
            </Grid>
          )}
        </>
      )}

      {/* Create Event Dialog */}
      <CreateEventDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateEvent}
        loading={loading}
        groupId={parseInt(groupId!)}
      />

      {/* Edit Event Dialog */}
      <CreateEventDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        onSubmit={handleUpdateEvent}
        loading={loading}
        groupId={parseInt(groupId!)}
        initialData={selectedEvent!}
        isEdit
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Event</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{selectedEvent?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteEvent} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default LocalEventManagement;
