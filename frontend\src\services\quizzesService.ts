import api from './api';

export interface Quiz {
  id: number;
  title: string;
  description: string;
  timeLimit: number; // in minutes, 0 means no time limit
  passingScore: number; // percentage required to pass
  isPublished: boolean;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  categoryId?: number;
  categoryName?: string;
  courseId?: number;
  lessonId?: number;
  tutorialId?: number;
}

export interface Question {
  id: number;
  quizId: number;
  questionText: string;
  questionType: 'multiple_choice' | 'true_false' | 'matching' | 'short_answer' | 'essay';
  options: string[]; // For multiple choice and matching questions
  correctAnswer: string | string[]; // Can be a single answer or array of answers
  points: number;
  order: number;
  explanation?: string; // Explanation of the correct answer
  imageUrl?: string; // Optional image for the question
}

export interface QuizAttempt {
  id: number;
  quizId: number;
  userId: number;
  startTime: string;
  endTime?: string;
  score: number;
  isPassed: boolean;
  answers: {
    questionId: number;
    userAnswer: string | string[];
    isCorrect: boolean;
    points: number;
  }[];
}

const quizzesService = {
  // Quiz CRUD operations
  getQuizzes: async (params?: { page?: number; pageSize?: number; filters?: any }) => {
    const response = await api.get('/quizzes', { params });
    return response.data;
  },

  getQuizById: async (id: number) => {
    const response = await api.get(`/quizzes/${id}`);
    return response.data;
  },

  getQuizzesByCategory: async (categoryId: number) => {
    const response = await api.get(`/quizzes/category/${categoryId}`);
    return response.data;
  },

  getQuizzesByCourse: async (courseId: number) => {
    const response = await api.get(`/quizzes/course/${courseId}`);
    return response.data;
  },

  getQuizzesByLesson: async (lessonId: number) => {
    const response = await api.get(`/quizzes/lesson/${lessonId}`);
    return response.data;
  },

  getQuizzesByTutorial: async (tutorialId: number) => {
    const response = await api.get(`/quizzes/tutorial/${tutorialId}`);
    return response.data;
  },

  getUserCreatedQuizzes: async (userId: number) => {
    const response = await api.get(`/quizzes/user/${userId}/created`);
    return response.data;
  },

  getUserAttemptedQuizzes: async (userId: number) => {
    const response = await api.get(`/quizzes/user/${userId}/attempted`);
    return response.data;
  },

  createQuiz: async (quiz: Partial<Quiz>) => {
    const response = await api.post('/quizzes', quiz);
    return response.data;
  },

  updateQuiz: async (id: number, quiz: Partial<Quiz>) => {
    const response = await api.put(`/quizzes/${id}`, quiz);
    return response.data;
  },

  deleteQuiz: async (id: number) => {
    const response = await api.delete(`/quizzes/${id}`);
    return response.data;
  },

  publishQuiz: async (id: number) => {
    const response = await api.put(`/quizzes/${id}/publish`);
    return response.data;
  },

  unpublishQuiz: async (id: number) => {
    const response = await api.put(`/quizzes/${id}/unpublish`);
    return response.data;
  },

  // Question CRUD operations
  getQuestions: async (quizId: number) => {
    const response = await api.get(`/quizzes/${quizId}/questions`);
    return response.data;
  },

  getQuestionById: async (id: number) => {
    const response = await api.get(`/questions/${id}`);
    return response.data;
  },

  createQuestion: async (quizId: number, question: Partial<Question>) => {
    const response = await api.post(`/quizzes/${quizId}/questions`, question);
    return response.data;
  },

  updateQuestion: async (id: number, question: Partial<Question>) => {
    const response = await api.put(`/questions/${id}`, question);
    return response.data;
  },

  deleteQuestion: async (id: number) => {
    const response = await api.delete(`/questions/${id}`);
    return response.data;
  },

  reorderQuestions: async (quizId: number, questionIds: number[]) => {
    const response = await api.put(`/quizzes/${quizId}/questions/reorder`, { questionIds });
    return response.data;
  },

  // Quiz attempt operations
  startQuizAttempt: async (quizId: number, userId: number) => {
    const response = await api.post('/quiz-attempts', { quizId, userId });
    return response.data;
  },

  submitQuizAttempt: async (attemptId: number, answers: { questionId: number; answer: string | string[] }[]) => {
    const response = await api.put(`/quiz-attempts/${attemptId}/submit`, { answers });
    return response.data;
  },

  getQuizAttempt: async (attemptId: number) => {
    const response = await api.get(`/quiz-attempts/${attemptId}`);
    return response.data;
  },

  getUserQuizAttempts: async (userId: number, quizId: number) => {
    const response = await api.get(`/quiz-attempts/user/${userId}/quiz/${quizId}`);
    return response.data;
  },

  // Quiz analytics
  getQuizAnalytics: async (quizId: number) => {
    const response = await api.get(`/quizzes/${quizId}/analytics`);
    return response.data;
  },

  getQuestionAnalytics: async (questionId: number) => {
    const response = await api.get(`/questions/${questionId}/analytics`);
    return response.data;
  },

  getUserQuizAnalytics: async (userId: number) => {
    const response = await api.get(`/users/${userId}/quiz-analytics`);
    return response.data;
  },
};

export default quizzesService;
