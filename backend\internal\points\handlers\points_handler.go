package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/models"
        "github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/service"
)

// PointsHandler handles API requests for points
type PointsHandler struct {
        pointsService service.PointsService
}

// NewPointsHandler creates a new points handler
func NewPointsHandler(pointsService service.PointsService) *PointsHandler {
        return &PointsHandler{
                pointsService: pointsService,
        }
}

// RegisterRoutes registers the points routes
func (h *PointsHandler) RegisterRoutes(router *gin.RouterGroup) {
        points := router.Group("/points")
        {
                // Basic points operations
                points.GET("/balance", h.GetUserPointsBalance)
                points.GET("/history", h.GetPointsHistory)
                points.GET("/history/:type", h.GetPointsHistoryByType)
                points.POST("/award", h.AwardPoints)
                points.POST("/deduct", h.DeductPoints)
                
                // Membership tier
                points.GET("/levels", h.GetTierRequirements)
                points.GET("/membership", h.GetUserMembership)
                points.GET("/users/tier/:tier", h.GetUsersByTier)
                points.POST("/evaluate-tier/:userId", h.EvaluateUserTier)
                points.POST("/evaluate-tiers", h.BulkEvaluateUserTiers)
                points.POST("/set-premium", h.SetPremiumMembership)
                
                // Register discussion points handlers (new)
                discPointsConfig := DefaultDiscussionPointsConfig()
                discussionPointsHandler := NewDiscussionPointsHandler(h.pointsService, discPointsConfig)
                
                // Discussion points routes
                points.POST("/discussion/topic", discussionPointsHandler.AwardNewTopicPoints)
                points.POST("/discussion/reply", discussionPointsHandler.AwardReplyPoints)
                points.POST("/discussion/upvote", discussionPointsHandler.AwardUpvotePoints)
                points.POST("/discussion/featured", discussionPointsHandler.AwardFeaturedTopicPoints)
                points.GET("/discussion/config", discussionPointsHandler.GetDiscussionPointsConfig)
                
                // Admin route for updating discussion points config
                adminPoints := router.Group("/admin/points")
                adminPoints.PUT("/discussion/config", discussionPointsHandler.UpdateDiscussionPointsConfig)
                
                // Points expiration
                points.POST("/expiration-rules", h.SetExpirationRule)
                points.GET("/expiration-rules", h.GetExpirationRules)
                points.POST("/process-expirations", h.ProcessExpiringPoints)
                points.DELETE("/expiration-rules/:id", h.DisableExpirationRule)
                
                // Achievements
                points.POST("/achievements", h.CreateAchievement)
                points.GET("/achievements", h.GetAchievements)
                points.POST("/achievements/:id/award", h.AwardAchievement)
                points.GET("/user-achievements", h.GetUserAchievements)
                points.GET("/achievements/eligible", h.CheckEligibleAchievements)
                points.PUT("/user-achievements/:id/featured", h.MarkAchievementAsFeatured)
                points.PUT("/user-achievements/:id/notified", h.MarkAchievementNotified)
                points.PUT("/achievements/:id", h.UpdateAchievement)
                points.GET("/unnotified-achievements", h.GetUnnotifiedAchievements)
                
                // Points events
                points.POST("/events", h.CreatePointsEvent)
                points.GET("/events/active", h.GetActivePointsEvents)
                points.GET("/events/:id", h.GetPointsEventByID)
                points.PUT("/events/:id", h.UpdatePointsEvent)
                
                // Points transfers
                points.POST("/transfer", h.TransferPoints)
                points.GET("/transfers", h.GetTransfersByUser)
                
                // Redemption system
                points.POST("/redemption-items", h.CreateRedemptionItem)
                points.GET("/redemption-items", h.GetRedemptionItems)
                points.POST("/redeem", h.RedeemPoints)
                points.GET("/redemption-history", h.GetUserRedemptionHistory)
                points.PUT("/redemption-items/:id/status", h.UpdateRedemptionItemStatus)
                points.PUT("/redemptions/:id/status", h.UpdateRedemptionStatus)
                
                // Challenge system
                points.POST("/challenges", h.CreateChallenge)
                points.GET("/challenges/active", h.GetActiveChallenges)
                points.GET("/challenges/:id", h.GetChallengeByID)
                points.PUT("/challenges/:id/progress", h.UpdateChallengeProgress)
                points.POST("/challenges/:id/complete", h.CompleteChallenge)
                points.GET("/user-challenges", h.GetUserChallenges)
                
                // Specific points awarding
                points.POST("/reading", h.AwardReadingPoints)
                points.POST("/discussion", h.AwardDiscussionPoints)
                points.POST("/content-creation", h.AwardContentCreationPoints)
                points.POST("/social-sharing", h.AwardSocialSharingPoints)
                
                // Game mechanics
                points.POST("/streak", h.UpdateStreak)
                points.DELETE("/streak", h.ResetStreak)
                
                // Leaderboards
                points.GET("/leaderboard/global", h.GetGlobalLeaderboard)
                points.GET("/leaderboard/category/:category", h.GetCategoryLeaderboard)
                points.GET("/leaderboard/period/:period", h.GetTimeRangeLeaderboard)
        }
}

// GetUserPointsBalance gets the user's points balance
func (h *PointsHandler) GetUserPointsBalance(c *gin.Context) {
        // Get user ID from context (set by auth middleware)
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get balance
        balance, err := h.pointsService.GetUserPointsBalance(userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get points balance: " + err.Error()})
                return
        }
        
        // If no balance exists yet, return default values
        if balance == nil {
                c.JSON(http.StatusOK, gin.H{
                        "userId":          userID,
                        "totalPoints":     0,
                        "availablePoints": 0,
                        "pendingPoints":   0,
                        "expiredPoints":   0,
                        "redeemedPoints":  0,
                })
                return
        }
        
        c.JSON(http.StatusOK, balance)
}

// GetPointsHistoryRequest represents the parameters for a points history request
type GetPointsHistoryRequest struct {
        Page     int `form:"page" binding:"min=1"`
        PageSize int `form:"pageSize" binding:"min=1,max=100"`
}

// GetPointsHistory gets the user's points transaction history
func (h *PointsHandler) GetPointsHistory(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse pagination parameters
        var req GetPointsHistoryRequest
        if err := c.ShouldBindQuery(&req); err != nil {
                // Default values
                req.Page = 1
                req.PageSize = 20
        }
        
        // Get history
        transactions, total, err := h.pointsService.GetPointsHistory(userID.(uint), req.Page, req.PageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get points history: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "transactions": transactions,
                "total":        total,
                "page":         req.Page,
                "pageSize":     req.PageSize,
        })
}

// GetPointsHistoryByType gets the user's points transaction history for a specific type
func (h *PointsHandler) GetPointsHistoryByType(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get transaction type from URL
        typeStr := c.Param("type")
        
        // Convert to PointSourceType
        var sourceType models.PointSourceType
        switch typeStr {
        case "reading":
                sourceType = models.PointSourceReading
        case "discussion":
                sourceType = models.PointSourceDiscussion
        case "content":
                sourceType = models.PointSourceContent
        case "social":
                sourceType = models.PointSourceSocial
        case "quality":
                sourceType = models.PointSourceQuality
        case "challenge":
                sourceType = models.PointSourceChallenge
        case "event":
                sourceType = models.PointSourceEvent
        case "streak":
                sourceType = models.PointSourceGameStreak
        case "admin":
                sourceType = models.PointSourceAdmin
        case "redemption":
                sourceType = models.PointSourceRedemption
        case "transfer":
                sourceType = models.PointSourceTransfer
        case "expiration":
                sourceType = models.PointSourceExpiration
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transaction type"})
                return
        }
        
        // Parse pagination parameters
        var req GetPointsHistoryRequest
        if err := c.ShouldBindQuery(&req); err != nil {
                // Default values
                req.Page = 1
                req.PageSize = 20
        }
        
        // Get history
        transactions, err := h.pointsService.GetPointsTransactionsByType(userID.(uint), sourceType, req.Page, req.PageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get points history: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "transactions": transactions,
                "page":         req.Page,
                "pageSize":     req.PageSize,
        })
}

// AwardPointsRequest represents a request to award points
type AwardPointsRequest struct {
        UserID        uint                   `json:"userId" binding:"required"`
        Amount        int                    `json:"amount" binding:"required,min=1"`
        SourceType    string                 `json:"sourceType" binding:"required"`
        ReferenceType string                 `json:"referenceType" binding:"required"`
        ReferenceID   uint                   `json:"referenceId" binding:"required"`
        Description   string                 `json:"description" binding:"required"`
        MetaData      map[string]interface{} `json:"metaData"`
}

// AwardPoints awards points to a user
func (h *PointsHandler) AwardPoints(c *gin.Context) {
        // Check admin authentication
        adminID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req AwardPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Convert to PointSourceType
        var sourceType models.PointSourceType
        switch req.SourceType {
        case "reading":
                sourceType = models.PointSourceReading
        case "discussion":
                sourceType = models.PointSourceDiscussion
        case "content":
                sourceType = models.PointSourceContent
        case "social":
                sourceType = models.PointSourceSocial
        case "quality":
                sourceType = models.PointSourceQuality
        case "challenge":
                sourceType = models.PointSourceChallenge
        case "event":
                sourceType = models.PointSourceEvent
        case "streak":
                sourceType = models.PointSourceGameStreak
        case "admin":
                sourceType = models.PointSourceAdmin
        case "redemption":
                sourceType = models.PointSourceRedemption
        case "transfer":
                sourceType = models.PointSourceTransfer
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source type"})
                return
        }
        
        // Update metadata with admin info
        if req.MetaData == nil {
                req.MetaData = make(map[string]interface{})
        }
        req.MetaData["adminId"] = adminID
        
        // Award points
        transaction, err := h.pointsService.AwardPoints(
                req.UserID,
                req.Amount,
                sourceType,
                req.ReferenceType,
                req.ReferenceID,
                req.Description,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// DeductPointsRequest represents a request to deduct points
type DeductPointsRequest struct {
        UserID        uint                   `json:"userId" binding:"required"`
        Amount        int                    `json:"amount" binding:"required,min=1"`
        SourceType    string                 `json:"sourceType" binding:"required"`
        ReferenceType string                 `json:"referenceType" binding:"required"`
        ReferenceID   uint                   `json:"referenceId" binding:"required"`
        Description   string                 `json:"description" binding:"required"`
        MetaData      map[string]interface{} `json:"metaData"`
}

// DeductPoints deducts points from a user
func (h *PointsHandler) DeductPoints(c *gin.Context) {
        // Check admin authentication
        adminID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req DeductPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Convert to PointSourceType
        var sourceType models.PointSourceType
        switch req.SourceType {
        case "reading":
                sourceType = models.PointSourceReading
        case "discussion":
                sourceType = models.PointSourceDiscussion
        case "content":
                sourceType = models.PointSourceContent
        case "social":
                sourceType = models.PointSourceSocial
        case "quality":
                sourceType = models.PointSourceQuality
        case "challenge":
                sourceType = models.PointSourceChallenge
        case "event":
                sourceType = models.PointSourceEvent
        case "streak":
                sourceType = models.PointSourceGameStreak
        case "admin":
                sourceType = models.PointSourceAdmin
        case "redemption":
                sourceType = models.PointSourceRedemption
        case "transfer":
                sourceType = models.PointSourceTransfer
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source type"})
                return
        }
        
        // Update metadata with admin info
        if req.MetaData == nil {
                req.MetaData = make(map[string]interface{})
        }
        req.MetaData["adminId"] = adminID
        
        // Deduct points
        transaction, err := h.pointsService.DeductPoints(
                req.UserID,
                req.Amount,
                sourceType,
                req.ReferenceType,
                req.ReferenceID,
                req.Description,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to deduct points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// GetTierRequirements gets the requirements for each membership tier
func (h *PointsHandler) GetTierRequirements(c *gin.Context) {
        // Get tier requirements
        requirements := h.pointsService.GetTierRequirements()
        
        c.JSON(http.StatusOK, requirements)
}

// GetUserMembership gets the user's membership tier
func (h *PointsHandler) GetUserMembership(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get membership
        membership, err := h.pointsService.GetUserMembership(userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user membership: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, membership)
}

// GetUsersByTier gets users in a specific membership tier
func (h *PointsHandler) GetUsersByTier(c *gin.Context) {
        // Get tier from URL
        tierStr := c.Param("tier")
        
        // Convert to MembershipTier
        var tier models.MembershipTier
        switch tierStr {
        case "basic":
                tier = models.TierBasic
        case "engaged":
                tier = models.TierEngaged
        case "active":
                tier = models.TierActive
        case "premium":
                tier = models.TierPremium
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid membership tier"})
                return
        }
        
        // Get users
        users, err := h.pointsService.GetUsersByTier(tier)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users by tier: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, users)
}

// EvaluateUserTier evaluates and potentially updates a user's membership tier
func (h *PointsHandler) EvaluateUserTier(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get user ID from URL
        userIDStr := c.Param("userId")
        userID, err := strconv.ParseUint(userIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
                return
        }
        
        // Evaluate tier
        tier, changed, err := h.pointsService.EvaluateUserTier(uint(userID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to evaluate user tier: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "userId":      userID,
                "tier":        tier,
                "tierChanged": changed,
        })
}

// BulkEvaluateUserTiers evaluates tiers for all users
func (h *PointsHandler) BulkEvaluateUserTiers(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Evaluate tiers
        changedCount, err := h.pointsService.BulkEvaluateUserTiers()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to evaluate user tiers: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "changedCount": changedCount,
        })
}

// SetPremiumMembershipRequest represents a request to set premium membership
type SetPremiumMembershipRequest struct {
        UserID     uint       `json:"userId" binding:"required"`
        IsPremium  bool       `json:"isPremium" binding:"required"`
        ExpiryDate *time.Time `json:"expiryDate"`
}

// SetPremiumMembership sets a user's premium membership status
func (h *PointsHandler) SetPremiumMembership(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req SetPremiumMembershipRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Set premium membership
        if err := h.pointsService.SetPremiumMembership(req.UserID, req.IsPremium, req.ExpiryDate); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set premium membership: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Premium membership updated successfully",
        })
}

// SetExpirationRuleRequest represents a request to set an expiration rule
type SetExpirationRuleRequest struct {
        SourceType   string `json:"sourceType" binding:"required"`
        DaysToExpire int    `json:"daysToExpire" binding:"min=0"`
}

// SetExpirationRule sets an expiration rule for a points source type
func (h *PointsHandler) SetExpirationRule(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req SetExpirationRuleRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Convert to PointSourceType
        var sourceType models.PointSourceType
        switch req.SourceType {
        case "reading":
                sourceType = models.PointSourceReading
        case "discussion":
                sourceType = models.PointSourceDiscussion
        case "content":
                sourceType = models.PointSourceContent
        case "social":
                sourceType = models.PointSourceSocial
        case "quality":
                sourceType = models.PointSourceQuality
        case "challenge":
                sourceType = models.PointSourceChallenge
        case "event":
                sourceType = models.PointSourceEvent
        case "streak":
                sourceType = models.PointSourceGameStreak
        case "admin":
                sourceType = models.PointSourceAdmin
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source type"})
                return
        }
        
        // Set expiration rule
        rule, err := h.pointsService.SetExpirationRule(sourceType, req.DaysToExpire, userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set expiration rule: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, rule)
}

// GetExpirationRules gets all expiration rules
func (h *PointsHandler) GetExpirationRules(c *gin.Context) {
        // Get rules
        rules, err := h.pointsService.GetExpirationRules()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get expiration rules: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, rules)
}

// ProcessExpiringPoints processes points that are expiring
func (h *PointsHandler) ProcessExpiringPoints(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Process expirations
        count, err := h.pointsService.ProcessExpiringPoints()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process expiring points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "processedCount": count,
        })
}

// DisableExpirationRule disables an expiration rule
func (h *PointsHandler) DisableExpirationRule(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get rule ID from URL
        ruleIDStr := c.Param("id")
        ruleID, err := strconv.ParseUint(ruleIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
                return
        }
        
        // Disable rule
        if err := h.pointsService.DisableExpirationRule(uint(ruleID), userID.(uint)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to disable expiration rule: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Expiration rule disabled successfully",
        })
}

// CreateAchievementRequest represents a request to create an achievement
type CreateAchievementRequest struct {
        Name            string                 `json:"name" binding:"required"`
        Description     string                 `json:"description" binding:"required"`
        Category        string                 `json:"category" binding:"required"`
        ImageURL        string                 `json:"imageUrl" binding:"required"`
        PointsAwarded   int                    `json:"pointsAwarded" binding:"min=0"`
        RequiredActions map[string]interface{} `json:"requiredActions" binding:"required"`
        DisplayOrder    int                    `json:"displayOrder" binding:"min=0"`
}

// CreateAchievement creates a new achievement
func (h *PointsHandler) CreateAchievement(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req CreateAchievementRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Create achievement
        achievement, err := h.pointsService.CreateAchievement(
                req.Name,
                req.Description,
                req.Category,
                req.ImageURL,
                req.PointsAwarded,
                req.RequiredActions,
                req.DisplayOrder,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create achievement: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusCreated, achievement)
}

// GetAchievements gets all achievements
func (h *PointsHandler) GetAchievements(c *gin.Context) {
        // Get active flag from query string
        activeOnly := true
        activeStr := c.DefaultQuery("active", "true")
        if activeStr == "false" {
                activeOnly = false
        }
        
        // Get achievements
        achievements, err := h.pointsService.GetAchievements(activeOnly)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get achievements: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, achievements)
}

// AwardAchievementRequest represents a request to award an achievement
type AwardAchievementRequest struct {
        UserID        uint `json:"userId" binding:"required"`
        AchievementID uint `json:"achievementId" binding:"required"`
}

// AwardAchievement awards an achievement to a user
func (h *PointsHandler) AwardAchievement(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req AwardAchievementRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Award achievement
        userAchievement, err := h.pointsService.AwardAchievement(req.UserID, req.AchievementID)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award achievement: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, userAchievement)
}

// GetUserAchievements gets all achievements for a user
func (h *PointsHandler) GetUserAchievements(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get achievements
        achievements, err := h.pointsService.GetUserAchievements(userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user achievements: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, achievements)
}

// CheckEligibleAchievements checks which achievements a user is eligible for
func (h *PointsHandler) CheckEligibleAchievements(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Check eligibility
        achievements, err := h.pointsService.CheckEligibleAchievements(userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check eligible achievements: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, achievements)
}

// MarkAchievementAsFeaturedRequest represents a request to mark an achievement as featured
type MarkAchievementAsFeaturedRequest struct {
        IsFeatured bool `json:"isFeatured"`
}

// MarkAchievementAsFeatured marks a user's achievement as featured
func (h *PointsHandler) MarkAchievementAsFeatured(c *gin.Context) {
        // Get user ID from context
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get achievement ID from URL
        achievementIDStr := c.Param("id")
        achievementID, err := strconv.ParseUint(achievementIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid achievement ID"})
                return
        }
        
        // Parse request
        var req MarkAchievementAsFeaturedRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Mark as featured
        if err := h.pointsService.MarkAchievementAsFeatured(uint(achievementID), req.IsFeatured); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark achievement as featured: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Achievement featured status updated successfully",
        })
}

// MarkAchievementNotified marks a user's achievement as notified
func (h *PointsHandler) MarkAchievementNotified(c *gin.Context) {
        // Check authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
                return
        }
        
        // Get achievement ID from URL
        achievementIDStr := c.Param("id")
        achievementID, err := strconv.ParseUint(achievementIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid achievement ID"})
                return
        }
        
        // Mark as notified
        if err := h.pointsService.MarkAchievementNotified(uint(achievementID)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark achievement as notified: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Achievement marked as notified successfully",
        })
}

// UpdateAchievementRequest represents a request to update an achievement
type UpdateAchievementRequest struct {
        Name            string                 `json:"name" binding:"required"`
        Description     string                 `json:"description" binding:"required"`
        Category        string                 `json:"category" binding:"required"`
        ImageURL        string                 `json:"imageUrl" binding:"required"`
        PointsAwarded   int                    `json:"pointsAwarded" binding:"min=0"`
        RequiredActions map[string]interface{} `json:"requiredActions" binding:"required"`
        IsActive        bool                   `json:"isActive"`
        DisplayOrder    int                    `json:"displayOrder" binding:"min=0"`
}

// UpdateAchievement updates an achievement
func (h *PointsHandler) UpdateAchievement(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get achievement ID from URL
        achievementIDStr := c.Param("id")
        achievementID, err := strconv.ParseUint(achievementIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid achievement ID"})
                return
        }
        
        // Parse request
        var req UpdateAchievementRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Update achievement
        achievement, err := h.pointsService.UpdateAchievement(
                uint(achievementID),
                req.Name,
                req.Description,
                req.Category,
                req.ImageURL,
                req.PointsAwarded,
                req.RequiredActions,
                req.IsActive,
                req.DisplayOrder,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update achievement: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, achievement)
}

// GetUnnotifiedAchievements gets achievements that have not been notified
func (h *PointsHandler) GetUnnotifiedAchievements(c *gin.Context) {
        // Check admin authentication
        _, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get unnotified achievements
        achievements, err := h.pointsService.GetUnnotifiedAchievements()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get unnotified achievements: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, achievements)
}

// CreatePointsEventRequest represents a request to create a points event
type CreatePointsEventRequest struct {
        Name              string    `json:"name" binding:"required"`
        Description       string    `json:"description" binding:"required"`
        EventType         string    `json:"eventType" binding:"required"`
        StartDate         time.Time `json:"startDate" binding:"required"`
        EndDate           time.Time `json:"endDate" binding:"required"`
        Multiplier        float64   `json:"multiplier" binding:"required,min=0.1"`
        ApplicableSources []string  `json:"applicableSources" binding:"required"`
}

// CreatePointsEvent creates a new points event
func (h *PointsHandler) CreatePointsEvent(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req CreatePointsEventRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Convert event type
        var eventType models.PointsEventType
        switch req.EventType {
        case "double_points":
                eventType = models.EventTypeDoublePoints
        case "bonus_challenge":
                eventType = models.EventTypeBonusChallenge
        case "streak_multiplier":
                eventType = models.EventTypeStreakMultiplier
        case "special_activity":
                eventType = models.EventTypeSpecialActivity
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event type"})
                return
        }
        
        // Convert applicable sources
        var applicableSources []models.PointSourceType
        for _, sourceStr := range req.ApplicableSources {
                var sourceType models.PointSourceType
                switch sourceStr {
                case "reading":
                        sourceType = models.PointSourceReading
                case "discussion":
                        sourceType = models.PointSourceDiscussion
                case "content":
                        sourceType = models.PointSourceContent
                case "social":
                        sourceType = models.PointSourceSocial
                case "quality":
                        sourceType = models.PointSourceQuality
                case "challenge":
                        sourceType = models.PointSourceChallenge
                case "event":
                        sourceType = models.PointSourceEvent
                case "streak":
                        sourceType = models.PointSourceGameStreak
                case "admin":
                        sourceType = models.PointSourceAdmin
                default:
                        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source type: " + sourceStr})
                        return
                }
                applicableSources = append(applicableSources, sourceType)
        }
        
        // Create event
        event, err := h.pointsService.CreatePointsEvent(
                req.Name,
                req.Description,
                eventType,
                req.StartDate,
                req.EndDate,
                req.Multiplier,
                applicableSources,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create points event: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusCreated, event)
}

// GetActivePointsEvents gets all active points events
func (h *PointsHandler) GetActivePointsEvents(c *gin.Context) {
        // Get active events
        events, err := h.pointsService.GetActivePointsEvents()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active points events: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, events)
}

// GetPointsEventByID gets a points event by ID
func (h *PointsHandler) GetPointsEventByID(c *gin.Context) {
        // Get event ID from URL
        eventIDStr := c.Param("id")
        eventID, err := strconv.ParseUint(eventIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
                return
        }
        
        // Get event
        event, err := h.pointsService.GetPointsEventByID(uint(eventID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get points event: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, event)
}

// UpdatePointsEventRequest represents a request to update a points event
type UpdatePointsEventRequest struct {
        Name              string    `json:"name" binding:"required"`
        Description       string    `json:"description" binding:"required"`
        EventType         string    `json:"eventType" binding:"required"`
        StartDate         time.Time `json:"startDate" binding:"required"`
        EndDate           time.Time `json:"endDate" binding:"required"`
        Multiplier        float64   `json:"multiplier" binding:"required,min=0.1"`
        ApplicableSources []string  `json:"applicableSources" binding:"required"`
        IsActive          bool      `json:"isActive"`
}

// UpdatePointsEvent updates a points event
func (h *PointsHandler) UpdatePointsEvent(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get event ID from URL
        eventIDStr := c.Param("id")
        eventID, err := strconv.ParseUint(eventIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
                return
        }
        
        // Parse request
        var req UpdatePointsEventRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Convert event type
        var eventType models.PointsEventType
        switch req.EventType {
        case "double_points":
                eventType = models.EventTypeDoublePoints
        case "bonus_challenge":
                eventType = models.EventTypeBonusChallenge
        case "streak_multiplier":
                eventType = models.EventTypeStreakMultiplier
        case "special_activity":
                eventType = models.EventTypeSpecialActivity
        default:
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event type"})
                return
        }
        
        // Convert applicable sources
        var applicableSources []models.PointSourceType
        for _, sourceStr := range req.ApplicableSources {
                var sourceType models.PointSourceType
                switch sourceStr {
                case "reading":
                        sourceType = models.PointSourceReading
                case "discussion":
                        sourceType = models.PointSourceDiscussion
                case "content":
                        sourceType = models.PointSourceContent
                case "social":
                        sourceType = models.PointSourceSocial
                case "quality":
                        sourceType = models.PointSourceQuality
                case "challenge":
                        sourceType = models.PointSourceChallenge
                case "event":
                        sourceType = models.PointSourceEvent
                case "streak":
                        sourceType = models.PointSourceGameStreak
                case "admin":
                        sourceType = models.PointSourceAdmin
                default:
                        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source type: " + sourceStr})
                        return
                }
                applicableSources = append(applicableSources, sourceType)
        }
        
        // Update event
        event, err := h.pointsService.UpdatePointsEvent(
                uint(eventID),
                req.Name,
                req.Description,
                eventType,
                req.StartDate,
                req.EndDate,
                req.Multiplier,
                applicableSources,
                req.IsActive,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update points event: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, event)
}

// TransferPointsRequest represents a request to transfer points
type TransferPointsRequest struct {
        ToUserID uint   `json:"toUserId" binding:"required"`
        Amount   int    `json:"amount" binding:"required,min=1"`
        Notes    string `json:"notes"`
}

// TransferPoints transfers points from one user to another
func (h *PointsHandler) TransferPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req TransferPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Transfer points
        transfer, err := h.pointsService.TransferPoints(
                userID.(uint),
                req.ToUserID,
                req.Amount,
                req.Notes,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to transfer points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transfer)
}

// GetTransfersByUser gets transfers for a user
func (h *PointsHandler) GetTransfersByUser(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get direction from query string
        direction := c.DefaultQuery("direction", "sent")
        isReceiver := direction == "received"
        
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
        
        // Get transfers
        transfers, err := h.pointsService.GetTransfersByUser(userID.(uint), isReceiver, page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get transfers: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "transfers": transfers,
                "page":      page,
                "pageSize":  pageSize,
        })
}

// CreateRedemptionItemRequest represents a request to create a redemption item
type CreateRedemptionItemRequest struct {
        Name              string `json:"name" binding:"required"`
        Description       string `json:"description" binding:"required"`
        PointsCost        int    `json:"pointsCost" binding:"required,min=1"`
        Category          string `json:"category" binding:"required"`
        ImageURL          string `json:"imageUrl" binding:"required"`
        IsDigital         bool   `json:"isDigital"`
        QuantityAvailable int    `json:"quantityAvailable" binding:"min=0"`
        RedemptionCode    string `json:"redemptionCode"`
}

// CreateRedemptionItem creates a new redemption item
func (h *PointsHandler) CreateRedemptionItem(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req CreateRedemptionItemRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Create item
        item, err := h.pointsService.CreateRedemptionItem(
                req.Name,
                req.Description,
                req.Category,
                req.ImageURL,
                req.PointsCost,
                req.IsDigital,
                req.QuantityAvailable,
                req.RedemptionCode,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create redemption item: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusCreated, item)
}

// GetRedemptionItems gets all redemption items
func (h *PointsHandler) GetRedemptionItems(c *gin.Context) {
        // Get active flag from query string
        activeOnly := true
        activeStr := c.DefaultQuery("active", "true")
        if activeStr == "false" {
                activeOnly = false
        }
        
        // Get items
        items, err := h.pointsService.GetRedemptionItems(activeOnly)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get redemption items: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, items)
}

// RedeemPointsRequest represents a request to redeem points
type RedeemPointsRequest struct {
        ItemID          uint   `json:"itemId" binding:"required"`
        DeliveryAddress string `json:"deliveryAddress"`
        Notes           string `json:"notes"`
}

// RedeemPoints redeems points for an item
func (h *PointsHandler) RedeemPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req RedeemPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Redeem points
        record, err := h.pointsService.RedeemPoints(
                userID.(uint),
                req.ItemID,
                req.DeliveryAddress,
                req.Notes,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to redeem points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, record)
}

// GetUserRedemptionHistory gets redemption records for a user
func (h *PointsHandler) GetUserRedemptionHistory(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
        
        // Get history
        records, err := h.pointsService.GetUserRedemptionHistory(userID.(uint), page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get redemption history: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "records":  records,
                "page":     page,
                "pageSize": pageSize,
        })
}

// UpdateRedemptionItemStatusRequest represents a request to update a redemption item's status
type UpdateRedemptionItemStatusRequest struct {
        IsActive bool `json:"isActive" binding:"required"`
}

// UpdateRedemptionItemStatus updates the status of a redemption item
func (h *PointsHandler) UpdateRedemptionItemStatus(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get item ID from URL
        itemIDStr := c.Param("id")
        itemID, err := strconv.ParseUint(itemIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
                return
        }
        
        // Parse request
        var req UpdateRedemptionItemStatusRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Update status
        if err := h.pointsService.UpdateRedemptionItemStatus(uint(itemID), req.IsActive, userID.(uint)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update redemption item status: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Redemption item status updated successfully",
        })
}

// UpdateRedemptionStatusRequest represents a request to update a redemption status
type UpdateRedemptionStatusRequest struct {
        Status string `json:"status" binding:"required"`
}

// UpdateRedemptionStatus updates the status of a redemption
func (h *PointsHandler) UpdateRedemptionStatus(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Get redemption ID from URL
        redemptionIDStr := c.Param("id")
        redemptionID, err := strconv.ParseUint(redemptionIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid redemption ID"})
                return
        }
        
        // Parse request
        var req UpdateRedemptionStatusRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Validate status
        validStatuses := map[string]bool{
                "processing": true,
                "shipped":    true,
                "delivered":  true,
                "completed":  true,
                "cancelled":  true,
        }
        
        if !validStatuses[req.Status] {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status"})
                return
        }
        
        // Update status
        if err := h.pointsService.UpdateRedemptionStatus(uint(redemptionID), req.Status, userID.(uint)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update redemption status: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Redemption status updated successfully",
        })
}

// CreateChallengeRequest represents a request to create a challenge
type CreateChallengeRequest struct {
        Name            string                 `json:"name" binding:"required"`
        Description     string                 `json:"description" binding:"required"`
        PointsAwarded   int                    `json:"pointsAwarded" binding:"required,min=1"`
        RequiredActions map[string]interface{} `json:"requiredActions" binding:"required"`
        StartDate       time.Time              `json:"startDate" binding:"required"`
        EndDate         time.Time              `json:"endDate" binding:"required"`
        MaxCompletions  int                    `json:"maxCompletions" binding:"min=0"`
        Category        string                 `json:"category" binding:"required"`
        Difficulty      string                 `json:"difficulty" binding:"required"`
}

// CreateChallenge creates a new challenge
func (h *PointsHandler) CreateChallenge(c *gin.Context) {
        // Check admin authentication
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Admin not authenticated"})
                return
        }
        
        // Parse request
        var req CreateChallengeRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Create challenge
        challenge, err := h.pointsService.CreateChallenge(
                req.Name,
                req.Description,
                req.PointsAwarded,
                req.RequiredActions,
                req.StartDate,
                req.EndDate,
                req.MaxCompletions,
                req.Category,
                req.Difficulty,
                userID.(uint),
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create challenge: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusCreated, challenge)
}

// GetActiveChallenges gets all active challenges
func (h *PointsHandler) GetActiveChallenges(c *gin.Context) {
        // Get active challenges
        challenges, err := h.pointsService.GetActiveChallenges()
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active challenges: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, challenges)
}

// GetChallengeByID gets a challenge by ID
func (h *PointsHandler) GetChallengeByID(c *gin.Context) {
        // Get challenge ID from URL
        challengeIDStr := c.Param("id")
        challengeID, err := strconv.ParseUint(challengeIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid challenge ID"})
                return
        }
        
        // Get challenge
        challenge, err := h.pointsService.GetChallengeByID(uint(challengeID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get challenge: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, challenge)
}

// UpdateChallengeProgressRequest represents a request to update challenge progress
type UpdateChallengeProgressRequest struct {
        Progress float64 `json:"progress" binding:"required,min=0,max=1"`
}

// UpdateChallengeProgress updates a user's progress on a challenge
func (h *PointsHandler) UpdateChallengeProgress(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get challenge ID from URL
        challengeIDStr := c.Param("id")
        challengeID, err := strconv.ParseUint(challengeIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid challenge ID"})
                return
        }
        
        // Parse request
        var req UpdateChallengeProgressRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Update progress
        if err := h.pointsService.UpdateChallengeProgress(userID.(uint), uint(challengeID), req.Progress); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update challenge progress: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Challenge progress updated successfully",
        })
}

// CompleteChallenge completes a challenge for a user
func (h *PointsHandler) CompleteChallenge(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get challenge ID from URL
        challengeIDStr := c.Param("id")
        challengeID, err := strconv.ParseUint(challengeIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid challenge ID"})
                return
        }
        
        // Complete challenge
        if err := h.pointsService.CompleteChallenge(userID.(uint), uint(challengeID)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete challenge: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Challenge completed successfully",
        })
}

// GetUserChallenges gets all challenges for a user
func (h *PointsHandler) GetUserChallenges(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Get challenges
        challenges, err := h.pointsService.GetUserChallenges(userID.(uint))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user challenges: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, challenges)
}

// AwardReadingPointsRequest represents a request to award reading points
type AwardReadingPointsRequest struct {
        SectionID uint                   `json:"sectionId" binding:"required"`
        MetaData  map[string]interface{} `json:"metaData"`
}

// AwardReadingPoints awards points for reading
func (h *PointsHandler) AwardReadingPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req AwardReadingPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Award points
        transaction, err := h.pointsService.AwardReadingPoints(
                userID.(uint),
                req.SectionID,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award reading points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// AwardDiscussionPointsRequest represents a request to award discussion points
type AwardDiscussionPointsRequest struct {
        DiscussionType string                 `json:"discussionType" binding:"required"`
        DiscussionID   uint                   `json:"discussionId" binding:"required"`
        MetaData       map[string]interface{} `json:"metaData"`
}

// AwardDiscussionPoints awards points for discussion participation
func (h *PointsHandler) AwardDiscussionPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req AwardDiscussionPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Award points
        transaction, err := h.pointsService.AwardDiscussionPoints(
                userID.(uint),
                req.DiscussionType,
                req.DiscussionID,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award discussion points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// AwardContentCreationPointsRequest represents a request to award content creation points
type AwardContentCreationPointsRequest struct {
        ContentType string                 `json:"contentType" binding:"required"`
        ContentID   uint                   `json:"contentId" binding:"required"`
        Quality     float64                `json:"quality" binding:"min=0,max=1"`
        MetaData    map[string]interface{} `json:"metaData"`
}

// AwardContentCreationPoints awards points for content creation
func (h *PointsHandler) AwardContentCreationPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req AwardContentCreationPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Award points
        transaction, err := h.pointsService.AwardContentCreationPoints(
                userID.(uint),
                req.ContentType,
                req.ContentID,
                req.Quality,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award content creation points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// AwardSocialSharingPointsRequest represents a request to award social sharing points
type AwardSocialSharingPointsRequest struct {
        Platform    string                 `json:"platform" binding:"required"`
        ContentType string                 `json:"contentType" binding:"required"`
        ContentID   uint                   `json:"contentId" binding:"required"`
        MetaData    map[string]interface{} `json:"metaData"`
}

// AwardSocialSharingPoints awards points for social sharing
func (h *PointsHandler) AwardSocialSharingPoints(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req AwardSocialSharingPointsRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Award points
        transaction, err := h.pointsService.AwardSocialSharingPoints(
                userID.(uint),
                req.Platform,
                req.ContentType,
                req.ContentID,
                req.MetaData,
        )
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award social sharing points: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, transaction)
}

// UpdateStreakRequest represents a request to update a streak
type UpdateStreakRequest struct {
        ActivityType string `json:"activityType" binding:"required"`
}

// UpdateStreak updates a user's activity streak
func (h *PointsHandler) UpdateStreak(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Parse request
        var req UpdateStreakRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Update streak
        streak, err := h.pointsService.UpdateStreak(userID.(uint), req.ActivityType)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update streak: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "streak": streak,
        })
}

// ResetStreak resets a user's activity streak
func (h *PointsHandler) ResetStreak(c *gin.Context) {
        // Get user ID from context
        userID, exists := c.Get("userID")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Reset streak
        if err := h.pointsService.ResetStreak(userID.(uint)); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reset streak: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "message": "Streak reset successfully",
        })
}

// GetGlobalLeaderboard gets the global leaderboard
func (h *PointsHandler) GetGlobalLeaderboard(c *gin.Context) {
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
        
        // Get leaderboard
        leaderboard, err := h.pointsService.GetGlobalLeaderboard(page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get global leaderboard: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "leaderboard": leaderboard,
                "page":        page,
                "pageSize":    pageSize,
        })
}

// GetCategoryLeaderboard gets a category-specific leaderboard
func (h *PointsHandler) GetCategoryLeaderboard(c *gin.Context) {
        // Get category from URL
        category := c.Param("category")
        
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
        
        // Get leaderboard
        leaderboard, err := h.pointsService.GetCategoryLeaderboard(category, page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get category leaderboard: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "leaderboard": leaderboard,
                "category":    category,
                "page":        page,
                "pageSize":    pageSize,
        })
}

// GetTimeRangeLeaderboard gets a time-range leaderboard
func (h *PointsHandler) GetTimeRangeLeaderboard(c *gin.Context) {
        // Get period from URL
        period := c.Param("period")
        
        // Validate period
        validPeriods := map[string]bool{
                "daily":   true,
                "weekly":  true,
                "monthly": true,
                "alltime": true,
        }
        
        if !validPeriods[period] {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid period"})
                return
        }
        
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
        
        // Get leaderboard
        leaderboard, err := h.pointsService.GetTimeRangeLeaderboard(period, page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get time-range leaderboard: " + err.Error()})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{
                "leaderboard": leaderboard,
                "period":      period,
                "page":        page,
                "pageSize":    pageSize,
        })
}