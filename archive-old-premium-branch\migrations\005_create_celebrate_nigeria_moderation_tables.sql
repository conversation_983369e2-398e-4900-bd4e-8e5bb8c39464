-- Create entry_flags table
CREATE TABLE IF NOT EXISTS entry_flags (
    id SERIAL PRIMARY KEY,
    entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    reason TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    moderator_id BIGINT,
    moderated_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for entry_flags
CREATE INDEX IF NOT EXISTS idx_entry_flags_entry_id ON entry_flags(entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_flags_user_id ON entry_flags(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_flags_status ON entry_flags(status);

-- Create entry_moderation_queue table
CREATE TABLE IF NOT EXISTS entry_moderation_queue (
    id SERIAL PRIMARY KEY,
    entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    reason TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    priority INT NOT NULL DEFAULT 3,
    assigned_to BIGINT,
    moderator_id BIGINT,
    moderated_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for entry_moderation_queue
CREATE INDEX IF NOT EXISTS idx_entry_moderation_queue_entry_id ON entry_moderation_queue(entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_moderation_queue_user_id ON entry_moderation_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_moderation_queue_status ON entry_moderation_queue(status);
CREATE INDEX IF NOT EXISTS idx_entry_moderation_queue_priority ON entry_moderation_queue(priority);

-- Create entry_filter_results table
CREATE TABLE IF NOT EXISTS entry_filter_results (
    id SERIAL PRIMARY KEY,
    entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    triggered_rules TEXT,
    action VARCHAR(20) NOT NULL DEFAULT 'none',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for entry_filter_results
CREATE INDEX IF NOT EXISTS idx_entry_filter_results_entry_id ON entry_filter_results(entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_filter_results_action ON entry_filter_results(action);
