import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import Header from '../components/Header';
import Footer from '../components/Footer';
import ContextualTipsComponent from '../components/tips/ContextualTipsComponent';

const MainContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
`;

const Content = styled.main`
  flex: 1;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
`;

const MainLayout: React.FC = () => {
  const location = useLocation();

  // Determine context type and ID based on the current route
  const getContextInfo = () => {
    const path = location.pathname;

    // Book viewer page
    if (path.startsWith('/books/') && path.split('/').length > 2) {
      const bookId = path.split('/')[2];
      return { type: 'book', id: bookId };
    }

    // Forum topic page
    if (path.startsWith('/forum/topic/') && path.split('/').length > 3) {
      const topicId = path.split('/')[3];
      return { type: 'forum_topic', id: topicId };
    }

    // Marketplace page
    if (path.startsWith('/marketplace')) {
      return { type: 'marketplace', id: 'main' };
    }

    // Profile page
    if (path.startsWith('/profile')) {
      return { type: 'profile', id: 'main' };
    }

    // Default to page context
    return { type: 'page', id: path };
  };

  const { type, id } = getContextInfo();

  return (
    <MainContainer>
      <Header />
      <Content>
        <Outlet />
      </Content>
      <Footer />

      {/* Contextual Tips Component */}
      <ContextualTipsComponent
        contextType={type}
        contextId={id}
        position="bottom"
      />
    </MainContainer>
  );
};

export default MainLayout;
