package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/handlers"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/config"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/database"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/middleware"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}

	// Initialize logger
	logger := logger.NewLogger()
	logger.Info("Starting Great Nigeria Points Service")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		logger.Fatal("Failed to load configuration: " + err.Error())
	}

	// Connect to database
	db, err := database.NewDatabase(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database: " + err.Error())
	}

	// Initialize repositories
	pointsRepo := repository.NewPointsRepository(db)
	activityRepo := repository.NewActivityRepository(db)
	levelRepo := repository.NewLevelRepository(db)

	// Initialize services
	pointsService := service.NewPointsService(pointsRepo, activityRepo, levelRepo, logger)

	// Initialize handlers
	pointsHandler := handlers.NewPointsHandler(pointsService, logger)

	// Set up Gin router
	router := gin.Default()
	router.Use(gin.Recovery())
	router.Use(middleware.RequestLogger())

	// All routes are protected - require authentication
	router.Use(middleware.JWTAuth())

	// Define API routes
	router.GET("/points/balance", pointsHandler.GetBalance)
	router.GET("/points/history", pointsHandler.GetHistory)
	router.GET("/points/levels", pointsHandler.GetLevels)
	router.GET("/points/level/current", pointsHandler.GetCurrentLevel)
	router.GET("/points/level/next", pointsHandler.GetNextLevel)
	router.POST("/points/award", pointsHandler.AwardPoints)

	// Admin routes
	adminRoutes := router.Group("/admin")
	adminRoutes.Use(middleware.AdminAuth())
	{
		adminRoutes.POST("/points/levels", pointsHandler.CreateLevel)
		adminRoutes.PUT("/points/levels/:id", pointsHandler.UpdateLevel)
		adminRoutes.DELETE("/points/levels/:id", pointsHandler.DeleteLevel)
		adminRoutes.POST("/points/adjust", pointsHandler.AdjustPoints)
	}

	// Start server
	port := os.Getenv("POINTS_SERVICE_PORT")
	if port == "" {
		port = "8004" // Default port for points service
	}

	logger.Info("Points Service starting on port " + port)
	if err := router.Run("0.0.0.0:" + port); err != nil {
		logger.Fatal("Failed to start Points Service: " + err.Error())
	}
}
