package main

import (
	"context"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/handlers"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/config"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/database"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/middleware"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}

	// Initialize logger
	logger := logger.NewLogger()
	logger.Info("Starting Great Nigeria Live Streaming Service")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		logger.Fatal("Failed to load configuration: " + err.Error())
	}

	// Connect to database
	db, err := database.NewDatabase(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database: " + err.Error())
	}

	// Auto-migrate database models
	if err := db.AutoMigrate(
		&repository.VirtualCurrency{},
		&repository.VirtualCurrencyTransaction{},
		&repository.LiveStream{},
		&repository.LiveStreamGift{},
		&repository.GifterRanking{},
		&repository.CreatorRevenue{},
	); err != nil {
		logger.Fatal("Failed to migrate database: " + err.Error())
	}

	// Initialize repositories
	currencyRepo := repository.NewVirtualCurrencyRepository(db)
	streamRepo := repository.NewLiveStreamRepository(db)
	giftRepo := repository.NewLiveStreamGiftRepository(db)
	rankingRepo := repository.NewGifterRankingRepository(db)
	revenueRepo := repository.NewCreatorRevenueRepository(db)

	// Initialize services
	currencyService := service.NewVirtualCurrencyService(currencyRepo, logger)
	streamService := service.NewLiveStreamService(streamRepo, logger)
	giftService := service.NewLiveStreamGiftService(giftRepo, currencyRepo, rankingRepo, revenueRepo, logger)
	rankingService := service.NewGifterRankingService(rankingRepo, logger)
	revenueService := service.NewCreatorRevenueService(revenueRepo, logger)

	// Initialize handlers
	currencyHandler := handlers.NewVirtualCurrencyHandler(currencyService, logger)
	streamHandler := handlers.NewLiveStreamHandler(streamService, logger)
	giftHandler := handlers.NewLiveStreamGiftHandler(giftService, logger)
	rankingHandler := handlers.NewGifterRankingHandler(rankingService, logger)
	revenueHandler := handlers.NewCreatorRevenueHandler(revenueService, logger)

	// Initialize WebSocket hub
	hub := handlers.NewWebSocketHub(logger)
	go hub.Run()

	// Initialize router
	router := gin.Default()
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.LoggerMiddleware(logger))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"service": "livestream",
		})
	})

	// API routes
	api := router.Group("/api/v1")
	{
		// Virtual Currency endpoints
		currency := api.Group("/currency")
		{
			currency.GET("/balance/:userId", currencyHandler.GetBalance)
			currency.POST("/purchase", middleware.AuthMiddleware(), currencyHandler.PurchaseCoins)
			currency.GET("/transactions/:userId", middleware.AuthMiddleware(), currencyHandler.GetTransactions)
			currency.GET("/packages", currencyHandler.GetCoinPackages)
		}

		// Live Stream endpoints
		streams := api.Group("/streams")
		{
			streams.GET("/", streamHandler.GetActiveStreams)
			streams.GET("/:streamId", streamHandler.GetStreamDetails)
			streams.POST("/", middleware.AuthMiddleware(), streamHandler.CreateStream)
			streams.PUT("/:streamId", middleware.AuthMiddleware(), streamHandler.UpdateStream)
			streams.DELETE("/:streamId", middleware.AuthMiddleware(), streamHandler.EndStream)
			streams.GET("/:streamId/viewers", streamHandler.GetViewers)
		}

		// Live Stream Gift endpoints
		gifts := api.Group("/gifts")
		{
			gifts.POST("/send", middleware.AuthMiddleware(), giftHandler.SendGift)
			gifts.GET("/stream/:streamId", giftHandler.GetStreamGifts)
			gifts.GET("/user/:userId/sent", middleware.AuthMiddleware(), giftHandler.GetUserSentGifts)
			gifts.GET("/user/:userId/received", middleware.AuthMiddleware(), giftHandler.GetUserReceivedGifts)
		}

		// Gifter Ranking endpoints
		rankings := api.Group("/rankings")
		{
			rankings.GET("/stream/:streamId", rankingHandler.GetStreamRankings)
			rankings.GET("/global", rankingHandler.GetGlobalRankings)
			rankings.GET("/user/:userId", rankingHandler.GetUserRanking)
		}

		// Creator Revenue endpoints
		revenue := api.Group("/revenue")
		{
			revenue.GET("/creator/:userId", middleware.AuthMiddleware(), revenueHandler.GetCreatorRevenue)
			revenue.GET("/summary/:userId", middleware.AuthMiddleware(), revenueHandler.GetRevenueSummary)
			revenue.POST("/withdraw", middleware.AuthMiddleware(), revenueHandler.RequestWithdrawal)
		}

		// WebSocket endpoint for real-time communication
		api.GET("/ws", func(c *gin.Context) {
			handlers.ServeWs(hub, c)
		})
	}

	// Start server
	port := os.Getenv("LIVESTREAM_SERVICE_PORT")
	if port == "" {
		port = "8006" // Default port for livestream service
	}

	logger.Info("Live Streaming Service starting on port " + port)
	if err := router.Run("0.0.0.0:" + port); err != nil {
		logger.Fatal("Failed to start Live Streaming Service: " + err.Error())
	}
}
