package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"

	_ "github.com/lib/pq"
)

// Citation represents a single bibliographic entry
type Citation struct {
	ID           int    `json:"id"`
	BookID       int    `json:"book_id"`
	CitationKey  string `json:"citation_key"`  // Unique identifier like "achebe1983"
	RefNumber    int    `json:"ref_number"`    // Number used in the in-text citation
	Author       string `json:"author"`        // Author(s) name(s)
	Year         string `json:"year"`          // Publication year
	Title        string `json:"title"`         // Title of work
	Source       string `json:"source"`        // Source information (publisher, journal, etc.)
	URL          string `json:"url"`           // Online link if available
	Type         string `json:"type"`          // Type of reference (book, article, interview, etc.)
	ChapterIDs   []int  `json:"chapter_ids"`   // Chapters where this citation is used
	SectionIDs   []int  `json:"section_ids"`   // Sections where this citation is used
	CitedCount   int    `json:"cited_count"`   // How many times this source is cited across the book
}

// Book represents a collection of citations used in a specific book
type Book struct {
	ID          int                 `json:"id"`
	Title       string              `json:"title"`
	Citations   map[string]Citation `json:"citations"`   // Map of CitationKey to Citation
	CitationIDs []string            `json:"citation_ids"` // Ordered list of citation keys
}

// CitationTracker manages citations across all books
type CitationTracker struct {
	Books             map[int]*Book         `json:"books"`               // Map of BookID to Book
	GlobalCitations   map[string]Citation   `json:"global_citations"`    // Map of CitationKey to Citation (across all books)
	DB                *sql.DB               `json:"-"`                   // Database connection (not serialized)
	AutoNumberingMode string                `json:"auto_numbering_mode"` // "global" or "per_chapter"
}

// NewCitationTracker creates a new citation tracker
func NewCitationTracker(db *sql.DB) *CitationTracker {
	return &CitationTracker{
		Books:             make(map[int]*Book),
		GlobalCitations:   make(map[string]Citation),
		DB:                db,
		AutoNumberingMode: "global", // Default to global numbering
	}
}

// LoadFromDatabase loads all citations from the database
func (ct *CitationTracker) LoadFromDatabase() error {
	// In a real implementation, this would query the database
	// For demonstration, we'll create sample data
	
	// Initialize books
	book1 := &Book{
		ID:          1,
		Title:       "The Diagnostic Edition: Understanding Nigeria's Challenges",
		Citations:   make(map[string]Citation),
		CitationIDs: []string{},
	}
	
	book2 := &Book{
		ID:          2,
		Title:       "The Solution Blueprint: Strategic Path to Transformation",
		Citations:   make(map[string]Citation),
		CitationIDs: []string{},
	}
	
	book3 := &Book{
		ID:          3,
		Title:       "Great Nigeria: A Story of Crises, Hope, and Collective Victory",
		Citations:   make(map[string]Citation),
		CitationIDs: []string{},
	}
	
	ct.Books[1] = book1
	ct.Books[2] = book2
	ct.Books[3] = book3
	
	// Add sample citations to Book 1
	book1Citations := []Citation{
		{ID: 1, BookID: 1, CitationKey: "achebe1983", RefNumber: 1, Author: "Achebe, Chinua", Year: "1983", Title: "The Trouble with Nigeria", Source: "Heinemann Educational Publishers", Type: "book", ChapterIDs: []int{1, 2}, SectionIDs: []int{1, 2, 3}, CitedCount: 5},
		{ID: 2, BookID: 1, CitationKey: "falola2008", RefNumber: 2, Author: "Falola, Toyin & Heaton, Matthew M.", Year: "2008", Title: "A History of Nigeria", Source: "Cambridge University Press", Type: "book", ChapterIDs: []int{1, 3}, SectionIDs: []int{1, 6, 7}, CitedCount: 3},
		{ID: 3, BookID: 1, CitationKey: "maier2000", RefNumber: 3, Author: "Maier, Karl", Year: "2000", Title: "This House Has Fallen: Nigeria in Crisis", Source: "Penguin Books", Type: "book", ChapterIDs: []int{1, 2, 4}, SectionIDs: []int{2, 8, 12}, CitedCount: 4},
		{ID: 4, BookID: 1, CitationKey: "cdd2023", RefNumber: 4, Author: "Centre for Democracy and Development", Year: "2023", Title: "The State of Democracy in Nigeria", Source: "CDD West Africa", Type: "report", ChapterIDs: []int{3, 5}, SectionIDs: []int{9, 15}, CitedCount: 2},
		{ID: 5, BookID: 1, CitationKey: "icg2023", RefNumber: 5, Author: "International Crisis Group", Year: "2023", Title: "Managing Vigilantism in Nigeria: A Near-term Necessity", Source: "Africa Report No. 308", Type: "report", ChapterIDs: []int{2, 4}, SectionIDs: []int{5, 13}, CitedCount: 2},
	}
	
	for _, c := range book1Citations {
		book1.Citations[c.CitationKey] = c
		book1.CitationIDs = append(book1.CitationIDs, c.CitationKey)
		ct.GlobalCitations[c.CitationKey] = c
	}
	
	// Add sample citations to Book 2
	book2Citations := []Citation{
		{ID: 6, BookID: 2, CitationKey: "acemoglu2012", RefNumber: 1, Author: "Acemoglu, Daron & Robinson, James A.", Year: "2012", Title: "Why Nations Fail: The Origins of Power, Prosperity, and Poverty", Source: "Crown Business", Type: "book", ChapterIDs: []int{1, 3}, SectionIDs: []int{1, 7}, CitedCount: 4},
		{ID: 7, BookID: 2, CitationKey: "elrufai2013", RefNumber: 2, Author: "El-Rufai, Nasir A.", Year: "2013", Title: "The Accidental Public Servant", Source: "Safari Books", Type: "book", ChapterIDs: []int{2, 5}, SectionIDs: []int{4, 16}, CitedCount: 3},
		{ID: 8, BookID: 2, CitationKey: "okonjo2018", RefNumber: 3, Author: "Okonjo-Iweala, Ngozi", Year: "2018", Title: "Fighting Corruption Is Dangerous: The Story Behind the Headlines", Source: "MIT Press", Type: "book", ChapterIDs: []int{2, 6}, SectionIDs: []int{5, 19}, CitedCount: 3},
		{ID: 9, BookID: 2, CitationKey: "cppa2023", RefNumber: 4, Author: "Centre for Public Policy Alternatives", Year: "2023", Title: "Nigeria Governance Reform Roadmap", Source: "CPPA Policy Papers", Type: "report", ChapterIDs: []int{1, 4}, SectionIDs: []int{2, 11}, CitedCount: 2},
		{ID: 10, BookID: 2, CitationKey: "mgi2022", RefNumber: 5, Author: "McKinsey Global Institute", Year: "2022", Title: "Nigeria's Renewal: Delivering Inclusive Growth", Source: "MGI Reports", Type: "report", ChapterIDs: []int{3, 7}, SectionIDs: []int{8, 22}, CitedCount: 2},
	}
	
	for _, c := range book2Citations {
		book2.Citations[c.CitationKey] = c
		book2.CitationIDs = append(book2.CitationIDs, c.CitationKey)
		ct.GlobalCitations[c.CitationKey] = c
	}
	
	// Add sample citations to Book 3 (including some shared with other books)
	book3Citations := []Citation{
		{ID: 11, BookID: 3, CitationKey: "achebe1983", RefNumber: 1, Author: "Achebe, Chinua", Year: "1983", Title: "The Trouble with Nigeria", Source: "Heinemann Educational Publishers", Type: "book", ChapterIDs: []int{1, 3}, SectionIDs: []int{1, 9}, CitedCount: 4},
		{ID: 12, BookID: 3, CitationKey: "collier2007", RefNumber: 2, Author: "Collier, Paul", Year: "2007", Title: "The Bottom Billion: Why the Poorest Countries are Failing and What Can Be Done About It", Source: "Oxford University Press", Type: "book", ChapterIDs: []int{2, 5}, SectionIDs: []int{5, 16}, CitedCount: 3},
		{ID: 13, BookID: 3, CitationKey: "diamond2010", RefNumber: 3, Author: "Diamond, Larry", Year: "2010", Title: "The Spirit of Democracy: The Struggle to Build Free Societies Throughout the World", Source: "St. Martin's Griffin", Type: "book", ChapterIDs: []int{4, 6}, SectionIDs: []int{13, 19}, CitedCount: 2},
		{ID: 14, BookID: 3, CitationKey: "easterly2006", RefNumber: 4, Author: "Easterly, William", Year: "2006", Title: "The White Man's Burden: Why the West's Efforts to Aid the Rest Have Done So Much Ill and So Little Good", Source: "Penguin Books", Type: "book", ChapterIDs: []int{3, 7}, SectionIDs: []int{8, 22}, CitedCount: 2},
		{ID: 15, BookID: 3, CitationKey: "falola2008", RefNumber: 5, Author: "Falola, Toyin & Heaton, Matthew M.", Year: "2008", Title: "A History of Nigeria", Source: "Cambridge University Press", Type: "book", ChapterIDs: []int{2, 8}, SectionIDs: []int{6, 25}, CitedCount: 3},
	}
	
	for _, c := range book3Citations {
		book3.Citations[c.CitationKey] = c
		book3.CitationIDs = append(book3.CitationIDs, c.CitationKey)
		ct.GlobalCitations[c.CitationKey] = c
	}
	
	return nil
}

// AddCitation adds a new citation to the tracker and database
func (ct *CitationTracker) AddCitation(bookID int, c Citation) error {
	// Check if book exists
	book, exists := ct.Books[bookID]
	if !exists {
		return fmt.Errorf("book with ID %d does not exist", bookID)
	}
	
	// Check if citation already exists
	if _, exists := book.Citations[c.CitationKey]; exists {
		return fmt.Errorf("citation with key %s already exists in book %d", c.CitationKey, bookID)
	}
	
	// Assign a reference number
	if ct.AutoNumberingMode == "global" {
		// Global numbering - assign the next available number for the book
		c.RefNumber = len(book.Citations) + 1
	} else if ct.AutoNumberingMode == "per_chapter" {
		// Per-chapter numbering - would need to track chapter-specific numbers
		// For demonstration, we'll still use global numbering
		c.RefNumber = len(book.Citations) + 1
	}
	
	// Add to book and global citations
	c.BookID = bookID
	book.Citations[c.CitationKey] = c
	book.CitationIDs = append(book.CitationIDs, c.CitationKey)
	ct.GlobalCitations[c.CitationKey] = c
	
	// In a real implementation, save to database
	// For demonstration, just print
	fmt.Printf("Added citation: %s (RefNumber: %d) to Book %d\n", c.CitationKey, c.RefNumber, bookID)
	
	return nil
}

// UseCitation records usage of a citation in a specific chapter and section
func (ct *CitationTracker) UseCitation(bookID int, citationKey string, chapterID, sectionID int) error {
	// Check if book exists
	book, exists := ct.Books[bookID]
	if !exists {
		return fmt.Errorf("book with ID %d does not exist", bookID)
	}
	
	// Check if citation exists
	c, exists := book.Citations[citationKey]
	if !exists {
		// Check if it exists globally but not in this book
		globalCitation, globalExists := ct.GlobalCitations[citationKey]
		if globalExists {
			// Citation exists in another book, clone it for this book
			globalCitation.BookID = bookID
			globalCitation.RefNumber = len(book.Citations) + 1
			globalCitation.ChapterIDs = []int{}
			globalCitation.SectionIDs = []int{}
			globalCitation.CitedCount = 0
			c = globalCitation
		} else {
			return fmt.Errorf("citation with key %s does not exist", citationKey)
		}
	}
	
	// Update chapter and section usage
	chapterFound := false
	for _, id := range c.ChapterIDs {
		if id == chapterID {
			chapterFound = true
			break
		}
	}
	if !chapterFound {
		c.ChapterIDs = append(c.ChapterIDs, chapterID)
	}
	
	sectionFound := false
	for _, id := range c.SectionIDs {
		if id == sectionID {
			sectionFound = true
			break
		}
	}
	if !sectionFound {
		c.SectionIDs = append(c.SectionIDs, sectionID)
	}
	
	// Increment citation count
	c.CitedCount++
	
	// Update the citation in the book and global maps
	book.Citations[citationKey] = c
	ct.GlobalCitations[citationKey] = c
	
	// In a real implementation, save to database
	// For demonstration, just print
	fmt.Printf("Used citation: %s in Chapter %d, Section %d of Book %d (Count: %d)\n", 
		citationKey, chapterID, sectionID, bookID, c.CitedCount)
	
	return nil
}

// GenerateBibliography creates a bibliography for a specific book
func (ct *CitationTracker) GenerateBibliography(bookID int) (string, error) {
	// Check if book exists
	book, exists := ct.Books[bookID]
	if !exists {
		return "", fmt.Errorf("book with ID %d does not exist", bookID)
	}
	
	// Get all citations for this book
	var citations []Citation
	for _, key := range book.CitationIDs {
		citations = append(citations, book.Citations[key])
	}
	
	// Sort citations by reference number
	sort.Slice(citations, func(i, j int) bool {
		return citations[i].RefNumber < citations[j].RefNumber
	})
	
	// Generate the bibliography content in proper academic format
	var bibliography strings.Builder
	bibliography.WriteString(fmt.Sprintf("# Bibliography for %s\n\n", book.Title))
	
	// Categorize citations
	bookCitations := []Citation{}
	journalCitations := []Citation{}
	reportCitations := []Citation{}
	governmentCitations := []Citation{}
	interviewCitations := []Citation{}
	surveyCitations := []Citation{}
	mediaCitations := []Citation{}
	
	for _, c := range citations {
		switch c.Type {
		case "book":
			bookCitations = append(bookCitations, c)
		case "journal":
			journalCitations = append(journalCitations, c)
		case "report":
			reportCitations = append(reportCitations, c)
		case "government":
			governmentCitations = append(governmentCitations, c)
		case "interview":
			interviewCitations = append(interviewCitations, c)
		case "survey":
			surveyCitations = append(surveyCitations, c)
		case "media":
			mediaCitations = append(mediaCitations, c)
		}
	}
	
	// Academic sources section
	bibliography.WriteString("## Academic Sources\n\n")
	
	if len(bookCitations) > 0 {
		bibliography.WriteString("### Books\n\n")
		for _, c := range bookCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	if len(journalCitations) > 0 {
		bibliography.WriteString("### Journal Articles\n\n")
		for _, c := range journalCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). %s. *%s*. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	if len(reportCitations) > 0 {
		bibliography.WriteString("### Reports and Working Papers\n\n")
		for _, c := range reportCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	// Government sources section
	if len(governmentCitations) > 0 {
		bibliography.WriteString("## Government and Institutional Sources\n\n")
		for _, c := range governmentCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	// Research data section
	bibliography.WriteString("## Research Data\n\n")
	
	if len(interviewCitations) > 0 {
		bibliography.WriteString("### Field Interviews and Focus Groups\n\n")
		for _, c := range interviewCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). %s. %s. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	if len(surveyCitations) > 0 {
		bibliography.WriteString("### Surveys and Statistical Data\n\n")
		for _, c := range surveyCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	// Media section
	if len(mediaCitations) > 0 {
		bibliography.WriteString("## Additional Sources\n\n")
		bibliography.WriteString("### Media and Online Resources\n\n")
		for _, c := range mediaCitations {
			bibliography.WriteString(fmt.Sprintf("%s. (%s). %s. *%s*. [%d]\n\n", 
				c.Author, c.Year, c.Title, c.Source, c.RefNumber))
		}
	}
	
	// Citation guide
	bibliography.WriteString("## Citation Index\n\n")
	bibliography.WriteString("The numbers in square brackets [1] throughout the book refer to the entries in this bibliography. You can find specific citation references at the end of each chapter or section.\n\n")
	
	return bibliography.String(), nil
}

// SaveToFile saves the citation tracker state to a JSON file
func (ct *CitationTracker) SaveToFile(filename string) error {
	// Serialize to JSON
	data, err := json.MarshalIndent(ct, "", "  ")
	if err != nil {
		return fmt.Errorf("error serializing citation tracker: %v", err)
	}
	
	// Write to file
	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("error writing to file: %v", err)
	}
	
	fmt.Printf("Citation tracker saved to %s\n", filename)
	return nil
}

// LoadFromFile loads the citation tracker state from a JSON file
func (ct *CitationTracker) LoadFromFile(filename string) error {
	// Read file
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("error reading file: %v", err)
	}
	
	// Deserialize from JSON
	err = json.Unmarshal(data, ct)
	if err != nil {
		return fmt.Errorf("error deserializing citation tracker: %v", err)
	}
	
	fmt.Printf("Citation tracker loaded from %s\n", filename)
	return nil
}

// GetStatistics returns statistics about citations
func (ct *CitationTracker) GetStatistics() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// Count total citations
	stats["total_citations"] = len(ct.GlobalCitations)
	
	// Count citations by book
	citationsByBook := make(map[int]int)
	for _, book := range ct.Books {
		citationsByBook[book.ID] = len(book.Citations)
	}
	stats["citations_by_book"] = citationsByBook
	
	// Count citations by type
	citationsByType := make(map[string]int)
	for _, c := range ct.GlobalCitations {
		citationsByType[c.Type]++
	}
	stats["citations_by_type"] = citationsByType
	
	// Find most cited sources
	type citationCount struct {
		Key   string
		Count int
	}
	var counts []citationCount
	for key, c := range ct.GlobalCitations {
		counts = append(counts, citationCount{Key: key, Count: c.CitedCount})
	}
	sort.Slice(counts, func(i, j int) bool {
		return counts[i].Count > counts[j].Count
	})
	
	mostCited := make([]map[string]interface{}, 0)
	for i := 0; i < 5 && i < len(counts); i++ {
		c := ct.GlobalCitations[counts[i].Key]
		mostCited = append(mostCited, map[string]interface{}{
			"key":   c.CitationKey,
			"count": c.CitedCount,
			"title": c.Title,
			"author": c.Author,
		})
	}
	stats["most_cited"] = mostCited
	
	return stats
}

func main() {
	// Create a new citation tracker
	tracker := NewCitationTracker(nil)
	
	// Load sample data
	err := tracker.LoadFromDatabase()
	if err != nil {
		log.Fatalf("Error loading citations: %v", err)
	}
	
	// Example: Use a citation
	err = tracker.UseCitation(3, "achebe1983", 1, 2)
	if err != nil {
		log.Printf("Error using citation: %v", err)
	}
	
	// Example: Add a new citation
	newCitation := Citation{
		CitationKey: "worldbank2024",
		Author:      "World Bank",
		Year:        "2024",
		Title:       "Nigeria Development Update: Resilience through Reforms",
		Source:      "World Bank Publications",
		Type:        "report",
	}
	err = tracker.AddCitation(3, newCitation)
	if err != nil {
		log.Printf("Error adding citation: %v", err)
	}
	
	// Generate bibliography for Book 3
	bibliography, err := tracker.GenerateBibliography(3)
	if err != nil {
		log.Fatalf("Error generating bibliography: %v", err)
	}
	fmt.Println("BIBLIOGRAPHY FOR BOOK 3:")
	fmt.Println(bibliography)
	
	// Save citation tracker to file
	err = tracker.SaveToFile("citation_tracker.json")
	if err != nil {
		log.Printf("Error saving citation tracker: %v", err)
	}
	
	// Get and print statistics
	stats := tracker.GetStatistics()
	fmt.Println("\nCITATION STATISTICS:")
	statsJSON, _ := json.MarshalIndent(stats, "", "  ")
	fmt.Println(string(statsJSON))
}