import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import tipsService, { Tip, TipRequest, TipStatistics, TipFeedback } from '../../api/tipsService';

// Define types
interface TipsState {
  tips: Tip[];
  contextualTips: Tip[];
  currentTip: Tip | null;
  statistics: TipStatistics[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: TipsState = {
  tips: [],
  contextualTips: [],
  currentTip: null,
  statistics: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchAllTips = createAsyncThunk(
  'tips/fetchAllTips',
  async (_, { rejectWithValue }) => {
    try {
      return await tipsService.getAllTips();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tips');
    }
  }
);

export const fetchTipById = createAsyncThunk(
  'tips/fetchTipById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await tipsService.getTipById(id);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tip');
    }
  }
);

export const fetchTipsByCategory = createAsyncThunk(
  'tips/fetchTipsByCategory',
  async (category: string, { rejectWithValue }) => {
    try {
      return await tipsService.getTipsByCategory(category);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tips by category');
    }
  }
);

export const fetchContextualTips = createAsyncThunk(
  'tips/fetchContextualTips',
  async (request: TipRequest, { rejectWithValue }) => {
    try {
      const response = await tipsService.getContextualTips(request);
      return response.tips;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch contextual tips');
    }
  }
);

export const createTip = createAsyncThunk(
  'tips/createTip',
  async (tip: Omit<Tip, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      return await tipsService.createTip(tip);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create tip');
    }
  }
);

export const updateTip = createAsyncThunk(
  'tips/updateTip',
  async ({ id, tip }: { id: number; tip: Partial<Tip> }, { rejectWithValue }) => {
    try {
      return await tipsService.updateTip(id, tip);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update tip');
    }
  }
);

export const deleteTip = createAsyncThunk(
  'tips/deleteTip',
  async (id: number, { rejectWithValue }) => {
    try {
      await tipsService.deleteTip(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete tip');
    }
  }
);

export const recordTipView = createAsyncThunk(
  'tips/recordTipView',
  async (id: number, { rejectWithValue }) => {
    try {
      await tipsService.recordTipView(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to record tip view');
    }
  }
);

export const recordTipDismiss = createAsyncThunk(
  'tips/recordTipDismiss',
  async (id: number, { rejectWithValue }) => {
    try {
      await tipsService.recordTipDismiss(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to record tip dismiss');
    }
  }
);

export const recordTipClick = createAsyncThunk(
  'tips/recordTipClick',
  async (id: number, { rejectWithValue }) => {
    try {
      await tipsService.recordTipClick(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to record tip click');
    }
  }
);

export const submitTipFeedback = createAsyncThunk(
  'tips/submitTipFeedback',
  async (feedback: TipFeedback, { rejectWithValue }) => {
    try {
      await tipsService.submitTipFeedback(feedback);
      return feedback;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to submit tip feedback');
    }
  }
);

export const fetchTipStatistics = createAsyncThunk(
  'tips/fetchTipStatistics',
  async (id: number, { rejectWithValue }) => {
    try {
      return await tipsService.getTipStatistics(id);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tip statistics');
    }
  }
);

export const fetchAllTipStatistics = createAsyncThunk(
  'tips/fetchAllTipStatistics',
  async (_, { rejectWithValue }) => {
    try {
      return await tipsService.getAllTipStatistics();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tip statistics');
    }
  }
);

// Create slice
const tipsSlice = createSlice({
  name: 'tips',
  initialState,
  reducers: {
    clearTipsError: (state) => {
      state.error = null;
    },
    clearContextualTips: (state) => {
      state.contextualTips = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchAllTips
      .addCase(fetchAllTips.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllTips.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tips = action.payload;
      })
      .addCase(fetchAllTips.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchTipById
      .addCase(fetchTipById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTipById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTip = action.payload;
      })
      .addCase(fetchTipById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchTipsByCategory
      .addCase(fetchTipsByCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTipsByCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tips = action.payload;
      })
      .addCase(fetchTipsByCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchContextualTips
      .addCase(fetchContextualTips.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContextualTips.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contextualTips = action.payload;
      })
      .addCase(fetchContextualTips.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // createTip
      .addCase(createTip.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTip.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tips.push(action.payload);
      })
      .addCase(createTip.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // updateTip
      .addCase(updateTip.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTip.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.tips.findIndex((tip) => tip.id === action.payload.id);
        if (index !== -1) {
          state.tips[index] = action.payload;
        }
        if (state.currentTip && state.currentTip.id === action.payload.id) {
          state.currentTip = action.payload;
        }
      })
      .addCase(updateTip.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // deleteTip
      .addCase(deleteTip.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTip.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tips = state.tips.filter((tip) => tip.id !== action.payload);
        if (state.currentTip && state.currentTip.id === action.payload) {
          state.currentTip = null;
        }
      })
      .addCase(deleteTip.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchAllTipStatistics
      .addCase(fetchAllTipStatistics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllTipStatistics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.statistics = action.payload;
      })
      .addCase(fetchAllTipStatistics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearTipsError, clearContextualTips } = tipsSlice.actions;

// Export selectors
export const selectAllTips = (state: RootState) => state.tips.tips;
export const selectContextualTips = (state: RootState) => state.tips.contextualTips;
export const selectCurrentTip = (state: RootState) => state.tips.currentTip;
export const selectTipStatistics = (state: RootState) => state.tips.statistics;
export const selectTipsLoading = (state: RootState) => state.tips.isLoading;
export const selectTipsError = (state: RootState) => state.tips.error;

// Export reducer
export default tipsSlice.reducer;
