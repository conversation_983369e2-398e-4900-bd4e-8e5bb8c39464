import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  EmojiEvents as TrophyIcon,
  Stars as StarsIcon,
  Redeem as RedeemIcon,
  Rule as RuleIcon,
  Timeline as TimelineIcon,
  Group as GroupIcon,
  Insights as InsightsIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchRules,
  fetchTiers,
  fetchAchievements,
  createRule,
  updateRule,
  deleteRule,
  createTier,
  updateTier,
  deleteTier,
  createAchievement,
  updateAchievement,
  deleteAchievement,
} from '../store/slices/rewardsSlice';
import { RewardRule, RewardTier, Achievement } from '../services/rewardsService';

// Helper components
const RuleItem: React.FC<{
  rule: RewardRule;
  onEdit: () => void;
  onDelete: () => void;
}> = ({ rule, onEdit, onDelete }) => {
  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography variant="h6">{rule.name}</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {rule.description}
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Typography variant="body2" component="span">
              <strong>Trigger:</strong> {rule.triggerAction}
            </Typography>
            <Typography variant="body2" component="span">
              <strong>Points:</strong> {rule.pointsAwarded}
            </Typography>
            {rule.cooldownPeriod && (
              <Typography variant="body2" component="span">
                <strong>Cooldown:</strong> {rule.cooldownPeriod}s
              </Typography>
            )}
            {rule.maxOccurrences && (
              <Typography variant="body2" component="span">
                <strong>Max:</strong> {rule.maxOccurrences} times
              </Typography>
            )}
          </Box>
        </Box>
        <Box>
          <IconButton onClick={onEdit} size="small" sx={{ mr: 1 }}>
            <EditIcon />
          </IconButton>
          <IconButton onClick={onDelete} size="small" color="error">
            <DeleteIcon />
          </IconButton>
        </Box>
      </Box>
    </Paper>
  );
};

const TierItem: React.FC<{
  tier: RewardTier;
  onEdit: () => void;
  onDelete: () => void;
}> = ({ tier, onEdit, onDelete }) => {
  return (
    <Paper
      sx={{
        p: 2,
        mb: 2,
        borderLeft: '4px solid',
        borderColor: tier.color
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography variant="h6">{tier.name}</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Level {tier.level} - {tier.pointsRequired} points required
          </Typography>
          <Typography variant="subtitle2">Benefits:</Typography>
          <List dense>
            {tier.benefits.map((benefit, index) => (
              <ListItem key={index} sx={{ py: 0 }}>
                <ListItemText primary={benefit} />
              </ListItem>
            ))}
          </List>
        </Box>
        <Box>
          <IconButton onClick={onEdit} size="small" sx={{ mr: 1 }}>
            <EditIcon />
          </IconButton>
          <IconButton onClick={onDelete} size="small" color="error">
            <DeleteIcon />
          </IconButton>
        </Box>
      </Box>
    </Paper>
  );
};

// Main component
const EngagementDashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useSelector((state: RootState) => state.auth);
  const { items: rules, loading: rulesLoading } = useSelector(
    (state: RootState) => state.rewards.rules
  );
  const { items: tiers, loading: tiersLoading } = useSelector(
    (state: RootState) => state.rewards.tiers
  );
  const { items: achievements, loading: achievementsLoading } = useSelector(
    (state: RootState) => state.rewards.achievements
  );

  const [tabValue, setTabValue] = useState(0);
  const [selectedRule, setSelectedRule] = useState<RewardRule | null>(null);
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [selectedTier, setSelectedTier] = useState<RewardTier | null>(null);
  const [tierDialogOpen, setTierDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'rule' | 'tier' | 'achievement'; id: number } | null>(null);

  useEffect(() => {
    dispatch(fetchRules());
    dispatch(fetchTiers());
    dispatch(fetchAchievements());
  }, [dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Rule handlers
  const handleAddRule = () => {
    setSelectedRule({
      id: 0,
      name: '',
      description: '',
      triggerAction: '',
      pointsAwarded: 10,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    setRuleDialogOpen(true);
  };

  const handleEditRule = (rule: RewardRule) => {
    setSelectedRule(rule);
    setRuleDialogOpen(true);
  };

  const handleDeleteRule = (rule: RewardRule) => {
    setItemToDelete({ type: 'rule', id: rule.id });
    setDeleteDialogOpen(true);
  };

  const handleCloseRuleDialog = () => {
    setRuleDialogOpen(false);
    setSelectedRule(null);
  };

  // Tier handlers
  const handleAddTier = () => {
    setSelectedTier({
      id: 0,
      name: '',
      level: tiers.length + 1,
      pointsRequired: 0,
      benefits: [''],
      icon: '',
      color: '#3f51b5',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    setTierDialogOpen(true);
  };

  const handleEditTier = (tier: RewardTier) => {
    setSelectedTier(tier);
    setTierDialogOpen(true);
  };

  const handleDeleteTier = (tier: RewardTier) => {
    setItemToDelete({ type: 'tier', id: tier.id });
    setDeleteDialogOpen(true);
  };

  const handleCloseTierDialog = () => {
    setTierDialogOpen(false);
    setSelectedTier(null);
  };

  // Delete dialog handlers
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (!itemToDelete) return;

    try {
      if (itemToDelete.type === 'rule') {
        await dispatch(deleteRule(itemToDelete.id)).unwrap();
      } else if (itemToDelete.type === 'tier') {
        await dispatch(deleteTier(itemToDelete.id)).unwrap();
      } else if (itemToDelete.type === 'achievement') {
        await dispatch(deleteAchievement(itemToDelete.id)).unwrap();
      }
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Failed to delete item:', error);
    }
  };

  // Save rule handler
  const handleSaveRule = async () => {
    if (!selectedRule) return;

    try {
      if (selectedRule.id === 0) {
        await dispatch(createRule(selectedRule)).unwrap();
      } else {
        await dispatch(updateRule({ id: selectedRule.id, rule: selectedRule })).unwrap();
      }
      handleCloseRuleDialog();
    } catch (error) {
      console.error('Failed to save rule:', error);
    }
  };

  // Save tier handler
  const handleSaveTier = async () => {
    if (!selectedTier) return;

    try {
      if (selectedTier.id === 0) {
        await dispatch(createTier(selectedTier)).unwrap();
      } else {
        await dispatch(updateTier({ id: selectedTier.id, tier: selectedTier })).unwrap();
      }
      handleCloseTierDialog();
    } catch (error) {
      console.error('Failed to save tier:', error);
    }
  };

  if (!user || !user.isAdmin) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be an administrator to access this page.
        </Alert>
      </Container>
    );
  }

  const loading = rulesLoading || tiersLoading || achievementsLoading;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Engagement Dashboard
      </Typography>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<RuleIcon />} label="Reward Rules" />
          <Tab icon={<TrophyIcon />} label="Tiers" />
          <Tab icon={<InsightsIcon />} label="Analytics" />
        </Tabs>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Rules Tab */}
          {tabValue === 0 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5">Reward Rules</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRule}
                >
                  Add Rule
                </Button>
              </Box>

              {rules.length === 0 ? (
                <Alert severity="info">
                  No reward rules defined yet. Click "Add Rule" to create your first rule.
                </Alert>
              ) : (
                rules.map((rule) => (
                  <RuleItem
                    key={rule.id}
                    rule={rule}
                    onEdit={() => handleEditRule(rule)}
                    onDelete={() => handleDeleteRule(rule)}
                  />
                ))
              )}
            </Box>
          )}

          {/* Tiers Tab */}
          {tabValue === 1 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5">Reward Tiers</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddTier}
                >
                  Add Tier
                </Button>
              </Box>

              {tiers.length === 0 ? (
                <Alert severity="info">
                  No reward tiers defined yet. Click "Add Tier" to create your first tier.
                </Alert>
              ) : (
                tiers
                  .sort((a, b) => a.level - b.level)
                  .map((tier) => (
                    <TierItem
                      key={tier.id}
                      tier={tier}
                      onEdit={() => handleEditTier(tier)}
                      onDelete={() => handleDeleteTier(tier)}
                    />
                  ))
              )}
            </Box>
          )}

          {/* Analytics Tab */}
          {tabValue === 2 && (
            <Box>
              <Typography variant="h5" gutterBottom>
                Engagement Analytics
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Analytics dashboard is under development. Check back soon for detailed engagement metrics.
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Total Users
                      </Typography>
                      <Typography variant="h3">1,245</Typography>
                      <Typography variant="body2" color="text.secondary">
                        +12% from last month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Active Users
                      </Typography>
                      <Typography variant="h3">876</Typography>
                      <Typography variant="body2" color="text.secondary">
                        +5% from last month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Points Awarded
                      </Typography>
                      <Typography variant="h3">24,680</Typography>
                      <Typography variant="body2" color="text.secondary">
                        +18% from last month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </>
      )}

      {/* Rule Dialog */}
      <Dialog open={ruleDialogOpen} onClose={handleCloseRuleDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedRule?.id === 0 ? 'Add New Rule' : 'Edit Rule'}</DialogTitle>
        <DialogContent>
          {selectedRule && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Rule Name"
                  value={selectedRule.name}
                  onChange={(e) => setSelectedRule({ ...selectedRule, name: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={selectedRule.description}
                  onChange={(e) => setSelectedRule({ ...selectedRule, description: e.target.value })}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Trigger Action"
                  value={selectedRule.triggerAction}
                  onChange={(e) => setSelectedRule({ ...selectedRule, triggerAction: e.target.value })}
                  helperText="e.g., 'complete_course', 'post_comment'"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Points Awarded"
                  type="number"
                  value={selectedRule.pointsAwarded}
                  onChange={(e) => setSelectedRule({ ...selectedRule, pointsAwarded: parseInt(e.target.value) || 0 })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cooldown Period (seconds)"
                  type="number"
                  value={selectedRule.cooldownPeriod || ''}
                  onChange={(e) => setSelectedRule({ ...selectedRule, cooldownPeriod: parseInt(e.target.value) || undefined })}
                  helperText="Optional: Minimum time between awards"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Max Occurrences"
                  type="number"
                  value={selectedRule.maxOccurrences || ''}
                  onChange={(e) => setSelectedRule({ ...selectedRule, maxOccurrences: parseInt(e.target.value) || undefined })}
                  helperText="Optional: Maximum times this rule can be triggered"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={selectedRule.isActive}
                      onChange={(e) => setSelectedRule({ ...selectedRule, isActive: e.target.checked })}
                    />
                  }
                  label="Rule is active"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRuleDialog}>Cancel</Button>
          <Button onClick={handleSaveRule} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Tier Dialog */}
      <Dialog open={tierDialogOpen} onClose={handleCloseTierDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedTier?.id === 0 ? 'Add New Tier' : 'Edit Tier'}</DialogTitle>
        <DialogContent>
          {selectedTier && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Tier Name"
                  value={selectedTier.name}
                  onChange={(e) => setSelectedTier({ ...selectedTier, name: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Level"
                  type="number"
                  value={selectedTier.level}
                  onChange={(e) => setSelectedTier({ ...selectedTier, level: parseInt(e.target.value) || 1 })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Points Required"
                  type="number"
                  value={selectedTier.pointsRequired}
                  onChange={(e) => setSelectedTier({ ...selectedTier, pointsRequired: parseInt(e.target.value) || 0 })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Icon"
                  value={selectedTier.icon}
                  onChange={(e) => setSelectedTier({ ...selectedTier, icon: e.target.value })}
                  helperText="Icon name or URL"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Color"
                  value={selectedTier.color}
                  onChange={(e) => setSelectedTier({ ...selectedTier, color: e.target.value })}
                  helperText="Color hex code (e.g., #3f51b5)"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Benefits
                </Typography>
                {selectedTier.benefits.map((benefit, index) => (
                  <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                    <TextField
                      fullWidth
                      label={`Benefit ${index + 1}`}
                      value={benefit}
                      onChange={(e) => {
                        const newBenefits = [...selectedTier.benefits];
                        newBenefits[index] = e.target.value;
                        setSelectedTier({ ...selectedTier, benefits: newBenefits });
                      }}
                    />
                    <IconButton
                      color="error"
                      onClick={() => {
                        const newBenefits = selectedTier.benefits.filter((_, i) => i !== index);
                        setSelectedTier({ ...selectedTier, benefits: newBenefits });
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))}
                <Button
                  startIcon={<AddIcon />}
                  onClick={() => {
                    setSelectedTier({
                      ...selectedTier,
                      benefits: [...selectedTier.benefits, ''],
                    });
                  }}
                >
                  Add Benefit
                </Button>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTierDialog}>Cancel</Button>
          <Button onClick={handleSaveTier} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this {itemToDelete?.type}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default EngagementDashboard;
