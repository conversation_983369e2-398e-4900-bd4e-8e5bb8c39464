import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { RootState } from '../store';
import {
  fetchCategories,
  fetchResourcesByCategory,
  searchResources,
  trackDownload,
} from '../features/resources/resourcesSlice';
import { ResourceCategory } from '../types';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
  max-width: 800px;
`;

const SearchBar = styled.div`
  display: flex;
  margin-bottom: 2rem;
  max-width: 600px;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #16213e;
  }
`;

const SearchButton = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0 1.5rem;
  cursor: pointer;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const ResourcesLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  
  @media (min-width: 992px) {
    grid-template-columns: 250px 1fr;
  }
`;

const Sidebar = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
`;

const SidebarTitle = styled.h2`
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #16213e;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
`;

const CategoryList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const CategoryItem = styled.li<{ active: boolean }>`
  margin-bottom: 0.5rem;
  
  a {
    display: block;
    padding: 0.75rem;
    border-radius: 4px;
    text-decoration: none;
    color: ${(props) => (props.active ? 'white' : '#333')};
    background-color: ${(props) => (props.active ? '#16213e' : 'transparent')};
    transition: all 0.3s ease;
    
    &:hover {
      background-color: ${(props) => (props.active ? '#0f3460' : '#e9ecef')};
    }
    
    .count {
      float: right;
      background-color: ${(props) => (props.active ? 'white' : '#16213e')};
      color: ${(props) => (props.active ? '#16213e' : 'white')};
      border-radius: 10px;
      padding: 0.1rem 0.5rem;
      font-size: 0.8rem;
    }
  }
`;

const MainContent = styled.div``;

const ResourcesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
`;

const ResourceCard = styled.div`
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const ResourceIcon = styled.div<{ fileType: string }>`
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.fileType.toLowerCase()) {
      case 'pdf':
        return '#f44336';
      case 'doc':
      case 'docx':
        return '#2196f3';
      case 'xls':
      case 'xlsx':
        return '#4caf50';
      case 'ppt':
      case 'pptx':
        return '#ff9800';
      case 'zip':
      case 'rar':
        return '#9c27b0';
      default:
        return '#16213e';
    }
  }};
  color: white;
  font-size: 3rem;
  font-weight: bold;
`;

const ResourceInfo = styled.div`
  padding: 1.5rem;
`;

const ResourceTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #16213e;
`;

const ResourceDescription = styled.p`
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ResourceMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 1rem;
`;

const ResourceType = styled.span`
  text-transform: uppercase;
  background-color: #f0f0f0;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
`;

const ResourceSize = styled.span``;

const ResourceDownloads = styled.span``;

const ResourceActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const DownloadButton = styled.a`
  display: inline-block;
  background-color: #16213e;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const ViewDetailsLink = styled(Link)`
  color: #16213e;
  text-decoration: none;
  font-size: 0.9rem;
  
  &:hover {
    text-decoration: underline;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #666;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  &:after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #16213e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  padding: 1rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
`;

const PageButton = styled.button<{ active?: boolean }>`
  padding: 0.5rem 1rem;
  border: 1px solid ${(props) => (props.active ? '#16213e' : '#ddd')};
  background-color: ${(props) => (props.active ? '#16213e' : 'white')};
  color: ${(props) => (props.active ? 'white' : '#333')};
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: ${(props) => (props.active ? '#0f3460' : '#f0f0f0')};
  }
  
  &:disabled {
    background-color: #f0f0f0;
    color: #999;
    cursor: not-allowed;
  }
`;

const ResourcesPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const categoryId = searchParams.get('category') || 'all';
  const searchQuery = searchParams.get('q') || '';
  
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [currentPage, setCurrentPage] = useState(1);
  const resourcesPerPage = 9;
  
  const dispatch = useDispatch();
  
  const { categories, resources, isLoading, error } = useSelector(
    (state: RootState) => state.resources
  );
  
  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);
  
  useEffect(() => {
    if (searchQuery) {
      dispatch(searchResources(searchQuery));
    } else if (categoryId && categoryId !== 'all') {
      dispatch(fetchResourcesByCategory(categoryId));
    } else if (categories.length > 0) {
      // If no category is selected, fetch resources from the first category
      dispatch(fetchResourcesByCategory(categories[0].id));
    }
  }, [dispatch, categoryId, searchQuery, categories]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setSearchParams({ q: searchTerm });
    } else {
      // If search is cleared, go back to category view
      setSearchParams({ category: categoryId !== 'all' ? categoryId : '' });
    }
    setCurrentPage(1);
  };
  
  const handleCategoryChange = (category: ResourceCategory) => {
    setSearchParams({ category: category.id });
    setSearchTerm('');
    setCurrentPage(1);
  };
  
  const handleDownload = (resourceId: string) => {
    dispatch(trackDownload(resourceId));
  };
  
  // Pagination
  const indexOfLastResource = currentPage * resourcesPerPage;
  const indexOfFirstResource = indexOfLastResource - resourcesPerPage;
  const currentResources = resources.slice(indexOfFirstResource, indexOfLastResource);
  const totalPages = Math.ceil(resources.length / resourcesPerPage);
  
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // Get file icon
  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return 'PDF';
      case 'doc':
      case 'docx':
        return 'DOC';
      case 'xls':
      case 'xlsx':
        return 'XLS';
      case 'ppt':
      case 'pptx':
        return 'PPT';
      case 'zip':
      case 'rar':
        return 'ZIP';
      default:
        return 'FILE';
    }
  };
  
  // Get active category
  const activeCategory = categories.find((cat) => cat.id === categoryId);
  
  return (
    <Container>
      <Title>Resources</Title>
      <Subtitle>
        Access tools, templates, and guides to help you implement the ideas from the books and make a
        real impact in your community.
      </Subtitle>
      
      <form onSubmit={handleSearch}>
        <SearchBar>
          <SearchInput
            type="text"
            placeholder="Search resources..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <SearchButton type="submit">Search</SearchButton>
        </SearchBar>
      </form>
      
      <ResourcesLayout>
        <Sidebar>
          <SidebarTitle>Categories</SidebarTitle>
          <CategoryList>
            <CategoryItem active={categoryId === 'all'}>
              <Link to="/resources?category=all">
                All Resources
                <span className="count">{resources.length}</span>
              </Link>
            </CategoryItem>
            {categories.map((category) => (
              <CategoryItem
                key={category.id}
                active={categoryId === category.id}
              >
                <Link
                  to={`/resources?category=${category.id}`}
                  onClick={() => handleCategoryChange(category)}
                >
                  {category.name}
                  <span className="count">{category.resources_count}</span>
                </Link>
              </CategoryItem>
            ))}
          </CategoryList>
        </Sidebar>
        
        <MainContent>
          {searchQuery ? (
            <h2>Search Results for "{searchQuery}"</h2>
          ) : (
            <h2>{activeCategory ? activeCategory.name : 'All Resources'}</h2>
          )}
          
          {error && <ErrorMessage>{error}</ErrorMessage>}
          
          {isLoading ? (
            <LoadingSpinner />
          ) : currentResources.length === 0 ? (
            <EmptyState>
              <p>No resources found.</p>
            </EmptyState>
          ) : (
            <ResourcesGrid>
              {currentResources.map((resource) => (
                <ResourceCard key={resource.id}>
                  <ResourceIcon fileType={resource.file_type}>
                    {getFileIcon(resource.file_type)}
                  </ResourceIcon>
                  <ResourceInfo>
                    <ResourceTitle>{resource.title}</ResourceTitle>
                    <ResourceDescription>{resource.description}</ResourceDescription>
                    <ResourceMeta>
                      <ResourceType>{resource.file_type.toUpperCase()}</ResourceType>
                      <ResourceSize>{formatFileSize(resource.file_size)}</ResourceSize>
                      <ResourceDownloads>{resource.downloads_count} downloads</ResourceDownloads>
                    </ResourceMeta>
                    <ResourceActions>
                      <DownloadButton
                        href={resource.download_url}
                        onClick={() => handleDownload(resource.id)}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Download
                      </DownloadButton>
                      <ViewDetailsLink to={`/resources/${resource.id}`}>
                        View Details
                      </ViewDetailsLink>
                    </ResourceActions>
                  </ResourceInfo>
                </ResourceCard>
              ))}
            </ResourcesGrid>
          )}
          
          {totalPages > 1 && (
            <Pagination>
              <PageButton
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </PageButton>
              
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                <PageButton
                  key={number}
                  active={number === currentPage}
                  onClick={() => paginate(number)}
                >
                  {number}
                </PageButton>
              ))}
              
              <PageButton
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </PageButton>
            </Pagination>
          )}
        </MainContent>
      </ResourcesLayout>
    </Container>
  );
};

export default ResourcesPage;
