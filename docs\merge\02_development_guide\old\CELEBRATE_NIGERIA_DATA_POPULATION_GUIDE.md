# Celebrate Nigeria Data Population Guide

This guide explains how to run the data population script for the Celebrate Nigeria feature to import initial data into the database.

## Prerequisites

Before running the data population script, ensure you have:

1. A running PostgreSQL database with the correct schema
2. The Go programming language installed (version 1.16 or higher)
3. Environment variables set up in a `.env` file with database connection details
4. Proper permissions to write to the database

## Database Configuration

The script uses the following environment variables for database connection:

```
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=greatnigeria
```

Make sure these are set in your `.env` file or in your environment.

## Directory Structure

The script will create the following directory structure for images:

```
web/static/images/celebrate/
├── people/
├── places/
└── events/
```

## Running the Script

### Option 1: Using the Convenience Script

The easiest way to run the data population is to use the provided convenience script:

```bash
# Make the script executable
chmod +x scripts/run_celebrate_nigeria_population.sh

# Run the script
./scripts/run_celebrate_nigeria_population.sh
```

This script will:
1. Create the necessary image directories
2. Build the Go data population script
3. Run the script to populate the database
4. Clean up temporary files

### Option 2: Manual Execution

If you prefer to run the steps manually:

1. Create the image directories:
   ```bash
   chmod +x scripts/create_celebrate_image_dirs.sh
   ./scripts/create_celebrate_image_dirs.sh
   ```

2. Build and run the data population script:
   ```bash
   go build -o populate_celebrate_nigeria scripts/populate_celebrate_nigeria.go
   ./populate_celebrate_nigeria
   ```

3. Clean up:
   ```bash
   rm -f populate_celebrate_nigeria
   ```

## What Data Gets Imported

The script will import the following data:

### People
- Chinua Achebe (Nigerian novelist, poet, and critic)
- Wole Soyinka (Nobel Prize-winning playwright and poet)
- Ngozi Okonjo-Iweala (Economist and WTO Director-General)

### Places
- Zuma Rock (Impressive monolith near Abuja)
- Osun-Osogbo Sacred Grove (UNESCO World Heritage Site)
- Eko Atlantic City (Urban development project in Lagos)

### Events
- Eyo Festival (Lagos cultural tradition with masquerades)
- Nigeria Independence Day (Annual celebration on October 1)
- Lagos International Jazz Festival (Annual music festival)

Each entry includes:
- Basic information (title, description, etc.)
- Category associations
- Key facts
- Media items (images)
- Type-specific data (birth dates for people, coordinates for places, etc.)

## Verifying the Import

After running the script, you can verify the data was imported correctly by:

1. Checking the database tables:
   ```sql
   SELECT COUNT(*) FROM celebration_entries;
   SELECT COUNT(*) FROM person_entries;
   SELECT COUNT(*) FROM place_entries;
   SELECT COUNT(*) FROM event_entries;
   ```

2. Accessing the Celebrate Nigeria feature in your browser:
   - Main page: `http://localhost:5000/celebrate`
   - People: `http://localhost:5000/celebrate/person/chinua-achebe`
   - Places: `http://localhost:5000/celebrate/place/zuma-rock`
   - Events: `http://localhost:5000/celebrate/event/eyo-festival`

## Troubleshooting

### Common Issues

**Database Connection Errors**
- Verify your database is running
- Check the connection details in your `.env` file
- Ensure the database user has proper permissions

**Missing Tables**
- Make sure you've run the database migrations
- Check if the schema exists and is correctly set up

**Image Directory Issues**
- Verify you have write permissions to the web/static directory
- Check if the directories were created correctly

### Logs

The script outputs detailed logs to help diagnose issues:
- Successful operations are logged with "Successfully inserted..."
- Errors are logged with detailed error messages

## Adding Custom Data

To add your own custom data, you can modify the `scripts/populate_celebrate_nigeria.go` file:

1. Add new entries to the appropriate arrays:
   - `people` array for person entries
   - `places` array for place entries
   - `events` array for event entries

2. Follow the existing structure for each entry type

3. Run the script again to import your custom data

## Conclusion

After successfully running the data population script, the Celebrate Nigeria feature will have initial data that showcases various aspects of Nigerian culture, history, and achievements. This provides a foundation that can be expanded with more entries over time.

For any issues not covered in this guide, please refer to the technical documentation or contact the development team.
