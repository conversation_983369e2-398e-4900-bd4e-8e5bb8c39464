# Great Nigeria Platform - API Documentation (Part 2)

## Core Services

### Auth Service

The Auth Service handles user authentication, registration, and authorization.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/auth/register` | Register a new user | None |
| POST | `/auth/login` | Authenticate a user | None |
| POST | `/auth/refresh-token` | Refresh an access token | None |
| POST | `/auth/password/reset` | Request a password reset | None |
| POST | `/auth/logout` | Logout a user | Required |
| GET | `/auth/oauth/{provider}` | Authenticate with OAuth provider | None |
| POST | `/auth/2fa/enable` | Enable two-factor authentication | Required |
| POST | `/auth/2fa/validate` | Validate 2FA code | Required |

#### Example: Password Reset Request

```http
POST /api/auth/password/reset HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

Response:

```json
{
  "status": "success",
  "message": "Password reset instructions sent to your email"
}
```

### User Service

The User Service manages user profiles, relationships, and settings.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/users/{id}` | Get user details | Required |
| PATCH | `/users/{id}` | Update user details | Required |
| GET | `/users/{id}/profile` | Get user profile | Required |
| GET | `/users/{id}/friends` | Get user friends | Required |
| POST | `/users/{id}/friends/request` | Send friend request | Required |
| GET | `/users/{id}/followers` | Get user followers | Required |
| POST | `/users/{id}/follow` | Follow a user | Required |
| GET | `/users/{id}/features` | Get user feature toggles | Required |
| PATCH | `/users/{id}/features/{featureId}` | Update feature toggle | Required |

#### Example: Update User Profile

```http
PATCH /api/users/user-uuid HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "full_name": "Updated Name",
  "bio": "This is my updated bio",
  "profile_image": "https://example.com/image.jpg"
}
```

Response:

```json
{
  "status": "success",
  "message": "User updated successfully",
  "data": {
    "id": "user-uuid",
    "username": "username",
    "full_name": "Updated Name",
    "bio": "This is my updated bio",
    "profile_image": "https://example.com/image.jpg",
    "updated_at": "2025-04-23T14:30:00Z"
  }
}
```

### Content Service

The Content Service manages book content, chapters, sections, and user progress.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/books` | Get all books | Optional |
| GET | `/books/{id}` | Get book details | Optional |
| GET | `/books/{id}/chapters` | Get book chapters | Optional |
| GET | `/books/{id}/chapters/{chapterId}` | Get chapter details | Optional |
| GET | `/books/{id}/sections/{sectionId}` | Get section content | Optional |
| POST | `/books/{id}/progress` | Update reading progress | Required |
| POST | `/books/{id}/bookmarks` | Create bookmark | Required |
| POST | `/books/{id}/notes` | Create note | Required |

#### Example: Get Book Details

```http
GET /api/books/book-uuid HTTP/1.1
Host: api.greatnigeria.net
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Response:

```json
{
  "status": "success",
  "data": {
    "id": "book-uuid",
    "title": "Book Title",
    "description": "Book description",
    "author": "Author Name",
    "cover_image": "https://example.com/cover.jpg",
    "access_level": 1,
    "published_at": "2025-01-15T00:00:00Z",
    "chapters": [
      {
        "id": "chapter-uuid-1",
        "title": "Chapter 1",
        "order_number": 1
      },
      {
        "id": "chapter-uuid-2",
        "title": "Chapter 2",
        "order_number": 2
      }
    ]
  }
}
```

#### Example: Update Reading Progress

```http
POST /api/books/book-uuid/progress HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "chapter_id": "chapter-uuid",
  "section_id": "section-uuid",
  "position": 1250,
  "percent_complete": 45.5
}
```

Response:

```json
{
  "status": "success",
  "message": "Progress updated successfully",
  "data": {
    "book_id": "book-uuid",
    "chapter_id": "chapter-uuid",
    "section_id": "section-uuid",
    "position": 1250,
    "percent_complete": 45.5,
    "last_updated": "2025-04-23T14:35:00Z"
  }
}
```

### Social Service

The Social Service manages social interactions, posts, and content sharing.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/feed` | Get user feed | Required |
| POST | `/posts` | Create a post | Required |
| GET | `/posts/{id}` | Get post details | Required |
| PATCH | `/posts/{id}` | Update a post | Required |
| DELETE | `/posts/{id}` | Delete a post | Required |
| POST | `/posts/{id}/comments` | Comment on a post | Required |
| POST | `/posts/{id}/reactions` | React to a post | Required |
| GET | `/groups` | Get user groups | Required |
| POST | `/groups` | Create a group | Required |
| GET | `/groups/{id}/posts` | Get group posts | Required |

#### Example: Create a Post

```http
POST /api/posts HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "content": "This is my new post about the Great Nigeria platform!",
  "media_urls": ["https://example.com/image1.jpg"],
  "feeling": "excited",
  "privacy": "public"
}
```

Response:

```json
{
  "status": "success",
  "message": "Post created successfully",
  "data": {
    "id": "post-uuid",
    "user_id": "user-uuid",
    "content": "This is my new post about the Great Nigeria platform!",
    "media_urls": ["https://example.com/image1.jpg"],
    "feeling": "excited",
    "privacy": "public",
    "created_at": "2025-04-23T14:40:00Z"
  }
}
```
