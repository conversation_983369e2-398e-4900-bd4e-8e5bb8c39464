import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import skillsService, {
  Skill,
  UserSkill,
  SkillEndorsement,
  SkillAssessment,
  SkillAssessmentAttempt,
  SkillNeed,
  SkillMatch,
  SkillCategory,
} from '../../services/skillsService';

interface SkillsState {
  skills: {
    items: Skill[];
    loading: boolean;
    error: string | null;
  };
  userSkills: {
    items: UserSkill[];
    loading: boolean;
    error: string | null;
  };
  currentSkill: {
    data: Skill | null;
    loading: boolean;
    error: string | null;
  };
  endorsements: {
    items: SkillEndorsement[];
    loading: boolean;
    error: string | null;
  };
  assessments: {
    items: SkillAssessment[];
    loading: boolean;
    error: string | null;
  };
  currentAssessment: {
    data: SkillAssessment | null;
    loading: boolean;
    error: string | null;
  };
  assessmentAttempts: {
    items: SkillAssessmentAttempt[];
    loading: boolean;
    error: string | null;
  };
  currentAttempt: {
    data: SkillAssessmentAttempt | null;
    loading: boolean;
    error: string | null;
  };
  skillNeeds: {
    items: SkillNeed[];
    loading: boolean;
    error: string | null;
  };
  currentNeed: {
    data: SkillNeed | null;
    loading: boolean;
    error: string | null;
  };
  skillMatches: {
    items: SkillMatch[];
    loading: boolean;
    error: string | null;
  };
  categories: {
    items: SkillCategory[];
    loading: boolean;
    error: string | null;
  };
}

const initialState: SkillsState = {
  skills: {
    items: [],
    loading: false,
    error: null,
  },
  userSkills: {
    items: [],
    loading: false,
    error: null,
  },
  currentSkill: {
    data: null,
    loading: false,
    error: null,
  },
  endorsements: {
    items: [],
    loading: false,
    error: null,
  },
  assessments: {
    items: [],
    loading: false,
    error: null,
  },
  currentAssessment: {
    data: null,
    loading: false,
    error: null,
  },
  assessmentAttempts: {
    items: [],
    loading: false,
    error: null,
  },
  currentAttempt: {
    data: null,
    loading: false,
    error: null,
  },
  skillNeeds: {
    items: [],
    loading: false,
    error: null,
  },
  currentNeed: {
    data: null,
    loading: false,
    error: null,
  },
  skillMatches: {
    items: [],
    loading: false,
    error: null,
  },
  categories: {
    items: [],
    loading: false,
    error: null,
  },
};

// Skills thunks
export const fetchSkills = createAsyncThunk(
  'skills/fetchSkills',
  async (params?: { category?: string; search?: string }, { rejectWithValue }) => {
    try {
      return await skillsService.getSkills(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skills');
    }
  }
);

export const fetchSkillById = createAsyncThunk(
  'skills/fetchSkillById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skill');
    }
  }
);

export const createSkill = createAsyncThunk(
  'skills/createSkill',
  async (skill: Partial<Skill>, { rejectWithValue }) => {
    try {
      return await skillsService.createSkill(skill);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create skill');
    }
  }
);

export const updateSkill = createAsyncThunk(
  'skills/updateSkill',
  async ({ id, skill }: { id: number; skill: Partial<Skill> }, { rejectWithValue }) => {
    try {
      return await skillsService.updateSkill(id, skill);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update skill');
    }
  }
);

export const deleteSkill = createAsyncThunk(
  'skills/deleteSkill',
  async (id: number, { rejectWithValue }) => {
    try {
      await skillsService.deleteSkill(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete skill');
    }
  }
);

// User skills thunks
export const fetchUserSkills = createAsyncThunk(
  'skills/fetchUserSkills',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getUserSkills(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user skills');
    }
  }
);

export const addUserSkill = createAsyncThunk(
  'skills/addUserSkill',
  async (
    { userId, userSkill }: { userId: number; userSkill: Partial<UserSkill> },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.addUserSkill(userId, userSkill);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add user skill');
    }
  }
);

export const updateUserSkill = createAsyncThunk(
  'skills/updateUserSkill',
  async (
    {
      userId,
      skillId,
      userSkill,
    }: { userId: number; skillId: number; userSkill: Partial<UserSkill> },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.updateUserSkill(userId, skillId, userSkill);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update user skill');
    }
  }
);

export const deleteUserSkill = createAsyncThunk(
  'skills/deleteUserSkill',
  async ({ userId, skillId }: { userId: number; skillId: number }, { rejectWithValue }) => {
    try {
      await skillsService.deleteUserSkill(userId, skillId);
      return skillId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete user skill');
    }
  }
);

// Endorsements thunks
export const fetchSkillEndorsements = createAsyncThunk(
  'skills/fetchSkillEndorsements',
  async (userSkillId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillEndorsements(userSkillId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch endorsements');
    }
  }
);

export const endorseSkill = createAsyncThunk(
  'skills/endorseSkill',
  async (
    {
      userSkillId,
      endorserId,
      comment,
    }: { userSkillId: number; endorserId: number; comment?: string },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.endorseSkill(userSkillId, endorserId, comment);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to endorse skill');
    }
  }
);

// Skill assessments thunks
export const fetchSkillAssessments = createAsyncThunk(
  'skills/fetchSkillAssessments',
  async (skillId?: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillAssessments(skillId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch assessments');
    }
  }
);

export const fetchSkillAssessmentById = createAsyncThunk(
  'skills/fetchSkillAssessmentById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillAssessmentById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch assessment');
    }
  }
);

export const createSkillAssessment = createAsyncThunk(
  'skills/createSkillAssessment',
  async (assessment: Partial<SkillAssessment>, { rejectWithValue }) => {
    try {
      return await skillsService.createSkillAssessment(assessment);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create assessment');
    }
  }
);

export const updateSkillAssessment = createAsyncThunk(
  'skills/updateSkillAssessment',
  async (
    { id, assessment }: { id: number; assessment: Partial<SkillAssessment> },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.updateSkillAssessment(id, assessment);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update assessment');
    }
  }
);

export const deleteSkillAssessment = createAsyncThunk(
  'skills/deleteSkillAssessment',
  async (id: number, { rejectWithValue }) => {
    try {
      await skillsService.deleteSkillAssessment(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete assessment');
    }
  }
);

// Assessment attempts thunks
export const startAssessmentAttempt = createAsyncThunk(
  'skills/startAssessmentAttempt',
  async (
    { assessmentId, userId }: { assessmentId: number; userId: number },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.startAssessmentAttempt(assessmentId, userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to start assessment attempt');
    }
  }
);

export const submitAssessmentAttempt = createAsyncThunk(
  'skills/submitAssessmentAttempt',
  async (
    { attemptId, answers }: { attemptId: number; answers: any[] },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.submitAssessmentAttempt(attemptId, answers);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit assessment attempt');
    }
  }
);

export const fetchUserAssessmentAttempts = createAsyncThunk(
  'skills/fetchUserAssessmentAttempts',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getUserAssessmentAttempts(userId);
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch assessment attempts'
      );
    }
  }
);

// Skill needs thunks
export const fetchSkillNeeds = createAsyncThunk(
  'skills/fetchSkillNeeds',
  async (params?: { status?: string; location?: string }, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillNeeds(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skill needs');
    }
  }
);

export const fetchSkillNeedById = createAsyncThunk(
  'skills/fetchSkillNeedById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillNeedById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skill need');
    }
  }
);

export const fetchUserSkillNeeds = createAsyncThunk(
  'skills/fetchUserSkillNeeds',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getUserSkillNeeds(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user skill needs');
    }
  }
);

export const createSkillNeed = createAsyncThunk(
  'skills/createSkillNeed',
  async (need: Partial<SkillNeed>, { rejectWithValue }) => {
    try {
      return await skillsService.createSkillNeed(need);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create skill need');
    }
  }
);

export const updateSkillNeed = createAsyncThunk(
  'skills/updateSkillNeed',
  async ({ id, need }: { id: number; need: Partial<SkillNeed> }, { rejectWithValue }) => {
    try {
      return await skillsService.updateSkillNeed(id, need);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update skill need');
    }
  }
);

export const deleteSkillNeed = createAsyncThunk(
  'skills/deleteSkillNeed',
  async (id: number, { rejectWithValue }) => {
    try {
      await skillsService.deleteSkillNeed(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete skill need');
    }
  }
);

// Skill matching thunks
export const fetchSkillMatches = createAsyncThunk(
  'skills/fetchSkillMatches',
  async (needId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillMatches(needId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skill matches');
    }
  }
);

export const fetchUserSkillMatches = createAsyncThunk(
  'skills/fetchUserSkillMatches',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await skillsService.getUserSkillMatches(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user skill matches');
    }
  }
);

export const updateMatchStatus = createAsyncThunk(
  'skills/updateMatchStatus',
  async (
    { matchId, status }: { matchId: number; status: 'accepted' | 'rejected' | 'completed' },
    { rejectWithValue }
  ) => {
    try {
      return await skillsService.updateMatchStatus(matchId, status);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update match status');
    }
  }
);

export const findMatches = createAsyncThunk(
  'skills/findMatches',
  async (needId: number, { rejectWithValue }) => {
    try {
      return await skillsService.findMatches(needId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to find matches');
    }
  }
);

// Skill categories thunks
export const fetchSkillCategories = createAsyncThunk(
  'skills/fetchSkillCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await skillsService.getSkillCategories();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch skill categories');
    }
  }
);

const skillsSlice = createSlice({
  name: 'skills',
  initialState,
  reducers: {
    resetCurrentSkill: (state) => {
      state.currentSkill.data = null;
      state.currentSkill.error = null;
    },
    resetCurrentAssessment: (state) => {
      state.currentAssessment.data = null;
      state.currentAssessment.error = null;
    },
    resetCurrentAttempt: (state) => {
      state.currentAttempt.data = null;
      state.currentAttempt.error = null;
    },
    resetCurrentNeed: (state) => {
      state.currentNeed.data = null;
      state.currentNeed.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch skills
    builder.addCase(fetchSkills.pending, (state) => {
      state.skills.loading = true;
      state.skills.error = null;
    });
    builder.addCase(fetchSkills.fulfilled, (state, action) => {
      state.skills.loading = false;
      state.skills.items = action.payload;
    });
    builder.addCase(fetchSkills.rejected, (state, action) => {
      state.skills.loading = false;
      state.skills.error = action.payload as string;
    });

    // Fetch skill by ID
    builder.addCase(fetchSkillById.pending, (state) => {
      state.currentSkill.loading = true;
      state.currentSkill.error = null;
    });
    builder.addCase(fetchSkillById.fulfilled, (state, action) => {
      state.currentSkill.loading = false;
      state.currentSkill.data = action.payload;
    });
    builder.addCase(fetchSkillById.rejected, (state, action) => {
      state.currentSkill.loading = false;
      state.currentSkill.error = action.payload as string;
    });

    // Create skill
    builder.addCase(createSkill.fulfilled, (state, action) => {
      state.skills.items.push(action.payload);
      state.currentSkill.data = action.payload;
    });

    // Update skill
    builder.addCase(updateSkill.fulfilled, (state, action) => {
      const updatedSkill = action.payload;
      const index = state.skills.items.findIndex((skill) => skill.id === updatedSkill.id);
      if (index !== -1) {
        state.skills.items[index] = updatedSkill;
      }
      state.currentSkill.data = updatedSkill;
    });

    // Delete skill
    builder.addCase(deleteSkill.fulfilled, (state, action) => {
      const skillId = action.payload;
      state.skills.items = state.skills.items.filter((skill) => skill.id !== skillId);
      if (state.currentSkill.data?.id === skillId) {
        state.currentSkill.data = null;
      }
    });

    // Fetch user skills
    builder.addCase(fetchUserSkills.pending, (state) => {
      state.userSkills.loading = true;
      state.userSkills.error = null;
    });
    builder.addCase(fetchUserSkills.fulfilled, (state, action) => {
      state.userSkills.loading = false;
      state.userSkills.items = action.payload;
    });
    builder.addCase(fetchUserSkills.rejected, (state, action) => {
      state.userSkills.loading = false;
      state.userSkills.error = action.payload as string;
    });

    // Add user skill
    builder.addCase(addUserSkill.fulfilled, (state, action) => {
      state.userSkills.items.push(action.payload);
    });

    // Update user skill
    builder.addCase(updateUserSkill.fulfilled, (state, action) => {
      const updatedUserSkill = action.payload;
      const index = state.userSkills.items.findIndex(
        (userSkill) => userSkill.id === updatedUserSkill.id
      );
      if (index !== -1) {
        state.userSkills.items[index] = updatedUserSkill;
      }
    });

    // Delete user skill
    builder.addCase(deleteUserSkill.fulfilled, (state, action) => {
      const skillId = action.payload;
      state.userSkills.items = state.userSkills.items.filter(
        (userSkill) => userSkill.skillId !== skillId
      );
    });

    // Fetch skill endorsements
    builder.addCase(fetchSkillEndorsements.pending, (state) => {
      state.endorsements.loading = true;
      state.endorsements.error = null;
    });
    builder.addCase(fetchSkillEndorsements.fulfilled, (state, action) => {
      state.endorsements.loading = false;
      state.endorsements.items = action.payload;
    });
    builder.addCase(fetchSkillEndorsements.rejected, (state, action) => {
      state.endorsements.loading = false;
      state.endorsements.error = action.payload as string;
    });

    // Endorse skill
    builder.addCase(endorseSkill.fulfilled, (state, action) => {
      state.endorsements.items.push(action.payload);
      // Update the endorsement count in the user skill
      const userSkill = state.userSkills.items.find(
        (us) => us.id === action.payload.userSkillId
      );
      if (userSkill) {
        userSkill.endorsements += 1;
      }
    });

    // Fetch skill assessments
    builder.addCase(fetchSkillAssessments.pending, (state) => {
      state.assessments.loading = true;
      state.assessments.error = null;
    });
    builder.addCase(fetchSkillAssessments.fulfilled, (state, action) => {
      state.assessments.loading = false;
      state.assessments.items = action.payload;
    });
    builder.addCase(fetchSkillAssessments.rejected, (state, action) => {
      state.assessments.loading = false;
      state.assessments.error = action.payload as string;
    });

    // Fetch skill assessment by ID
    builder.addCase(fetchSkillAssessmentById.pending, (state) => {
      state.currentAssessment.loading = true;
      state.currentAssessment.error = null;
    });
    builder.addCase(fetchSkillAssessmentById.fulfilled, (state, action) => {
      state.currentAssessment.loading = false;
      state.currentAssessment.data = action.payload;
    });
    builder.addCase(fetchSkillAssessmentById.rejected, (state, action) => {
      state.currentAssessment.loading = false;
      state.currentAssessment.error = action.payload as string;
    });

    // Create skill assessment
    builder.addCase(createSkillAssessment.fulfilled, (state, action) => {
      state.assessments.items.push(action.payload);
      state.currentAssessment.data = action.payload;
    });

    // Update skill assessment
    builder.addCase(updateSkillAssessment.fulfilled, (state, action) => {
      const updatedAssessment = action.payload;
      const index = state.assessments.items.findIndex(
        (assessment) => assessment.id === updatedAssessment.id
      );
      if (index !== -1) {
        state.assessments.items[index] = updatedAssessment;
      }
      state.currentAssessment.data = updatedAssessment;
    });

    // Delete skill assessment
    builder.addCase(deleteSkillAssessment.fulfilled, (state, action) => {
      const assessmentId = action.payload;
      state.assessments.items = state.assessments.items.filter(
        (assessment) => assessment.id !== assessmentId
      );
      if (state.currentAssessment.data?.id === assessmentId) {
        state.currentAssessment.data = null;
      }
    });

    // Start assessment attempt
    builder.addCase(startAssessmentAttempt.pending, (state) => {
      state.currentAttempt.loading = true;
      state.currentAttempt.error = null;
    });
    builder.addCase(startAssessmentAttempt.fulfilled, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.data = action.payload;
    });
    builder.addCase(startAssessmentAttempt.rejected, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.error = action.payload as string;
    });

    // Submit assessment attempt
    builder.addCase(submitAssessmentAttempt.pending, (state) => {
      state.currentAttempt.loading = true;
      state.currentAttempt.error = null;
    });
    builder.addCase(submitAssessmentAttempt.fulfilled, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.data = action.payload;
      // Add to assessment attempts list if not already there
      const index = state.assessmentAttempts.items.findIndex(
        (attempt) => attempt.id === action.payload.id
      );
      if (index === -1) {
        state.assessmentAttempts.items.push(action.payload);
      } else {
        state.assessmentAttempts.items[index] = action.payload;
      }
    });
    builder.addCase(submitAssessmentAttempt.rejected, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.error = action.payload as string;
    });

    // Fetch user assessment attempts
    builder.addCase(fetchUserAssessmentAttempts.pending, (state) => {
      state.assessmentAttempts.loading = true;
      state.assessmentAttempts.error = null;
    });
    builder.addCase(fetchUserAssessmentAttempts.fulfilled, (state, action) => {
      state.assessmentAttempts.loading = false;
      state.assessmentAttempts.items = action.payload;
    });
    builder.addCase(fetchUserAssessmentAttempts.rejected, (state, action) => {
      state.assessmentAttempts.loading = false;
      state.assessmentAttempts.error = action.payload as string;
    });

    // Fetch skill needs
    builder.addCase(fetchSkillNeeds.pending, (state) => {
      state.skillNeeds.loading = true;
      state.skillNeeds.error = null;
    });
    builder.addCase(fetchSkillNeeds.fulfilled, (state, action) => {
      state.skillNeeds.loading = false;
      state.skillNeeds.items = action.payload;
    });
    builder.addCase(fetchSkillNeeds.rejected, (state, action) => {
      state.skillNeeds.loading = false;
      state.skillNeeds.error = action.payload as string;
    });

    // Fetch skill need by ID
    builder.addCase(fetchSkillNeedById.pending, (state) => {
      state.currentNeed.loading = true;
      state.currentNeed.error = null;
    });
    builder.addCase(fetchSkillNeedById.fulfilled, (state, action) => {
      state.currentNeed.loading = false;
      state.currentNeed.data = action.payload;
    });
    builder.addCase(fetchSkillNeedById.rejected, (state, action) => {
      state.currentNeed.loading = false;
      state.currentNeed.error = action.payload as string;
    });

    // Fetch user skill needs
    builder.addCase(fetchUserSkillNeeds.fulfilled, (state, action) => {
      state.skillNeeds.loading = false;
      state.skillNeeds.items = action.payload;
    });

    // Create skill need
    builder.addCase(createSkillNeed.fulfilled, (state, action) => {
      state.skillNeeds.items.push(action.payload);
      state.currentNeed.data = action.payload;
    });

    // Update skill need
    builder.addCase(updateSkillNeed.fulfilled, (state, action) => {
      const updatedNeed = action.payload;
      const index = state.skillNeeds.items.findIndex((need) => need.id === updatedNeed.id);
      if (index !== -1) {
        state.skillNeeds.items[index] = updatedNeed;
      }
      state.currentNeed.data = updatedNeed;
    });

    // Delete skill need
    builder.addCase(deleteSkillNeed.fulfilled, (state, action) => {
      const needId = action.payload;
      state.skillNeeds.items = state.skillNeeds.items.filter((need) => need.id !== needId);
      if (state.currentNeed.data?.id === needId) {
        state.currentNeed.data = null;
      }
    });

    // Fetch skill matches
    builder.addCase(fetchSkillMatches.pending, (state) => {
      state.skillMatches.loading = true;
      state.skillMatches.error = null;
    });
    builder.addCase(fetchSkillMatches.fulfilled, (state, action) => {
      state.skillMatches.loading = false;
      state.skillMatches.items = action.payload;
    });
    builder.addCase(fetchSkillMatches.rejected, (state, action) => {
      state.skillMatches.loading = false;
      state.skillMatches.error = action.payload as string;
    });

    // Fetch user skill matches
    builder.addCase(fetchUserSkillMatches.pending, (state) => {
      state.skillMatches.loading = true;
      state.skillMatches.error = null;
    });
    builder.addCase(fetchUserSkillMatches.fulfilled, (state, action) => {
      state.skillMatches.loading = false;
      state.skillMatches.items = action.payload;
    });
    builder.addCase(fetchUserSkillMatches.rejected, (state, action) => {
      state.skillMatches.loading = false;
      state.skillMatches.error = action.payload as string;
    });

    // Update match status
    builder.addCase(updateMatchStatus.fulfilled, (state, action) => {
      const updatedMatch = action.payload;
      const index = state.skillMatches.items.findIndex((match) => match.id === updatedMatch.id);
      if (index !== -1) {
        state.skillMatches.items[index] = updatedMatch;
      }
    });

    // Find matches
    builder.addCase(findMatches.fulfilled, (state, action) => {
      state.skillMatches.items = action.payload;
    });

    // Fetch skill categories
    builder.addCase(fetchSkillCategories.pending, (state) => {
      state.categories.loading = true;
      state.categories.error = null;
    });
    builder.addCase(fetchSkillCategories.fulfilled, (state, action) => {
      state.categories.loading = false;
      state.categories.items = action.payload;
    });
    builder.addCase(fetchSkillCategories.rejected, (state, action) => {
      state.categories.loading = false;
      state.categories.error = action.payload as string;
    });
  },
});

export const {
  resetCurrentSkill,
  resetCurrentAssessment,
  resetCurrentAttempt,
  resetCurrentNeed,
} = skillsSlice.actions;

export default skillsSlice.reducer;
