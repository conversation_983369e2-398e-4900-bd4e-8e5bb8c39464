import React from 'react';
import { Box, Container, Typography, Paper } from '@mui/material';
import LearningStyleAssessment from '../components/personalization/LearningStyleAssessment';

const LearningStyleAssessmentPage: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Box py={4}>
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom align="center">
            Discover Your Learning Style
          </Typography>
          <Typography variant="body1" paragraph align="center">
            Understanding how you learn best is the first step to a personalized learning experience.
            Complete this assessment to discover your unique learning style and get recommendations
            tailored to how you learn.
          </Typography>
        </Paper>
        
        <LearningStyleAssessment />
      </Box>
    </Container>
  );
};

export default LearningStyleAssessmentPage;
