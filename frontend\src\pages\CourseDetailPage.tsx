import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Rating,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
  Paper,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon,
  School as SchoolIcon,
  Person as PersonIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchCourseByID,
  fetchModulesByCourseID,
  fetchEnrollment,
  createEnrollment,
  fetchReviewsByCourseID,
} from '../store/slices/coursesSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`course-tabpanel-${index}`}
      aria-labelledby={`course-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `course-tab-${index}`,
    'aria-controls': `course-tabpanel-${index}`,
  };
}

const CourseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: course, loading: courseLoading, error: courseError } = useSelector(
    (state: RootState) => state.courses.currentCourse
  );
  const { items: modules, loading: modulesLoading } = useSelector(
    (state: RootState) => state.courses.modules
  );
  const { data: enrollment, loading: enrollmentLoading } = useSelector(
    (state: RootState) => state.courses.enrollment
  );
  const { items: reviews, loading: reviewsLoading } = useSelector(
    (state: RootState) => state.courses.reviews
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [tabValue, setTabValue] = useState(0);
  
  useEffect(() => {
    if (id) {
      dispatch(fetchCourseByID(parseInt(id)));
      dispatch(fetchModulesByCourseID(parseInt(id)));
      dispatch(fetchReviewsByCourseID(parseInt(id)));
      
      if (user) {
        dispatch(fetchEnrollment({ userID: user.id, courseID: parseInt(id) }));
      }
    }
  }, [dispatch, id, user]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleEnroll = () => {
    if (!user) {
      navigate('/login', { state: { from: `/courses/${id}` } });
      return;
    }
    
    if (course) {
      dispatch(
        createEnrollment({
          userID: user.id,
          courseID: course.id,
          enrollmentDate: new Date().toISOString(),
          isCompleted: false,
          progress: 0,
        })
      );
    }
  };
  
  const handleStartLearning = () => {
    if (modules.length > 0 && modules[0].lessons && modules[0].lessons.length > 0) {
      navigate(`/courses/${id}/lessons/${modules[0].lessons[0].id}`);
    } else {
      navigate(`/courses/${id}/content`);
    }
  };
  
  if (courseLoading || modulesLoading || enrollmentLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (courseError || !course) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {courseError || 'Course not found. Please try again later.'}
        </Alert>
      </Container>
    );
  }
  
  const isEnrolled = enrollment !== null;
  const totalLessons = modules.reduce(
    (total, module) => total + (module.lessons ? module.lessons.length : 0),
    0
  );
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Typography variant="h3" component="h1" gutterBottom>
            {course.title}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Rating value={course.rating} precision={0.5} readOnly />
            <Typography variant="body2" sx={{ ml: 1 }}>
              ({course.rating.toFixed(1)}) • {course.enrollmentCount} students
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar sx={{ mr: 1 }}>
              <PersonIcon />
            </Avatar>
            <Typography variant="body1">
              Created by <strong>{course.instructorName}</strong>
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
            <Chip
              icon={<AccessTimeIcon />}
              label={`${course.duration} hours`}
              variant="outlined"
            />
            <Chip
              icon={<SchoolIcon />}
              label={course.level}
              variant="outlined"
              color={
                course.level === 'beginner'
                  ? 'success'
                  : course.level === 'intermediate'
                  ? 'primary'
                  : 'secondary'
              }
            />
            {course.tags.map((tag) => (
              <Chip key={tag} label={tag} variant="outlined" />
            ))}
          </Box>
          
          <Box sx={{ mb: 4 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="course tabs">
              <Tab label="Overview" {...a11yProps(0)} />
              <Tab label="Curriculum" {...a11yProps(1)} />
              <Tab label="Reviews" {...a11yProps(2)} />
            </Tabs>
            
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h5" gutterBottom>
                About this course
              </Typography>
              <Typography variant="body1" paragraph>
                {course.description}
              </Typography>
              
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                What you'll learn
              </Typography>
              <Grid container spacing={2}>
                {course.objectives.split('\n').map((objective, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <CheckCircleIcon color="primary" sx={{ mr: 1, mt: 0.3 }} fontSize="small" />
                      <Typography variant="body1">{objective}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
              
              {course.prerequisites && (
                <>
                  <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Prerequisites
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {course.prerequisites}
                  </Typography>
                </>
              )}
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h5" gutterBottom>
                Course Content
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {modules.length} modules • {totalLessons} lessons • {course.duration} hours total
              </Typography>
              
              {modules.map((module) => (
                <Accordion key={module.id} defaultExpanded={modules.length === 1}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">{module.title}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List disablePadding>
                      {module.lessons && module.lessons.map((lesson) => (
                        <ListItem
                          key={lesson.id}
                          sx={{
                            borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                            py: 1,
                          }}
                        >
                          <ListItemIcon>
                            {lesson.contentType === 'video' ? (
                              <PlayArrowIcon />
                            ) : lesson.contentType === 'quiz' ? (
                              <QuizIcon />
                            ) : (
                              <AssignmentIcon />
                            )}
                          </ListItemIcon>
                          <ListItemText
                            primary={lesson.title}
                            secondary={`${lesson.duration} min`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </TabPanel>
            
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h5" gutterBottom>
                Student Reviews
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mr: 4 }}>
                  <Typography variant="h3">{course.rating.toFixed(1)}</Typography>
                  <Rating value={course.rating} precision={0.5} readOnly size="large" />
                  <Typography variant="body2" color="text.secondary">
                    {reviews.length} reviews
                  </Typography>
                </Box>
                
                <Box sx={{ flexGrow: 1 }}>
                  {[5, 4, 3, 2, 1].map((star) => {
                    const count = reviews.filter((review) => Math.round(review.rating) === star).length;
                    const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0;
                    
                    return (
                      <Box key={star} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: 40 }}>
                          <Typography variant="body2">{star}</Typography>
                          <StarIcon fontSize="small" sx={{ ml: 0.5 }} />
                        </Box>
                        <Box
                          sx={{
                            flexGrow: 1,
                            height: 8,
                            bgcolor: 'grey.300',
                            borderRadius: 1,
                            mx: 1,
                          }}
                        >
                          <Box
                            sx={{
                              height: '100%',
                              bgcolor: 'primary.main',
                              borderRadius: 1,
                              width: `${percentage}%`,
                            }}
                          />
                        </Box>
                        <Typography variant="body2" sx={{ width: 40 }}>
                          {percentage.toFixed(0)}%
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Box>
              
              <Divider sx={{ mb: 3 }} />
              
              {reviewsLoading ? (
                <CircularProgress />
              ) : reviews.length === 0 ? (
                <Typography variant="body1">No reviews yet.</Typography>
              ) : (
                reviews.map((review) => (
                  <Paper key={review.id} sx={{ p: 2, mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Avatar sx={{ mr: 1 }}>
                        <PersonIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1">User #{review.userID}</Typography>
                        <Rating value={review.rating} precision={0.5} readOnly size="small" />
                      </Box>
                    </Box>
                    <Typography variant="body1">{review.comment}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </Typography>
                  </Paper>
                ))
              )}
            </TabPanel>
          </Box>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 20 }}>
            <CardContent>
              {course.thumbnailURL && (
                <Box
                  component="img"
                  src={course.thumbnailURL}
                  alt={course.title}
                  sx={{
                    width: '100%',
                    height: 200,
                    objectFit: 'cover',
                    borderRadius: 1,
                    mb: 2,
                  }}
                />
              )}
              
              <Typography variant="h4" gutterBottom>
                {course.isFree ? 'Free' : `₦${course.price.toLocaleString()}`}
              </Typography>
              
              {isEnrolled ? (
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  size="large"
                  onClick={handleStartLearning}
                  sx={{ mb: 2 }}
                >
                  Continue Learning
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  size="large"
                  onClick={handleEnroll}
                  sx={{ mb: 2 }}
                >
                  {course.isFree ? 'Enroll Now - Free' : `Enroll Now - ₦${course.price.toLocaleString()}`}
                </Button>
              )}
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {isEnrolled
                  ? `You enrolled on ${new Date(enrollment!.enrollmentDate).toLocaleDateString()}`
                  : 'Full lifetime access'}
              </Typography>
              
              <List disablePadding>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <AccessTimeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${course.duration} hours of content`}
                    secondary="Learn at your own pace"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <SchoolIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${course.level} level`}
                    secondary={
                      course.level === 'beginner'
                        ? 'No prior knowledge required'
                        : course.level === 'intermediate'
                        ? 'Some knowledge required'
                        : 'Advanced knowledge required'
                    }
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <QuizIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Quizzes & Assignments"
                    secondary="Test your knowledge"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Certificate of Completion"
                    secondary="Upon finishing the course"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CourseDetailPage;
