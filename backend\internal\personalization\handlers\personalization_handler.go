package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/service"
	"github.com/sirupsen/logrus"
)

// PersonalizationHandler handles HTTP requests for personalization
type PersonalizationHandler struct {
	service service.PersonalizationService
	logger  *logrus.Logger
}

// NewPersonalizationHandler creates a new personalization handler
func NewPersonalizationHandler(service service.PersonalizationService, logger *logrus.Logger) *PersonalizationHandler {
	return &PersonalizationHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers the routes for the personalization handler
func (h *PersonalizationHandler) RegisterRoutes(router *gin.RouterGroup) {
	personalizationGroup := router.Group("/personalization")
	{
		// Learning Style routes
		personalizationGroup.GET("/learning-style", h.GetLearningStyle)
		personalizationGroup.POST("/learning-style", h.SaveLearningStyle)
		
		// Learning Preference routes
		personalizationGroup.GET("/preferences", h.GetLearningPreference)
		personalizationGroup.POST("/preferences", h.SaveLearningPreference)
		
		// Assessment routes
		personalizationGroup.GET("/assessment/questions", h.GetAssessmentQuestions)
		personalizationGroup.POST("/assessment/submit", h.SubmitAssessment)
		
		// Personalized Path routes
		personalizationGroup.GET("/paths", h.GetPersonalizedPaths)
		personalizationGroup.GET("/paths/:id", h.GetPersonalizedPathWithItems)
		personalizationGroup.POST("/paths", h.CreatePersonalizedPath)
		personalizationGroup.PUT("/paths/:id", h.UpdatePersonalizedPath)
		personalizationGroup.DELETE("/paths/:id", h.DeletePersonalizedPath)
		
		// Path Item routes
		personalizationGroup.POST("/paths/:pathId/items", h.AddPathItem)
		personalizationGroup.PUT("/paths/items/:id", h.UpdatePathItem)
		personalizationGroup.DELETE("/paths/items/:id", h.DeletePathItem)
		personalizationGroup.PUT("/paths/items/:id/complete", h.MarkPathItemComplete)
		
		// Recommendation routes
		personalizationGroup.POST("/recommendations", h.GetRecommendations)
		personalizationGroup.PUT("/recommendations/:id/status", h.UpdateRecommendationStatus)
		
		// User Performance routes
		personalizationGroup.GET("/performance", h.GetUserPerformance)
		personalizationGroup.PUT("/performance", h.UpdateUserPerformance)
		
		// Admin routes (should be protected with admin middleware)
		admin := personalizationGroup.Group("/admin")
		{
			// Learning Path Template routes
			admin.GET("/templates", h.GetLearningPathTemplates)
			admin.GET("/templates/:id", h.GetLearningPathTemplateWithItems)
			// Additional admin routes can be added here
		}
	}
}

// GetLearningStyle handles GET /personalization/learning-style
func (h *PersonalizationHandler) GetLearningStyle(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	style, err := h.service.GetLearningStyleByUserID(userID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get learning style")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get learning style"})
		return
	}
	
	if style == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Learning style not found"})
		return
	}
	
	c.JSON(http.StatusOK, style)
}

// SaveLearningStyle handles POST /personalization/learning-style
func (h *PersonalizationHandler) SaveLearningStyle(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var style models.LearningStyle
	if err := c.ShouldBindJSON(&style); err != nil {
		h.logger.WithError(err).Error("Invalid learning style data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid learning style data"})
		return
	}
	
	style.UserID = userID.(uint)
	
	if err := h.service.SaveLearningStyle(&style); err != nil {
		h.logger.WithError(err).Error("Failed to save learning style")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save learning style"})
		return
	}
	
	c.JSON(http.StatusOK, style)
}

// GetLearningPreference handles GET /personalization/preferences
func (h *PersonalizationHandler) GetLearningPreference(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	pref, err := h.service.GetLearningPreferenceByUserID(userID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get learning preference")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get learning preference"})
		return
	}
	
	if pref == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Learning preference not found"})
		return
	}
	
	c.JSON(http.StatusOK, pref)
}

// SaveLearningPreference handles POST /personalization/preferences
func (h *PersonalizationHandler) SaveLearningPreference(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var pref models.LearningPreference
	if err := c.ShouldBindJSON(&pref); err != nil {
		h.logger.WithError(err).Error("Invalid learning preference data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid learning preference data"})
		return
	}
	
	pref.UserID = userID.(uint)
	
	if err := h.service.SaveLearningPreference(&pref); err != nil {
		h.logger.WithError(err).Error("Failed to save learning preference")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save learning preference"})
		return
	}
	
	c.JSON(http.StatusOK, pref)
}

// GetAssessmentQuestions handles GET /personalization/assessment/questions
func (h *PersonalizationHandler) GetAssessmentQuestions(c *gin.Context) {
	questions, err := h.service.GetAssessmentQuestions()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get assessment questions")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get assessment questions"})
		return
	}
	
	c.JSON(http.StatusOK, questions)
}

// SubmitAssessment handles POST /personalization/assessment/submit
func (h *PersonalizationHandler) SubmitAssessment(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var responses []models.AssessmentResponse
	if err := c.ShouldBindJSON(&responses); err != nil {
		h.logger.WithError(err).Error("Invalid assessment response data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assessment response data"})
		return
	}
	
	// Set user ID for all responses
	for i := range responses {
		responses[i].UserID = userID.(uint)
	}
	
	result, err := h.service.SubmitAssessmentResponses(userID.(uint), responses)
	if err != nil {
		h.logger.WithError(err).Error("Failed to submit assessment")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to submit assessment"})
		return
	}
	
	c.JSON(http.StatusOK, result)
}

// GetPersonalizedPaths handles GET /personalization/paths
func (h *PersonalizationHandler) GetPersonalizedPaths(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	paths, err := h.service.GetPersonalizedPathsByUserID(userID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get personalized paths")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get personalized paths"})
		return
	}
	
	c.JSON(http.StatusOK, paths)
}

// GetPersonalizedPathWithItems handles GET /personalization/paths/:id
func (h *PersonalizationHandler) GetPersonalizedPathWithItems(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	pathIDStr := c.Param("id")
	pathID, err := strconv.ParseUint(pathIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid path ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path ID"})
		return
	}
	
	path, items, err := h.service.GetPersonalizedPathWithItems(uint(pathID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get personalized path"})
		return
	}
	
	// Check if path belongs to user
	if path.UserID != userID.(uint) {
		h.logger.Error("Unauthorized access to personalized path")
		c.JSON(http.StatusForbidden, gin.H{"error": "Unauthorized access to personalized path"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"path":  path,
		"items": items,
	})
}

// CreatePersonalizedPath handles POST /personalization/paths
func (h *PersonalizationHandler) CreatePersonalizedPath(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var request struct {
		Path  models.PersonalizedPath `json:"path"`
		Items []models.PathItem       `json:"items"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid personalized path data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid personalized path data"})
		return
	}
	
	request.Path.UserID = userID.(uint)
	
	if err := h.service.CreatePersonalizedPath(&request.Path, request.Items); err != nil {
		h.logger.WithError(err).Error("Failed to create personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create personalized path"})
		return
	}
	
	c.JSON(http.StatusCreated, request.Path)
}

// UpdatePersonalizedPath handles PUT /personalization/paths/:id
func (h *PersonalizationHandler) UpdatePersonalizedPath(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	pathIDStr := c.Param("id")
	pathID, err := strconv.ParseUint(pathIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid path ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path ID"})
		return
	}
	
	// Get existing path to check ownership
	existingPath, _, err := h.service.GetPersonalizedPathWithItems(uint(pathID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get personalized path"})
		return
	}
	
	// Check if path belongs to user
	if existingPath.UserID != userID.(uint) {
		h.logger.Error("Unauthorized access to personalized path")
		c.JSON(http.StatusForbidden, gin.H{"error": "Unauthorized access to personalized path"})
		return
	}
	
	var path models.PersonalizedPath
	if err := c.ShouldBindJSON(&path); err != nil {
		h.logger.WithError(err).Error("Invalid personalized path data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid personalized path data"})
		return
	}
	
	path.ID = uint(pathID)
	path.UserID = userID.(uint)
	
	if err := h.service.UpdatePersonalizedPath(&path); err != nil {
		h.logger.WithError(err).Error("Failed to update personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update personalized path"})
		return
	}
	
	c.JSON(http.StatusOK, path)
}

// DeletePersonalizedPath handles DELETE /personalization/paths/:id
func (h *PersonalizationHandler) DeletePersonalizedPath(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	pathIDStr := c.Param("id")
	pathID, err := strconv.ParseUint(pathIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid path ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path ID"})
		return
	}
	
	// Get existing path to check ownership
	existingPath, _, err := h.service.GetPersonalizedPathWithItems(uint(pathID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get personalized path"})
		return
	}
	
	// Check if path belongs to user
	if existingPath.UserID != userID.(uint) {
		h.logger.Error("Unauthorized access to personalized path")
		c.JSON(http.StatusForbidden, gin.H{"error": "Unauthorized access to personalized path"})
		return
	}
	
	if err := h.service.DeletePersonalizedPath(uint(pathID)); err != nil {
		h.logger.WithError(err).Error("Failed to delete personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete personalized path"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Personalized path deleted successfully"})
}

// AddPathItem handles POST /personalization/paths/:pathId/items
func (h *PersonalizationHandler) AddPathItem(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	pathIDStr := c.Param("pathId")
	pathID, err := strconv.ParseUint(pathIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid path ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path ID"})
		return
	}
	
	// Get existing path to check ownership
	existingPath, _, err := h.service.GetPersonalizedPathWithItems(uint(pathID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get personalized path")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get personalized path"})
		return
	}
	
	// Check if path belongs to user
	if existingPath.UserID != userID.(uint) {
		h.logger.Error("Unauthorized access to personalized path")
		c.JSON(http.StatusForbidden, gin.H{"error": "Unauthorized access to personalized path"})
		return
	}
	
	var item models.PathItem
	if err := c.ShouldBindJSON(&item); err != nil {
		h.logger.WithError(err).Error("Invalid path item data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path item data"})
		return
	}
	
	item.PathID = uint(pathID)
	
	if err := h.service.AddPathItem(&item); err != nil {
		h.logger.WithError(err).Error("Failed to add path item")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add path item"})
		return
	}
	
	c.JSON(http.StatusCreated, item)
}

// UpdatePathItem handles PUT /personalization/paths/items/:id
func (h *PersonalizationHandler) UpdatePathItem(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	itemIDStr := c.Param("id")
	itemID, err := strconv.ParseUint(itemIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid item ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}
	
	var item models.PathItem
	if err := c.ShouldBindJSON(&item); err != nil {
		h.logger.WithError(err).Error("Invalid path item data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path item data"})
		return
	}
	
	item.ID = uint(itemID)
	
	// TODO: Check if item belongs to user's path
	
	if err := h.service.UpdatePathItem(&item); err != nil {
		h.logger.WithError(err).Error("Failed to update path item")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update path item"})
		return
	}
	
	c.JSON(http.StatusOK, item)
}

// DeletePathItem handles DELETE /personalization/paths/items/:id
func (h *PersonalizationHandler) DeletePathItem(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	itemIDStr := c.Param("id")
	itemID, err := strconv.ParseUint(itemIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid item ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}
	
	// TODO: Check if item belongs to user's path
	
	if err := h.service.DeletePathItem(uint(itemID)); err != nil {
		h.logger.WithError(err).Error("Failed to delete path item")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete path item"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Path item deleted successfully"})
}

// MarkPathItemComplete handles PUT /personalization/paths/items/:id/complete
func (h *PersonalizationHandler) MarkPathItemComplete(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	itemIDStr := c.Param("id")
	itemID, err := strconv.ParseUint(itemIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid item ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}
	
	var request struct {
		Completed bool `json:"completed"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid request data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}
	
	// TODO: Check if item belongs to user's path
	
	if err := h.service.MarkPathItemComplete(uint(itemID), request.Completed); err != nil {
		h.logger.WithError(err).Error("Failed to mark path item as complete")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark path item as complete"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Path item updated successfully"})
}

// GetRecommendations handles POST /personalization/recommendations
func (h *PersonalizationHandler) GetRecommendations(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var request models.PersonalizationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid request data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}
	
	request.UserID = userID.(uint)
	
	response, err := h.service.GetRecommendationsForUser(request)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get recommendations")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recommendations"})
		return
	}
	
	c.JSON(http.StatusOK, response)
}

// UpdateRecommendationStatus handles PUT /personalization/recommendations/:id/status
func (h *PersonalizationHandler) UpdateRecommendationStatus(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	recIDStr := c.Param("id")
	recID, err := strconv.ParseUint(recIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid recommendation ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recommendation ID"})
		return
	}
	
	var request struct {
		Viewed   bool `json:"viewed"`
		Saved    bool `json:"saved"`
		Rejected bool `json:"rejected"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid request data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}
	
	// TODO: Check if recommendation belongs to user
	
	if err := h.service.UpdateRecommendationStatus(uint(recID), request.Viewed, request.Saved, request.Rejected); err != nil {
		h.logger.WithError(err).Error("Failed to update recommendation status")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recommendation status"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Recommendation status updated successfully"})
}

// GetUserPerformance handles GET /personalization/performance
func (h *PersonalizationHandler) GetUserPerformance(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	performance, err := h.service.GetUserPerformance(userID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user performance")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user performance"})
		return
	}
	
	if performance == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User performance not found"})
		return
	}
	
	c.JSON(http.StatusOK, performance)
}

// UpdateUserPerformance handles PUT /personalization/performance
func (h *PersonalizationHandler) UpdateUserPerformance(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	var performance models.UserPerformance
	if err := c.ShouldBindJSON(&performance); err != nil {
		h.logger.WithError(err).Error("Invalid performance data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid performance data"})
		return
	}
	
	performance.UserID = userID.(uint)
	
	if err := h.service.UpdateUserPerformance(&performance); err != nil {
		h.logger.WithError(err).Error("Failed to update user performance")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user performance"})
		return
	}
	
	c.JSON(http.StatusOK, performance)
}

// GetLearningPathTemplates handles GET /personalization/admin/templates
func (h *PersonalizationHandler) GetLearningPathTemplates(c *gin.Context) {
	templates, err := h.service.GetLearningPathTemplates()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get learning path templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get learning path templates"})
		return
	}
	
	c.JSON(http.StatusOK, templates)
}

// GetLearningPathTemplateWithItems handles GET /personalization/admin/templates/:id
func (h *PersonalizationHandler) GetLearningPathTemplateWithItems(c *gin.Context) {
	templateIDStr := c.Param("id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid template ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}
	
	template, items, err := h.service.GetLearningPathTemplateWithItems(uint(templateID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get learning path template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get learning path template"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"template": template,
		"items":    items,
	})
}
