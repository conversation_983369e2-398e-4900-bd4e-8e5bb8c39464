import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { RootState } from '../store';
import { fetchCategories, fetchTopicsByCategory, searchTopics } from '../features/forum/forumSlice';
import { ForumCategory } from '../types';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
  max-width: 800px;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const SearchBar = styled.div`
  display: flex;
  flex: 1;
  min-width: 200px;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #16213e;
  }
`;

const SearchButton = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0 1rem;
  cursor: pointer;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const NewTopicButton = styled(Link)`
  display: inline-block;
  background-color: #16213e;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const ForumLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  
  @media (min-width: 992px) {
    grid-template-columns: 250px 1fr;
  }
`;

const Sidebar = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
`;

const SidebarTitle = styled.h2`
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #16213e;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
`;

const CategoryList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const CategoryItem = styled.li<{ active: boolean }>`
  margin-bottom: 0.5rem;
  
  a {
    display: block;
    padding: 0.75rem;
    border-radius: 4px;
    text-decoration: none;
    color: ${(props) => (props.active ? 'white' : '#333')};
    background-color: ${(props) => (props.active ? '#16213e' : 'transparent')};
    transition: all 0.3s ease;
    
    &:hover {
      background-color: ${(props) => (props.active ? '#0f3460' : '#e9ecef')};
    }
    
    .count {
      float: right;
      background-color: ${(props) => (props.active ? 'white' : '#16213e')};
      color: ${(props) => (props.active ? '#16213e' : 'white')};
      border-radius: 10px;
      padding: 0.1rem 0.5rem;
      font-size: 0.8rem;
    }
  }
`;

const MainContent = styled.div``;

const TopicsTable = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TopicsHeader = styled.div`
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  padding: 1rem 1.5rem;
  background-color: #16213e;
  color: white;
  font-weight: bold;
  
  @media (max-width: 768px) {
    grid-template-columns: 3fr 1fr;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const TopicsList = styled.div``;

const TopicItem = styled.div`
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 3fr 1fr;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const TopicTitle = styled(Link)`
  font-weight: bold;
  color: #16213e;
  text-decoration: none;
  margin-bottom: 0.25rem;
  display: block;
  
  &:hover {
    text-decoration: underline;
  }
`;

const TopicMeta = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const TopicAuthor = styled.span`
  a {
    color: #16213e;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const TopicReplies = styled.div`
  text-align: center;
`;

const TopicViews = styled.div`
  text-align: center;
`;

const TopicLastReply = styled.div`
  font-size: 0.9rem;
  
  .date {
    color: #666;
  }
  
  a {
    color: #16213e;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #666;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  &:after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #16213e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  padding: 1rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
`;

const PageButton = styled.button<{ active?: boolean }>`
  padding: 0.5rem 1rem;
  border: 1px solid ${(props) => (props.active ? '#16213e' : '#ddd')};
  background-color: ${(props) => (props.active ? '#16213e' : 'white')};
  color: ${(props) => (props.active ? 'white' : '#333')};
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: ${(props) => (props.active ? '#0f3460' : '#f0f0f0')};
  }
  
  &:disabled {
    background-color: #f0f0f0;
    color: #999;
    cursor: not-allowed;
  }
`;

const ForumPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const categoryId = searchParams.get('category') || 'all';
  const searchQuery = searchParams.get('q') || '';
  
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [currentPage, setCurrentPage] = useState(1);
  const topicsPerPage = 10;
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { categories, topics, isLoading, error } = useSelector(
    (state: RootState) => state.forum
  );
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);
  
  useEffect(() => {
    if (searchQuery) {
      dispatch(searchTopics(searchQuery));
    } else if (categoryId && categoryId !== 'all') {
      dispatch(fetchTopicsByCategory(categoryId));
    } else if (categories.length > 0) {
      // If no category is selected, fetch topics from the first category
      dispatch(fetchTopicsByCategory(categories[0].id));
    }
  }, [dispatch, categoryId, searchQuery, categories]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setSearchParams({ q: searchTerm });
    } else {
      // If search is cleared, go back to category view
      setSearchParams({ category: categoryId !== 'all' ? categoryId : '' });
    }
    setCurrentPage(1);
  };
  
  const handleCategoryChange = (category: ForumCategory) => {
    setSearchParams({ category: category.id });
    setSearchTerm('');
    setCurrentPage(1);
  };
  
  // Pagination
  const indexOfLastTopic = currentPage * topicsPerPage;
  const indexOfFirstTopic = indexOfLastTopic - topicsPerPage;
  const currentTopics = topics.slice(indexOfFirstTopic, indexOfLastTopic);
  const totalPages = Math.ceil(topics.length / topicsPerPage);
  
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };
  
  // Get active category
  const activeCategory = categories.find((cat) => cat.id === categoryId);
  
  return (
    <Container>
      <Title>Community Forum</Title>
      <Subtitle>
        Connect with like-minded Nigerians, share ideas, and collaborate on initiatives to build a
        better Nigeria.
      </Subtitle>
      
      <ActionBar>
        <SearchBar>
          <form onSubmit={handleSearch} style={{ display: 'flex', flex: 1 }}>
            <SearchInput
              type="text"
              placeholder="Search topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <SearchButton type="submit">Search</SearchButton>
          </form>
        </SearchBar>
        
        {isAuthenticated && (
          <NewTopicButton to="/community/new-topic">New Topic</NewTopicButton>
        )}
      </ActionBar>
      
      <ForumLayout>
        <Sidebar>
          <SidebarTitle>Categories</SidebarTitle>
          <CategoryList>
            <CategoryItem active={categoryId === 'all'}>
              <Link to="/community?category=all">
                All Categories
                <span className="count">{topics.length}</span>
              </Link>
            </CategoryItem>
            {categories.map((category) => (
              <CategoryItem
                key={category.id}
                active={categoryId === category.id}
              >
                <Link
                  to={`/community?category=${category.id}`}
                  onClick={() => handleCategoryChange(category)}
                >
                  {category.name}
                  <span className="count">{category.topics_count}</span>
                </Link>
              </CategoryItem>
            ))}
          </CategoryList>
        </Sidebar>
        
        <MainContent>
          {searchQuery ? (
            <h2>Search Results for "{searchQuery}"</h2>
          ) : (
            <h2>{activeCategory ? activeCategory.name : 'All Categories'}</h2>
          )}
          
          {error && <ErrorMessage>{error}</ErrorMessage>}
          
          {isLoading ? (
            <LoadingSpinner />
          ) : currentTopics.length === 0 ? (
            <EmptyState>
              <p>No topics found.</p>
              {isAuthenticated ? (
                <p>
                  <NewTopicButton to="/community/new-topic">
                    Start a New Topic
                  </NewTopicButton>
                </p>
              ) : (
                <p>
                  <Link to="/login">Login</Link> or <Link to="/register">Register</Link> to start a
                  new topic.
                </p>
              )}
            </EmptyState>
          ) : (
            <TopicsTable>
              <TopicsHeader>
                <div>Topic</div>
                <div className="hide-mobile" style={{ textAlign: 'center' }}>
                  Replies
                </div>
                <div className="hide-mobile" style={{ textAlign: 'center' }}>
                  Views
                </div>
                <div>Last Reply</div>
              </TopicsHeader>
              
              <TopicsList>
                {currentTopics.map((topic) => (
                  <TopicItem key={topic.id}>
                    <div>
                      <TopicTitle to={`/community/topic/${topic.id}`}>
                        {topic.title}
                      </TopicTitle>
                      <TopicMeta>
                        Started by{' '}
                        <TopicAuthor>
                          <Link to={`/profile/${topic.author.id}`}>{topic.author.name}</Link>
                        </TopicAuthor>{' '}
                        on {formatDate(topic.created_at)}
                      </TopicMeta>
                    </div>
                    
                    <TopicReplies className="hide-mobile">
                      {topic.replies_count || 0}
                    </TopicReplies>
                    
                    <TopicViews className="hide-mobile">
                      {/* Placeholder for views, not in API */}
                      {Math.floor(Math.random() * 100) + 10}
                    </TopicViews>
                    
                    <TopicLastReply>
                      {topic.last_reply_at ? (
                        <>
                          <div className="date">{formatDate(topic.last_reply_at)}</div>
                          {/* Placeholder for last reply author, not in API */}
                          <div>
                            by <a href="#">User</a>
                          </div>
                        </>
                      ) : (
                        <div className="date">No replies yet</div>
                      )}
                    </TopicLastReply>
                  </TopicItem>
                ))}
              </TopicsList>
            </TopicsTable>
          )}
          
          {totalPages > 1 && (
            <Pagination>
              <PageButton
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </PageButton>
              
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                <PageButton
                  key={number}
                  active={number === currentPage}
                  onClick={() => paginate(number)}
                >
                  {number}
                </PageButton>
              ))}
              
              <PageButton
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </PageButton>
            </Pagination>
          )}
        </MainContent>
      </ForumLayout>
    </Container>
  );
};

export default ForumPage;
