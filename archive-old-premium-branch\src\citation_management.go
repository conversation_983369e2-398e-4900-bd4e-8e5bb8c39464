package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/greatnigeria/internal/content/models"
	"github.com/greatnigeria/internal/content/repository/migration"
)

func main() {
	// Load environment variables
	godotenv.Load()

	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		log.Fatal("DATABASE_URL environment variable is not set")
	}

	// Initialize database connection
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}
	db, err := gorm.Open(postgres.Open(dbURL), config)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get database connection: %v", err)
	}
	defer sqlDB.Close()

	// Check command line arguments
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	switch os.Args[1] {
	case "migrate":
		// Run database migrations
		fmt.Println("Running citation database migrations...")
		if err := migration.RunCitationMigrations(db); err != nil {
			log.Fatalf("Failed to run migrations: %v", err)
		}
		fmt.Println("Citations database schema migration completed successfully")

	case "import":
		// Import sample citations
		if len(os.Args) < 3 {
			fmt.Println("Please specify a book ID")
			os.Exit(1)
		}

		bookID, err := strconv.ParseUint(os.Args[2], 10, 64)
		if err != nil {
			log.Fatalf("Invalid book ID: %v", err)
		}

		fmt.Printf("Importing sample citations for Book %d...\n", bookID)
		switch bookID {
		case 3:
			if err := migration.ImportBook3Citations(db); err != nil {
				log.Fatalf("Failed to import citations for Book 3: %v", err)
			}
			fmt.Println("Imported Book 3 citations successfully")
		default:
			fmt.Printf("No sample citations available for Book %d\n", bookID)
		}

	case "generate":
		// Generate bibliography for a book
		if len(os.Args) < 3 {
			fmt.Println("Please specify a book ID")
			os.Exit(1)
		}

		bookID, err := strconv.ParseUint(os.Args[2], 10, 64)
		if err != nil {
			log.Fatalf("Invalid book ID: %v", err)
		}

		fmt.Printf("Generating bibliography for Book %d...\n", bookID)
		generateBibliography(db, uint(bookID))

	case "stats":
		// Show citation statistics for a book
		if len(os.Args) < 3 {
			fmt.Println("Please specify a book ID")
			os.Exit(1)
		}

		bookID, err := strconv.ParseUint(os.Args[2], 10, 64)
		if err != nil {
			log.Fatalf("Invalid book ID: %v", err)
		}

		fmt.Printf("Citation statistics for Book %d:\n", bookID)
		showCitationStats(db, uint(bookID))

	case "export":
		// Export citations as JSON
		if len(os.Args) < 3 {
			fmt.Println("Please specify a book ID")
			os.Exit(1)
		}

		bookID, err := strconv.ParseUint(os.Args[2], 10, 64)
		if err != nil {
			log.Fatalf("Invalid book ID: %v", err)
		}

		outputFile := "citations.json"
		if len(os.Args) >= 4 {
			outputFile = os.Args[3]
		}

		fmt.Printf("Exporting citations for Book %d to %s...\n", bookID, outputFile)
		exportCitations(db, uint(bookID), outputFile)

	case "update-appendix":
		// Update appendix with bibliography
		if len(os.Args) < 3 {
			fmt.Println("Please specify a book ID")
			os.Exit(1)
		}

		bookID, err := strconv.ParseUint(os.Args[2], 10, 64)
		if err != nil {
			log.Fatalf("Invalid book ID: %v", err)
		}

		fmt.Printf("Updating appendix with bibliography for Book %d...\n", bookID)
		updateAppendixWithBibliography(db, uint(bookID))

	default:
		printUsage()
		os.Exit(1)
	}
}

// Generate bibliography for a book
func generateBibliography(db *gorm.DB, bookID uint) {
	// Get all citations for this book
	var citations []models.Citation
	if err := db.Where("book_id = ?", bookID).Order("ref_number").Find(&citations).Error; err != nil {
		log.Fatalf("Failed to get citations: %v", err)
	}

	// Get book information
	var book models.Book
	if err := db.First(&book, bookID).Error; err != nil {
		log.Fatalf("Book not found: %v", err)
	}

	// Generate bibliography content
	var bibliography string
	bibliography = fmt.Sprintf("# Bibliography for %s\n\n", book.Title)

	// Group citations by type
	byType := make(map[string][]models.Citation)
	for _, c := range citations {
		byType[c.Type] = append(byType[c.Type], c)
	}

	// Academic sources section
	bibliography += "## Academic Sources\n\n"

	// Books
	if bookCitations, ok := byType["book"]; ok && len(bookCitations) > 0 {
		bibliography += "### Books\n\n"
		for _, c := range bookCitations {
			bibliography += fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Journal articles
	if journalCitations, ok := byType["journal"]; ok && len(journalCitations) > 0 {
		bibliography += "### Journal Articles\n\n"
		for _, c := range journalCitations {
			bibliography += fmt.Sprintf("%s. (%s). '%s'. *%s*. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Reports
	if reportCitations, ok := byType["report"]; ok && len(reportCitations) > 0 {
		bibliography += "## Reports and Institutional Publications\n\n"
		for _, c := range reportCitations {
			bibliography += fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Government sources
	if govCitations, ok := byType["government"]; ok && len(govCitations) > 0 {
		bibliography += "## Government Sources\n\n"
		for _, c := range govCitations {
			bibliography += fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Field research
	bibliography += "## Field Research\n\n"

	// Interviews
	if interviewCitations, ok := byType["interview"]; ok && len(interviewCitations) > 0 {
		bibliography += "### Interviews and Focus Groups\n\n"
		bibliography += "*Note: Names have been changed to protect privacy*\n\n"
		for _, c := range interviewCitations {
			bibliography += fmt.Sprintf("%s. (%s). %s. %s. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Survey data
	if surveyCitations, ok := byType["survey"]; ok && len(surveyCitations) > 0 {
		bibliography += "### Survey Data\n\n"
		for _, c := range surveyCitations {
			bibliography += fmt.Sprintf("%s. (%s). %s. %s. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Media sources
	if mediaCitations, ok := byType["media"]; ok && len(mediaCitations) > 0 {
		bibliography += "## Media Sources\n\n"
		for _, c := range mediaCitations {
			bibliography += fmt.Sprintf("%s. (%s). '%s'. *%s*. [%d]\n\n",
				c.Author, c.Year, c.Title, c.Source, c.RefNumber)
		}
	}

	// Add citation statistics
	totalCitations := len(citations)
	bibliography += "## Citation Statistics\n\n"
	bibliography += "| Source Type | Count | Percentage |\n"
	bibliography += "|-------------|-------|------------|\n"

	types := []string{"book", "journal", "report", "government", "interview", "survey", "media"}
	typeLabels := map[string]string{
		"book":       "Books",
		"journal":    "Journal Articles",
		"report":     "Research Reports",
		"government": "Government Sources",
		"interview":  "Interviews/Focus Groups",
		"survey":     "Survey Data",
		"media":      "Media Sources",
	}

	for _, t := range types {
		if typeCitations, ok := byType[t]; ok {
			count := len(typeCitations)
			if count > 0 {
				percentage := float64(count) / float64(totalCitations) * 100
				bibliography += fmt.Sprintf("| %s | %d | %.1f%% |\n",
					typeLabels[t], count, percentage)
			}
		}
	}

	fmt.Println(bibliography)

	// Save to file
	file, err := os.Create(fmt.Sprintf("bibliography_book_%d.md", bookID))
	if err != nil {
		log.Fatalf("Failed to create file: %v", err)
	}
	defer file.Close()

	_, err = file.WriteString(bibliography)
	if err != nil {
		log.Fatalf("Failed to write to file: %v", err)
	}

	fmt.Printf("Bibliography generated and saved to bibliography_book_%d.md\n", bookID)
}

// Show citation statistics for a book
func showCitationStats(db *gorm.DB, bookID uint) {
	// Get all citations for this book
	var citations []models.Citation
	if err := db.Where("book_id = ?", bookID).Order("ref_number").Find(&citations).Error; err != nil {
		log.Fatalf("Failed to get citations: %v", err)
	}

	// Group citations by type
	byType := make(map[string][]models.Citation)
	for _, c := range citations {
		byType[c.Type] = append(byType[c.Type], c)
	}

	totalCitations := len(citations)
	fmt.Printf("Total citations: %d\n\n", totalCitations)

	fmt.Println("Citations by type:")
	types := []string{"book", "journal", "report", "government", "interview", "survey", "media"}
	typeLabels := map[string]string{
		"book":       "Books",
		"journal":    "Journal Articles",
		"report":     "Research Reports",
		"government": "Government Sources",
		"interview":  "Interviews/Focus Groups",
		"survey":     "Survey Data",
		"media":      "Media Sources",
	}

	for _, t := range types {
		if typeCitations, ok := byType[t]; ok {
			count := len(typeCitations)
			if count > 0 {
				percentage := float64(count) / float64(totalCitations) * 100
				fmt.Printf("- %s: %d (%.1f%%)\n", typeLabels[t], count, percentage)
			}
		}
	}

	// Most cited sources
	fmt.Println("\nMost cited sources:")
	var mostCited []models.Citation
	if err := db.Where("book_id = ?", bookID).Order("cited_count DESC").Limit(5).Find(&mostCited).Error; err != nil {
		log.Fatalf("Failed to get most cited sources: %v", err)
	}

	for i, c := range mostCited {
		fmt.Printf("%d. %s (%s): cited %d times\n", i+1, c.Title, c.Author, c.CitedCount)
	}
}

// Export citations as JSON
func exportCitations(db *gorm.DB, bookID uint, outputFile string) {
	// Get all citations for this book
	var citations []models.Citation
	if err := db.Where("book_id = ?", bookID).Order("ref_number").Find(&citations).Error; err != nil {
		log.Fatalf("Failed to get citations: %v", err)
	}

	// Marshal to JSON
	jsonData, err := json.MarshalIndent(citations, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal citations to JSON: %v", err)
	}

	// Save to file
	if err := os.WriteFile(outputFile, jsonData, 0644); err != nil {
		log.Fatalf("Failed to write to file: %v", err)
	}

	fmt.Printf("Exported %d citations to %s\n", len(citations), outputFile)
}

// Update appendix with bibliography
func updateAppendixWithBibliography(db *gorm.DB, bookID uint) {
	// Get book information
	var book models.Book
	if err := db.First(&book, bookID).Error; err != nil {
		log.Fatalf("Book not found: %v", err)
	}

	// Generate bibliography
	var bibliography string
	{
		// Get all citations for this book
		var citations []models.Citation
		if err := db.Where("book_id = ?", bookID).Order("ref_number").Find(&citations).Error; err != nil {
			log.Fatalf("Failed to get citations: %v", err)
		}

		// Generate bibliography content (simplified version of the function above)
		bibliography = fmt.Sprintf("# Bibliography for %s\n\n", book.Title)
		bibliography += "## Academic Sources\n\n"

		// Group citations by type
		byType := make(map[string][]models.Citation)
		for _, c := range citations {
			byType[c.Type] = append(byType[c.Type], c)
		}

		// Books
		if bookCitations, ok := byType["book"]; ok && len(bookCitations) > 0 {
			bibliography += "### Books\n\n"
			for _, c := range bookCitations {
				bibliography += fmt.Sprintf("%s. (%s). *%s*. %s. [%d]\n\n",
					c.Author, c.Year, c.Title, c.Source, c.RefNumber)
			}
		}

		// Other citation types would be processed here...
	}

	// Get the book's back matter
	var backMatter models.BookBackMatter
	if err := db.Where("book_id = ?", bookID).First(&backMatter).Error; err != nil {
		log.Fatalf("Back matter not found: %v", err)
	}

	// Parse the existing appendix JSON
	var appendixItems []models.AppendixItem
	if backMatter.AppendixJSON != "" {
		if err := json.Unmarshal([]byte(backMatter.AppendixJSON), &appendixItems); err != nil {
			log.Fatalf("Failed to parse appendix JSON: %v", err)
		}
	}

	// Check if we already have a Bibliography appendix
	bibliographyExists := false
	for i, item := range appendixItems {
		if item.Title == "Bibliography and Citations" {
			// Update the existing bibliography appendix
			appendixItems[i].Content = bibliography
			bibliographyExists = true
			break
		}
	}

	// If no bibliography appendix exists, add one
	if !bibliographyExists {
		appendixItems = append(appendixItems, models.AppendixItem{
			Title:   "Bibliography and Citations",
			Content: bibliography,
		})
	}

	// Marshal the updated appendix items back to JSON
	appendixJSON, err := json.Marshal(appendixItems)
	if err != nil {
		log.Fatalf("Failed to marshal appendix JSON: %v", err)
	}

	// Update the back matter
	backMatter.AppendixJSON = string(appendixJSON)
	if err := db.Save(&backMatter).Error; err != nil {
		log.Fatalf("Failed to update back matter: %v", err)
	}

	fmt.Println("Bibliography appendix updated successfully")
}

// Print usage instructions
func printUsage() {
	fmt.Println(`
Citation Management Tool

Usage:
  go run citation_management.go <command> [arguments]

Commands:
  migrate                       Run database migrations for citation tables
  import <book_id>              Import sample citations for a book
  generate <book_id>            Generate bibliography for a book
  stats <book_id>               Show citation statistics for a book
  export <book_id> [file]       Export citations as JSON
  update-appendix <book_id>     Update appendix with bibliography
`)
}