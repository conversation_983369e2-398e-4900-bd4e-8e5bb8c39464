# Great Nigeria Library Project Setup Guide

This guide will help you set up the Great Nigeria Library project on your server from its GitHub repositories.

## Prerequisites

Before starting, ensure your server has the following installed:
- Git
- Go (version 1.18 or later)
- MySQL or MariaDB
- Node.js and npm (for the frontend)

## Step 1: Clone the Repositories

First, clone both the backend and frontend repositories:

```bash
# Create a project directory
mkdir -p /path/to/project
cd /path/to/project

# Clone the backend repository
git clone https://github.com/yerenwgventures/GreatNigeriaLibrary.git
cd GreatNigeriaLibrary

# Clone the frontend repository inside the backend directory
git clone https://github.com/yerenwgventures/great-nigeria-frontend.git
```

## Step 2: Set Up the Database

```bash
# Navigate to the database directory
cd /path/to/project/GreatNigeriaLibrary/database

# Import the database
mysql -u your_username -p your_database_name < great_nigeria_db_2025-04-23.sql
```

If the SQL file is compressed:
```bash
gunzip -c great_nigeria_db_2025-04-23.sql.gz | mysql -u your_username -p your_database_name
```

## Step 3: Configure Environment Variables

Create or update the `.env` file in the root directory:

```bash
cd /path/to/project/GreatNigeriaLibrary
cp .env.example .env  # If an example file exists
```

Edit the `.env` file with your database credentials and other configuration:

```
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name

# Server Configuration
SERVER_PORT=5000
API_BASE_URL=http://localhost:5000/api
```

## Step 4: Build and Run the Backend

```bash
cd /path/to/project/GreatNigeriaLibrary

# Get Go dependencies
go mod download

# Build the API gateway
go build -o bin/api-gateway ./cmd/api-gateway

# Run the API gateway
./bin/api-gateway
```

For production deployment, you might want to set up a service manager like systemd:

```bash
# Create a systemd service file
sudo nano /etc/systemd/system/great-nigeria-api.service
```

Add the following content:

```
[Unit]
Description=Great Nigeria Library API Gateway
After=network.target mysql.service

[Service]
User=your_server_user
WorkingDirectory=/path/to/project/GreatNigeriaLibrary
ExecStart=/path/to/project/GreatNigeriaLibrary/bin/api-gateway
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl enable great-nigeria-api
sudo systemctl start great-nigeria-api
```

## Step 5: Set Up the Frontend

```bash
cd /path/to/project/GreatNigeriaLibrary/great-nigeria-frontend

# Install dependencies
npm install

# Create or update environment variables
cp .env.example .env  # If an example file exists
```

Edit the `.env` file:

```
REACT_APP_API_URL=http://your_server_ip:5000/api
```

Build the frontend:

```bash
npm run build
```

## Step 6: Serve the Frontend

### Option 1: Using the Go server

The Go backend can serve the frontend static files. Make sure the build output is in the correct location for the Go server to find it.

### Option 2: Using a web server like Nginx

```bash
# Install Nginx if not already installed
sudo apt-get install nginx

# Create a site configuration
sudo nano /etc/nginx/sites-available/great-nigeria
```

Add the following configuration:

```
server {
    listen 80;
    server_name your_domain.com;

    location / {
        root /path/to/project/GreatNigeriaLibrary/great-nigeria-frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site and restart Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/great-nigeria /etc/nginx/sites-enabled/
sudo nginx -t  # Test the configuration
sudo systemctl restart nginx
```

## Step 7: Run Database Migrations (if needed)

```bash
cd /path/to/project/GreatNigeriaLibrary
go run ./migrations/runner.go
```

## Step 8: Verify the Setup

1. Check if the API server is running:
```bash
curl http://localhost:5000/api/health
```

2. Open your browser and navigate to your domain or server IP to see if the frontend is working.

## Troubleshooting

### Database Connection Issues
- Verify database credentials in the `.env` file
- Check if the MySQL service is running: `sudo systemctl status mysql`
- Ensure the database exists and has been properly imported

### API Server Issues
- Check the logs: `sudo journalctl -u great-nigeria-api`
- Verify the Go version: `go version`
- Make sure all dependencies are installed: `go mod download`

### Frontend Issues
- Check for build errors in the npm build output
- Verify that the API URL in the frontend `.env` file is correct
- Check browser console for any JavaScript errors

## Maintenance

### Updating the Project
To update the project with the latest changes from GitHub:

```bash
cd /path/to/project/GreatNigeriaLibrary
git pull

cd great-nigeria-frontend
git pull
npm install  # If dependencies have changed
npm run build

# Restart the API server
sudo systemctl restart great-nigeria-api
```

### Backing Up the Database
Regular database backups are recommended:

```bash
mysqldump -u your_username -p your_database_name > backup_$(date +%Y-%m-%d).sql
```

## Important Files and Directories

Make sure the following key files and directories are included in your deployment:

- `/cmd/api-gateway/` - Contains the main API gateway code
- `/internal/` - Contains all the internal packages and business logic
- `/migrations/` - Database migration files
- `/web/templates/` - HTML templates for server-rendered pages
- `/database/` - Database scripts and backups
- `/.env` - Environment configuration file
- `/go.mod` and `/go.sum` - Go module files
- `/great-nigeria-frontend/` - React frontend application
