import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import rewardsService, {
  RewardPoint,
  UserRewardSummary,
  RewardActivity,
  Achievement,
  UserAchievement,
  Reward,
  RewardRedemption,
  RewardRule,
  RewardTier,
  LeaderboardEntry,
} from '../../services/rewardsService';

interface RewardsState {
  userSummary: {
    data: UserRewardSummary | null;
    loading: boolean;
    error: string | null;
  };
  pointsHistory: {
    items: RewardPoint[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  activities: {
    items: RewardActivity[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  achievements: {
    items: Achievement[];
    loading: boolean;
    error: string | null;
  };
  userAchievements: {
    items: UserAchievement[];
    loading: boolean;
    error: string | null;
  };
  rewards: {
    items: Reward[];
    loading: boolean;
    error: string | null;
  };
  currentReward: {
    data: Reward | null;
    loading: boolean;
    error: string | null;
  };
  redemptions: {
    items: RewardRedemption[];
    loading: boolean;
    error: string | null;
  };
  rules: {
    items: RewardRule[];
    loading: boolean;
    error: string | null;
  };
  tiers: {
    items: RewardTier[];
    loading: boolean;
    error: string | null;
  };
  leaderboard: {
    items: LeaderboardEntry[];
    loading: boolean;
    error: string | null;
  };
}

const initialState: RewardsState = {
  userSummary: {
    data: null,
    loading: false,
    error: null,
  },
  pointsHistory: {
    items: [],
    total: 0,
    loading: false,
    error: null,
  },
  activities: {
    items: [],
    total: 0,
    loading: false,
    error: null,
  },
  achievements: {
    items: [],
    loading: false,
    error: null,
  },
  userAchievements: {
    items: [],
    loading: false,
    error: null,
  },
  rewards: {
    items: [],
    loading: false,
    error: null,
  },
  currentReward: {
    data: null,
    loading: false,
    error: null,
  },
  redemptions: {
    items: [],
    loading: false,
    error: null,
  },
  rules: {
    items: [],
    loading: false,
    error: null,
  },
  tiers: {
    items: [],
    loading: false,
    error: null,
  },
  leaderboard: {
    items: [],
    loading: false,
    error: null,
  },
};

// User summary thunks
export const fetchUserRewardSummary = createAsyncThunk(
  'rewards/fetchUserRewardSummary',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await rewardsService.getUserRewardSummary(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user reward summary');
    }
  }
);

// Points history thunks
export const fetchUserPointsHistory = createAsyncThunk(
  'rewards/fetchUserPointsHistory',
  async (
    { userId, params }: { userId: number; params?: { page?: number; limit?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await rewardsService.getUserPointsHistory(userId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch points history');
    }
  }
);

// Activities thunks
export const fetchUserActivities = createAsyncThunk(
  'rewards/fetchUserActivities',
  async (
    { userId, params }: { userId: number; params?: { page?: number; limit?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await rewardsService.getUserActivities(userId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user activities');
    }
  }
);

export const trackActivity = createAsyncThunk(
  'rewards/trackActivity',
  async (activity: Partial<RewardActivity>, { rejectWithValue }) => {
    try {
      return await rewardsService.trackActivity(activity);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to track activity');
    }
  }
);

// Achievements thunks
export const fetchAchievements = createAsyncThunk(
  'rewards/fetchAchievements',
  async (params?: { category?: string; isSecret?: boolean }, { rejectWithValue }) => {
    try {
      return await rewardsService.getAchievements(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch achievements');
    }
  }
);

export const fetchUserAchievements = createAsyncThunk(
  'rewards/fetchUserAchievements',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await rewardsService.getUserAchievements(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user achievements');
    }
  }
);

// Rewards thunks
export const fetchRewards = createAsyncThunk(
  'rewards/fetchRewards',
  async (params?: { category?: string; isActive?: boolean }, { rejectWithValue }) => {
    try {
      return await rewardsService.getRewards(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch rewards');
    }
  }
);

export const fetchRewardById = createAsyncThunk(
  'rewards/fetchRewardById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await rewardsService.getRewardById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch reward');
    }
  }
);

export const redeemReward = createAsyncThunk(
  'rewards/redeemReward',
  async ({ userId, rewardId }: { userId: number; rewardId: number }, { rejectWithValue }) => {
    try {
      return await rewardsService.redeemReward(userId, rewardId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to redeem reward');
    }
  }
);

export const fetchUserRedemptions = createAsyncThunk(
  'rewards/fetchUserRedemptions',
  async ({ userId, status }: { userId: number; status?: string }, { rejectWithValue }) => {
    try {
      return await rewardsService.getUserRedemptions(userId, { status });
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch redemptions');
    }
  }
);

// Admin thunks
export const createReward = createAsyncThunk(
  'rewards/createReward',
  async (reward: Partial<Reward>, { rejectWithValue }) => {
    try {
      return await rewardsService.createReward(reward);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create reward');
    }
  }
);

export const updateReward = createAsyncThunk(
  'rewards/updateReward',
  async ({ id, reward }: { id: number; reward: Partial<Reward> }, { rejectWithValue }) => {
    try {
      return await rewardsService.updateReward(id, reward);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update reward');
    }
  }
);

export const deleteReward = createAsyncThunk(
  'rewards/deleteReward',
  async (id: number, { rejectWithValue }) => {
    try {
      await rewardsService.deleteReward(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete reward');
    }
  }
);

// Rules thunks
export const fetchRules = createAsyncThunk(
  'rewards/fetchRules',
  async (_, { rejectWithValue }) => {
    try {
      return await rewardsService.getRules();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch rules');
    }
  }
);

export const createRule = createAsyncThunk(
  'rewards/createRule',
  async (rule: Partial<RewardRule>, { rejectWithValue }) => {
    try {
      return await rewardsService.createRule(rule);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create rule');
    }
  }
);

export const updateRule = createAsyncThunk(
  'rewards/updateRule',
  async ({ id, rule }: { id: number; rule: Partial<RewardRule> }, { rejectWithValue }) => {
    try {
      return await rewardsService.updateRule(id, rule);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update rule');
    }
  }
);

export const deleteRule = createAsyncThunk(
  'rewards/deleteRule',
  async (id: number, { rejectWithValue }) => {
    try {
      await rewardsService.deleteRule(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete rule');
    }
  }
);

// Tiers thunks
export const fetchTiers = createAsyncThunk(
  'rewards/fetchTiers',
  async (_, { rejectWithValue }) => {
    try {
      return await rewardsService.getTiers();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tiers');
    }
  }
);

export const createTier = createAsyncThunk(
  'rewards/createTier',
  async (tier: Partial<RewardTier>, { rejectWithValue }) => {
    try {
      return await rewardsService.createTier(tier);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create tier');
    }
  }
);

export const updateTier = createAsyncThunk(
  'rewards/updateTier',
  async ({ id, tier }: { id: number; tier: Partial<RewardTier> }, { rejectWithValue }) => {
    try {
      return await rewardsService.updateTier(id, tier);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update tier');
    }
  }
);

export const deleteTier = createAsyncThunk(
  'rewards/deleteTier',
  async (id: number, { rejectWithValue }) => {
    try {
      await rewardsService.deleteTier(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete tier');
    }
  }
);

// Leaderboard thunks
export const fetchLeaderboard = createAsyncThunk(
  'rewards/fetchLeaderboard',
  async (
    params?: { timeframe?: 'all' | 'month' | 'week' | 'day'; limit?: number },
    { rejectWithValue }
  ) => {
    try {
      return await rewardsService.getLeaderboard(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch leaderboard');
    }
  }
);

// Manual point adjustment thunk
export const adjustUserPoints = createAsyncThunk(
  'rewards/adjustUserPoints',
  async (
    { userId, amount, reason }: { userId: number; amount: number; reason: string },
    { rejectWithValue }
  ) => {
    try {
      return await rewardsService.adjustUserPoints(userId, amount, reason);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to adjust user points');
    }
  }
);

const rewardsSlice = createSlice({
  name: 'rewards',
  initialState,
  reducers: {
    resetCurrentReward: (state) => {
      state.currentReward.data = null;
      state.currentReward.error = null;
    },
  },
  extraReducers: (builder) => {
    // User summary
    builder.addCase(fetchUserRewardSummary.pending, (state) => {
      state.userSummary.loading = true;
      state.userSummary.error = null;
    });
    builder.addCase(fetchUserRewardSummary.fulfilled, (state, action) => {
      state.userSummary.loading = false;
      state.userSummary.data = action.payload;
    });
    builder.addCase(fetchUserRewardSummary.rejected, (state, action) => {
      state.userSummary.loading = false;
      state.userSummary.error = action.payload as string;
    });

    // Points history
    builder.addCase(fetchUserPointsHistory.pending, (state) => {
      state.pointsHistory.loading = true;
      state.pointsHistory.error = null;
    });
    builder.addCase(fetchUserPointsHistory.fulfilled, (state, action) => {
      state.pointsHistory.loading = false;
      state.pointsHistory.items = action.payload.items;
      state.pointsHistory.total = action.payload.total;
    });
    builder.addCase(fetchUserPointsHistory.rejected, (state, action) => {
      state.pointsHistory.loading = false;
      state.pointsHistory.error = action.payload as string;
    });

    // Activities
    builder.addCase(fetchUserActivities.pending, (state) => {
      state.activities.loading = true;
      state.activities.error = null;
    });
    builder.addCase(fetchUserActivities.fulfilled, (state, action) => {
      state.activities.loading = false;
      state.activities.items = action.payload.items;
      state.activities.total = action.payload.total;
    });
    builder.addCase(fetchUserActivities.rejected, (state, action) => {
      state.activities.loading = false;
      state.activities.error = action.payload as string;
    });

    // Track activity
    builder.addCase(trackActivity.fulfilled, (state, action) => {
      state.activities.items.unshift(action.payload);
    });

    // Achievements
    builder.addCase(fetchAchievements.pending, (state) => {
      state.achievements.loading = true;
      state.achievements.error = null;
    });
    builder.addCase(fetchAchievements.fulfilled, (state, action) => {
      state.achievements.loading = false;
      state.achievements.items = action.payload;
    });
    builder.addCase(fetchAchievements.rejected, (state, action) => {
      state.achievements.loading = false;
      state.achievements.error = action.payload as string;
    });

    // User achievements
    builder.addCase(fetchUserAchievements.pending, (state) => {
      state.userAchievements.loading = true;
      state.userAchievements.error = null;
    });
    builder.addCase(fetchUserAchievements.fulfilled, (state, action) => {
      state.userAchievements.loading = false;
      state.userAchievements.items = action.payload;
    });
    builder.addCase(fetchUserAchievements.rejected, (state, action) => {
      state.userAchievements.loading = false;
      state.userAchievements.error = action.payload as string;
    });

    // Rewards
    builder.addCase(fetchRewards.pending, (state) => {
      state.rewards.loading = true;
      state.rewards.error = null;
    });
    builder.addCase(fetchRewards.fulfilled, (state, action) => {
      state.rewards.loading = false;
      state.rewards.items = action.payload;
    });
    builder.addCase(fetchRewards.rejected, (state, action) => {
      state.rewards.loading = false;
      state.rewards.error = action.payload as string;
    });

    // Reward by ID
    builder.addCase(fetchRewardById.pending, (state) => {
      state.currentReward.loading = true;
      state.currentReward.error = null;
    });
    builder.addCase(fetchRewardById.fulfilled, (state, action) => {
      state.currentReward.loading = false;
      state.currentReward.data = action.payload;
    });
    builder.addCase(fetchRewardById.rejected, (state, action) => {
      state.currentReward.loading = false;
      state.currentReward.error = action.payload as string;
    });

    // Redeem reward
    builder.addCase(redeemReward.fulfilled, (state, action) => {
      state.redemptions.items.unshift(action.payload);
      if (state.userSummary.data) {
        state.userSummary.data.availablePoints -= action.payload.pointsSpent;
        state.userSummary.data.redeemedPoints += action.payload.pointsSpent;
      }
    });

    // User redemptions
    builder.addCase(fetchUserRedemptions.pending, (state) => {
      state.redemptions.loading = true;
      state.redemptions.error = null;
    });
    builder.addCase(fetchUserRedemptions.fulfilled, (state, action) => {
      state.redemptions.loading = false;
      state.redemptions.items = action.payload;
    });
    builder.addCase(fetchUserRedemptions.rejected, (state, action) => {
      state.redemptions.loading = false;
      state.redemptions.error = action.payload as string;
    });

    // Create reward
    builder.addCase(createReward.fulfilled, (state, action) => {
      state.rewards.items.push(action.payload);
    });

    // Update reward
    builder.addCase(updateReward.fulfilled, (state, action) => {
      const updatedReward = action.payload;
      const index = state.rewards.items.findIndex((reward) => reward.id === updatedReward.id);
      if (index !== -1) {
        state.rewards.items[index] = updatedReward;
      }
      if (state.currentReward.data?.id === updatedReward.id) {
        state.currentReward.data = updatedReward;
      }
    });

    // Delete reward
    builder.addCase(deleteReward.fulfilled, (state, action) => {
      const rewardId = action.payload;
      state.rewards.items = state.rewards.items.filter((reward) => reward.id !== rewardId);
      if (state.currentReward.data?.id === rewardId) {
        state.currentReward.data = null;
      }
    });

    // Rules
    builder.addCase(fetchRules.pending, (state) => {
      state.rules.loading = true;
      state.rules.error = null;
    });
    builder.addCase(fetchRules.fulfilled, (state, action) => {
      state.rules.loading = false;
      state.rules.items = action.payload;
    });
    builder.addCase(fetchRules.rejected, (state, action) => {
      state.rules.loading = false;
      state.rules.error = action.payload as string;
    });

    // Create rule
    builder.addCase(createRule.fulfilled, (state, action) => {
      state.rules.items.push(action.payload);
    });

    // Update rule
    builder.addCase(updateRule.fulfilled, (state, action) => {
      const updatedRule = action.payload;
      const index = state.rules.items.findIndex((rule) => rule.id === updatedRule.id);
      if (index !== -1) {
        state.rules.items[index] = updatedRule;
      }
    });

    // Delete rule
    builder.addCase(deleteRule.fulfilled, (state, action) => {
      const ruleId = action.payload;
      state.rules.items = state.rules.items.filter((rule) => rule.id !== ruleId);
    });

    // Tiers
    builder.addCase(fetchTiers.pending, (state) => {
      state.tiers.loading = true;
      state.tiers.error = null;
    });
    builder.addCase(fetchTiers.fulfilled, (state, action) => {
      state.tiers.loading = false;
      state.tiers.items = action.payload;
    });
    builder.addCase(fetchTiers.rejected, (state, action) => {
      state.tiers.loading = false;
      state.tiers.error = action.payload as string;
    });

    // Create tier
    builder.addCase(createTier.fulfilled, (state, action) => {
      state.tiers.items.push(action.payload);
    });

    // Update tier
    builder.addCase(updateTier.fulfilled, (state, action) => {
      const updatedTier = action.payload;
      const index = state.tiers.items.findIndex((tier) => tier.id === updatedTier.id);
      if (index !== -1) {
        state.tiers.items[index] = updatedTier;
      }
    });

    // Delete tier
    builder.addCase(deleteTier.fulfilled, (state, action) => {
      const tierId = action.payload;
      state.tiers.items = state.tiers.items.filter((tier) => tier.id !== tierId);
    });

    // Leaderboard
    builder.addCase(fetchLeaderboard.pending, (state) => {
      state.leaderboard.loading = true;
      state.leaderboard.error = null;
    });
    builder.addCase(fetchLeaderboard.fulfilled, (state, action) => {
      state.leaderboard.loading = false;
      state.leaderboard.items = action.payload;
    });
    builder.addCase(fetchLeaderboard.rejected, (state, action) => {
      state.leaderboard.loading = false;
      state.leaderboard.error = action.payload as string;
    });

    // Adjust user points
    builder.addCase(adjustUserPoints.fulfilled, (state, action) => {
      if (state.userSummary.data) {
        state.userSummary.data.availablePoints += action.payload.amount;
        state.userSummary.data.totalPoints += action.payload.amount;
      }
      state.pointsHistory.items.unshift(action.payload);
    });
  },
});

export const { resetCurrentReward } = rewardsSlice.actions;

export default rewardsSlice.reducer;
