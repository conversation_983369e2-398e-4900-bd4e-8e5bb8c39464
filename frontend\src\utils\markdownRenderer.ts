import { marked } from 'marked';

/**
 * Renders markdown content to HTML
 * @param {string} markdown - The markdown content to render
 * @returns {string} The rendered HTML
 */
export const renderMarkdown = (markdown: string): string => {
  if (!markdown) return '';

  // Configure marked options if needed
  marked.setOptions({
    breaks: true,
    gfm: true,
  });

  // The marked function can return a Promise<string> in async mode,
  // but we're using it in sync mode, so we can safely cast it to string
  return marked(markdown) as string;
};
