{"name": "great-nigeria-frontend", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.39", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "axios": "^1.4.0", "marked": "^15.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "styled-components": "^6.0.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:prod": "env-cmd -f .env.production react-scripts build", "build:nolint": "set DISABLE_ESLINT_PLUGIN=true && set ESLINT_NO_DEV_ERRORS=true && set EXTEND_ESLINT=false && set CI=false && set TSC_COMPILE_ON_ERROR=true && react-scripts build", "build:nocheck": "set DISABLE_ESLINT_PLUGIN=true && set ESLINT_NO_DEV_ERRORS=true && set EXTEND_ESLINT=false && set CI=false && set TSC_COMPILE_ON_ERROR=true && set GENERATE_SOURCEMAP=false && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "format": "prettier --write \"src/**/*.{ts,tsx}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/marked": "^5.0.2", "@types/styled-components": "^5.1.26", "env-cmd": "^10.1.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.1"}}