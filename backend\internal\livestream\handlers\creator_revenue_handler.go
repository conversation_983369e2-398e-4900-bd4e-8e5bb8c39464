package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// CreatorRevenueHandler handles HTTP requests for creator revenue operations
type CreatorRevenueHandler struct {
	service service.CreatorRevenueService
	logger  *logger.Logger
}

// NewCreatorRevenueHandler creates a new instance of the creator revenue handler
func NewCreatorRevenueHandler(service service.CreatorRevenueService, logger *logger.Logger) *CreatorRevenueHandler {
	return &CreatorRevenueHandler{
		service: service,
		logger:  logger,
	}
}

// GetCreatorRevenue retrieves revenue records for a creator
func (h *CreatorRevenueHandler) GetCreatorRevenue(c *gin.Context) {
	// Get creator ID from URL parameter
	creatorIDStr := c.Param("userId")
	creatorID, err := strconv.ParseUint(creatorIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid creator ID"})
		return
	}
	
	// Get authenticated user ID
	authUserID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Only allow users to view their own revenue
	if authUserID.(uint) != uint(creatorID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only view your own revenue"})
		return
	}
	
	// Get period from query parameter
	period := c.Query("period")
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get revenue
	revenues, total, err := h.service.GetCreatorRevenue(c.Request.Context(), uint(creatorID), period, page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get revenue for creator %d: %v", creatorID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get revenue"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"revenues": revenues,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetRevenueSummary retrieves a summary of a creator's revenue
func (h *CreatorRevenueHandler) GetRevenueSummary(c *gin.Context) {
	// Get creator ID from URL parameter
	creatorIDStr := c.Param("userId")
	creatorID, err := strconv.ParseUint(creatorIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid creator ID"})
		return
	}
	
	// Get authenticated user ID
	authUserID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Only allow users to view their own revenue summary
	if authUserID.(uint) != uint(creatorID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only view your own revenue summary"})
		return
	}
	
	// Get revenue summary
	summary, err := h.service.GetRevenueSummary(c.Request.Context(), uint(creatorID))
	if err != nil {
		h.logger.Errorf("Failed to get revenue summary for creator %d: %v", creatorID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get revenue summary"})
		return
	}
	
	c.JSON(http.StatusOK, summary)
}

// RequestWithdrawal creates a withdrawal request
func (h *CreatorRevenueHandler) RequestWithdrawal(c *gin.Context) {
	// Get authenticated user ID
	creatorID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Parse request body
	var request struct {
		Amount        float64 `json:"amount" binding:"required"`
		BankName      string  `json:"bankName" binding:"required"`
		AccountNumber string  `json:"accountNumber" binding:"required"`
		AccountName   string  `json:"accountName" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Create withdrawal request
	withdrawalRequest, err := h.service.RequestWithdrawal(
		c.Request.Context(),
		creatorID.(uint),
		request.Amount,
		request.BankName,
		request.AccountNumber,
		request.AccountName,
	)
	
	if err != nil {
		h.logger.Errorf("Failed to create withdrawal request for creator %d: %v", creatorID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, withdrawalRequest)
}

// GetPendingWithdrawalRequests retrieves pending withdrawal requests (admin only)
func (h *CreatorRevenueHandler) GetPendingWithdrawalRequests(c *gin.Context) {
	// Check if user is admin
	isAdmin, exists := c.Get("isAdmin")
	if !exists || !isAdmin.(bool) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get pending withdrawal requests
	requests, total, err := h.service.GetPendingWithdrawalRequests(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get pending withdrawal requests: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pending withdrawal requests"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"requests": requests,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// ProcessWithdrawalRequest processes a withdrawal request (admin only)
func (h *CreatorRevenueHandler) ProcessWithdrawalRequest(c *gin.Context) {
	// Check if user is admin
	isAdmin, exists := c.Get("isAdmin")
	if !exists || !isAdmin.(bool) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}
	
	// Get authenticated user ID
	adminID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Parse request body
	var request struct {
		RequestID           uint   `json:"requestId" binding:"required"`
		Status              string `json:"status" binding:"required"`
		Notes               string `json:"notes"`
		TransactionReference string `json:"transactionReference"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Process withdrawal request
	err := h.service.ProcessWithdrawalRequest(
		c.Request.Context(),
		request.RequestID,
		request.Status,
		request.Notes,
		adminID.(uint),
		request.TransactionReference,
	)
	
	if err != nil {
		h.logger.Errorf("Failed to process withdrawal request %d: %v", request.RequestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Withdrawal request processed successfully",
	})
}
