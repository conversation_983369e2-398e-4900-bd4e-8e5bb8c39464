import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import { RootState } from '../store';
import { fetchBooks } from '../features/books/booksSlice';

const HeroSection = styled.section`
  background-color: #16213e;
  color: white;
  padding: 4rem 1rem;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 3rem;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  
  @media (min-width: 768px) {
    font-size: 3.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 2rem;
  
  @media (min-width: 768px) {
    font-size: 1.5rem;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
`;

const Button = styled(Link)`
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
`;

const PrimaryButton = styled(Button)`
  background-color: #e94560;
  color: white;
  
  &:hover {
    background-color: #d63553;
    text-decoration: none;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: transparent;
  color: white;
  border: 2px solid white;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 2rem;
  text-align: center;
`;

const BooksSection = styled.section`
  margin-bottom: 3rem;
`;

const BooksGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
`;

const BookCard = styled.div`
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const BookCover = styled.img`
  width: 100%;
  height: 350px;
  object-fit: cover;
`;

const BookInfo = styled.div`
  padding: 1rem;
`;

const BookTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
`;

const BookAuthor = styled.p`
  color: #666;
  margin-bottom: 1rem;
`;

const BookLink = styled(Link)`
  display: inline-block;
  color: #16213e;
  font-weight: bold;
  
  &:hover {
    text-decoration: underline;
  }
`;

const FeaturesSection = styled.section`
  margin-bottom: 3rem;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  padding: 2rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  text-align: center;
`;

const FeatureIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 1rem;
`;

const FeatureDescription = styled.p`
  color: #666;
`;

const CTASection = styled.section`
  background-color: #16213e;
  color: white;
  padding: 3rem 1rem;
  text-align: center;
  border-radius: 8px;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const CTADescription = styled.p`
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 2rem;
`;

const HomePage: React.FC = () => {
  const dispatch = useDispatch();
  const { books, isLoading, error } = useSelector((state: RootState) => state.books);
  
  useEffect(() => {
    dispatch(fetchBooks());
  }, [dispatch]);
  
  return (
    <div>
      <HeroSection>
        <HeroTitle>Transform Nigeria Through Knowledge, Community, and Action</HeroTitle>
        <HeroSubtitle>
          Join thousands of Nigerians working together to build a more prosperous, just, and united
          nation through practical, grassroots solutions.
        </HeroSubtitle>
        <ButtonGroup>
          <PrimaryButton to="/books">Explore eBooks</PrimaryButton>
          <SecondaryButton to="/community">Join Community</SecondaryButton>
        </ButtonGroup>
      </HeroSection>
      
      <BooksSection>
        <SectionTitle>Featured Books</SectionTitle>
        {isLoading ? (
          <p>Loading books...</p>
        ) : error ? (
          <p>Error loading books: {error}</p>
        ) : (
          <BooksGrid>
            {books.slice(0, 4).map((book) => (
              <BookCard key={book.id}>
                <BookCover src={book.cover_image} alt={book.title} />
                <BookInfo>
                  <BookTitle>{book.title}</BookTitle>
                  <BookAuthor>By {book.author}</BookAuthor>
                  <BookLink to={`/book-viewer?book=${book.id}`}>Start Reading</BookLink>
                </BookInfo>
              </BookCard>
            ))}
          </BooksGrid>
        )}
      </BooksSection>
      
      <FeaturesSection>
        <SectionTitle>Why Great Nigeria?</SectionTitle>
        <FeaturesGrid>
          <FeatureCard>
            <FeatureIcon>📚</FeatureIcon>
            <FeatureTitle>Comprehensive Resources</FeatureTitle>
            <FeatureDescription>
              Access a growing library of books, guides, and tools designed specifically for Nigerian
              nation-building.
            </FeatureDescription>
          </FeatureCard>
          
          <FeatureCard>
            <FeatureIcon>👥</FeatureIcon>
            <FeatureTitle>Vibrant Community</FeatureTitle>
            <FeatureDescription>
              Connect with like-minded Nigerians committed to positive change and collaborate on
              initiatives.
            </FeatureDescription>
          </FeatureCard>
          
          <FeatureCard>
            <FeatureIcon>🌟</FeatureIcon>
            <FeatureTitle>Celebrate Excellence</FeatureTitle>
            <FeatureDescription>
              Discover and celebrate Nigerian people, places, and achievements that inspire pride and
              progress.
            </FeatureDescription>
          </FeatureCard>
        </FeaturesGrid>
      </FeaturesSection>
      
      <CTASection>
        <CTATitle>Ready to Make a Difference?</CTATitle>
        <CTADescription>
          Join our community of change-makers and start your journey toward building a better Nigeria
          today.
        </CTADescription>
        <PrimaryButton to="/register">Get Started Now</PrimaryButton>
      </CTASection>
    </div>
  );
};

export default HomePage;
