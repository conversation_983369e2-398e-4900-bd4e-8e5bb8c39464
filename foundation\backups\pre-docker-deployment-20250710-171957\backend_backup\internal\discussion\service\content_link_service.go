package service

import (
	"errors"
	"fmt"
	"strings"
	"text/template"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary-Foundation/backend/internal/content/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary-Foundation/backend/internal/discussion/models"
	discussionrepo "github.com/yerenwgventures/GreatNigeriaLibrary-Foundation/backend/internal/discussion/repository"
)

// ContentLinkService defines the interface for content link operations
type ContentLinkService interface {
	// Topic content links
	LinkTopicToContent(topicID uint, contentType models.ContentReferenceType, contentID uint, userID uint, isHighlighted bool) (*models.TopicContentLink, error)
	GetTopicContentLinks(topicID uint) ([]models.TopicContentLink, error)
	GetLinkedTopics(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error)
	HighlightTopicLink(linkID uint, highlighted bool, userID uint) error
	RemoveTopicLink(linkID uint, userID uint) error

	// Comment content links (citations)
	AddContentCitation(commentID uint, contentType models.ContentReferenceType, contentID uint, userID uint, citationText, citationContext string) (*models.CommentContentLink, error)
	GetCommentContentLinks(commentID uint) ([]models.CommentContentLink, error)
	GetContentCitations(contentType models.ContentReferenceType, contentID uint) ([]models.CommentContentLink, error)
	UpdateContentCitation(linkID uint, citationText, citationContext string, userID uint) error
	RemoveContentCitation(linkID uint, userID uint) error

	// Auto-generated topic management
	CreateTopicTemplate(name string, contentType models.ContentReferenceType, titleTemplate, bodyTemplate string, userID uint) (*models.AutoGeneratedTopicTemplate, error)
	GetTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error)
	UpdateTopicTemplate(templateID uint, name string, titleTemplate, bodyTemplate string, isActive bool, userID uint) (*models.AutoGeneratedTopicTemplate, error)
	DeleteTopicTemplate(templateID uint, userID uint) error

	// Auto-generate topics for content
	GenerateTopicsForContent(contentType models.ContentReferenceType, contentID uint, userID uint) ([]uint, error)

	// Discussion recommendations
	AddContentRecommendation(contentType models.ContentReferenceType, contentID uint, topicID uint, score float64, userID uint) (*models.ContentDiscussionRecommendation, error)
	GetContentRecommendations(contentType models.ContentReferenceType, contentID uint, limit int) ([]models.ContentDiscussionRecommendation, error)
	RemoveContentRecommendation(recommendationID uint, userID uint) error

	// Generate context-aware recommendations
	GenerateRecommendationsForContent(contentType models.ContentReferenceType, contentID uint) (int, error)
}

// ContentLinkServiceImpl implements the ContentLinkService interface
type ContentLinkServiceImpl struct {
	contentLinkRepo discussionrepo.ContentLinkRepository
	topicRepo       discussionrepo.TopicRepository
	commentRepo     discussionrepo.CommentRepository
	bookRepo        repository.BookRepository
	chapterRepo     repository.ChapterRepository
	sectionRepo     repository.SectionRepository
}

// NewContentLinkService creates a new content link service
func NewContentLinkService(
	contentLinkRepo discussionrepo.ContentLinkRepository,
	topicRepo discussionrepo.TopicRepository,
	commentRepo discussionrepo.CommentRepository,
	bookRepo repository.BookRepository,
	chapterRepo repository.ChapterRepository,
	sectionRepo repository.SectionRepository,
) ContentLinkService {
	return &ContentLinkServiceImpl{
		contentLinkRepo: contentLinkRepo,
		topicRepo:       topicRepo,
		commentRepo:     commentRepo,
		bookRepo:        bookRepo,
		chapterRepo:     chapterRepo,
		sectionRepo:     sectionRepo,
	}
}

// LinkTopicToContent links a topic to a book content
func (s *ContentLinkServiceImpl) LinkTopicToContent(topicID uint, contentType models.ContentReferenceType, contentID uint, userID uint, isHighlighted bool) (*models.TopicContentLink, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return nil, errors.New("invalid content type")
	}

	// Validate topic exists
	if _, err := s.topicRepo.GetTopicByID(topicID); err != nil {
		return nil, fmt.Errorf("topic not found: %w", err)
	}

	// Validate content exists
	if err := s.validateContent(contentType, contentID); err != nil {
		return nil, err
	}

	// Check if link already exists
	links, err := s.contentLinkRepo.GetTopicContentLinks(topicID)
	if err != nil {
		return nil, fmt.Errorf("error getting existing links: %w", err)
	}

	for _, link := range links {
		if link.ContentType == contentType && link.ContentID == contentID {
			// Update if exists
			link.IsHighlighted = isHighlighted
			link.UpdatedAt = time.Now()
			if err := s.contentLinkRepo.UpdateTopicContentLink(&link); err != nil {
				return nil, fmt.Errorf("error updating existing link: %w", err)
			}
			return &link, nil
		}
	}

	// Create new link
	link := &models.TopicContentLink{
		TopicID:       topicID,
		ContentType:   contentType,
		ContentID:     contentID,
		CreatedBy:     userID,
		IsHighlighted: isHighlighted,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.contentLinkRepo.CreateTopicContentLink(link); err != nil {
		return nil, fmt.Errorf("error creating link: %w", err)
	}

	return link, nil
}

// GetTopicContentLinks retrieves content links for a topic
func (s *ContentLinkServiceImpl) GetTopicContentLinks(topicID uint) ([]models.TopicContentLink, error) {
	return s.contentLinkRepo.GetTopicContentLinks(topicID)
}

// GetLinkedTopics retrieves topics linked to a content
func (s *ContentLinkServiceImpl) GetLinkedTopics(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error) {
	return s.contentLinkRepo.GetTopicsByContent(contentType, contentID)
}

// HighlightTopicLink highlights or unhighlights a topic link
func (s *ContentLinkServiceImpl) HighlightTopicLink(linkID uint, highlighted bool, userID uint) error {
	// Get the link
	links, err := s.contentLinkRepo.GetTopicContentLinks(linkID)
	if err != nil || len(links) == 0 {
		return errors.New("link not found")
	}

	// Update the link
	link := links[0]
	link.IsHighlighted = highlighted
	link.UpdatedAt = time.Now()

	return s.contentLinkRepo.UpdateTopicContentLink(&link)
}

// RemoveTopicLink removes a topic link
func (s *ContentLinkServiceImpl) RemoveTopicLink(linkID uint, userID uint) error {
	// For a real implementation, you might want to check if the user has permission to remove the link
	return s.contentLinkRepo.DeleteTopicContentLink(linkID)
}

// AddContentCitation adds a citation to a comment
func (s *ContentLinkServiceImpl) AddContentCitation(commentID uint, contentType models.ContentReferenceType, contentID uint, userID uint, citationText, citationContext string) (*models.CommentContentLink, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return nil, errors.New("invalid content type")
	}

	// Validate comment exists
	if _, err := s.commentRepo.GetCommentByID(commentID); err != nil {
		return nil, fmt.Errorf("comment not found: %w", err)
	}

	// Validate content exists
	if err := s.validateContent(contentType, contentID); err != nil {
		return nil, err
	}

	// Create citation
	citation := &models.CommentContentLink{
		CommentID:       commentID,
		ContentType:     contentType,
		ContentID:       contentID,
		CreatedBy:       userID,
		CitationText:    citationText,
		CitationContext: citationContext,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.contentLinkRepo.CreateCommentContentLink(citation); err != nil {
		return nil, fmt.Errorf("error creating citation: %w", err)
	}

	return citation, nil
}

// GetCommentContentLinks retrieves content links for a comment
func (s *ContentLinkServiceImpl) GetCommentContentLinks(commentID uint) ([]models.CommentContentLink, error) {
	return s.contentLinkRepo.GetCommentContentLinks(commentID)
}

// GetContentCitations retrieves citations for a content
func (s *ContentLinkServiceImpl) GetContentCitations(contentType models.ContentReferenceType, contentID uint) ([]models.CommentContentLink, error) {
	return s.contentLinkRepo.GetCommentsByContent(contentType, contentID)
}

// UpdateContentCitation updates a content citation
func (s *ContentLinkServiceImpl) UpdateContentCitation(linkID uint, citationText, citationContext string, userID uint) error {
	// Get the citation
	links, err := s.contentLinkRepo.GetCommentContentLinks(linkID)
	if err != nil || len(links) == 0 {
		return errors.New("citation not found")
	}

	// Update the citation
	link := links[0]

	// In a real implementation, you'd check if the user has permission to update the citation
	if link.CreatedBy != userID {
		return errors.New("you don't have permission to update this citation")
	}

	link.CitationText = citationText
	link.CitationContext = citationContext
	link.UpdatedAt = time.Now()

	return s.contentLinkRepo.UpdateCommentContentLink(&link)
}

// RemoveContentCitation removes a content citation
func (s *ContentLinkServiceImpl) RemoveContentCitation(linkID uint, userID uint) error {
	// Get the citation
	links, err := s.contentLinkRepo.GetCommentContentLinks(linkID)
	if err != nil || len(links) == 0 {
		return errors.New("citation not found")
	}

	// Check if user has permission to delete the citation
	link := links[0]
	if link.CreatedBy != userID {
		return errors.New("you don't have permission to remove this citation")
	}

	return s.contentLinkRepo.DeleteCommentContentLink(linkID)
}

// CreateTopicTemplate creates a new auto-generated topic template
func (s *ContentLinkServiceImpl) CreateTopicTemplate(name string, contentType models.ContentReferenceType, titleTemplate, bodyTemplate string, userID uint) (*models.AutoGeneratedTopicTemplate, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return nil, errors.New("invalid content type")
	}

	// Validate templates
	if err := s.validateTemplate(titleTemplate); err != nil {
		return nil, fmt.Errorf("invalid title template: %w", err)
	}

	if err := s.validateTemplate(bodyTemplate); err != nil {
		return nil, fmt.Errorf("invalid body template: %w", err)
	}

	// Create template
	template := &models.AutoGeneratedTopicTemplate{
		Name:          name,
		ContentType:   contentType,
		TitleTemplate: titleTemplate,
		BodyTemplate:  bodyTemplate,
		IsActive:      true,
		CreatedBy:     userID,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.contentLinkRepo.CreateAutoGeneratedTopicTemplate(template); err != nil {
		return nil, fmt.Errorf("error creating template: %w", err)
	}

	return template, nil
}

// GetTopicTemplates retrieves auto-generated topic templates for a content type
func (s *ContentLinkServiceImpl) GetTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error) {
	return s.contentLinkRepo.GetAutoGeneratedTopicTemplates(contentType)
}

// UpdateTopicTemplate updates an auto-generated topic template
func (s *ContentLinkServiceImpl) UpdateTopicTemplate(templateID uint, name string, titleTemplate, bodyTemplate string, isActive bool, userID uint) (*models.AutoGeneratedTopicTemplate, error) {
	// Get the template
	template, err := s.contentLinkRepo.GetAutoGeneratedTopicTemplateByID(templateID)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// In a real implementation, you'd check if the user has permission to update the template

	// Validate templates
	if err := s.validateTemplate(titleTemplate); err != nil {
		return nil, fmt.Errorf("invalid title template: %w", err)
	}

	if err := s.validateTemplate(bodyTemplate); err != nil {
		return nil, fmt.Errorf("invalid body template: %w", err)
	}

	// Update the template
	template.Name = name
	template.TitleTemplate = titleTemplate
	template.BodyTemplate = bodyTemplate
	template.IsActive = isActive
	template.UpdatedAt = time.Now()

	if err := s.contentLinkRepo.UpdateAutoGeneratedTopicTemplate(template); err != nil {
		return nil, fmt.Errorf("error updating template: %w", err)
	}

	return template, nil
}

// DeleteTopicTemplate deletes an auto-generated topic template
func (s *ContentLinkServiceImpl) DeleteTopicTemplate(templateID uint, userID uint) error {
	// Get the template
	template, err := s.contentLinkRepo.GetAutoGeneratedTopicTemplateByID(templateID)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}

	// In a real implementation, you'd check if the user has permission to delete the template

	return s.contentLinkRepo.DeleteAutoGeneratedTopicTemplate(template.ID)
}

// GenerateTopicsForContent auto-generates topics for a content
func (s *ContentLinkServiceImpl) GenerateTopicsForContent(contentType models.ContentReferenceType, contentID uint, userID uint) ([]uint, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return nil, errors.New("invalid content type")
	}

	// Validate content exists
	if err := s.validateContent(contentType, contentID); err != nil {
		return nil, err
	}

	// Get active templates for this content type
	templates, err := s.contentLinkRepo.GetActiveAutoGeneratedTopicTemplates(contentType)
	if err != nil {
		return nil, fmt.Errorf("error getting templates: %w", err)
	}

	if len(templates) == 0 {
		return nil, errors.New("no active templates found for this content type")
	}

	// Get content data
	contentData, err := s.getContentData(contentType, contentID)
	if err != nil {
		return nil, fmt.Errorf("error getting content data: %w", err)
	}

	// Generate topics
	topicIDs := make([]uint, 0, len(templates))
	for _, tmpl := range templates {
		// Execute title template
		title, err := s.executeTemplate(tmpl.TitleTemplate, contentData)
		if err != nil {
			// Log error and continue
			fmt.Printf("Error executing title template: %v\n", err)
			continue
		}

		// Execute body template
		body, err := s.executeTemplate(tmpl.BodyTemplate, contentData)
		if err != nil {
			// Log error and continue
			fmt.Printf("Error executing body template: %v\n", err)
			continue
		}

		// Create topic
		topic := &models.Topic{
			Title:      title,
			Content:    body,
			UserID:     userID,
			CategoryID: 0, // You'd set appropriate category based on content
			IsPinned:   false,
			IsLocked:   false,
			ViewCount:  0,
		}

		// Use a different variable name
		createErr := s.topicRepo.CreateTopic(topic)
		if createErr != nil {
			// Log error and continue
			fmt.Printf("Error creating topic: %v\n", createErr)
			continue
		}

		// Link topic to content
		link := &models.TopicContentLink{
			TopicID:         topic.ID,
			ContentType:     contentType,
			ContentID:       contentID,
			CreatedBy:       userID,
			IsAutoGenerated: true,
			IsHighlighted:   false,
		}

		if err := s.contentLinkRepo.CreateTopicContentLink(link); err != nil {
			// Log error but continue
			fmt.Printf("Error linking topic to content: %v\n", err)
		}

		topicIDs = append(topicIDs, topic.ID)
	}

	return topicIDs, nil
}

// AddContentRecommendation adds a recommendation for a content
func (s *ContentLinkServiceImpl) AddContentRecommendation(contentType models.ContentReferenceType, contentID uint, topicID uint, score float64, userID uint) (*models.ContentDiscussionRecommendation, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return nil, errors.New("invalid content type")
	}

	// Validate content exists
	if err := s.validateContent(contentType, contentID); err != nil {
		return nil, err
	}

	// Validate topic exists
	if _, err := s.topicRepo.GetTopicByID(topicID); err != nil {
		return nil, fmt.Errorf("topic not found: %w", err)
	}

	// Check if recommendation already exists
	recommendations, err := s.contentLinkRepo.GetContentDiscussionRecommendations(contentType, contentID, 0)
	if err != nil {
		return nil, fmt.Errorf("error getting existing recommendations: %w", err)
	}

	for _, rec := range recommendations {
		if rec.TopicID == topicID {
			// Update if exists
			rec.RecommendationScore = score
			rec.IsManuallyAdded = true
			rec.AddedBy = &userID
			rec.UpdatedAt = time.Now()
			if err := s.contentLinkRepo.UpdateContentDiscussionRecommendation(&rec); err != nil {
				return nil, fmt.Errorf("error updating existing recommendation: %w", err)
			}
			return &rec, nil
		}
	}

	// Create new recommendation
	recommendation := &models.ContentDiscussionRecommendation{
		ContentType:         contentType,
		ContentID:           contentID,
		TopicID:             topicID,
		RecommendationScore: score,
		IsManuallyAdded:     true,
		AddedBy:             &userID,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	if err := s.contentLinkRepo.CreateContentDiscussionRecommendation(recommendation); err != nil {
		return nil, fmt.Errorf("error creating recommendation: %w", err)
	}

	return recommendation, nil
}

// GetContentRecommendations retrieves recommendations for a content
func (s *ContentLinkServiceImpl) GetContentRecommendations(contentType models.ContentReferenceType, contentID uint, limit int) ([]models.ContentDiscussionRecommendation, error) {
	return s.contentLinkRepo.GetContentDiscussionRecommendations(contentType, contentID, limit)
}

// RemoveContentRecommendation removes a content recommendation
func (s *ContentLinkServiceImpl) RemoveContentRecommendation(recommendationID uint, userID uint) error {
	// For a real implementation, you might want to check if the user has permission to remove the recommendation
	return s.contentLinkRepo.DeleteContentDiscussionRecommendation(recommendationID)
}

// GenerateRecommendationsForContent generates recommendations for a content based on semantic similarity
func (s *ContentLinkServiceImpl) GenerateRecommendationsForContent(contentType models.ContentReferenceType, contentID uint) (int, error) {
	// Validate content type
	if !s.isValidContentType(contentType) {
		return 0, errors.New("invalid content type")
	}

	// Validate content exists
	if err := s.validateContent(contentType, contentID); err != nil {
		return 0, err
	}

	// In a real implementation, you would use a semantic similarity algorithm to find related topics
	// For now, we'll simulate it with a dummy implementation

	// Get content data
	contentData, err := s.getContentData(contentType, contentID)
	if err != nil {
		return 0, fmt.Errorf("error getting content data: %w", err)
	}

	// Get all topics
	topics, err := s.topicRepo.GetAllTopics(1, 100) // Limit for example purposes
	if err != nil {
		return 0, fmt.Errorf("error getting topics: %w", err)
	}

	// Generate recommendations
	recommendationCount := 0
	for _, topic := range topics {
		// Compute a dummy similarity score based on title match
		// In a real implementation, you'd use proper similarity algorithms
		var score float64 = 0

		if strings.Contains(strings.ToLower(topic.Title), strings.ToLower(contentData["title"].(string))) {
			score = 0.8
		} else if strings.Contains(strings.ToLower(topic.Content), strings.ToLower(contentData["title"].(string))) {
			score = 0.5
		} else {
			// Skip topics with no similarity
			continue
		}

		// Create recommendation
		recommendation := &models.ContentDiscussionRecommendation{
			ContentType:         contentType,
			ContentID:           contentID,
			TopicID:             topic.ID,
			RecommendationScore: score,
			IsManuallyAdded:     false,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}

		if err := s.contentLinkRepo.CreateContentDiscussionRecommendation(recommendation); err != nil {
			// Log error but continue
			fmt.Printf("Error creating recommendation: %v\n", err)
			continue
		}

		recommendationCount++
	}

	return recommendationCount, nil
}

// Helper functions

// isValidContentType checks if a content type is valid
func (s *ContentLinkServiceImpl) isValidContentType(contentType models.ContentReferenceType) bool {
	return contentType == models.BookReference ||
		contentType == models.ChapterReference ||
		contentType == models.SectionReference
}

// validateContent checks if the content exists
func (s *ContentLinkServiceImpl) validateContent(contentType models.ContentReferenceType, contentID uint) error {
	switch contentType {
	case models.BookReference:
		_, err := s.bookRepo.GetBookByID(contentID)
		return err
	case models.ChapterReference:
		_, err := s.chapterRepo.GetChapterByID(contentID)
		return err
	case models.SectionReference:
		_, err := s.sectionRepo.GetSectionByID(contentID)
		return err
	default:
		return errors.New("invalid content type")
	}
}

// validateTemplate validates a template
func (s *ContentLinkServiceImpl) validateTemplate(tmpl string) error {
	// Check for the required template variables
	validTemplateVars := []string{
		"{{.title}}",
		"{{.contentType}}",
		"{{.id}}",
	}

	for _, v := range validTemplateVars {
		if !strings.Contains(tmpl, v) {
			return fmt.Errorf("template missing required variable: %s", v)
		}
	}

	// Parse template to check for syntax errors
	_, err := template.New("validator").Parse(tmpl)
	return err
}

// executeTemplate executes a template with content data
func (s *ContentLinkServiceImpl) executeTemplate(tmpl string, data map[string]interface{}) (string, error) {
	t, err := template.New("template").Parse(tmpl)
	if err != nil {
		return "", err
	}

	var buf strings.Builder
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// getContentData gets data for a content
func (s *ContentLinkServiceImpl) getContentData(contentType models.ContentReferenceType, contentID uint) (map[string]interface{}, error) {
	data := make(map[string]interface{})
	data["contentType"] = string(contentType)
	data["id"] = contentID

	switch contentType {
	case models.BookReference:
		book, err := s.bookRepo.GetBookByID(contentID)
		if err != nil {
			return nil, err
		}
		data["title"] = book.Title
		data["description"] = book.Description
		data["author"] = book.Author

	case models.ChapterReference:
		chapter, err := s.chapterRepo.GetChapterByID(contentID)
		if err != nil {
			return nil, err
		}
		data["title"] = chapter.Title
		data["description"] = chapter.Description
		data["number"] = chapter.Number

		// Get parent book
		book, err := s.bookRepo.GetBookByID(chapter.BookID)
		if err == nil {
			data["bookTitle"] = book.Title
		}

	case models.SectionReference:
		section, err := s.sectionRepo.GetSectionByID(contentID)
		if err != nil {
			return nil, err
		}
		data["title"] = section.Title
		data["content"] = section.Content
		data["number"] = section.Number

		// Get parent chapter
		chapter, err := s.chapterRepo.GetChapterByID(section.ChapterID)
		if err == nil {
			data["chapterTitle"] = chapter.Title
			data["chapterNumber"] = chapter.Number

			// Get parent book
			book, err := s.bookRepo.GetBookByID(chapter.BookID)
			if err == nil {
				data["bookTitle"] = book.Title
			}
		}
	}

	return data, nil
}
