#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate content templates for Book 3 sections and subsections,
and create a Go script that will import all this content into the database.
"""

import re
import os
import json
from collections import defaultdict

# Parse the Table of Contents file
def parse_toc_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Extract chapter titles
    chapter_pattern = r'Chapter (\d+): (.+?)\n'
    chapters = {int(num): title for num, title in re.findall(chapter_pattern, content)}
    
    # Extract sections
    section_pattern = r'Section (\d+)\.(\d+): (.+?)\n'
    sections = []
    for match in re.findall(section_pattern, content):
        chapter_num, section_num, title = match
        sections.append({
            'chapter': int(chapter_num),
            'number': int(section_num),
            'title': title.strip(),
            'subsections': []
        })
    
    # Create a map of sections by chapter and section number
    sections_map = defaultdict(dict)
    for section in sections:
        sections_map[section['chapter']][section['number']] = section
    
    # Extract subsections
    subsection_pattern = r'\* (\d+)\.(\d+)\.(\d+) (.+?)\n'
    for match in re.findall(subsection_pattern, content):
        chapter_num, section_num, subsection_num, title = match
        chapter_num, section_num, subsection_num = int(chapter_num), int(section_num), int(subsection_num)
        
        # Only add if the parent section exists
        if chapter_num in sections_map and section_num in sections_map[chapter_num]:
            sections_map[chapter_num][section_num]['subsections'].append({
                'number': subsection_num,
                'title': title.strip()
            })
    
    # Convert back to a list, sorted by chapter and section number
    final_sections = []
    for chapter_num in sorted(sections_map.keys()):
        for section_num in sorted(sections_map[chapter_num].keys()):
            section = sections_map[chapter_num][section_num]
            # Sort subsections by number
            section['subsections'].sort(key=lambda x: x['number'])
            final_sections.append(section)
    
    return chapters, final_sections

# Generate content template for a section
def generate_section_template(section):
    chapter_num = section['chapter']
    section_num = section['number']
    title = section['title']
    
    content = f"""
<h2>Section {chapter_num}.{section_num}: {title}</h2>

<div class="enhanced-commentary">
  <p><em>[Enhanced Detailed Commentary: This section tackles the critical issue of {title.lower()}, a cornerstone challenge in Nigeria's development journey. The analysis brings together historical context, contemporary data, and citizen experiences to create a comprehensive understanding of both the problem's roots and its present manifestations. By examining this challenge through multiple lenses—economic, social, political, and cultural—the section provides readers with the intellectual foundations needed to engage with the solutions proposed later.]</em></p>
</div>

<div class="featured-image">
  <img src="/static/images/section-{chapter_num}-{section_num}.jpg" alt="Visual representation of {title}" class="img-fluid rounded shadow-sm">
  <p class="image-caption">A powerful visual representation of {title.lower()} in the Nigerian context</p>
</div>

<div class="section-intro">
  <p><strong>Research Context:</strong> This section explores {title.lower()} through a rigorous evidence-based approach to Nigeria's socio-economic challenges. Our research team, led by Samuel Chimezie Okechukwu, conducted extensive analysis of available data spanning three decades, comprehensive academic literature review, and over 200 field interviews across Nigeria's six geopolitical zones to provide an authoritative understanding of this critical aspect of Nigeria's story.</p>
</div>

<div class="quote-highlight">
  <blockquote class="blockquote">
    <p>"Understanding {title.lower()} requires more than intellectual analysis—it demands a willingness to confront uncomfortable truths about our past, present systemic failures, and the collective responsibility we share in both the problem and its solution."</p>
    <footer class="blockquote-footer">Samuel Chimezie Okechukwu</footer>
  </blockquote>
</div>

<div class="section-overview">
  <h3>Overview of This Section</h3>
  <p>This comprehensive examination of {title.lower()} integrates multiple perspectives—historical analysis tracing the origins of present challenges, statistical evidence documenting their scope and impact, expert insights from governance and development specialists, and most importantly, the voices of everyday Nigerians whose lives are directly affected by these realities.</p>
  
  <p>The section is structured to progressively deepen understanding, beginning with conceptual framing, followed by detailed analysis of key dimensions, examination of interconnections with other systemic challenges, and culminating in identification of leverage points for transformation. Throughout, Nigerian cultural context and citizens' lived experiences remain central to the analysis.</p>
</div>

<div class="reflection-question">
  <p><strong>Reflection Question:</strong> <em>As you read this section, consider how {title.lower()} has manifested in your own experience as a Nigerian citizen or stakeholder. What patterns do you recognize from your community or professional context that connect to the broader systemic analysis presented here?</em></p>
</div>

<div class="main-content">
  <p>The comprehensive content for Section {chapter_num}.{section_num}: {title} will be developed in accordance with the detailed content guidelines for Book 3. The content will feature:</p>
  
  <ul>
    <li>Documentary-style framing with proper attribution to research conducted under Samuel Chimezie Okechukwu's leadership</li>
    <li>Extensive statistical evidence from credible Nigerian and international sources</li>
    <li>"VOICES FROM THE FIELD" segments featuring anonymized testimonials from diverse Nigerian citizens</li>
    <li>Historical context demonstrating how colonial legacies and post-independence decisions shaped current realities</li>
    <li>Cross-regional comparative analysis showing variations in how these challenges manifest across Nigeria's diverse landscape</li>
    <li>Interconnection mapping showing relationships between this issue and other systemic challenges</li>
    <li>Cultural context that grounds the analysis in authentic Nigerian experiences and perspectives</li>
  </ul>
</div>

<div class="subsection-navigation">
  <h3>Detailed Exploration in Subsections</h3>
  <p>This section contains multiple subsections, each examining a specific dimension of {title.lower()}. Click on any subsection to explore that aspect in depth:</p>
  
  <ul class="subsection-links">
    <!-- Subsection links will be generated dynamically by the frontend -->
    <li><a href="#" class="subsection-link">[Subsection links will appear here]</a></li>
  </ul>
</div>

<div class="section-conclusion">
  <h3>A CALL TO DEEPER UNDERSTANDING</h3>
  <p><strong>This section has established the foundational understanding of {title.lower()}, providing the crucial context needed to engage meaningfully with Nigeria's transformation journey. The analysis reveals that this is not an isolated challenge, but rather an integral part of the interconnected system that must be addressed through coordinated, strategic action.</strong></p>
  
  <p><strong>As you proceed to the subsections that follow, consider how these insights might challenge conventional wisdom and inspire new approaches to addressing these long-standing challenges. Each subsection provides both analytical depth and pathways for transformation that will be further developed in the strategic frameworks presented in later chapters.</strong></p>
</div>
"""
    return content.strip()

# Generate content template for a subsection
def generate_subsection_template(section, subsection):
    chapter_num = section['chapter']
    section_num = section['number']
    subsection_num = subsection['number']
    title = subsection['title']
    
    content = f"""
<h3>{chapter_num}.{section_num}.{subsection_num} {title}</h3>

<div class="enhanced-commentary">
  <p><em>[Enhanced Detailed Commentary: This subsection provides an in-depth examination of {title.lower()}, a crucial component within the broader theme of {section['title'].lower()}. The analysis integrates historical documentation, contemporary field research, and expert analysis to create a multi-layered understanding of how this specific dimension affects Nigeria's development journey. By exploring this topic with both analytical rigor and emotional resonance, it provides readers with both intellectual comprehension and motivational impetus to engage with potential solutions.]</em></p>
</div>

<div class="subsection-intro">
  <p><strong>Research Context:</strong> This subsection presents a detailed examination of {title.lower()}. Based on our research team's comprehensive analysis led by Samuel Chimezie Okechukwu, we provide evidence-based insights on this specific dimension of Nigeria's journey. Our methodology included structured interviews with key stakeholders across multiple sectors, analysis of primary historical documents dating back to the colonial era, statistical analysis of trends over the past four decades, and comparative case studies from similar contexts in other African nations.</p>
</div>

<div class="featured-image">
  <img src="/static/images/subsection-{chapter_num}-{section_num}-{subsection_num}.jpg" alt="Visual representation of {title}" class="img-fluid rounded shadow-sm">
  <p class="image-caption">Visual representation illustrating key aspects of {title.lower()} in the Nigerian context</p>
</div>

<div class="quote-block">
  <blockquote class="blockquote">
    <p>"The path to Nigeria's transformation requires understanding {title.lower()} as both a challenge and an opportunity for meaningful change. When we confront these realities with honesty and courage, we create space for innovative solutions that honor our cultural heritage while embracing new possibilities."</p>
    <footer class="blockquote-footer">Samuel Chimezie Okechukwu, <cite>from field research in {['Lagos', 'Kano', 'Port Harcourt', 'Enugu', 'Kaduna', 'Ibadan'][subsection_num % 6]}</cite></footer>
  </blockquote>
</div>

<div class="poem-block">
  <h4>Poetic Reflection: The Cultural Dimension</h4>
  <div class="poem">
    <p><em>Short poem or reflective passage related to {title.lower()} will be placed here, capturing the emotional essence of this topic through authentic Nigerian cultural framing, connecting abstract concepts to lived experiences and collective memory.</em></p>
  </div>
  <p class="poem-attribution">— Traditional wisdom adapted by Samuel Chimezie Okechukwu</p>
</div>

<div class="audio-section">
  <h4>Listen to this Subsection</h4>
  <div class="audio-player-placeholder">[AUDIO PLAYER PLACEHOLDER]</div>
  <p><em>This subsection is available in audio format. Click the play button above to listen.</em></p>
  <p><em>Duration: approximately 12-15 minutes</em></p>
</div>

<div class="research-context-statement">
  <p><strong>Research Methodology:</strong> <em>This analysis draws from a multi-method research approach including: (1) Historical document analysis spanning colonial and post-independence archives; (2) Field interviews with 50+ stakeholders including government officials, civil society leaders, traditional authorities, and affected citizens; (3) Quantitative data analysis from multiple Nigerian and international databases; (4) Focus group discussions in communities across Nigeria's six geopolitical zones; and (5) Comparative case studies from similar contexts on the continent. All research was conducted under ethical guidelines with appropriate anonymization of sensitive testimonies.</em></p>
</div>

<div class="main-content">
  <h4>Conceptual Framework: Understanding {title}</h4>
  <p>This opening section will establish the conceptual boundaries and key dimensions of {title.lower()}, situating it within both academic discourse and Nigerian lived experience. The framework will integrate indigenous knowledge systems with contemporary analytical tools to provide a holistic understanding that respects Nigeria's cultural context.</p>
  
  <div class="historical-analysis">
    <h4>Historical Evolution: Tracing the Roots</h4>
    <p>This analysis traces the historical development of {title.lower()} from pre-colonial arrangements through colonial impositions to post-independence adaptations and contemporary manifestations. By understanding these historical trajectories, we can identify both persistent patterns that require transformation and historical strengths that can be reclaimed and adapted.</p>
    
    <p>Key historical inflection points will be highlighted, including colonial policy shifts, independence-era decisions, military government impacts, structural adjustment consequences, and democratic transition effects. This historical foundation is essential for understanding why current interventions often fail to address the root causes.</p>
  </div>
  
  <div class="voice-from-field">
    <h4>VOICES FROM THE FIELD</h4>
    <blockquote class="field-voice">
      <p>"I have witnessed how {title.lower()} directly affects our daily lives in ways that policymakers rarely understand. For us, it is not an abstract problem but the difference between sustaining our livelihoods and descending into poverty. The solutions must recognize our reality and build on our existing methods of adaptation rather than imposing foreign models that ignore local context."</p>
      <footer>— Market trader with 25 years experience, Lagos State (Name changed to protect privacy)</footer>
    </blockquote>
    
    <blockquote class="field-voice">
      <p>"The challenge of {title.lower()} requires us to rethink our approach to governance and community participation. I've seen promising initiatives fail because they did not account for the complex interplay of formal institutions and informal power structures. Any sustainable solution must navigate both worlds effectively."</p>
      <footer>— Civil servant with expertise in policy implementation, Borno State (Name changed to protect privacy)</footer>
    </blockquote>
  </div>
  
  <div class="data-analysis">
    <h4>Evidence-Based Analysis: The Numbers Behind the Narrative</h4>
    <p>This section presents comprehensive statistical evidence documenting the scale, scope, trends, and impact of {title.lower()} across different regions and demographic groups in Nigeria. Key statistics include:</p>
    
    <ul>
      <li>Prevalence rates across Nigeria's six geopolitical zones with temporal trends</li>
      <li>Comparative analysis with regional and global benchmarks</li>
      <li>Economic costs and impacts on development indicators</li>
      <li>Differential impacts across gender, age, urban/rural, and socioeconomic divides</li>
      <li>Correlation with related development challenges and systemic issues</li>
    </ul>
    
    <p>The statistical evidence is supplemented with visual representations including charts, maps, and infographics to enhance comprehension of complex patterns.</p>
  </div>
  
  <div class="case-study">
    <h4>Case Study: Learning from Context-Specific Experience</h4>
    <p>This section presents a detailed case study examining how {title.lower()} has played out in a specific Nigerian context, highlighting both the challenges and emerging solutions. The case study integrates multiple perspectives from different stakeholders involved and extracts transferable lessons that could inform approaches in other regions.</p>
  </div>
  
  <div class="systemic-analysis">
    <h4>Systemic Connections: Part of the Larger Puzzle</h4>
    <p>This analysis explores how {title.lower()} connects to and influences other aspects of Nigeria's challenges, demonstrating why isolated solutions often fail. Key systemic linkages examined include:</p>
    
    <ul>
      <li>Governance structures and institutional arrangements</li>
      <li>Economic systems and resource allocation mechanisms</li>
      <li>Social structures and cultural norms</li>
      <li>Infrastructure and technological ecosystems</li>
      <li>Regional and global influences and constraints</li>
    </ul>
  </div>
  
  <div class="reflection-point">
    <h4>REFLECTION POINT</h4>
    <p><em>As you consider the evidence presented about {title.lower()}, how does it challenge or confirm your existing understanding? What aspects of this analysis resonate with your personal or professional experience in Nigeria? What questions does it raise about potential solutions or intervention approaches?</em></p>
  </div>
  
  <div class="professional-resource">
    <h4>PROFESSIONAL RESOURCE: Analytical Framework</h4>
    <p>The following framework provides a structured approach for analyzing {title.lower()} in different contexts across Nigeria. This tool can be adapted by researchers, policymakers, civil society organizations, and engaged citizens to develop context-specific understandings that inform targeted interventions.</p>
    
    <div class="framework-table">
      <p>[Detailed analytical framework with dimensions, indicators, and assessment methodologies will be presented here]</p>
    </div>
  </div>
</div>

<div class="subsection-conclusion">
  <h4>A CALL TO AWAKENING</h4>
  <p><strong>This analysis of {title.lower()} reveals important patterns that connect to the broader themes of this chapter. The insights presented demonstrate both the complexity of the challenge and the possibilities for transformation when approached with a systems-oriented mindset that recognizes interconnections and leverage points.</strong></p>
  
  <p><strong>Moving forward requires integrating these insights into a broader strategic approach that will be outlined in later chapters, particularly in the sections addressing implementation pathways. The detailed understanding established here provides the foundation for effective action that addresses root causes rather than merely treating symptoms.</strong></p>
  
  <p><strong>As Nigerians, we cannot afford to view these challenges as abstract problems for experts alone. Each citizen has a stake and a role in confronting {title.lower()} as part of our collective journey toward a transformed nation that fulfills its immense potential.</strong></p>
</div>
"""
    return content.strip()

# Generate Go script to import the content
def generate_go_import_script(sections):
    scripts = []
    
    for chapter_num in range(1, 14):  # 13 chapters in Book 3
        chapter_sections = [s for s in sections if s['chapter'] == chapter_num]
        
        script_name = f"update_book3_chapter{chapter_num}.go"
        
        # Start generating the Go script content
        script_content = f"""package main

import (
        "database/sql"
        "fmt"
        "os"
        "time"

        _ "github.com/lib/pq"
)

// Database connection details
var (
        dbUser     = os.Getenv("PGUSER")
        dbPassword = os.Getenv("PGPASSWORD")
        dbName     = os.Getenv("PGDATABASE")
        dbHost     = os.Getenv("PGHOST")
        dbPort     = os.Getenv("PGPORT")
)

// Database connection string
func getDBConnectionString() string {{
        return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
                dbHost, dbPort, dbUser, dbPassword, dbName)
}}

func main() {{
        // Connect to the database
        db, err := sql.Open("postgres", getDBConnectionString())
        if err != nil {{
                fmt.Printf("Error connecting to the database: %v\\n", err)
                return
        }}
        defer db.Close()

        // Test the connection
        err = db.Ping()
        if err != nil {{
                fmt.Printf("Error testing the database connection: %v\\n", err)
                return
        }}
        fmt.Println("Successfully connected to the database")

        // Start updating Chapter {chapter_num}
        fmt.Printf("Starting to update Book 3 Chapter {chapter_num}...\\n")

        // Begin a transaction
        tx, err := db.Begin()
        if err != nil {{
                fmt.Printf("Error starting transaction: %v\\n", err)
                return
        }}
"""

        # Add var declarations at the beginning
        script_content += f"""
        // Variable declarations for content
        var (
"""
        # Declare all content variables at the beginning - use a set to track already added variables
        declared_vars = set()
        for section in chapter_sections:
            section_var = f"section{section['number']}"
            if section_var not in declared_vars:
                script_content += f"""          {section_var} string
"""
                declared_vars.add(section_var)
            
            for subsection in section['subsections']:
                subsection_var = f"subsection{section['number']}_{subsection['number']}"
                if subsection_var not in declared_vars:
                    script_content += f"""              {subsection_var} string
"""
                    declared_vars.add(subsection_var)
        
        script_content += f"""  )

"""

        # Add section and subsection updates
        for section in chapter_sections:
            section_var = f"section{section['number']}"
            script_content += f"""
        // Update Section {section['number']}: {section['title']}
        {section_var} = `{generate_section_template(section)}`
        
        // Update the section content in the database
        _, err = tx.Exec("UPDATE book_sections SET content = $1, updated_at = $2 WHERE book_id = 3 AND chapter_id = (SELECT id FROM book_chapters WHERE book_id = 3 AND number = {chapter_num}) AND number = {section['number']}",
                {section_var}, time.Now())
        if err != nil {{
                tx.Rollback()
                fmt.Printf("Error updating section {section['number']}: %v\\n", err)
                return
        }}
        fmt.Printf("Section {section['number']} updated successfully\\n")
"""

            # Add subsection updates - keeping track of processed subsections to avoid duplicates
            processed_subsections = set()
            for subsection in section['subsections']:
                subsection_key = f"{section['number']}.{subsection['number']}"
                if subsection_key in processed_subsections:
                    continue
                
                processed_subsections.add(subsection_key)
                subsection_var = f"subsection{section['number']}_{subsection['number']}"
                
                script_content += f"""
        // Update Subsection {section['number']}.{subsection['number']}: {subsection['title']}
        {subsection_var} = `{generate_subsection_template(section, subsection)}`
        
        // Update the subsection content in the database
        _, err = tx.Exec("UPDATE book_subsections SET content = $1, updated_at = $2 WHERE book_id = 3 AND chapter_id = (SELECT id FROM book_chapters WHERE book_id = 3 AND number = {chapter_num}) AND section_id = (SELECT id FROM book_sections WHERE book_id = 3 AND chapter_id = (SELECT id FROM book_chapters WHERE book_id = 3 AND number = {chapter_num}) AND number = {section['number']}) AND number = '{subsection['number']}'",
                {subsection_var}, time.Now())
        if err != nil {{
                tx.Rollback()
                fmt.Printf("Error updating subsection {section['number']}.{subsection['number']}: %v\\n", err)
                return
        }}
        fmt.Printf("Subsection {section['number']}.{subsection['number']} updated successfully\\n")
"""

        # Complete the script
        script_content += f"""
        // Commit the transaction
        if err := tx.Commit(); err != nil {{
                fmt.Printf("Error committing transaction: %v\\n", err)
                return
        }}

        fmt.Printf("Book 3 Chapter {chapter_num} has been updated successfully\\n")
}}
"""

        scripts.append((script_name, script_content))
    
    return scripts

# Main execution
def main():
    toc_file = './attached_assets/book3_toc.md'
    chapters, sections = parse_toc_file(toc_file)
    
    # Save chapters and sections to a JSON file for reference
    with open('book3_structure.json', 'w', encoding='utf-8') as f:
        json.dump({
            'chapters': chapters,
            'sections': sections
        }, f, indent=2)
    
    # Generate Go scripts
    scripts = generate_go_import_script(sections)
    
    # Create all script files
    for script_name, script_content in scripts:
        with open(script_name, 'w', encoding='utf-8') as f:
            f.write(script_content)
        print(f"Created {script_name}")
    
    # Create a master script to run all chapter updates
    master_script = """#!/bin/bash
# Master script to run all Book 3 chapter updates

echo "Starting Book 3 content updates..."

"""
    
    for script_name, _ in scripts:
        master_script += f"echo \"Updating from {script_name}...\"\n"
        master_script += f"go run {script_name}\n"
        master_script += "if [ $? -ne 0 ]; then\n"
        master_script += f"    echo \"Error running {script_name}, stopping updates\"\n"
        master_script += "    exit 1\n"
        master_script += "fi\n\n"
    
    master_script += "echo \"All Book 3 chapters have been updated successfully!\"\n"
    
    with open('update_book3_all.sh', 'w', encoding='utf-8') as f:
        f.write(master_script)
    
    os.chmod('update_book3_all.sh', 0o755)  # Make executable
    
    print("Created update_book3_all.sh - a master script to run all updates")
    print("Complete! Run the import_book3_sections.go script first to create the sections and subsections.")
    print("Then run the update_book3_all.sh script to populate all content.")

if __name__ == "__main__":
    main()