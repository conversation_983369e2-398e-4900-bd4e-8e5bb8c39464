package service

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jung-kurt/gofpdf"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/repository"
)

// ReceiptServiceImpl implements the ReceiptService interface
type ReceiptServiceImpl struct {
	receiptRepo    repository.ReceiptRepository
	paymentRepo    repository.PaymentRepository
	subscriptionRepo repository.SubscriptionRepository
	refundRepo     repository.RefundRepository
	storageBasePath string
	publicBaseURL   string
}

// NewReceiptService creates a new receipt service
func NewReceiptService(
	receiptRepo repository.ReceiptRepository,
	paymentRepo repository.PaymentRepository,
	subscriptionRepo repository.SubscriptionRepository,
	refundRepo repository.RefundRepository,
	storageBasePath string,
	publicBaseURL string,
) ReceiptService {
	return &ReceiptServiceImpl{
		receiptRepo:    receiptRepo,
		paymentRepo:    paymentRepo,
		subscriptionRepo: subscriptionRepo,
		refundRepo:     refundRepo,
		storageBasePath: storageBasePath,
		publicBaseURL:   publicBaseURL,
	}
}

// GenerateReceiptForPayment generates a receipt for a payment
func (s *ReceiptServiceImpl) GenerateReceiptForPayment(ctx context.Context, paymentID uint) (*models.Receipt, error) {
	// Check if receipt already exists
	existingReceipt, err := s.receiptRepo.GetReceiptByPaymentID(ctx, paymentID)
	if err == nil && existingReceipt != nil {
		return existingReceipt, nil
	}

	// Get payment information
	payment, err := s.paymentRepo.GetPaymentByID(ctx, paymentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	// Only generate receipt for successful payments
	if payment.Status != models.StatusSucceeded {
		return nil, fmt.Errorf("cannot generate receipt for payment with status: %s", payment.Status)
	}

	// Create receipt record
	now := time.Now()
	receiptNumber := generateReceiptNumber("P", payment.ID, now)
	receipt := &models.Receipt{
		UserID:        payment.UserID,
		PaymentID:     &payment.ID,
		Type:          models.ReceiptTypePayment,
		Status:        models.ReceiptStatusPending,
		ReceiptNumber: receiptNumber,
		Amount:        payment.Amount,
		Currency:      payment.Currency,
		Description:   payment.Description,
		IssuedAt:      now,
	}

	if err := s.receiptRepo.CreateReceipt(ctx, receipt); err != nil {
		return nil, fmt.Errorf("failed to create receipt: %w", err)
	}

	// Generate PDF asynchronously
	go func() {
		bgCtx := context.Background()
		if err := s.generateAndStorePDF(bgCtx, receipt); err != nil {
			s.receiptRepo.UpdateReceiptStatus(bgCtx, receipt.ID, models.ReceiptStatusFailed)
			return
		}
	}()

	return receipt, nil
}

// GenerateReceiptForSubscription generates a receipt for a subscription payment
func (s *ReceiptServiceImpl) GenerateReceiptForSubscription(ctx context.Context, subscriptionID uint) (*models.Receipt, error) {
	// Check if receipt already exists
	existingReceipt, err := s.receiptRepo.GetReceiptBySubscriptionID(ctx, subscriptionID)
	if err == nil && existingReceipt != nil {
		return existingReceipt, nil
	}

	// Get subscription information
	subscription, err := s.subscriptionRepo.GetSubscriptionByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// Only generate receipt for active subscriptions
	if subscription.Status != models.SubscriptionStatusActive {
		return nil, fmt.Errorf("cannot generate receipt for subscription with status: %s", subscription.Status)
	}

	// Get plan information
	plan, err := s.subscriptionRepo.GetPlanByID(ctx, subscription.PlanID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription plan: %w", err)
	}

	// Create receipt record
	now := time.Now()
	receiptNumber := generateReceiptNumber("S", subscription.ID, now)
	receipt := &models.Receipt{
		UserID:         subscription.UserID,
		SubscriptionID: &subscription.ID,
		Type:           models.ReceiptTypeSubscription,
		Status:         models.ReceiptStatusPending,
		ReceiptNumber:  receiptNumber,
		Amount:         plan.Amount,
		Currency:       plan.Currency,
		Description:    fmt.Sprintf("Subscription to %s", plan.Name),
		IssuedAt:       now,
	}

	if err := s.receiptRepo.CreateReceipt(ctx, receipt); err != nil {
		return nil, fmt.Errorf("failed to create receipt: %w", err)
	}

	// Generate PDF asynchronously
	go func() {
		bgCtx := context.Background()
		if err := s.generateAndStorePDF(bgCtx, receipt); err != nil {
			s.receiptRepo.UpdateReceiptStatus(bgCtx, receipt.ID, models.ReceiptStatusFailed)
			return
		}
	}()

	return receipt, nil
}

// GenerateReceiptForRefund generates a receipt for a refund
func (s *ReceiptServiceImpl) GenerateReceiptForRefund(ctx context.Context, refundID uint) (*models.Receipt, error) {
	// Check if receipt already exists
	existingReceipt, err := s.receiptRepo.GetReceiptByRefundID(ctx, refundID)
	if err == nil && existingReceipt != nil {
		return existingReceipt, nil
	}

	// Get refund information
	refund, err := s.refundRepo.GetRefundByID(ctx, refundID)
	if err != nil {
		return nil, fmt.Errorf("failed to get refund: %w", err)
	}

	// Create receipt record
	now := time.Now()
	receiptNumber := generateReceiptNumber("R", refund.ID, now)
	receipt := &models.Receipt{
		UserID:        refund.UserID,
		RefundID:      &refund.ID,
		Type:          models.ReceiptTypeRefund,
		Status:        models.ReceiptStatusPending,
		ReceiptNumber: receiptNumber,
		Amount:        refund.Amount,
		Currency:      refund.Currency,
		Description:   fmt.Sprintf("Refund: %s", refund.Reason),
		IssuedAt:      now,
	}

	if err := s.receiptRepo.CreateReceipt(ctx, receipt); err != nil {
		return nil, fmt.Errorf("failed to create receipt: %w", err)
	}

	// Generate PDF asynchronously
	go func() {
		bgCtx := context.Background()
		if err := s.generateAndStorePDF(bgCtx, receipt); err != nil {
			s.receiptRepo.UpdateReceiptStatus(bgCtx, receipt.ID, models.ReceiptStatusFailed)
			return
		}
	}()

	return receipt, nil
}

// GetReceiptByID retrieves a receipt by its ID
func (s *ReceiptServiceImpl) GetReceiptByID(ctx context.Context, id uint) (*models.Receipt, error) {
	return s.receiptRepo.GetReceiptByID(ctx, id)
}

// GetReceiptByNumber retrieves a receipt by its receipt number
func (s *ReceiptServiceImpl) GetReceiptByNumber(ctx context.Context, receiptNumber string) (*models.Receipt, error) {
	return s.receiptRepo.GetReceiptByNumber(ctx, receiptNumber)
}

// GetReceiptsByUserID retrieves all receipts for a user
func (s *ReceiptServiceImpl) GetReceiptsByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.Receipt, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	return s.receiptRepo.GetReceiptsByUserID(ctx, userID, pageSize, offset)
}

// GetReceiptPDF retrieves the PDF for a receipt
func (s *ReceiptServiceImpl) GetReceiptPDF(ctx context.Context, id uint) (io.ReadCloser, string, error) {
	receipt, err := s.receiptRepo.GetReceiptByID(ctx, id)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get receipt: %w", err)
	}

	if receipt.Status != models.ReceiptStatusGenerated {
		return nil, "", fmt.Errorf("receipt PDF not yet generated")
	}

	if receipt.PDFPath == "" {
		return nil, "", fmt.Errorf("receipt PDF path not found")
	}

	file, err := os.Open(receipt.PDFPath)
	if err != nil {
		return nil, "", fmt.Errorf("failed to open receipt PDF: %w", err)
	}

	filename := fmt.Sprintf("receipt_%s.pdf", receipt.ReceiptNumber)
	return file, filename, nil
}

// EmailReceiptToUser sends a receipt to a user by email
func (s *ReceiptServiceImpl) EmailReceiptToUser(ctx context.Context, id uint, emailAddress string) error {
	receipt, err := s.receiptRepo.GetReceiptByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get receipt: %w", err)
	}

	if receipt.Status != models.ReceiptStatusGenerated {
		return fmt.Errorf("receipt PDF not yet generated")
	}

	// TODO: Implement email sending logic
	// For now, just update the emailed status
	return s.receiptRepo.UpdateReceiptEmailInfo(ctx, id, emailAddress)
}

// DeleteReceipt deletes a receipt by its ID
func (s *ReceiptServiceImpl) DeleteReceipt(ctx context.Context, id uint) error {
	receipt, err := s.receiptRepo.GetReceiptByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get receipt: %w", err)
	}

	// Delete the PDF file if it exists
	if receipt.PDFPath != "" {
		if err := os.Remove(receipt.PDFPath); err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("failed to delete receipt PDF: %w", err)
		}
	}

	return s.receiptRepo.DeleteReceipt(ctx, id)
}

// CreateReceiptTemplate creates a new receipt template
func (s *ReceiptServiceImpl) CreateReceiptTemplate(
	ctx context.Context,
	name, templateContent, headerImagePath, footerText, customCSS string,
	isDefault bool,
	createdBy uint,
) (*models.ReceiptTemplate, error) {
	template := &models.ReceiptTemplate{
		Name:            name,
		Type:            "html",
		TemplateContent: templateContent,
		HeaderImagePath: headerImagePath,
		FooterText:      footerText,
		CustomCSS:       customCSS,
		IsDefault:       isDefault,
		IsActive:        true,
		CreatedBy:       createdBy,
		UpdatedBy:       createdBy,
	}

	if err := s.receiptRepo.CreateReceiptTemplate(ctx, template); err != nil {
		return nil, fmt.Errorf("failed to create receipt template: %w", err)
	}

	return template, nil
}

// GetReceiptTemplateByID retrieves a receipt template by its ID
func (s *ReceiptServiceImpl) GetReceiptTemplateByID(ctx context.Context, id uint) (*models.ReceiptTemplate, error) {
	return s.receiptRepo.GetReceiptTemplateByID(ctx, id)
}

// GetDefaultReceiptTemplate retrieves the default receipt template
func (s *ReceiptServiceImpl) GetDefaultReceiptTemplate(ctx context.Context) (*models.ReceiptTemplate, error) {
	return s.receiptRepo.GetDefaultReceiptTemplate(ctx)
}

// UpdateReceiptTemplate updates a receipt template
func (s *ReceiptServiceImpl) UpdateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error {
	return s.receiptRepo.UpdateReceiptTemplate(ctx, template)
}

// DeleteReceiptTemplate deletes a receipt template by its ID
func (s *ReceiptServiceImpl) DeleteReceiptTemplate(ctx context.Context, id uint) error {
	return s.receiptRepo.DeleteReceiptTemplate(ctx, id)
}

// CreateReceiptCustomization creates a new receipt customization
func (s *ReceiptServiceImpl) CreateReceiptCustomization(ctx context.Context, receiptID uint, customization *models.ReceiptCustomization) error {
	customization.ReceiptID = receiptID
	return s.receiptRepo.CreateReceiptCustomization(ctx, customization)
}

// GetReceiptCustomizationByReceiptID retrieves a receipt customization by its receipt ID
func (s *ReceiptServiceImpl) GetReceiptCustomizationByReceiptID(ctx context.Context, receiptID uint) (*models.ReceiptCustomization, error) {
	return s.receiptRepo.GetReceiptCustomizationByReceiptID(ctx, receiptID)
}

// UpdateReceiptCustomization updates a receipt customization
func (s *ReceiptServiceImpl) UpdateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error {
	return s.receiptRepo.UpdateReceiptCustomization(ctx, customization)
}

// DeleteReceiptCustomization deletes a receipt customization by its ID
func (s *ReceiptServiceImpl) DeleteReceiptCustomization(ctx context.Context, id uint) error {
	return s.receiptRepo.DeleteReceiptCustomization(ctx, id)
}

// Helper functions

// generateReceiptNumber generates a unique receipt number
func generateReceiptNumber(prefix string, id uint, timestamp time.Time) string {
	dateStr := timestamp.Format("20060102")
	return fmt.Sprintf("%s%s%06d", prefix, dateStr, id)
}

// generateAndStorePDF generates a PDF for the receipt and stores it
func (s *ReceiptServiceImpl) generateAndStorePDF(ctx context.Context, receipt *models.Receipt) error {
	// Create directory if it doesn't exist
	receiptDir := filepath.Join(s.storageBasePath, "receipts", time.Now().Format("2006/01/02"))
	if err := os.MkdirAll(receiptDir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Generate PDF filename
	pdfFilename := fmt.Sprintf("%s.pdf", receipt.ReceiptNumber)
	pdfPath := filepath.Join(receiptDir, pdfFilename)
	publicURL := fmt.Sprintf("%s/receipts/%s/%s", s.publicBaseURL, time.Now().Format("2006/01/02"), pdfFilename)

	// Generate PDF content
	pdfContent, err := s.createPDF(ctx, receipt)
	if err != nil {
		return fmt.Errorf("failed to create PDF: %w", err)
	}

	// Write PDF to file
	if err := os.WriteFile(pdfPath, pdfContent, 0644); err != nil {
		return fmt.Errorf("failed to write PDF: %w", err)
	}

	// Calculate SHA-256 hash for integrity verification
	hash := sha256.Sum256(pdfContent)
	contentHash := hex.EncodeToString(hash[:])

	// Update receipt with PDF info
	if err := s.receiptRepo.UpdateReceiptPDFInfo(ctx, receipt.ID, pdfPath, publicURL, contentHash); err != nil {
		return fmt.Errorf("failed to update receipt PDF info: %w", err)
	}

	// Update receipt status
	return s.receiptRepo.UpdateReceiptStatus(ctx, receipt.ID, models.ReceiptStatusGenerated)
}

// createPDF generates a PDF document for the receipt
func (s *ReceiptServiceImpl) createPDF(ctx context.Context, receipt *models.Receipt) ([]byte, error) {
	// Create a new PDF
	pdf := gofpdf.New("P", "mm", "A4", "")
	
	// Set up the document
	pdf.SetTitle(fmt.Sprintf("Receipt #%s", receipt.ReceiptNumber), true)
	pdf.SetAuthor("Great Nigeria", true)
	pdf.SetCreationDate(time.Now())
	
	// Add a page
	pdf.AddPage()
	
	// Set default font
	pdf.SetFont("Arial", "B", 16)
	
	// Add company logo and header
	s.addHeader(pdf)
	
	// Add receipt information
	s.addReceiptInfo(pdf, receipt)
	
	// Add payment details based on receipt type
	switch receipt.Type {
	case models.ReceiptTypePayment:
		if receipt.PaymentID != nil {
			s.addPaymentDetails(ctx, pdf, *receipt.PaymentID)
		}
	case models.ReceiptTypeSubscription:
		if receipt.SubscriptionID != nil {
			s.addSubscriptionDetails(ctx, pdf, *receipt.SubscriptionID)
		}
	case models.ReceiptTypeRefund:
		if receipt.RefundID != nil {
			s.addRefundDetails(ctx, pdf, *receipt.RefundID)
		}
	}
	
	// Add footer with terms
	s.addFooter(pdf)
	
	// Get the PDF as a byte array
	return pdf.Output()
}

// addHeader adds company information and logo to the PDF
func (s *ReceiptServiceImpl) addHeader(pdf *gofpdf.Fpdf) {
	// Add company name
	pdf.SetFont("Arial", "B", 20)
	pdf.Cell(190, 10, "Great Nigeria")
	pdf.Ln(15)
	
	// Add company address and info
	pdf.SetFont("Arial", "", 10)
	pdf.Cell(190, 5, "Great Nigeria Initiative")
	pdf.Ln(5)
	pdf.Cell(190, 5, "Lagos, Nigeria")
	pdf.Ln(5)
	pdf.Cell(190, 5, "Email: <EMAIL>")
	pdf.Ln(5)
	pdf.Cell(190, 5, "Website: www.greatnigeria.org")
	pdf.Ln(15)
}

// addReceiptInfo adds receipt information to the PDF
func (s *ReceiptServiceImpl) addReceiptInfo(pdf *gofpdf.Fpdf, receipt *models.Receipt) {
	// Add receipt title
	pdf.SetFont("Arial", "B", 16)
	var title string
	switch receipt.Type {
	case models.ReceiptTypePayment:
		title = "Payment Receipt"
	case models.ReceiptTypeSubscription:
		title = "Subscription Receipt"
	case models.ReceiptTypeRefund:
		title = "Refund Receipt"
	default:
		title = "Receipt"
	}
	pdf.Cell(190, 10, title)
	pdf.Ln(15)
	
	// Add receipt details
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Receipt Number:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, receipt.ReceiptNumber)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Date:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, receipt.IssuedAt.Format("January 2, 2006"))
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Amount:")
	pdf.SetFont("Arial", "", 12)
	
	// Format amount properly (converting from smallest unit to regular currency)
	var amountStr string
	switch receipt.Currency {
	case models.CurrencyNGN:
		amountStr = fmt.Sprintf("₦%.2f", float64(receipt.Amount)/100.0)
	case models.CurrencyUSD:
		amountStr = fmt.Sprintf("$%.2f", float64(receipt.Amount)/100.0)
	default:
		amountStr = fmt.Sprintf("%.2f %s", float64(receipt.Amount)/100.0, receipt.Currency)
	}
	pdf.Cell(140, 8, amountStr)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Description:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, receipt.Description)
	pdf.Ln(15)
}

// addPaymentDetails adds payment-specific details to the PDF
func (s *ReceiptServiceImpl) addPaymentDetails(ctx context.Context, pdf *gofpdf.Fpdf, paymentID uint) {
	payment, err := s.paymentRepo.GetPaymentByID(ctx, paymentID)
	if err != nil {
		// Just log the error and continue
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(190, 5, "Payment details not available")
		pdf.Ln(10)
		return
	}
	
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(190, 8, "Payment Details")
	pdf.Ln(10)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Payment Method:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, payment.PaymentMethodType)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Payment Date:")
	pdf.SetFont("Arial", "", 12)
	
	paymentDate := "N/A"
	if payment.PaidAt != nil {
		paymentDate = payment.PaidAt.Format("January 2, 2006 15:04:05")
	}
	pdf.Cell(140, 8, paymentDate)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Reference:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, payment.ProviderReference)
	pdf.Ln(15)
}

// addSubscriptionDetails adds subscription-specific details to the PDF
func (s *ReceiptServiceImpl) addSubscriptionDetails(ctx context.Context, pdf *gofpdf.Fpdf, subscriptionID uint) {
	subscription, err := s.subscriptionRepo.GetSubscriptionByID(ctx, subscriptionID)
	if err != nil {
		// Just log the error and continue
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(190, 5, "Subscription details not available")
		pdf.Ln(10)
		return
	}
	
	plan, err := s.subscriptionRepo.GetPlanByID(ctx, subscription.PlanID)
	if err != nil {
		// Just log the error and continue
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(190, 5, "Subscription plan details not available")
		pdf.Ln(10)
		return
	}
	
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(190, 8, "Subscription Details")
	pdf.Ln(10)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Plan Name:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, plan.Name)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Period Start:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, subscription.CurrentPeriodStart.Format("January 2, 2006"))
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Period End:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, subscription.CurrentPeriodEnd.Format("January 2, 2006"))
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Billing Interval:")
	pdf.SetFont("Arial", "", 12)
	var intervalStr string
	if plan.IntervalCount > 1 {
		intervalStr = fmt.Sprintf("Every %d %ss", plan.IntervalCount, plan.Interval)
	} else {
		intervalStr = fmt.Sprintf("%s", capitalizeFirst(string(plan.Interval)))
	}
	pdf.Cell(140, 8, intervalStr)
	pdf.Ln(15)
}

// addRefundDetails adds refund-specific details to the PDF
func (s *ReceiptServiceImpl) addRefundDetails(ctx context.Context, pdf *gofpdf.Fpdf, refundID uint) {
	refund, err := s.refundRepo.GetRefundByID(ctx, refundID)
	if err != nil {
		// Just log the error and continue
		pdf.SetFont("Arial", "I", 10)
		pdf.Cell(190, 5, "Refund details not available")
		pdf.Ln(10)
		return
	}
	
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(190, 8, "Refund Details")
	pdf.Ln(10)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Original Payment:")
	pdf.SetFont("Arial", "", 12)
	
	originalPayment, err := s.paymentRepo.GetPaymentByID(ctx, refund.PaymentID)
	paymentRef := "N/A"
	if err == nil {
		paymentRef = originalPayment.ProviderReference
	}
	pdf.Cell(140, 8, paymentRef)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Refund Reason:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, refund.Reason)
	pdf.Ln(8)
	
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Refund Date:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(140, 8, refund.CreatedAt.Format("January 2, 2006"))
	pdf.Ln(15)
}

// addFooter adds footer information to the PDF
func (s *ReceiptServiceImpl) addFooter(pdf *gofpdf.Fpdf) {
	// Add separator line
	pdf.SetLineWidth(0.5)
	pdf.Line(10, pdf.GetY(), 200, pdf.GetY())
	pdf.Ln(10)
	
	// Add thank you message
	pdf.SetFont("Arial", "I", 10)
	pdf.Cell(190, 5, "Thank you for your support. Together, we are building a Greater Nigeria.")
	pdf.Ln(5)
	
	// Add terms and conditions
	pdf.SetFont("Arial", "", 8)
	pdf.Cell(190, 4, "This is an official receipt issued by Great Nigeria. For questions, <NAME_EMAIL>.")
	pdf.Ln(4)
	pdf.Cell(190, 4, "This document is computer-generated and valid without signature.")
}

// capitalizeFirst capitalizes the first letter of a string
func capitalizeFirst(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(s[:1]) + s[1:]
}