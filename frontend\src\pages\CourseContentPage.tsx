import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Breadcrumbs,
  Link as MuiLink,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  PlayArrow as PlayArrowIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  ExpandMore as ExpandMoreIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { AppDispatch, RootState } from '../store';
import {
  fetchCourseByID,
  fetchModulesByCourseID,
  createModule,
  updateModule,
  deleteModule,
  createLesson,
  updateLesson,
  deleteLesson,
} from '../store/slices/coursesSlice';
import { Module, Lesson } from '../services/coursesService';

const CourseContentPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: course, loading: courseLoading, error: courseError } = useSelector(
    (state: RootState) => state.courses.currentCourse
  );
  const { items: modules, loading: modulesLoading, error: modulesError } = useSelector(
    (state: RootState) => state.courses.modules
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [moduleDialogOpen, setModuleDialogOpen] = useState(false);
  const [lessonDialogOpen, setLessonDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [deleteType, setDeleteType] = useState<'module' | 'lesson'>('module');
  
  const [moduleFormData, setModuleFormData] = useState({
    title: '',
    description: '',
  });
  
  const [lessonFormData, setLessonFormData] = useState({
    title: '',
    description: '',
    contentType: 'text',
    content: '',
    videoURL: '',
    duration: 0,
  });
  
  useEffect(() => {
    if (courseId) {
      dispatch(fetchCourseByID(parseInt(courseId)));
      dispatch(fetchModulesByCourseID(parseInt(courseId)));
    }
  }, [dispatch, courseId]);
  
  const handleModuleDialogOpen = (module?: Module) => {
    if (module) {
      setIsEditing(true);
      setSelectedModule(module);
      setModuleFormData({
        title: module.title,
        description: module.description,
      });
    } else {
      setIsEditing(false);
      setSelectedModule(null);
      setModuleFormData({
        title: '',
        description: '',
      });
    }
    setModuleDialogOpen(true);
  };
  
  const handleModuleDialogClose = () => {
    setModuleDialogOpen(false);
  };
  
  const handleLessonDialogOpen = (moduleId: number, lesson?: Lesson) => {
    const module = modules.find((m) => m.id === moduleId);
    if (module) {
      setSelectedModule(module);
      
      if (lesson) {
        setIsEditing(true);
        setSelectedLesson(lesson);
        setLessonFormData({
          title: lesson.title,
          description: lesson.description,
          contentType: lesson.contentType,
          content: lesson.content,
          videoURL: lesson.videoURL || '',
          duration: lesson.duration,
        });
      } else {
        setIsEditing(false);
        setSelectedLesson(null);
        setLessonFormData({
          title: '',
          description: '',
          contentType: 'text',
          content: '',
          videoURL: '',
          duration: 0,
        });
      }
      
      setLessonDialogOpen(true);
    }
  };
  
  const handleLessonDialogClose = () => {
    setLessonDialogOpen(false);
  };
  
  const handleDeleteDialogOpen = (type: 'module' | 'lesson', moduleId?: number, lessonId?: number) => {
    setDeleteType(type);
    
    if (type === 'module' && moduleId) {
      const module = modules.find((m) => m.id === moduleId);
      if (module) {
        setSelectedModule(module);
        setSelectedLesson(null);
      }
    } else if (type === 'lesson' && moduleId && lessonId) {
      const module = modules.find((m) => m.id === moduleId);
      if (module && module.lessons) {
        const lesson = module.lessons.find((l) => l.id === lessonId);
        if (lesson) {
          setSelectedModule(module);
          setSelectedLesson(lesson);
        }
      }
    }
    
    setDeleteDialogOpen(true);
  };
  
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
  };
  
  const handleModuleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setModuleFormData({
      ...moduleFormData,
      [name]: value,
    });
  };
  
  const handleLessonFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setLessonFormData({
      ...lessonFormData,
      [name]: value,
    });
  };
  
  const handleLessonSelectChange = (e: any) => {
    const { name, value } = e.target;
    setLessonFormData({
      ...lessonFormData,
      [name]: value,
    });
  };
  
  const handleModuleSubmit = async () => {
    if (courseId) {
      try {
        if (isEditing && selectedModule) {
          await dispatch(
            updateModule({
              id: selectedModule.id,
              module: {
                ...moduleFormData,
                courseID: parseInt(courseId),
              },
            })
          ).unwrap();
        } else {
          await dispatch(
            createModule({
              courseID: parseInt(courseId),
              module: {
                ...moduleFormData,
                courseID: parseInt(courseId),
                order: modules.length + 1,
              },
            })
          ).unwrap();
        }
        
        handleModuleDialogClose();
      } catch (error) {
        console.error('Failed to save module:', error);
      }
    }
  };
  
  const handleLessonSubmit = async () => {
    if (selectedModule) {
      try {
        if (isEditing && selectedLesson) {
          await dispatch(
            updateLesson({
              id: selectedLesson.id,
              lesson: {
                ...lessonFormData,
                moduleID: selectedModule.id,
              },
            })
          ).unwrap();
        } else {
          await dispatch(
            createLesson({
              moduleID: selectedModule.id,
              lesson: {
                ...lessonFormData,
                moduleID: selectedModule.id,
                order: selectedModule.lessons ? selectedModule.lessons.length + 1 : 1,
              },
            })
          ).unwrap();
        }
        
        handleLessonDialogClose();
      } catch (error) {
        console.error('Failed to save lesson:', error);
      }
    }
  };
  
  const handleDelete = async () => {
    try {
      if (deleteType === 'module' && selectedModule) {
        await dispatch(deleteModule(selectedModule.id)).unwrap();
      } else if (deleteType === 'lesson' && selectedLesson) {
        await dispatch(deleteLesson(selectedLesson.id)).unwrap();
      }
      
      handleDeleteDialogClose();
    } catch (error) {
      console.error('Failed to delete:', error);
    }
  };
  
  const handleDragEnd = (result: any) => {
    // TODO: Implement drag and drop reordering
    console.log('Drag result:', result);
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to manage course content. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  if (courseLoading || modulesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (courseError || !course) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {courseError || 'Course not found. Please try again later.'}
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Breadcrumbs sx={{ mb: 2 }}>
        <MuiLink
          component="button"
          variant="body2"
          onClick={() => navigate('/instructor/courses')}
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <ArrowBackIcon fontSize="small" sx={{ mr: 0.5 }} />
          Back to Courses
        </MuiLink>
        <Typography color="text.primary">{course.title}</Typography>
        <Typography color="text.primary">Content Management</Typography>
      </Breadcrumbs>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Course Content: {course.title}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleModuleDialogOpen()}
        >
          Add Module
        </Button>
      </Box>
      
      {modulesError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {modulesError}
        </Alert>
      )}
      
      <DragDropContext onDragEnd={handleDragEnd}>
        {modules.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              This course doesn't have any modules yet.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleModuleDialogOpen()}
              sx={{ mt: 2 }}
            >
              Add Your First Module
            </Button>
          </Paper>
        ) : (
          <Droppable droppableId="modules">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {modules.map((module, index) => (
                  <Draggable key={module.id} draggableId={`module-${module.id}`} index={index}>
                    {(provided) => (
                      <Paper
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        sx={{ mb: 3, overflow: 'hidden' }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            p: 2,
                            bgcolor: 'primary.main',
                            color: 'primary.contrastText',
                          }}
                        >
                          <Box {...provided.dragHandleProps} sx={{ mr: 1 }}>
                            <DragIndicatorIcon />
                          </Box>
                          <Typography variant="h6" sx={{ flexGrow: 1 }}>
                            Module {index + 1}: {module.title}
                          </Typography>
                          <Box>
                            <Tooltip title="Edit Module">
                              <IconButton
                                color="inherit"
                                onClick={() => handleModuleDialogOpen(module)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete Module">
                              <IconButton
                                color="inherit"
                                onClick={() => handleDeleteDialogOpen('module', module.id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                        
                        <Box sx={{ p: 2 }}>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {module.description}
                          </Typography>
                          
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="subtitle1">
                              Lessons ({module.lessons ? module.lessons.length : 0})
                            </Typography>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={() => handleLessonDialogOpen(module.id)}
                            >
                              Add Lesson
                            </Button>
                          </Box>
                          
                          <Droppable droppableId={`module-${module.id}-lessons`}>
                            {(provided) => (
                              <div {...provided.droppableProps} ref={provided.innerRef}>
                                {module.lessons && module.lessons.length > 0 ? (
                                  <List>
                                    {module.lessons.map((lesson, lessonIndex) => (
                                      <Draggable
                                        key={lesson.id}
                                        draggableId={`lesson-${lesson.id}`}
                                        index={lessonIndex}
                                      >
                                        {(provided) => (
                                          <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                          >
                                            <ListItem
                                              sx={{
                                                border: '1px solid',
                                                borderColor: 'divider',
                                                borderRadius: 1,
                                                mb: 1,
                                              }}
                                            >
                                              <Box {...provided.dragHandleProps} sx={{ mr: 1 }}>
                                                <DragIndicatorIcon />
                                              </Box>
                                              <ListItemIcon>
                                                {lesson.contentType === 'video' ? (
                                                  <PlayArrowIcon />
                                                ) : lesson.contentType === 'quiz' ? (
                                                  <QuizIcon />
                                                ) : (
                                                  <AssignmentIcon />
                                                )}
                                              </ListItemIcon>
                                              <ListItemText
                                                primary={`${lessonIndex + 1}. ${lesson.title}`}
                                                secondary={`${lesson.contentType} • ${lesson.duration} min`}
                                              />
                                              <ListItemSecondaryAction>
                                                <Tooltip title="Edit Lesson">
                                                  <IconButton
                                                    edge="end"
                                                    onClick={() => handleLessonDialogOpen(module.id, lesson)}
                                                    sx={{ mr: 1 }}
                                                  >
                                                    <EditIcon />
                                                  </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Delete Lesson">
                                                  <IconButton
                                                    edge="end"
                                                    onClick={() => handleDeleteDialogOpen('lesson', module.id, lesson.id)}
                                                  >
                                                    <DeleteIcon />
                                                  </IconButton>
                                                </Tooltip>
                                              </ListItemSecondaryAction>
                                            </ListItem>
                                          </div>
                                        )}
                                      </Draggable>
                                    ))}
                                    {provided.placeholder}
                                  </List>
                                ) : (
                                  <Box sx={{ textAlign: 'center', py: 2 }}>
                                    <Typography variant="body2" color="text.secondary">
                                      No lessons in this module yet.
                                    </Typography>
                                    <Button
                                      variant="text"
                                      startIcon={<AddIcon />}
                                      onClick={() => handleLessonDialogOpen(module.id)}
                                      sx={{ mt: 1 }}
                                    >
                                      Add Lesson
                                    </Button>
                                  </Box>
                                )}
                              </div>
                            )}
                          </Droppable>
                        </Box>
                      </Paper>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        )}
      </DragDropContext>
      
      {/* Module Dialog */}
      <Dialog open={moduleDialogOpen} onClose={handleModuleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Module' : 'Add Module'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="title"
            label="Module Title"
            type="text"
            fullWidth
            variant="outlined"
            value={moduleFormData.title}
            onChange={handleModuleFormChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Module Description"
            type="text"
            fullWidth
            variant="outlined"
            multiline
            rows={4}
            value={moduleFormData.description}
            onChange={handleModuleFormChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleModuleDialogClose}>Cancel</Button>
          <Button
            onClick={handleModuleSubmit}
            variant="contained"
            disabled={!moduleFormData.title}
          >
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Lesson Dialog */}
      <Dialog open={lessonDialogOpen} onClose={handleLessonDialogClose} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Lesson' : 'Add Lesson'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                autoFocus
                margin="dense"
                name="title"
                label="Lesson Title"
                type="text"
                fullWidth
                variant="outlined"
                value={lessonFormData.title}
                onChange={handleLessonFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                name="description"
                label="Lesson Description"
                type="text"
                fullWidth
                variant="outlined"
                multiline
                rows={2}
                value={lessonFormData.description}
                onChange={handleLessonFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined" margin="dense">
                <InputLabel>Content Type</InputLabel>
                <Select
                  name="contentType"
                  value={lessonFormData.contentType}
                  onChange={handleLessonSelectChange}
                  label="Content Type"
                >
                  <MenuItem value="text">Text</MenuItem>
                  <MenuItem value="video">Video</MenuItem>
                  <MenuItem value="quiz">Quiz</MenuItem>
                  <MenuItem value="assignment">Assignment</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                margin="dense"
                name="duration"
                label="Duration (minutes)"
                type="number"
                fullWidth
                variant="outlined"
                value={lessonFormData.duration}
                onChange={handleLessonFormChange}
                inputProps={{ min: 0 }}
              />
            </Grid>
            
            {lessonFormData.contentType === 'video' && (
              <Grid item xs={12}>
                <TextField
                  margin="dense"
                  name="videoURL"
                  label="Video URL"
                  type="text"
                  fullWidth
                  variant="outlined"
                  value={lessonFormData.videoURL}
                  onChange={handleLessonFormChange}
                  helperText="Enter YouTube or Vimeo URL"
                />
              </Grid>
            )}
            
            {(lessonFormData.contentType === 'text' || lessonFormData.contentType === 'assignment') && (
              <Grid item xs={12}>
                <TextField
                  margin="dense"
                  name="content"
                  label="Content"
                  type="text"
                  fullWidth
                  variant="outlined"
                  multiline
                  rows={8}
                  value={lessonFormData.content}
                  onChange={handleLessonFormChange}
                  helperText={
                    lessonFormData.contentType === 'text'
                      ? 'Enter lesson content (supports Markdown)'
                      : 'Enter assignment instructions'
                  }
                />
              </Grid>
            )}
            
            {lessonFormData.contentType === 'quiz' && (
              <Grid item xs={12}>
                <Alert severity="info" sx={{ mt: 1 }}>
                  You'll be able to add quiz questions after creating the lesson.
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleLessonDialogClose}>Cancel</Button>
          <Button
            onClick={handleLessonSubmit}
            variant="contained"
            disabled={!lessonFormData.title}
          >
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteDialogClose}>
        <DialogTitle>Delete {deleteType === 'module' ? 'Module' : 'Lesson'}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this {deleteType}
            {deleteType === 'module' && selectedModule
              ? `: "${selectedModule.title}"`
              : deleteType === 'lesson' && selectedLesson
              ? `: "${selectedLesson.title}"`
              : ''}
            ? This action cannot be undone.
            {deleteType === 'module' && selectedModule && selectedModule.lessons && selectedModule.lessons.length > 0 && (
              <Box component="span" sx={{ color: 'error.main', display: 'block', mt: 1 }}>
                Warning: This will also delete all {selectedModule.lessons.length} lessons in this module.
              </Box>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Cancel</Button>
          <Button onClick={handleDelete} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CourseContentPage;
