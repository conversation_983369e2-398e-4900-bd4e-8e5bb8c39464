import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from './store';
import { CircularProgress, Box } from '@mui/material';

// Layout components
import MainLayout from './layouts/MainLayout';
import ResponsiveLayout from './components/layout/ResponsiveLayout';
import { ThemeProvider } from './theme/ThemeProvider';

// Eagerly loaded components
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import NotFoundPage from './pages/NotFoundPage';

// Personalization pages
import LearningStyleAssessmentPage from './pages/LearningStyleAssessmentPage';
import PersonalizedPathsPage from './pages/PersonalizedPathsPage';
import CreatePersonalizedPathPage from './pages/CreatePersonalizedPathPage';
import EditPersonalizedPathPage from './pages/EditPersonalizedPathPage';
import MarketplacePage from './pages/MarketplacePage';
import SearchResultsPage from './pages/SearchResultsPage';

// Course Management pages
import CoursesPage from './pages/CoursesPage';
import CourseDetailPage from './pages/CourseDetailPage';
import CourseCreationPage from './pages/CourseCreationPage';
import CourseManagementPage from './pages/CourseManagementPage';
import CourseContentPage from './pages/CourseContentPage';
import CourseLessonPage from './pages/CourseLessonPage';

// Tutorial pages
import TutorialsListPage from './pages/TutorialsListPage';
import TutorialViewPage from './pages/TutorialViewPage';
import TutorialBuilder from './pages/TutorialBuilder';
import MyTutorialsPage from './pages/MyTutorialsPage';

// Quiz pages
import QuizBuilder from './pages/QuizBuilder';
import QuizTakingInterface from './pages/QuizTakingInterface';
import QuizzesListPage from './pages/QuizzesListPage';
import MyQuizzesPage from './pages/MyQuizzesPage';

// Impact pages
import ImpactDashboard from './pages/ImpactDashboard';
import ImpactReportingInterface from './pages/ImpactReportingInterface';

// Rewards pages
import RewardsInterface from './pages/RewardsInterface';
import EngagementDashboard from './pages/EngagementDashboard';

// Skills pages
import SkillsProfile from './pages/SkillsProfile';
import SkillMatchingInterface from './pages/SkillMatchingInterface';


// Lazily loaded components
const AboutPage = lazy(() => import('./pages/AboutPage'));
const AffiliatePage = lazy(() => import('./pages/AffiliatePage'));
const BadgesPage = lazy(() => import('./pages/BadgesPage'));
const BookListPage = lazy(() => import('./pages/BookListPage'));
const BookViewerPage = lazy(() => import('./pages/BookViewerPage'));
const CelebrateDetailPage = lazy(() => import('./pages/CelebrateDetailPage'));
const CelebratePage = lazy(() => import('./pages/CelebratePage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));
const CreatorRevenuePage = lazy(() => import('./pages/CreatorRevenuePage'));
const DisputeDetailsPage = lazy(() => import('./pages/DisputeDetailsPage'));
const EnhancedUserProfilePage = lazy(() => import('./pages/EnhancedUserProfilePage'));
const EscrowPage = lazy(() => import('./pages/EscrowPage'));
const ForumPage = lazy(() => import('./pages/ForumPage'));
const ForumTopicPage = lazy(() => import('./pages/ForumTopicPage'));
const LivestreamAdminPage = lazy(() => import('./pages/LivestreamAdminPage'));
const LivestreamPage = lazy(() => import('./pages/LivestreamPage'));
const LocalGroupsInterface = lazy(() => import('./pages/LocalGroupsInterface'));
const LocalEventManagement = lazy(() => import('./pages/LocalEventManagement'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const ProgressDashboardPage = lazy(() => import('./pages/ProgressDashboardPage'));
const ResourcesPage = lazy(() => import('./pages/ResourcesPage'));
const StreamDetailPage = lazy(() => import('./pages/StreamDetailPage'));
const StreamerDashboardPage = lazy(() => import('./pages/StreamerDashboardPage'));
const StreamerProfilePage = lazy(() => import('./pages/StreamerProfilePage'));
const TipsManagementPage = lazy(() => import('./pages/admin/TipsManagementPage'));
const VirtualCurrencyPage = lazy(() => import('./pages/VirtualCurrencyPage'));
const WalletPage = lazy(() => import('./pages/WalletPage'));

// Loading component
const LoadingComponent = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
    <CircularProgress />
  </Box>
);

// Protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return <>{children}</>;
};

function App() {
  return (
    <ThemeProvider>
      <Router>
        <Suspense fallback={<LoadingComponent />}>
          <Routes>
            <Route path="/" element={<ResponsiveLayout />}>
            {/* Public routes */}
            <Route index element={<HomePage />} />
            <Route path="login" element={<LoginPage />} />
            <Route path="register" element={<RegisterPage />} />
            <Route path="about" element={<AboutPage />} />
            <Route path="contact" element={<ContactPage />} />
            <Route path="resources" element={<ResourcesPage />} />
            <Route path="search" element={<SearchResultsPage />} />

            {/* Book routes */}
            <Route path="books" element={<BookListPage />} />
            <Route path="books/:bookId" element={<BookViewerPage />} />

            {/* Forum routes */}
            <Route path="forum" element={<ForumPage />} />
            <Route path="forum/topic/:topicId" element={<ForumTopicPage />} />

            {/* Celebration routes */}
            <Route path="celebrate" element={<CelebratePage />} />
            <Route path="celebrate/:celebrationId" element={<CelebrateDetailPage />} />

            {/* Protected routes */}
            <Route path="profile" element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            } />

            <Route path="enhanced-profile" element={
              <ProtectedRoute>
                <EnhancedUserProfilePage />
              </ProtectedRoute>
            } />

            <Route path="badges" element={
              <ProtectedRoute>
                <BadgesPage />
              </ProtectedRoute>
            } />

            {/* Livestream routes */}
            <Route path="livestream" element={
              <ProtectedRoute>
                <LivestreamPage />
              </ProtectedRoute>
            } />

            <Route path="livestream/admin" element={
              <ProtectedRoute>
                <LivestreamAdminPage />
              </ProtectedRoute>
            } />

            <Route path="livestream/:streamId" element={<StreamDetailPage />} />

            <Route path="streamer/dashboard" element={
              <ProtectedRoute>
                <StreamerDashboardPage />
              </ProtectedRoute>
            } />

            <Route path="streamer/:streamerId" element={<StreamerProfilePage />} />

            {/* Marketplace routes */}
            <Route path="marketplace" element={<MarketplacePage />} />

            {/* Wallet and currency routes */}
            <Route path="wallet" element={
              <ProtectedRoute>
                <WalletPage />
              </ProtectedRoute>
            } />

            <Route path="virtual-currency" element={
              <ProtectedRoute>
                <VirtualCurrencyPage />
              </ProtectedRoute>
            } />

            {/* Creator revenue routes */}
            <Route path="creator/revenue" element={
              <ProtectedRoute>
                <CreatorRevenuePage />
              </ProtectedRoute>
            } />

            {/* Escrow and dispute routes */}
            <Route path="escrow" element={
              <ProtectedRoute>
                <EscrowPage />
              </ProtectedRoute>
            } />

            <Route path="disputes/:disputeId" element={
              <ProtectedRoute>
                <DisputeDetailsPage />
              </ProtectedRoute>
            } />

            {/* Affiliate routes */}
            <Route path="affiliate" element={
              <ProtectedRoute>
                <AffiliatePage />
              </ProtectedRoute>
            } />

            {/* Progress Dashboard route */}
            <Route path="progress" element={
              <ProtectedRoute>
                <ProgressDashboardPage />
              </ProtectedRoute>
            } />

            {/* Admin routes */}
            <Route path="admin/tips" element={
              <ProtectedRoute>
                <TipsManagementPage />
              </ProtectedRoute>
            } />

            {/* Personalization routes */}
            <Route path="learning-style-assessment" element={
              <ProtectedRoute>
                <LearningStyleAssessmentPage />
              </ProtectedRoute>
            } />
            <Route path="personalized-paths" element={
              <ProtectedRoute>
                <PersonalizedPathsPage />
              </ProtectedRoute>
            } />
            <Route path="create-personalized-path" element={
              <ProtectedRoute>
                <CreatePersonalizedPathPage />
              </ProtectedRoute>
            } />
            <Route path="edit-personalized-path/:id" element={
              <ProtectedRoute>
                <EditPersonalizedPathPage />
              </ProtectedRoute>
            } />

            {/* Course Management routes */}
            <Route path="courses" element={<CoursesPage />} />
            <Route path="courses/:id" element={<CourseDetailPage />} />
            <Route path="courses/:courseId/lessons/:lessonId" element={<CourseLessonPage />} />

            {/* Instructor routes */}
            <Route path="instructor/courses" element={
              <ProtectedRoute>
                <CourseManagementPage />
              </ProtectedRoute>
            } />
            <Route path="instructor/courses/create" element={
              <ProtectedRoute>
                <CourseCreationPage />
              </ProtectedRoute>
            } />
            <Route path="instructor/courses/:courseId/content" element={
              <ProtectedRoute>
                <CourseContentPage />
              </ProtectedRoute>
            } />

            {/* Tutorial routes */}
            <Route path="tutorials" element={<TutorialsListPage />} />
            <Route path="tutorials/:id" element={<TutorialViewPage />} />
            <Route path="tutorials/slug/:slug" element={<TutorialViewPage />} />
            <Route path="tutorials/create" element={
              <ProtectedRoute>
                <TutorialBuilder />
              </ProtectedRoute>
            } />
            <Route path="tutorials/edit/:id" element={
              <ProtectedRoute>
                <TutorialBuilder />
              </ProtectedRoute>
            } />
            <Route path="tutorials/my-tutorials" element={
              <ProtectedRoute>
                <MyTutorialsPage />
              </ProtectedRoute>
            } />

            {/* Quiz routes */}
            <Route path="quizzes" element={<QuizzesListPage />} />
            <Route path="quizzes/:id" element={<QuizTakingInterface />} />
            <Route path="quizzes/create" element={
              <ProtectedRoute>
                <QuizBuilder />
              </ProtectedRoute>
            } />
            <Route path="quizzes/edit/:id" element={
              <ProtectedRoute>
                <QuizBuilder />
              </ProtectedRoute>
            } />
            <Route path="quizzes/my-quizzes" element={
              <ProtectedRoute>
                <MyQuizzesPage />
              </ProtectedRoute>
            } />

            {/* Impact routes */}
            <Route path="impact" element={
              <ProtectedRoute>
                <ImpactDashboard />
              </ProtectedRoute>
            } />
            <Route path="impact/reports/create" element={
              <ProtectedRoute>
                <ImpactReportingInterface />
              </ProtectedRoute>
            } />
            <Route path="impact/reports/edit/:id" element={
              <ProtectedRoute>
                <ImpactReportingInterface />
              </ProtectedRoute>
            } />

            {/* Rewards routes */}
            <Route path="rewards" element={
              <ProtectedRoute>
                <RewardsInterface />
              </ProtectedRoute>
            } />
            <Route path="rewards/admin" element={
              <ProtectedRoute>
                <EngagementDashboard />
              </ProtectedRoute>
            } />

            {/* Skills routes */}
            <Route path="skills/profile" element={
              <ProtectedRoute>
                <SkillsProfile />
              </ProtectedRoute>
            } />
            <Route path="skills/profile/:userId" element={
              <ProtectedRoute>
                <SkillsProfile />
              </ProtectedRoute>
            } />
            <Route path="skills/matching" element={
              <ProtectedRoute>
                <SkillMatchingInterface />
              </ProtectedRoute>
            } />
            <Route path="skills/needs/:needId/matches" element={
              <ProtectedRoute>
                <SkillMatchingInterface />
              </ProtectedRoute>
            } />

            {/* Local Groups routes */}
            <Route path="groups" element={
              <ProtectedRoute>
                <LocalGroupsInterface />
              </ProtectedRoute>
            } />
            <Route path="groups/:groupId" element={
              <ProtectedRoute>
                <LocalEventManagement />
              </ProtectedRoute>
            } />

            {/* 404 route */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Routes>
      </Suspense>
    </Router>
    </ThemeProvider>
  );
}

export default App;
