import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Feature } from '../../components/features/FeatureToggle';
import featuresService from '../../api/featuresService';

// Define the state interface
interface FeaturesState {
  features: Feature[];
  loading: boolean;
  error: string | null;
  initialized: boolean;
}

// Initial state
const initialState: FeaturesState = {
  features: [],
  loading: false,
  error: null,
  initialized: false
};

// Async thunks
export const fetchFeatures = createAsyncThunk(
  'features/fetchFeatures',
  async (_, { rejectWithValue }) => {
    try {
      return await featuresService.getFeatures();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch features');
    }
  }
);

export const toggleFeature = createAsyncThunk(
  'features/toggleFeature',
  async ({ featureId, enabled }: { featureId: string, enabled: boolean }, { rejectWithValue }) => {
    try {
      return await featuresService.updateFeature(featureId, enabled);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update feature');
    }
  }
);

export const resetFeaturesToDefault = createAsyncThunk(
  'features/resetFeaturesToDefault',
  async (_, { rejectWithValue }) => {
    try {
      return await featuresService.resetFeatures();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to reset features');
    }
  }
);

// Create the slice
const featuresSlice = createSlice({
  name: 'features',
  initialState,
  reducers: {
    // Local feature toggle (without API call)
    setFeatureEnabled: (state, action: PayloadAction<{ featureId: string, enabled: boolean }>) => {
      const { featureId, enabled } = action.payload;
      const feature = state.features.find(f => f.id === featureId);
      if (feature) {
        feature.enabled = enabled;
      }
    },
    
    // Reset error state
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchFeatures
      .addCase(fetchFeatures.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFeatures.fulfilled, (state, action) => {
        state.features = action.payload;
        state.loading = false;
        state.initialized = true;
      })
      .addCase(fetchFeatures.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // toggleFeature
      .addCase(toggleFeature.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(toggleFeature.fulfilled, (state, action) => {
        const updatedFeature = action.payload;
        const index = state.features.findIndex(f => f.id === updatedFeature.id);
        if (index !== -1) {
          state.features[index] = updatedFeature;
        }
        state.loading = false;
      })
      .addCase(toggleFeature.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // resetFeaturesToDefault
      .addCase(resetFeaturesToDefault.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetFeaturesToDefault.fulfilled, (state, action) => {
        state.features = action.payload;
        state.loading = false;
      })
      .addCase(resetFeaturesToDefault.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

// Export actions and reducer
export const { setFeatureEnabled, clearError } = featuresSlice.actions;
export default featuresSlice.reducer;

// Selectors
export const selectFeatures = (state: { features: FeaturesState }) => state.features.features;
export const selectFeatureById = (state: { features: FeaturesState }, featureId: string) => 
  state.features.features.find(feature => feature.id === featureId);
export const selectFeaturesLoading = (state: { features: FeaturesState }) => state.features.loading;
export const selectFeaturesError = (state: { features: FeaturesState }) => state.features.error;
export const selectFeaturesInitialized = (state: { features: FeaturesState }) => state.features.initialized;
export const selectEnabledFeatures = (state: { features: FeaturesState }) => 
  state.features.features.filter(feature => feature.enabled);
export const selectFeaturesByCategory = (state: { features: FeaturesState }, category: string) => 
  state.features.features.filter(feature => feature.category === category);
export const selectIsFeatureEnabled = (state: { features: FeaturesState }, featureId: string) => {
  const feature = state.features.features.find(f => f.id === featureId);
  return feature ? feature.enabled : false;
};
