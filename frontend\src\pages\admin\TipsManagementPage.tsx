import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Bar<PERSON>hart as BarChartIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { 
  fetchAllTips, 
  fetchAllTipStatistics, 
  createTip, 
  updateTip, 
  deleteTip,
  selectAllTips,
  selectTipStatistics,
  selectTipsLoading,
  selectTipsError,
} from '../../features/tips/tipsSlice';
import { Tip, TipStatistics } from '../../api/tipsService';
import { AppDispatch } from '../../store';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tips-tabpanel-${index}`}
      aria-labelledby={`tips-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TipsManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  
  const tips = useSelector(selectAllTips);
  const statistics = useSelector(selectTipStatistics);
  const isLoading = useSelector(selectTipsLoading);
  const error = useSelector(selectTipsError);
  
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [currentTip, setCurrentTip] = useState<Partial<Tip> | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  useEffect(() => {
    dispatch(fetchAllTips());
    dispatch(fetchAllTipStatistics());
  }, [dispatch]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleOpenDialog = (tip?: Tip) => {
    if (tip) {
      setCurrentTip(tip);
      setIsEditing(true);
    } else {
      setCurrentTip({
        title: '',
        content: '',
        category: 'feature',
        trigger: 'page_view',
        triggerData: '',
        priority: 2,
        imageUrl: '',
        actionUrl: '',
        actionText: '',
        active: true,
      });
      setIsEditing(false);
    }
    setOpenDialog(true);
  };
  
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setCurrentTip(null);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentTip((prev) => prev ? { ...prev, [name]: value } : null);
  };
  
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setCurrentTip((prev) => prev ? { ...prev, [name]: value } : null);
  };
  
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setCurrentTip((prev) => prev ? { ...prev, [name]: checked } : null);
  };
  
  const handleSaveTip = () => {
    if (!currentTip) return;
    
    if (isEditing && currentTip.id) {
      dispatch(updateTip({ id: currentTip.id, tip: currentTip }));
    } else {
      dispatch(createTip(currentTip as Omit<Tip, 'id' | 'createdAt' | 'updatedAt'>));
    }
    
    handleCloseDialog();
  };
  
  const handleDeleteTip = (id: number) => {
    if (window.confirm('Are you sure you want to delete this tip?')) {
      dispatch(deleteTip(id));
    }
  };
  
  const handleRefresh = () => {
    dispatch(fetchAllTips());
    dispatch(fetchAllTipStatistics());
  };
  
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'title', headerName: 'Title', width: 200 },
    { field: 'category', headerName: 'Category', width: 120 },
    { field: 'trigger', headerName: 'Trigger', width: 120 },
    { field: 'priority', headerName: 'Priority', width: 100 },
    { field: 'active', headerName: 'Active', width: 100, type: 'boolean' },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      renderCell: (params) => (
        <Box>
          <IconButton
            color="primary"
            onClick={() => handleOpenDialog(params.row as Tip)}
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            color="error"
            onClick={() => handleDeleteTip(params.row.id)}
            size="small"
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];
  
  const statisticsColumns: GridColDef[] = [
    { field: 'tipId', headerName: 'Tip ID', width: 100 },
    { 
      field: 'tipTitle', 
      headerName: 'Tip Title', 
      width: 200,
      valueGetter: (params) => {
        const tip = tips.find(t => t.id === params.row.tipId);
        return tip ? tip.title : 'Unknown';
      }
    },
    { field: 'viewCount', headerName: 'Views', width: 100 },
    { field: 'clickCount', headerName: 'Clicks', width: 100 },
    { field: 'dismissCount', headerName: 'Dismisses', width: 100 },
    { field: 'helpfulCount', headerName: 'Helpful', width: 100 },
    { 
      field: 'effectivenessRate', 
      headerName: 'Effectiveness', 
      width: 150,
      valueFormatter: (params) => `${params.value.toFixed(2)}%`,
    },
  ];
  
  // Prepare data for charts
  const chartData = statistics.map((stat) => {
    const tip = tips.find(t => t.id === stat.tipId);
    return {
      name: tip ? tip.title.substring(0, 20) + (tip.title.length > 20 ? '...' : '') : `Tip ${stat.tipId}`,
      views: stat.viewCount,
      clicks: stat.clickCount,
      dismisses: stat.dismissCount,
      helpful: stat.helpfulCount,
      effectiveness: parseFloat(stat.effectivenessRate.toFixed(2)),
    };
  });
  
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            Tips Management
          </Typography>
          <Box>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              sx={{ mr: 1 }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              Add New Tip
            </Button>
          </Box>
        </Box>
        
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="tips management tabs">
          <Tab label="Tips List" id="tips-tab-0" aria-controls="tips-tabpanel-0" />
          <Tab label="Statistics" id="tips-tab-1" aria-controls="tips-tabpanel-1" />
          <Tab label="Charts" id="tips-tab-2" aria-controls="tips-tabpanel-2" />
        </Tabs>
        
        <TabPanel value={tabValue} index={0}>
          <div style={{ height: 500, width: '100%' }}>
            <DataGrid
              rows={tips}
              columns={columns}
              loading={isLoading}
              pageSizeOptions={[5, 10, 25]}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 10, page: 0 },
                },
              }}
              disableRowSelectionOnClick
            />
          </div>
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <div style={{ height: 500, width: '100%' }}>
            <DataGrid
              rows={statistics}
              columns={statisticsColumns}
              loading={isLoading}
              pageSizeOptions={[5, 10, 25]}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 10, page: 0 },
                },
              }}
              disableRowSelectionOnClick
              getRowId={(row) => row.tipId}
            />
          </div>
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Tip Engagement Metrics
              </Typography>
              <Paper sx={{ p: 2, height: 400 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end" 
                      height={80} 
                      interval={0}
                    />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="views" fill={theme.palette.primary.main} name="Views" />
                    <Bar dataKey="clicks" fill={theme.palette.success.main} name="Clicks" />
                    <Bar dataKey="dismisses" fill={theme.palette.error.main} name="Dismisses" />
                    <Bar dataKey="helpful" fill={theme.palette.info.main} name="Helpful" />
                  </BarChart>
                </ResponsiveContainer>
              </Paper>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Tip Effectiveness Rate (%)
              </Typography>
              <Paper sx={{ p: 2, height: 400 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end" 
                      height={80} 
                      interval={0}
                    />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="effectiveness" fill={theme.palette.secondary.main} name="Effectiveness Rate (%)" />
                  </BarChart>
                </ResponsiveContainer>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
      
      {/* Tip Edit/Create Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Tip' : 'Create New Tip'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="title"
                label="Title"
                fullWidth
                value={currentTip?.title || ''}
                onChange={handleInputChange}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="content"
                label="Content"
                fullWidth
                multiline
                rows={4}
                value={currentTip?.content || ''}
                onChange={handleInputChange}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={currentTip?.category || 'feature'}
                  onChange={handleSelectChange}
                  label="Category"
                >
                  <MenuItem value="navigation">Navigation</MenuItem>
                  <MenuItem value="feature">Feature</MenuItem>
                  <MenuItem value="content">Content</MenuItem>
                  <MenuItem value="productivity">Productivity</MenuItem>
                  <MenuItem value="learning">Learning</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Trigger</InputLabel>
                <Select
                  name="trigger"
                  value={currentTip?.trigger || 'page_view'}
                  onChange={handleSelectChange}
                  label="Trigger"
                >
                  <MenuItem value="page_view">Page View</MenuItem>
                  <MenuItem value="time_on_page">Time on Page</MenuItem>
                  <MenuItem value="action">Action</MenuItem>
                  <MenuItem value="inactivity">Inactivity</MenuItem>
                  <MenuItem value="progress_level">Progress Level</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="triggerData"
                label="Trigger Data"
                fullWidth
                value={currentTip?.triggerData || ''}
                onChange={handleInputChange}
                helperText="Additional data for the trigger (e.g., time threshold, action name)"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  name="priority"
                  value={currentTip?.priority || 2}
                  onChange={handleSelectChange}
                  label="Priority"
                >
                  <MenuItem value={1}>Low</MenuItem>
                  <MenuItem value={2}>Medium</MenuItem>
                  <MenuItem value={3}>High</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }}>Action Details</Divider>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="actionText"
                label="Action Button Text"
                fullWidth
                value={currentTip?.actionText || ''}
                onChange={handleInputChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="actionUrl"
                label="Action URL"
                fullWidth
                value={currentTip?.actionUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="imageUrl"
                label="Image URL"
                fullWidth
                value={currentTip?.imageUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Active</InputLabel>
                <Select
                  name="active"
                  value={currentTip?.active === undefined ? true : currentTip.active}
                  onChange={handleSelectChange}
                  label="Active"
                >
                  <MenuItem value={true}>Yes</MenuItem>
                  <MenuItem value={false}>No</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveTip} variant="contained" color="primary">
            {isEditing ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TipsManagementPage;
