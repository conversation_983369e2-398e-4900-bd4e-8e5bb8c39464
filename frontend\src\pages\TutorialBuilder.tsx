import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  FormHelperText,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  Image as ImageIcon,
  Code as CodeIcon,
  VideoLibrary as VideoIcon,
  Quiz as QuizIcon,
  TouchApp as InteractiveIcon,
  TextFields as TextIcon,
  DragIndicator as DragIndicatorIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  createTutorial,
  updateTutorial,
  fetchTutorialById,
  fetchTutorialSteps,
  createStep,
  updateStep,
  deleteStep,
  fetchCategories,
} from '../store/slices/tutorialsSlice';
import { Tutorial, TutorialStep, TutorialCategory } from '../services/tutorialsService';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// Step 1: Basic Information
const BasicInformationStep: React.FC<{
  tutorialData: Partial<Tutorial>;
  setTutorialData: React.Dispatch<React.SetStateAction<Partial<Tutorial>>>;
  categories: TutorialCategory[];
  categoriesLoading: boolean;
}> = ({ tutorialData, setTutorialData, categories, categoriesLoading }) => {
  const [tagInput, setTagInput] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTutorialData({
      ...tutorialData,
      [name]: value,
    });
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setTutorialData({
      ...tutorialData,
      [name]: value,
    });
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tutorialData.tags?.includes(tagInput.trim())) {
      setTutorialData({
        ...tutorialData,
        tags: [...(tutorialData.tags || []), tagInput.trim()],
      });
      setTagInput('');
    }
  };

  const handleDeleteTag = (tagToDelete: string) => {
    setTutorialData({
      ...tutorialData,
      tags: tutorialData.tags?.filter((tag) => tag !== tagToDelete) || [],
    });
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          required
          fullWidth
          label="Tutorial Title"
          name="title"
          value={tutorialData.title || ''}
          onChange={handleInputChange}
          variant="outlined"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          required
          fullWidth
          label="Description"
          name="description"
          value={tutorialData.description || ''}
          onChange={handleInputChange}
          variant="outlined"
          multiline
          rows={4}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth variant="outlined">
          <InputLabel>Category</InputLabel>
          <Select
            name="categoryId"
            value={tutorialData.categoryId || ''}
            onChange={handleSelectChange}
            label="Category"
          >
            {categoriesLoading ? (
              <MenuItem disabled>Loading categories...</MenuItem>
            ) : (
              categories.map((category) => (
                <MenuItem key={category.id} value={category.id}>
                  {category.name}
                </MenuItem>
              ))
            )}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth variant="outlined">
          <InputLabel>Difficulty</InputLabel>
          <Select
            name="difficulty"
            value={tutorialData.difficulty || ''}
            onChange={handleSelectChange}
            label="Difficulty"
          >
            <MenuItem value="beginner">Beginner</MenuItem>
            <MenuItem value="intermediate">Intermediate</MenuItem>
            <MenuItem value="advanced">Advanced</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Estimated Time (minutes)"
          name="estimatedTime"
          type="number"
          value={tutorialData.estimatedTime || ''}
          onChange={handleInputChange}
          variant="outlined"
          inputProps={{ min: 1 }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Thumbnail URL"
          name="thumbnailURL"
          value={tutorialData.thumbnailURL || ''}
          onChange={handleInputChange}
          variant="outlined"
          helperText="Enter a URL for your tutorial thumbnail image"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Prerequisites"
          name="prerequisites"
          value={tutorialData.prerequisites || ''}
          onChange={handleInputChange}
          variant="outlined"
          multiline
          rows={3}
          helperText="What should users know before starting this tutorial? (Optional)"
        />
      </Grid>
      <Grid item xs={12}>
        <Box sx={{ mb: 1 }}>
          <Typography variant="subtitle1">Tags</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            fullWidth
            label="Add a tag"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            variant="outlined"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddTag();
              }
            }}
          />
          <Button
            variant="contained"
            onClick={handleAddTag}
            sx={{ ml: 1, height: 56 }}
          >
            Add
          </Button>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {tutorialData.tags?.map((tag) => (
            <Chip
              key={tag}
              label={tag}
              onDelete={() => handleDeleteTag(tag)}
            />
          ))}
        </Box>
      </Grid>
    </Grid>
  );
};

// Step 2: Content Steps
const ContentStepsStep: React.FC<{
  tutorialId: number | null;
  steps: TutorialStep[];
  setSteps: React.Dispatch<React.SetStateAction<TutorialStep[]>>;
  loading: boolean;
  error: string | null;
}> = ({ tutorialId, steps, setSteps, loading, error }) => {
  const [stepDialogOpen, setStepDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<Partial<TutorialStep> | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stepToDelete, setStepToDelete] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  const dispatch = useDispatch<AppDispatch>();

  const handleOpenStepDialog = (step?: TutorialStep) => {
    if (step) {
      setCurrentStep(step);
      setIsEditing(true);
    } else {
      setCurrentStep({
        title: '',
        content: '',
        type: 'text',
        order: steps.length + 1,
      });
      setIsEditing(false);
    }
    setStepDialogOpen(true);
  };

  const handleCloseStepDialog = () => {
    setStepDialogOpen(false);
    setCurrentStep(null);
  };

  const handleOpenDeleteDialog = (stepId: number) => {
    setStepToDelete(stepId);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setStepToDelete(null);
  };

  const handleStepInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentStep((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleStepSelectChange = (e: any) => {
    const { name, value } = e.target;
    setCurrentStep((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleContentChange = (value: string) => {
    setCurrentStep((prev) => ({
      ...prev,
      content: value,
    }));
  };

  const handleSaveStep = async () => {
    if (!currentStep || !tutorialId) return;

    try {
      if (isEditing && currentStep.id) {
        await dispatch(updateStep({ id: currentStep.id, step: currentStep })).unwrap();

        // Update local state
        setSteps((prevSteps) =>
          prevSteps.map((step) => (step.id === currentStep.id ? { ...step, ...currentStep } : step))
        );
      } else {
        const newStep = await dispatch(
          createStep({ tutorialId, step: currentStep })
        ).unwrap();

        // Update local state
        setSteps((prevSteps) => [...prevSteps, newStep]);
      }

      handleCloseStepDialog();
    } catch (error) {
      console.error('Failed to save step:', error);
    }
  };

  const handleDeleteStep = async () => {
    if (!stepToDelete) return;

    try {
      await dispatch(deleteStep(stepToDelete)).unwrap();

      // Update local state
      setSteps((prevSteps) => prevSteps.filter((step) => step.id !== stepToDelete));

      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Failed to delete step:', error);
    }
  };

  const handleMoveStep = (stepId: number, direction: 'up' | 'down') => {
    const stepIndex = steps.findIndex((step) => step.id === stepId);
    if (
      (direction === 'up' && stepIndex === 0) ||
      (direction === 'down' && stepIndex === steps.length - 1)
    ) {
      return;
    }

    const newSteps = [...steps];
    const targetIndex = direction === 'up' ? stepIndex - 1 : stepIndex + 1;

    // Swap steps
    [newSteps[stepIndex], newSteps[targetIndex]] = [newSteps[targetIndex], newSteps[stepIndex]];

    // Update order property
    newSteps[stepIndex].order = stepIndex + 1;
    newSteps[targetIndex].order = targetIndex + 1;

    setSteps(newSteps);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    const newSteps = [...steps];
    const [removed] = newSteps.splice(sourceIndex, 1);
    newSteps.splice(destinationIndex, 0, removed);

    // Update order property for all steps
    const updatedSteps = newSteps.map((step, index) => ({
      ...step,
      order: index + 1,
    }));

    setSteps(updatedSteps);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <TextIcon />;
      case 'image':
        return <ImageIcon />;
      case 'video':
        return <VideoIcon />;
      case 'code':
        return <CodeIcon />;
      case 'quiz':
        return <QuizIcon />;
      case 'interactive':
        return <InteractiveIcon />;
      default:
        return <TextIcon />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Tutorial Steps</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenStepDialog()}
        >
          Add Step
        </Button>
      </Box>

      {steps.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            No steps added yet. Click "Add Step" to create your first tutorial step.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenStepDialog()}
            sx={{ mt: 2 }}
          >
            Add Step
          </Button>
        </Paper>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="steps">
            {(provided) => (
              <List
                {...provided.droppableProps}
                ref={provided.innerRef}
                sx={{ bgcolor: 'background.paper' }}
              >
                {steps.map((step, index) => (
                  <Draggable key={step.id} draggableId={`step-${step.id}`} index={index}>
                    {(provided) => (
                      <ListItem
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        sx={{
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          mb: 2,
                        }}
                      >
                        <ListItemIcon {...provided.dragHandleProps}>
                          <DragIndicatorIcon />
                        </ListItemIcon>
                        <ListItemIcon>
                          {getStepIcon(step.type)}
                        </ListItemIcon>
                        <ListItemText
                          primary={`${index + 1}. ${step.title}`}
                          secondary={`Type: ${step.type}`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleMoveStep(step.id, 'up')}
                            disabled={index === 0}
                            sx={{ mr: 1 }}
                          >
                            <ArrowUpwardIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleMoveStep(step.id, 'down')}
                            disabled={index === steps.length - 1}
                            sx={{ mr: 1 }}
                          >
                            <ArrowDownwardIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleOpenStepDialog(step)}
                            sx={{ mr: 1 }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleOpenDeleteDialog(step.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </List>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* Step Dialog */}
      <Dialog open={stepDialogOpen} onClose={handleCloseStepDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Step' : 'Add Step'}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label="Basic Info" />
              <Tab label="Content" />
              {currentStep?.type === 'code' && <Tab label="Code" />}
              {currentStep?.type === 'quiz' && <Tab label="Quiz" />}
              {currentStep?.type === 'interactive' && <Tab label="Interactive" />}
            </Tabs>

            {activeTab === 0 && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    autoFocus
                    margin="dense"
                    name="title"
                    label="Step Title"
                    type="text"
                    fullWidth
                    variant="outlined"
                    value={currentStep?.title || ''}
                    onChange={handleStepInputChange}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>Content Type</InputLabel>
                    <Select
                      name="type"
                      value={currentStep?.type || 'text'}
                      onChange={handleStepSelectChange}
                      label="Content Type"
                    >
                      <MenuItem value="text">Text</MenuItem>
                      <MenuItem value="image">Image</MenuItem>
                      <MenuItem value="video">Video</MenuItem>
                      <MenuItem value="code">Code</MenuItem>
                      <MenuItem value="quiz">Quiz</MenuItem>
                      <MenuItem value="interactive">Interactive</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                {currentStep?.type === 'image' && (
                  <Grid item xs={12}>
                    <TextField
                      margin="dense"
                      name="mediaURL"
                      label="Image URL"
                      type="text"
                      fullWidth
                      variant="outlined"
                      value={currentStep?.mediaURL || ''}
                      onChange={handleStepInputChange}
                    />
                  </Grid>
                )}
                {currentStep?.type === 'video' && (
                  <Grid item xs={12}>
                    <TextField
                      margin="dense"
                      name="mediaURL"
                      label="Video URL"
                      type="text"
                      fullWidth
                      variant="outlined"
                      value={currentStep?.mediaURL || ''}
                      onChange={handleStepInputChange}
                      helperText="Enter YouTube or Vimeo URL"
                    />
                  </Grid>
                )}
              </Grid>
            )}

            {activeTab === 1 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Step Content
                </Typography>
                <ReactQuill
                  theme="snow"
                  value={currentStep?.content || ''}
                  onChange={handleContentChange}
                  style={{ height: '300px', marginBottom: '50px' }}
                  modules={{
                    toolbar: [
                      [{ header: [1, 2, 3, false] }],
                      ['bold', 'italic', 'underline', 'strike'],
                      [{ list: 'ordered' }, { list: 'bullet' }],
                      ['link', 'image'],
                      ['clean'],
                    ],
                  }}
                />
              </Box>
            )}

            {activeTab === 2 && currentStep?.type === 'code' && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>Programming Language</InputLabel>
                    <Select
                      name="language"
                      value={currentStep?.language || 'javascript'}
                      onChange={handleStepSelectChange}
                      label="Programming Language"
                    >
                      <MenuItem value="javascript">JavaScript</MenuItem>
                      <MenuItem value="typescript">TypeScript</MenuItem>
                      <MenuItem value="python">Python</MenuItem>
                      <MenuItem value="java">Java</MenuItem>
                      <MenuItem value="csharp">C#</MenuItem>
                      <MenuItem value="go">Go</MenuItem>
                      <MenuItem value="php">PHP</MenuItem>
                      <MenuItem value="ruby">Ruby</MenuItem>
                      <MenuItem value="swift">Swift</MenuItem>
                      <MenuItem value="kotlin">Kotlin</MenuItem>
                      <MenuItem value="html">HTML</MenuItem>
                      <MenuItem value="css">CSS</MenuItem>
                      <MenuItem value="sql">SQL</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    margin="dense"
                    name="codeSnippet"
                    label="Code Snippet"
                    multiline
                    rows={10}
                    fullWidth
                    variant="outlined"
                    value={currentStep?.codeSnippet || ''}
                    onChange={handleStepInputChange}
                  />
                </Grid>
              </Grid>
            )}

            {activeTab === 2 && currentStep?.type === 'quiz' && (
              <Typography variant="body1">
                Quiz configuration will be implemented in a future update.
              </Typography>
            )}

            {activeTab === 2 && currentStep?.type === 'interactive' && (
              <Typography variant="body1">
                Interactive content configuration will be implemented in a future update.
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseStepDialog}>Cancel</Button>
          <Button onClick={handleSaveStep} variant="contained" color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Step</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this step? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteStep} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Step 3: Preview & Publish
const PreviewPublishStep: React.FC<{
  tutorialData: Partial<Tutorial>;
  steps: TutorialStep[];
  onPublish: () => void;
  loading: boolean;
}> = ({ tutorialData, steps, onPublish, loading }) => {
  const [isPublished, setIsPublished] = useState(tutorialData.isPublished || false);

  const handlePublishChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(e.target.checked);
  };

  const renderStepContent = (step: TutorialStep) => {
    switch (step.type) {
      case 'text':
        return <ReactMarkdown>{step.content}</ReactMarkdown>;
      case 'image':
        return (
          <Box sx={{ textAlign: 'center', my: 2 }}>
            <img
              src={step.mediaURL}
              alt={step.title}
              style={{ maxWidth: '100%', maxHeight: '400px' }}
            />
            <Typography variant="caption" display="block">
              {step.content}
            </Typography>
          </Box>
        );
      case 'video':
        return (
          <Box sx={{ my: 2 }}>
            <Box sx={{ position: 'relative', paddingTop: '56.25%', mb: 2 }}>
              <iframe
                src={step.mediaURL}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none',
                }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title={step.title}
              />
            </Box>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      case 'code':
        return (
          <Box sx={{ my: 2 }}>
            <SyntaxHighlighter language={step.language || 'javascript'} style={materialDark}>
              {step.codeSnippet || ''}
            </SyntaxHighlighter>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      case 'quiz':
        return (
          <Box sx={{ my: 2 }}>
            <Alert severity="info">
              Quiz content will be displayed to users when they view the tutorial.
            </Alert>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      case 'interactive':
        return (
          <Box sx={{ my: 2 }}>
            <Alert severity="info">
              Interactive content will be displayed to users when they view the tutorial.
            </Alert>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      default:
        return <ReactMarkdown>{step.content}</ReactMarkdown>;
    }
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">{tutorialData.title}</Typography>
          <Chip
            label={tutorialData.difficulty}
            color={
              tutorialData.difficulty === 'beginner'
                ? 'success'
                : tutorialData.difficulty === 'intermediate'
                ? 'primary'
                : 'secondary'
            }
          />
        </Box>
        <Typography variant="body1" paragraph>
          {tutorialData.description}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {tutorialData.tags?.map((tag) => (
            <Chip key={tag} label={tag} variant="outlined" size="small" />
          ))}
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
            Estimated Time: {tutorialData.estimatedTime} minutes
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Prerequisites: {tutorialData.prerequisites || 'None'}
          </Typography>
        </Box>
      </Paper>

      <Typography variant="h6" gutterBottom>
        Tutorial Steps Preview
      </Typography>

      {steps.length === 0 ? (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No steps have been added to this tutorial yet. Go back to the previous step to add content.
        </Alert>
      ) : (
        steps
          .sort((a, b) => a.order - b.order)
          .map((step, index) => (
            <Paper key={step.id} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Step {index + 1}: {step.title}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {renderStepContent(step)}
            </Paper>
          ))
      )}

      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Publish Settings
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={isPublished}
              onChange={handlePublishChange}
              color="primary"
            />
          }
          label="Publish this tutorial"
        />
        <FormHelperText>
          {isPublished
            ? 'This tutorial will be visible to all users once published.'
            : 'This tutorial will be saved as a draft and only visible to you.'}
        </FormHelperText>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onPublish}
            disabled={loading || steps.length === 0}
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {isPublished ? 'Publish Tutorial' : 'Save as Draft'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

// Main Tutorial Builder Component
const TutorialBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useSelector((state: RootState) => state.auth);
  const { data: currentTutorial, loading: tutorialLoading, error: tutorialError } = useSelector(
    (state: RootState) => state.tutorials.currentTutorial
  );
  const { items: steps, loading: stepsLoading, error: stepsError } = useSelector(
    (state: RootState) => state.tutorials.tutorialSteps
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.tutorials.categories
  );

  const [activeStep, setActiveStep] = useState(0);
  const [tutorialData, setTutorialData] = useState<Partial<Tutorial>>({
    title: '',
    description: '',
    difficulty: 'beginner',
    estimatedTime: 30,
    prerequisites: '',
    tags: [],
    isPublished: false,
  });
  const [tutorialSteps, setTutorialSteps] = useState<TutorialStep[]>([]);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    dispatch(fetchCategories());

    if (id) {
      dispatch(fetchTutorialById(parseInt(id)));
      dispatch(fetchTutorialSteps(parseInt(id)));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (currentTutorial) {
      setTutorialData(currentTutorial);
    }
  }, [currentTutorial]);

  useEffect(() => {
    if (steps) {
      setTutorialSteps(steps);
    }
  }, [steps]);

  const handleNext = async () => {
    if (activeStep === 0) {
      // Save basic information
      try {
        setSaving(true);
        setError(null);

        const tutorialToSave = {
          ...tutorialData,
          authorId: user?.id,
          authorName: user?.name,
        };

        let savedTutorial;

        if (id) {
          // Update existing tutorial
          savedTutorial = await dispatch(
            updateTutorial({ id: parseInt(id), tutorial: tutorialToSave })
          ).unwrap();
        } else {
          // Create new tutorial
          savedTutorial = await dispatch(createTutorial(tutorialToSave)).unwrap();

          // Redirect to the edit page with the new ID
          navigate(`/tutorials/edit/${savedTutorial.id}`, { replace: true });
        }

        setTutorialData(savedTutorial);
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      } catch (err: any) {
        setError(err.message || 'Failed to save tutorial information');
      } finally {
        setSaving(false);
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handlePublish = async () => {
    try {
      setSaving(true);
      setError(null);

      // Update the tutorial with the published status
      const updatedTutorial = await dispatch(
        updateTutorial({
          id: parseInt(id!),
          tutorial: {
            ...tutorialData,
            isPublished: true,
          },
        })
      ).unwrap();

      // Navigate to the tutorial view page
      navigate(`/tutorials/${updatedTutorial.id}`);
    } catch (err: any) {
      setError(err.message || 'Failed to publish tutorial');
    } finally {
      setSaving(false);
    }
  };

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to create or edit tutorials. Please log in and try again.
        </Alert>
      </Container>
    );
  }

  const steps = ['Basic Information', 'Content Steps', 'Preview & Publish'];

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <BasicInformationStep
            tutorialData={tutorialData}
            setTutorialData={setTutorialData}
            categories={categories}
            categoriesLoading={categoriesLoading}
          />
        );
      case 1:
        return (
          <ContentStepsStep
            tutorialId={id ? parseInt(id) : null}
            steps={tutorialSteps}
            setSteps={setTutorialSteps}
            loading={stepsLoading}
            error={stepsError}
          />
        );
      case 2:
        return (
          <PreviewPublishStep
            tutorialData={tutorialData}
            steps={tutorialSteps}
            onPublish={handlePublish}
            loading={saving}
          />
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {id ? 'Edit Tutorial' : 'Create New Tutorial'}
        </Typography>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {(tutorialLoading || stepsLoading) && !error ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error || tutorialError ? (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error || tutorialError}
          </Alert>
        ) : (
          <>
            <Box sx={{ mb: 3 }}>{getStepContent(activeStep)}</Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                variant="outlined"
              >
                Back
              </Button>
              <Box>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/tutorials/my-tutorials')}
                  sx={{ mr: 1 }}
                >
                  Cancel
                </Button>
                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handlePublish}
                    disabled={saving || tutorialSteps.length === 0}
                    startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    {tutorialData.isPublished ? 'Publish Tutorial' : 'Save as Draft'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    disabled={
                      saving ||
                      (activeStep === 0 && (!tutorialData.title || !tutorialData.description))
                    }
                  >
                    {saving ? <CircularProgress size={24} /> : 'Next'}
                  </Button>
                )}
              </Box>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default TutorialBuilder;
