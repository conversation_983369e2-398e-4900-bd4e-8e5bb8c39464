import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as ApproveIcon,
  Close as RejectIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import { useNavigate } from 'react-router-dom';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Sample data for demonstration
const SAMPLE_STREAMS = [
  { id: 1, title: 'Introduction to Nigerian Literature', creatorId: 101, status: 'live', viewerCount: 245, totalGiftsValue: 15000, createdAt: '2023-06-15T14:30:00Z' },
  { id: 2, title: 'Discussing Chinua Achebe\'s Legacy', creatorId: 102, status: 'ended', viewerCount: 0, totalGiftsValue: 8500, createdAt: '2023-06-14T10:15:00Z' },
  { id: 3, title: 'Modern Nigerian Poetry Reading', creatorId: 103, status: 'scheduled', viewerCount: 0, totalGiftsValue: 0, createdAt: '2023-06-16T09:45:00Z' }
];

const SAMPLE_WITHDRAWALS = [
  { id: 1, creatorId: 101, amount: 12500, status: 'pending', bankName: 'First Bank', accountNumber: '**********', createdAt: '2023-06-15T16:45:00Z' },
  { id: 2, creatorId: 102, amount: 7500, status: 'approved', bankName: 'GTBank', accountNumber: '**********', createdAt: '2023-06-14T11:30:00Z' },
  { id: 3, creatorId: 103, amount: 5000, status: 'completed', bankName: 'Zenith Bank', accountNumber: '**********', createdAt: '2023-06-13T09:15:00Z' },
  { id: 4, creatorId: 104, amount: 3500, status: 'rejected', bankName: 'Access Bank', accountNumber: '**********', createdAt: '2023-06-12T14:20:00Z' }
];

const SAMPLE_COIN_PACKAGES = [
  { id: 1, name: 'Starter Pack', coinsAmount: 100, bonusCoins: 0, priceNaira: 1000, isActive: true, isPromotional: false },
  { id: 2, name: 'Popular Pack', coinsAmount: 500, bonusCoins: 50, priceNaira: 4500, isActive: true, isPromotional: false },
  { id: 3, name: 'Premium Pack', coinsAmount: 1000, bonusCoins: 150, priceNaira: 8000, isActive: true, isPromotional: false },
  { id: 4, name: 'Super Pack', coinsAmount: 5000, bonusCoins: 1000, priceNaira: 35000, isActive: true, isPromotional: false },
  { id: 5, name: 'Special Offer', coinsAmount: 2000, bonusCoins: 500, priceNaira: 15000, isActive: true, isPromotional: true }
];

const LivestreamAdminPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [tabValue, setTabValue] = useState(0);
  const [streams, setStreams] = useState(SAMPLE_STREAMS);
  const [withdrawals, setWithdrawals] = useState(SAMPLE_WITHDRAWALS);
  const [coinPackages, setCoinPackages] = useState(SAMPLE_COIN_PACKAGES);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Package dialog state
  const [isPackageDialogOpen, setIsPackageDialogOpen] = useState(false);
  const [editingPackage, setEditingPackage] = useState<any | null>(null);
  
  // Check if user is admin
  useEffect(() => {
    if (user && user.role !== 'admin') {
      navigate('/');
    }
  }, [user, navigate]);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const handleRefresh = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };
  
  // Stream management
  const handleFeatureStream = (streamId: number) => {
    // In a real app, this would call an API to feature the stream
    console.log(`Featuring stream ${streamId}`);
  };
  
  const handleDeleteStream = (streamId: number) => {
    // In a real app, this would call an API to delete the stream
    setStreams(streams.filter(stream => stream.id !== streamId));
  };
  
  // Withdrawal management
  const handleApproveWithdrawal = (withdrawalId: number) => {
    // In a real app, this would call an API to approve the withdrawal
    setWithdrawals(withdrawals.map(withdrawal => 
      withdrawal.id === withdrawalId 
        ? { ...withdrawal, status: 'approved' } 
        : withdrawal
    ));
  };
  
  const handleRejectWithdrawal = (withdrawalId: number) => {
    // In a real app, this would call an API to reject the withdrawal
    setWithdrawals(withdrawals.map(withdrawal => 
      withdrawal.id === withdrawalId 
        ? { ...withdrawal, status: 'rejected' } 
        : withdrawal
    ));
  };
  
  // Coin package management
  const handleAddPackage = () => {
    setEditingPackage({
      id: 0,
      name: '',
      coinsAmount: 0,
      bonusCoins: 0,
      priceNaira: 0,
      isActive: true,
      isPromotional: false
    });
    setIsPackageDialogOpen(true);
  };
  
  const handleEditPackage = (packageId: number) => {
    const pkg = coinPackages.find(p => p.id === packageId);
    if (pkg) {
      setEditingPackage({ ...pkg });
      setIsPackageDialogOpen(true);
    }
  };
  
  const handleDeletePackage = (packageId: number) => {
    // In a real app, this would call an API to delete the package
    setCoinPackages(coinPackages.filter(pkg => pkg.id !== packageId));
  };
  
  const handleSavePackage = () => {
    if (!editingPackage) return;
    
    if (editingPackage.id === 0) {
      // Add new package
      const newPackage = {
        ...editingPackage,
        id: Math.max(...coinPackages.map(p => p.id)) + 1
      };
      setCoinPackages([...coinPackages, newPackage]);
    } else {
      // Update existing package
      setCoinPackages(coinPackages.map(pkg => 
        pkg.id === editingPackage.id ? editingPackage : pkg
      ));
    }
    
    setIsPackageDialogOpen(false);
    setEditingPackage(null);
  };
  
  // Filter streams based on search term
  const filteredStreams = streams.filter(stream => 
    stream.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stream.creatorId.toString().includes(searchTerm)
  );
  
  // Filter withdrawals based on search term
  const filteredWithdrawals = withdrawals.filter(withdrawal => 
    withdrawal.creatorId.toString().includes(searchTerm) ||
    withdrawal.bankName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    withdrawal.accountNumber.includes(searchTerm)
  );
  
  // Filter coin packages based on search term
  const filteredCoinPackages = coinPackages.filter(pkg => 
    pkg.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Livestream Admin Dashboard
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Box sx={{ mb: 3 }}>
        <TextField
          label="Search"
          variant="outlined"
          fullWidth
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={handleRefresh} disabled={isLoading}>
                  {isLoading ? <CircularProgress size={24} /> : <RefreshIcon />}
                </IconButton>
              </InputAdornment>
            )
          }}
        />
      </Box>
      
      <Paper elevation={2}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
        >
          <Tab label="Streams" id="admin-tab-0" aria-controls="admin-tabpanel-0" />
          <Tab label="Withdrawals" id="admin-tab-1" aria-controls="admin-tabpanel-1" />
          <Tab label="Coin Packages" id="admin-tab-2" aria-controls="admin-tabpanel-2" />
        </Tabs>
        
        {/* Streams Tab */}
        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>Creator</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Viewers</TableCell>
                  <TableCell>Revenue</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredStreams.map((stream) => (
                  <TableRow key={stream.id}>
                    <TableCell>{stream.id}</TableCell>
                    <TableCell>{stream.title}</TableCell>
                    <TableCell>User #{stream.creatorId}</TableCell>
                    <TableCell>
                      <Chip 
                        label={stream.status.toUpperCase()} 
                        color={
                          stream.status === 'live' ? 'success' : 
                          stream.status === 'scheduled' ? 'primary' : 
                          'default'
                        } 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>{stream.viewerCount}</TableCell>
                    <TableCell>₦{stream.totalGiftsValue.toLocaleString()}</TableCell>
                    <TableCell>{new Date(stream.createdAt).toLocaleString()}</TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Button 
                          size="small" 
                          variant="outlined" 
                          onClick={() => handleFeatureStream(stream.id)}
                        >
                          Feature
                        </Button>
                        <IconButton 
                          size="small" 
                          color="error" 
                          onClick={() => handleDeleteStream(stream.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
                
                {filteredStreams.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No streams found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
        
        {/* Withdrawals Tab */}
        <TabPanel value={tabValue} index={1}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Creator</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Bank</TableCell>
                  <TableCell>Account</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Requested</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredWithdrawals.map((withdrawal) => (
                  <TableRow key={withdrawal.id}>
                    <TableCell>{withdrawal.id}</TableCell>
                    <TableCell>User #{withdrawal.creatorId}</TableCell>
                    <TableCell>₦{withdrawal.amount.toLocaleString()}</TableCell>
                    <TableCell>{withdrawal.bankName}</TableCell>
                    <TableCell>{withdrawal.accountNumber}</TableCell>
                    <TableCell>
                      <Chip 
                        label={withdrawal.status.toUpperCase()} 
                        color={
                          withdrawal.status === 'completed' ? 'success' : 
                          withdrawal.status === 'approved' ? 'primary' : 
                          withdrawal.status === 'rejected' ? 'error' : 
                          'warning'
                        } 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>{new Date(withdrawal.createdAt).toLocaleString()}</TableCell>
                    <TableCell>
                      {withdrawal.status === 'pending' && (
                        <Box display="flex" gap={1}>
                          <IconButton 
                            size="small" 
                            color="success" 
                            onClick={() => handleApproveWithdrawal(withdrawal.id)}
                          >
                            <ApproveIcon />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            color="error" 
                            onClick={() => handleRejectWithdrawal(withdrawal.id)}
                          >
                            <RejectIcon />
                          </IconButton>
                        </Box>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                
                {filteredWithdrawals.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No withdrawals found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
        
        {/* Coin Packages Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddPackage}
            >
              Add Package
            </Button>
          </Box>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Coins</TableCell>
                  <TableCell>Bonus</TableCell>
                  <TableCell>Price (₦)</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Promotional</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCoinPackages.map((pkg) => (
                  <TableRow key={pkg.id}>
                    <TableCell>{pkg.id}</TableCell>
                    <TableCell>{pkg.name}</TableCell>
                    <TableCell>{pkg.coinsAmount}</TableCell>
                    <TableCell>{pkg.bonusCoins}</TableCell>
                    <TableCell>₦{pkg.priceNaira.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip 
                        label={pkg.isActive ? 'ACTIVE' : 'INACTIVE'} 
                        color={pkg.isActive ? 'success' : 'default'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={pkg.isPromotional ? 'YES' : 'NO'} 
                        color={pkg.isPromotional ? 'secondary' : 'default'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton 
                          size="small" 
                          color="primary" 
                          onClick={() => handleEditPackage(pkg.id)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="error" 
                          onClick={() => handleDeletePackage(pkg.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
                
                {filteredCoinPackages.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No coin packages found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>
      
      {/* Coin Package Dialog */}
      <Dialog 
        open={isPackageDialogOpen} 
        onClose={() => setIsPackageDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingPackage?.id === 0 ? 'Add Coin Package' : 'Edit Coin Package'}
        </DialogTitle>
        
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Package Name"
                fullWidth
                value={editingPackage?.name || ''}
                onChange={(e) => setEditingPackage({ ...editingPackage, name: e.target.value })}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Coins Amount"
                fullWidth
                type="number"
                value={editingPackage?.coinsAmount || 0}
                onChange={(e) => setEditingPackage({ ...editingPackage, coinsAmount: parseInt(e.target.value) })}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Bonus Coins"
                fullWidth
                type="number"
                value={editingPackage?.bonusCoins || 0}
                onChange={(e) => setEditingPackage({ ...editingPackage, bonusCoins: parseInt(e.target.value) })}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                label="Price (₦)"
                fullWidth
                type="number"
                value={editingPackage?.priceNaira || 0}
                onChange={(e) => setEditingPackage({ ...editingPackage, priceNaira: parseInt(e.target.value) })}
                required
                InputProps={{
                  startAdornment: <InputAdornment position="start">₦</InputAdornment>,
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={editingPackage?.isActive ? 'active' : 'inactive'}
                  onChange={(e) => setEditingPackage({ ...editingPackage, isActive: e.target.value === 'active' })}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Promotional</InputLabel>
                <Select
                  value={editingPackage?.isPromotional ? 'yes' : 'no'}
                  onChange={(e) => setEditingPackage({ ...editingPackage, isPromotional: e.target.value === 'yes' })}
                  label="Promotional"
                >
                  <MenuItem value="yes">Yes</MenuItem>
                  <MenuItem value="no">No</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setIsPackageDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleSavePackage}
            disabled={!editingPackage?.name || !editingPackage?.coinsAmount || !editingPackage?.priceNaira}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default LivestreamAdminPage;
