import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  Divider,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  VerifiedUser as VerifiedIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchSkillNeeds,
  fetchUserSkillNeeds,
  fetchSkillNeedById,
  createSkillNeed,
  updateSkillNeed,
  deleteSkillNeed,
  fetchSkillMatches,
  fetchUserSkillMatches,
  updateMatchStatus,
  findMatches,
  fetchSkills,
  fetchSkillCategories,
} from '../store/slices/skillsSlice';
import { SkillNeed, SkillMatch, RequiredSkill, Skill } from '../services/skillsService';

// Helper components
const SkillNeedCard: React.FC<{
  need: SkillNeed;
  onView: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onFindMatches?: () => void;
  isOwner: boolean;
}> = ({ need, onView, onEdit, onDelete, onFindMatches, isOwner }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return '#4caf50'; // green
      case 'in-progress':
        return '#2196f3'; // blue
      case 'completed':
        return '#9e9e9e'; // grey
      case 'cancelled':
        return '#f44336'; // red
      default:
        return '#9e9e9e'; // grey
    }
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography variant="h6" component="div">
            {need.title}
          </Typography>
          <Chip
            label={need.status.charAt(0).toUpperCase() + need.status.slice(1)}
            size="small"
            sx={{ bgcolor: getStatusColor(need.status), color: 'white' }}
          />
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
          {need.description.length > 150
            ? `${need.description.substring(0, 150)}...`
            : need.description}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {need.projectType.charAt(0).toUpperCase() + need.projectType.slice(1)}
          </Typography>
        </Box>
        
        {need.location && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              {need.location} {need.isRemote && '(Remote Available)'}
            </Typography>
          </Box>
        )}
        
        {need.expiresAt && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              Expires: {new Date(need.expiresAt).toLocaleDateString()}
            </Typography>
          </Box>
        )}
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Required Skills:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {need.requiredSkills.map((requiredSkill) => (
              <Chip
                key={requiredSkill.skillId}
                label={`${requiredSkill.skill.name} (${
                  requiredSkill.minimumLevel.charAt(0).toUpperCase() +
                  requiredSkill.minimumLevel.slice(1)
                })`}
                size="small"
                color={requiredSkill.isRequired ? 'primary' : 'default'}
                variant={requiredSkill.isRequired ? 'filled' : 'outlined'}
              />
            ))}
          </Box>
        </Box>
      </CardContent>
      <CardActions>
        <Button size="small" onClick={onView}>
          View Details
        </Button>
        {isOwner && (
          <>
            {onEdit && (
              <Button size="small" startIcon={<EditIcon />} onClick={onEdit}>
                Edit
              </Button>
            )}
            {onFindMatches && need.status === 'open' && (
              <Button size="small" startIcon={<SearchIcon />} onClick={onFindMatches}>
                Find Matches
              </Button>
            )}
            {onDelete && (
              <Button size="small" color="error" startIcon={<DeleteIcon />} onClick={onDelete}>
                Delete
              </Button>
            )}
          </>
        )}
      </CardActions>
    </Card>
  );
};

const MatchCard: React.FC<{
  match: SkillMatch;
  onAccept?: () => void;
  onReject?: () => void;
  onComplete?: () => void;
  isNeedOwner: boolean;
}> = ({ match, onAccept, onReject, onComplete, isNeedOwner }) => {
  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ mr: 2 }}>
              {match.userProfileImage ? (
                <img src={match.userProfileImage} alt={match.userName} />
              ) : (
                <PersonIcon />
              )}
            </Avatar>
            <Box>
              <Typography variant="h6">{match.userName}</Typography>
              <Chip
                label={`${match.matchScore}% Match`}
                size="small"
                color={match.matchScore > 80 ? 'success' : match.matchScore > 60 ? 'primary' : 'default'}
                sx={{ mt: 0.5 }}
              />
            </Box>
          </Box>
          <Chip
            label={match.status.charAt(0).toUpperCase() + match.status.slice(1)}
            size="small"
            color={
              match.status === 'accepted'
                ? 'success'
                : match.status === 'rejected'
                ? 'error'
                : match.status === 'completed'
                ? 'default'
                : 'primary'
            }
          />
        </Box>
        
        <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
          Matched Skills:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
          {match.matchedSkills.map((skill, index) => (
            <Chip
              key={index}
              label={`${skill.skillName} (${skill.userLevel})`}
              size="small"
              color={skill.matchScore > 80 ? 'success' : 'default'}
              variant="outlined"
            />
          ))}
        </Box>
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Matched on {new Date(match.createdAt).toLocaleDateString()}
          </Typography>
        </Box>
      </CardContent>
      {isNeedOwner && match.status === 'pending' && (
        <CardActions>
          {onAccept && (
            <Button size="small" color="success" startIcon={<CheckIcon />} onClick={onAccept}>
              Accept
            </Button>
          )}
          {onReject && (
            <Button size="small" color="error" startIcon={<CloseIcon />} onClick={onReject}>
              Reject
            </Button>
          )}
        </CardActions>
      )}
      {isNeedOwner && match.status === 'accepted' && onComplete && (
        <CardActions>
          <Button size="small" color="primary" startIcon={<CheckIcon />} onClick={onComplete}>
            Mark as Completed
          </Button>
        </CardActions>
      )}
    </Card>
  );
};

// Main component
const SkillMatchingInterface: React.FC = () => {
  const { needId } = useParams<{ needId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { items: skillNeeds, loading: needsLoading, error: needsError } = useSelector(
    (state: RootState) => state.skills.skillNeeds
  );
  const { data: currentNeed, loading: currentNeedLoading } = useSelector(
    (state: RootState) => state.skills.currentNeed
  );
  const { items: skillMatches, loading: matchesLoading } = useSelector(
    (state: RootState) => state.skills.skillMatches
  );
  const { items: skills, loading: skillsLoading } = useSelector(
    (state: RootState) => state.skills.skills
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.skills.categories
  );
  
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedNeed, setSelectedNeed] = useState<SkillNeed | null>(null);
  const [selectedMatch, setSelectedMatch] = useState<SkillMatch | null>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [statusAction, setStatusAction] = useState<'accept' | 'reject' | 'complete' | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [findMatchesDialogOpen, setFindMatchesDialogOpen] = useState(false);
  
  useEffect(() => {
    if (user) {
      dispatch(fetchUserSkillNeeds(user.id));
      dispatch(fetchUserSkillMatches(user.id));
      dispatch(fetchSkills());
      dispatch(fetchSkillCategories());
    }
    
    if (needId) {
      dispatch(fetchSkillNeedById(parseInt(needId)));
      dispatch(fetchSkillMatches(parseInt(needId)));
    }
  }, [dispatch, user, needId]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleViewNeed = (need: SkillNeed) => {
    setSelectedNeed(need);
    setDetailsDialogOpen(true);
  };
  
  const handleEditNeed = (need: SkillNeed) => {
    setSelectedNeed(need);
    setEditDialogOpen(true);
  };
  
  const handleDeleteNeed = (need: SkillNeed) => {
    setSelectedNeed(need);
    setDeleteDialogOpen(true);
  };
  
  const handleConfirmDelete = async () => {
    if (!selectedNeed) return;
    
    try {
      await dispatch(deleteSkillNeed(selectedNeed.id)).unwrap();
      setDeleteDialogOpen(false);
      setSelectedNeed(null);
    } catch (error) {
      console.error('Failed to delete skill need:', error);
    }
  };
  
  const handleFindMatches = (need: SkillNeed) => {
    setSelectedNeed(need);
    setFindMatchesDialogOpen(true);
  };
  
  const handleConfirmFindMatches = async () => {
    if (!selectedNeed) return;
    
    try {
      await dispatch(findMatches(selectedNeed.id)).unwrap();
      setFindMatchesDialogOpen(false);
      navigate(`/skills/needs/${selectedNeed.id}/matches`);
    } catch (error) {
      console.error('Failed to find matches:', error);
    }
  };
  
  const handleAcceptMatch = (match: SkillMatch) => {
    setSelectedMatch(match);
    setStatusAction('accept');
    setStatusDialogOpen(true);
  };
  
  const handleRejectMatch = (match: SkillMatch) => {
    setSelectedMatch(match);
    setStatusAction('reject');
    setStatusDialogOpen(true);
  };
  
  const handleCompleteMatch = (match: SkillMatch) => {
    setSelectedMatch(match);
    setStatusAction('complete');
    setStatusDialogOpen(true);
  };
  
  const handleConfirmStatusChange = async () => {
    if (!selectedMatch || !statusAction) return;
    
    const status = statusAction === 'accept' ? 'accepted' : statusAction === 'reject' ? 'rejected' : 'completed';
    
    try {
      await dispatch(updateMatchStatus({ matchId: selectedMatch.id, status })).unwrap();
      setStatusDialogOpen(false);
      setSelectedMatch(null);
      setStatusAction(null);
    } catch (error) {
      console.error('Failed to update match status:', error);
    }
  };
  
  const loading = needsLoading || currentNeedLoading || matchesLoading || skillsLoading || categoriesLoading;
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to use the skill matching interface. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Skill Matching
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Skill Need
        </Button>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="My Skill Needs" />
          <Tab label="My Matches" />
          <Tab label="Browse Opportunities" />
        </Tabs>
      </Paper>
      
      {needsError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {needsError}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* My Skill Needs Tab */}
          {tabValue === 0 && (
            <>
              {skillNeeds.filter(need => need.userId === user.id).length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <WorkIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Skill Needs Created Yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Create a skill need to find people with the skills you're looking for.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    Create Your First Skill Need
                  </Button>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {skillNeeds
                    .filter(need => need.userId === user.id)
                    .map((need) => (
                      <Grid item key={need.id} xs={12} sm={6} md={4}>
                        <SkillNeedCard
                          need={need}
                          onView={() => handleViewNeed(need)}
                          onEdit={() => handleEditNeed(need)}
                          onDelete={() => handleDeleteNeed(need)}
                          onFindMatches={() => handleFindMatches(need)}
                          isOwner={true}
                        />
                      </Grid>
                    ))}
                </Grid>
              )}
            </>
          )}
          
          {/* My Matches Tab */}
          {tabValue === 1 && (
            <>
              {skillMatches.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <SearchIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Matches Found Yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Create a skill need and find matches, or browse opportunities that match your skills.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setTabValue(2)}
                  >
                    Browse Opportunities
                  </Button>
                </Paper>
              ) : (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Your Matches
                  </Typography>
                  {skillMatches.map((match) => (
                    <MatchCard
                      key={match.id}
                      match={match}
                      onAccept={() => handleAcceptMatch(match)}
                      onReject={() => handleRejectMatch(match)}
                      onComplete={() => handleCompleteMatch(match)}
                      isNeedOwner={match.need.userId === user.id}
                    />
                  ))}
                </Box>
              )}
            </>
          )}
          
          {/* Browse Opportunities Tab */}
          {tabValue === 2 && (
            <>
              {skillNeeds.filter(need => need.userId !== user.id && need.status === 'open').length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <WorkIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Open Opportunities
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    There are no open skill needs at the moment. Check back later or create your own.
                  </Typography>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {skillNeeds
                    .filter(need => need.userId !== user.id && need.status === 'open')
                    .map((need) => (
                      <Grid item key={need.id} xs={12} sm={6} md={4}>
                        <SkillNeedCard
                          need={need}
                          onView={() => handleViewNeed(need)}
                          isOwner={false}
                        />
                      </Grid>
                    ))}
                </Grid>
              )}
            </>
          )}
        </>
      )}
      
      {/* Details Dialog */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="md" fullWidth>
        {selectedNeed && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {selectedNeed.title}
                <Chip
                  label={selectedNeed.status.charAt(0).toUpperCase() + selectedNeed.status.slice(1)}
                  size="small"
                  color={
                    selectedNeed.status === 'open'
                      ? 'success'
                      : selectedNeed.status === 'in-progress'
                      ? 'primary'
                      : selectedNeed.status === 'completed'
                      ? 'default'
                      : 'error'
                  }
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                {selectedNeed.description}
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Project Type
                  </Typography>
                  <Typography variant="body2">
                    {selectedNeed.projectType.charAt(0).toUpperCase() + selectedNeed.projectType.slice(1)}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Location
                  </Typography>
                  <Typography variant="body2">
                    {selectedNeed.location || 'Not specified'} {selectedNeed.isRemote && '(Remote Available)'}
                  </Typography>
                </Grid>
                
                {selectedNeed.compensation && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Compensation
                    </Typography>
                    <Typography variant="body2">{selectedNeed.compensation}</Typography>
                  </Grid>
                )}
                
                {selectedNeed.expiresAt && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Expires
                    </Typography>
                    <Typography variant="body2">
                      {new Date(selectedNeed.expiresAt).toLocaleDateString()}
                    </Typography>
                  </Grid>
                )}
              </Grid>
              
              <Typography variant="h6" gutterBottom>
                Required Skills
              </Typography>
              <List>
                {selectedNeed.requiredSkills.map((requiredSkill) => (
                  <ListItem key={requiredSkill.skillId}>
                    <ListItemText
                      primary={requiredSkill.skill.name}
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            Minimum Level: {requiredSkill.minimumLevel.charAt(0).toUpperCase() + requiredSkill.minimumLevel.slice(1)}
                          </Typography>
                          <br />
                          <Typography variant="body2" component="span">
                            Importance: {requiredSkill.weight}/10
                          </Typography>
                        </>
                      }
                    />
                    {requiredSkill.isRequired && (
                      <Chip label="Required" color="primary" size="small" />
                    )}
                  </ListItem>
                ))}
              </List>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsDialogOpen(false)}>Close</Button>
              {selectedNeed.userId === user.id && (
                <>
                  <Button onClick={() => {
                    setDetailsDialogOpen(false);
                    handleEditNeed(selectedNeed);
                  }} color="primary">
                    Edit
                  </Button>
                  {selectedNeed.status === 'open' && (
                    <Button onClick={() => {
                      setDetailsDialogOpen(false);
                      handleFindMatches(selectedNeed);
                    }} color="primary" variant="contained">
                      Find Matches
                    </Button>
                  )}
                </>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Skill Need</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{selectedNeed?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Find Matches Confirmation Dialog */}
      <Dialog open={findMatchesDialogOpen} onClose={() => setFindMatchesDialogOpen(false)}>
        <DialogTitle>Find Matches</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Do you want to find people who match the skills required for "{selectedNeed?.title}"?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFindMatchesDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmFindMatches} color="primary" variant="contained">
            Find Matches
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Status Change Dialog */}
      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)}>
        <DialogTitle>
          {statusAction === 'accept'
            ? 'Accept Match'
            : statusAction === 'reject'
            ? 'Reject Match'
            : 'Complete Match'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {statusAction === 'accept'
              ? `Are you sure you want to accept ${selectedMatch?.userName} for this skill need?`
              : statusAction === 'reject'
              ? `Are you sure you want to reject ${selectedMatch?.userName} for this skill need?`
              : `Are you sure you want to mark this match with ${selectedMatch?.userName} as completed?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleConfirmStatusChange}
            color={statusAction === 'reject' ? 'error' : 'primary'}
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SkillMatchingInterface;
