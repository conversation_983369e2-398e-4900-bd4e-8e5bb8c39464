import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import escrowService, {
  EscrowTransaction,
  Dispute,
  DisputeEvidence,
  DisputeMessage,
  EscrowCreateRequest,
  EscrowUpdateRequest,
  DisputeCreateRequest,
  DisputeEvidenceCreateRequest,
  DisputeMessageCreateRequest,
  DisputeResolutionRequest
} from '../api/escrowService';

interface EscrowState {
  transactions: {
    items: EscrowTransaction[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  currentTransaction: {
    item: EscrowTransaction | null;
    loading: boolean;
    error: string | null;
  };
  disputes: {
    items: Dispute[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  currentDispute: {
    item: Dispute | null;
    evidence: DisputeEvidence[];
    messages: DisputeMessage[];
    loading: boolean;
    error: string | null;
  };
  operations: {
    creating: boolean;
    updating: boolean;
    releasing: boolean;
    refunding: boolean;
    cancelling: boolean;
    addingEvidence: boolean;
    addingMessage: boolean;
    resolving: boolean;
    closing: boolean;
    error: string | null;
  };
}

const initialState: EscrowState = {
  transactions: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  currentTransaction: {
    item: null,
    loading: false,
    error: null
  },
  disputes: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  currentDispute: {
    item: null,
    evidence: [],
    messages: [],
    loading: false,
    error: null
  },
  operations: {
    creating: false,
    updating: false,
    releasing: false,
    refunding: false,
    cancelling: false,
    addingEvidence: false,
    addingMessage: false,
    resolving: false,
    closing: false,
    error: null
  }
};

// Escrow transaction thunks
export const fetchEscrowTransactions = createAsyncThunk(
  'escrow/fetchTransactions',
  async ({ page = 1, limit = 10, status }: { page?: number; limit?: number; status?: string }, { rejectWithValue }) => {
    try {
      return await escrowService.getEscrowTransactions(page, limit, status);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch escrow transactions');
    }
  }
);

export const fetchEscrowTransactionById = createAsyncThunk(
  'escrow/fetchTransactionById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await escrowService.getEscrowTransactionById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch escrow transaction');
    }
  }
);

export const fetchEscrowTransactionByOrderId = createAsyncThunk(
  'escrow/fetchTransactionByOrderId',
  async (orderId: string, { rejectWithValue }) => {
    try {
      return await escrowService.getEscrowTransactionByOrderId(orderId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch escrow transaction');
    }
  }
);

export const createEscrowTransaction = createAsyncThunk(
  'escrow/createTransaction',
  async (request: EscrowCreateRequest, { rejectWithValue }) => {
    try {
      return await escrowService.createEscrowTransaction(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create escrow transaction');
    }
  }
);

export const updateEscrowTransaction = createAsyncThunk(
  'escrow/updateTransaction',
  async ({ id, request }: { id: string; request: EscrowUpdateRequest }, { rejectWithValue }) => {
    try {
      return await escrowService.updateEscrowTransaction(id, request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update escrow transaction');
    }
  }
);

export const releaseEscrowFunds = createAsyncThunk(
  'escrow/releaseFunds',
  async (id: string, { rejectWithValue }) => {
    try {
      return await escrowService.releaseEscrowFunds(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to release escrow funds');
    }
  }
);

export const refundEscrowFunds = createAsyncThunk(
  'escrow/refundFunds',
  async (id: string, { rejectWithValue }) => {
    try {
      return await escrowService.refundEscrowFunds(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to refund escrow funds');
    }
  }
);

export const cancelEscrowTransaction = createAsyncThunk(
  'escrow/cancelTransaction',
  async (id: string, { rejectWithValue }) => {
    try {
      return await escrowService.cancelEscrowTransaction(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to cancel escrow transaction');
    }
  }
);

// Dispute thunks
export const fetchDisputes = createAsyncThunk(
  'escrow/fetchDisputes',
  async ({ page = 1, limit = 10, status }: { page?: number; limit?: number; status?: string }, { rejectWithValue }) => {
    try {
      return await escrowService.getDisputes(page, limit, status);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch disputes');
    }
  }
);

export const fetchDisputeById = createAsyncThunk(
  'escrow/fetchDisputeById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await escrowService.getDisputeById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dispute');
    }
  }
);

export const fetchDisputeByEscrowTransactionId = createAsyncThunk(
  'escrow/fetchDisputeByEscrowTransactionId',
  async (escrowTransactionId: string, { rejectWithValue }) => {
    try {
      return await escrowService.getDisputeByEscrowTransactionId(escrowTransactionId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dispute');
    }
  }
);

export const createDispute = createAsyncThunk(
  'escrow/createDispute',
  async (request: DisputeCreateRequest, { rejectWithValue }) => {
    try {
      return await escrowService.createDispute(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create dispute');
    }
  }
);

export const fetchDisputeEvidence = createAsyncThunk(
  'escrow/fetchDisputeEvidence',
  async (disputeId: string, { rejectWithValue }) => {
    try {
      return await escrowService.getDisputeEvidence(disputeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dispute evidence');
    }
  }
);

export const addDisputeEvidence = createAsyncThunk(
  'escrow/addDisputeEvidence',
  async (
    { disputeId, evidence }: { disputeId: string; evidence: DisputeEvidenceCreateRequest },
    { rejectWithValue }
  ) => {
    try {
      return await escrowService.addDisputeEvidence(disputeId, evidence);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add dispute evidence');
    }
  }
);

export const fetchDisputeMessages = createAsyncThunk(
  'escrow/fetchDisputeMessages',
  async (disputeId: string, { rejectWithValue }) => {
    try {
      return await escrowService.getDisputeMessages(disputeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dispute messages');
    }
  }
);

export const addDisputeMessage = createAsyncThunk(
  'escrow/addDisputeMessage',
  async (
    { disputeId, message }: { disputeId: string; message: DisputeMessageCreateRequest },
    { rejectWithValue }
  ) => {
    try {
      return await escrowService.addDisputeMessage(disputeId, message);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add dispute message');
    }
  }
);

export const resolveDispute = createAsyncThunk(
  'escrow/resolveDispute',
  async (
    { disputeId, resolution }: { disputeId: string; resolution: DisputeResolutionRequest },
    { rejectWithValue }
  ) => {
    try {
      return await escrowService.resolveDispute(disputeId, resolution);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to resolve dispute');
    }
  }
);

export const closeDispute = createAsyncThunk(
  'escrow/closeDispute',
  async (disputeId: string, { rejectWithValue }) => {
    try {
      return await escrowService.closeDispute(disputeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to close dispute');
    }
  }
);

export const markDisputeMessageAsRead = createAsyncThunk(
  'escrow/markDisputeMessageAsRead',
  async ({ disputeId, messageId }: { disputeId: string; messageId: string }, { rejectWithValue }) => {
    try {
      await escrowService.markDisputeMessageAsRead(disputeId, messageId);
      return messageId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark message as read');
    }
  }
);

const escrowSlice = createSlice({
  name: 'escrow',
  initialState,
  reducers: {
    clearCurrentTransaction: (state) => {
      state.currentTransaction.item = null;
      state.currentTransaction.error = null;
    },
    clearCurrentDispute: (state) => {
      state.currentDispute.item = null;
      state.currentDispute.evidence = [];
      state.currentDispute.messages = [];
      state.currentDispute.error = null;
    },
    clearOperationErrors: (state) => {
      state.operations.error = null;
    }
  },
  extraReducers: (builder) => {
    // Escrow transactions reducers
    builder
      .addCase(fetchEscrowTransactions.pending, (state) => {
        state.transactions.loading = true;
        state.transactions.error = null;
      })
      .addCase(fetchEscrowTransactions.fulfilled, (state, action: PayloadAction<{
        transactions: EscrowTransaction[];
        total: number;
        page: number;
        totalPages: number;
      }>) => {
        state.transactions.items = action.payload.transactions;
        state.transactions.total = action.payload.total;
        state.transactions.page = action.payload.page;
        state.transactions.totalPages = action.payload.totalPages;
        state.transactions.loading = false;
      })
      .addCase(fetchEscrowTransactions.rejected, (state, action) => {
        state.transactions.loading = false;
        state.transactions.error = action.payload as string;
      })
      
      .addCase(fetchEscrowTransactionById.pending, (state) => {
        state.currentTransaction.loading = true;
        state.currentTransaction.error = null;
      })
      .addCase(fetchEscrowTransactionById.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        state.currentTransaction.item = action.payload;
        state.currentTransaction.loading = false;
      })
      .addCase(fetchEscrowTransactionById.rejected, (state, action) => {
        state.currentTransaction.loading = false;
        state.currentTransaction.error = action.payload as string;
      })
      
      .addCase(fetchEscrowTransactionByOrderId.pending, (state) => {
        state.currentTransaction.loading = true;
        state.currentTransaction.error = null;
      })
      .addCase(fetchEscrowTransactionByOrderId.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        state.currentTransaction.item = action.payload;
        state.currentTransaction.loading = false;
      })
      .addCase(fetchEscrowTransactionByOrderId.rejected, (state, action) => {
        state.currentTransaction.loading = false;
        state.currentTransaction.error = action.payload as string;
      })
      
      .addCase(createEscrowTransaction.pending, (state) => {
        state.operations.creating = true;
        state.operations.error = null;
      })
      .addCase(createEscrowTransaction.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        state.transactions.items.unshift(action.payload);
        state.transactions.total += 1;
        state.currentTransaction.item = action.payload;
        state.operations.creating = false;
      })
      .addCase(createEscrowTransaction.rejected, (state, action) => {
        state.operations.creating = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(updateEscrowTransaction.pending, (state) => {
        state.operations.updating = true;
        state.operations.error = null;
      })
      .addCase(updateEscrowTransaction.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        const index = state.transactions.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.transactions.items[index] = action.payload;
        }
        if (state.currentTransaction.item?.id === action.payload.id) {
          state.currentTransaction.item = action.payload;
        }
        state.operations.updating = false;
      })
      .addCase(updateEscrowTransaction.rejected, (state, action) => {
        state.operations.updating = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(releaseEscrowFunds.pending, (state) => {
        state.operations.releasing = true;
        state.operations.error = null;
      })
      .addCase(releaseEscrowFunds.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        const index = state.transactions.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.transactions.items[index] = action.payload;
        }
        if (state.currentTransaction.item?.id === action.payload.id) {
          state.currentTransaction.item = action.payload;
        }
        state.operations.releasing = false;
      })
      .addCase(releaseEscrowFunds.rejected, (state, action) => {
        state.operations.releasing = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(refundEscrowFunds.pending, (state) => {
        state.operations.refunding = true;
        state.operations.error = null;
      })
      .addCase(refundEscrowFunds.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        const index = state.transactions.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.transactions.items[index] = action.payload;
        }
        if (state.currentTransaction.item?.id === action.payload.id) {
          state.currentTransaction.item = action.payload;
        }
        state.operations.refunding = false;
      })
      .addCase(refundEscrowFunds.rejected, (state, action) => {
        state.operations.refunding = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(cancelEscrowTransaction.pending, (state) => {
        state.operations.cancelling = true;
        state.operations.error = null;
      })
      .addCase(cancelEscrowTransaction.fulfilled, (state, action: PayloadAction<EscrowTransaction>) => {
        const index = state.transactions.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.transactions.items[index] = action.payload;
        }
        if (state.currentTransaction.item?.id === action.payload.id) {
          state.currentTransaction.item = action.payload;
        }
        state.operations.cancelling = false;
      })
      .addCase(cancelEscrowTransaction.rejected, (state, action) => {
        state.operations.cancelling = false;
        state.operations.error = action.payload as string;
      })
      
      // Dispute reducers
      .addCase(fetchDisputes.pending, (state) => {
        state.disputes.loading = true;
        state.disputes.error = null;
      })
      .addCase(fetchDisputes.fulfilled, (state, action: PayloadAction<{
        disputes: Dispute[];
        total: number;
        page: number;
        totalPages: number;
      }>) => {
        state.disputes.items = action.payload.disputes;
        state.disputes.total = action.payload.total;
        state.disputes.page = action.payload.page;
        state.disputes.totalPages = action.payload.totalPages;
        state.disputes.loading = false;
      })
      .addCase(fetchDisputes.rejected, (state, action) => {
        state.disputes.loading = false;
        state.disputes.error = action.payload as string;
      })
      
      .addCase(fetchDisputeById.pending, (state) => {
        state.currentDispute.loading = true;
        state.currentDispute.error = null;
      })
      .addCase(fetchDisputeById.fulfilled, (state, action: PayloadAction<Dispute>) => {
        state.currentDispute.item = action.payload;
        state.currentDispute.loading = false;
      })
      .addCase(fetchDisputeById.rejected, (state, action) => {
        state.currentDispute.loading = false;
        state.currentDispute.error = action.payload as string;
      })
      
      .addCase(fetchDisputeByEscrowTransactionId.pending, (state) => {
        state.currentDispute.loading = true;
        state.currentDispute.error = null;
      })
      .addCase(fetchDisputeByEscrowTransactionId.fulfilled, (state, action: PayloadAction<Dispute>) => {
        state.currentDispute.item = action.payload;
        state.currentDispute.loading = false;
      })
      .addCase(fetchDisputeByEscrowTransactionId.rejected, (state, action) => {
        state.currentDispute.loading = false;
        state.currentDispute.error = action.payload as string;
      })
      
      .addCase(createDispute.pending, (state) => {
        state.operations.creating = true;
        state.operations.error = null;
      })
      .addCase(createDispute.fulfilled, (state, action: PayloadAction<Dispute>) => {
        state.disputes.items.unshift(action.payload);
        state.disputes.total += 1;
        state.currentDispute.item = action.payload;
        state.operations.creating = false;
      })
      .addCase(createDispute.rejected, (state, action) => {
        state.operations.creating = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(fetchDisputeEvidence.pending, (state) => {
        state.currentDispute.loading = true;
      })
      .addCase(fetchDisputeEvidence.fulfilled, (state, action: PayloadAction<DisputeEvidence[]>) => {
        state.currentDispute.evidence = action.payload;
        state.currentDispute.loading = false;
      })
      .addCase(fetchDisputeEvidence.rejected, (state, action) => {
        state.currentDispute.loading = false;
        state.currentDispute.error = action.payload as string;
      })
      
      .addCase(addDisputeEvidence.pending, (state) => {
        state.operations.addingEvidence = true;
        state.operations.error = null;
      })
      .addCase(addDisputeEvidence.fulfilled, (state, action: PayloadAction<DisputeEvidence>) => {
        state.currentDispute.evidence.push(action.payload);
        state.operations.addingEvidence = false;
      })
      .addCase(addDisputeEvidence.rejected, (state, action) => {
        state.operations.addingEvidence = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(fetchDisputeMessages.pending, (state) => {
        state.currentDispute.loading = true;
      })
      .addCase(fetchDisputeMessages.fulfilled, (state, action: PayloadAction<DisputeMessage[]>) => {
        state.currentDispute.messages = action.payload;
        state.currentDispute.loading = false;
      })
      .addCase(fetchDisputeMessages.rejected, (state, action) => {
        state.currentDispute.loading = false;
        state.currentDispute.error = action.payload as string;
      })
      
      .addCase(addDisputeMessage.pending, (state) => {
        state.operations.addingMessage = true;
        state.operations.error = null;
      })
      .addCase(addDisputeMessage.fulfilled, (state, action: PayloadAction<DisputeMessage>) => {
        state.currentDispute.messages.push(action.payload);
        state.operations.addingMessage = false;
      })
      .addCase(addDisputeMessage.rejected, (state, action) => {
        state.operations.addingMessage = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(resolveDispute.pending, (state) => {
        state.operations.resolving = true;
        state.operations.error = null;
      })
      .addCase(resolveDispute.fulfilled, (state, action: PayloadAction<Dispute>) => {
        const index = state.disputes.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.disputes.items[index] = action.payload;
        }
        if (state.currentDispute.item?.id === action.payload.id) {
          state.currentDispute.item = action.payload;
        }
        state.operations.resolving = false;
      })
      .addCase(resolveDispute.rejected, (state, action) => {
        state.operations.resolving = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(closeDispute.pending, (state) => {
        state.operations.closing = true;
        state.operations.error = null;
      })
      .addCase(closeDispute.fulfilled, (state, action: PayloadAction<Dispute>) => {
        const index = state.disputes.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.disputes.items[index] = action.payload;
        }
        if (state.currentDispute.item?.id === action.payload.id) {
          state.currentDispute.item = action.payload;
        }
        state.operations.closing = false;
      })
      .addCase(closeDispute.rejected, (state, action) => {
        state.operations.closing = false;
        state.operations.error = action.payload as string;
      })
      
      .addCase(markDisputeMessageAsRead.fulfilled, (state, action: PayloadAction<string>) => {
        const messageId = action.payload;
        state.currentDispute.messages = state.currentDispute.messages.map(message => 
          message.id === messageId ? { ...message, readAt: new Date().toISOString() } : message
        );
      });
  }
});

export const {
  clearCurrentTransaction,
  clearCurrentDispute,
  clearOperationErrors
} = escrowSlice.actions;

export default escrowSlice.reducer;
