package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// GifterRankingHandler handles HTTP requests for gifter ranking operations
type GifterRankingHandler struct {
	service service.GifterRankingService
	logger  *logger.Logger
}

// NewGifterRankingHandler creates a new instance of the gifter ranking handler
func NewGifterRankingHandler(service service.GifterRankingService, logger *logger.Logger) *GifterRankingHandler {
	return &GifterRankingHandler{
		service: service,
		logger:  logger,
	}
}

// GetStreamRankings retrieves rankings for a stream
func (h *GifterRankingHandler) GetStreamRankings(c *gin.Context) {
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Get period from query parameter
	period := c.DefaultQuery("period", "daily")
	
	// Get limit from query parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}
	
	// Get rankings
	rankings, err := h.service.GetStreamRankings(c.Request.Context(), uint(streamID), period, limit)
	if err != nil {
		h.logger.Errorf("Failed to get rankings for stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get rankings"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"rankings": rankings,
		"period":   period,
		"streamId": streamID,
	})
}

// GetGlobalRankings retrieves global rankings
func (h *GifterRankingHandler) GetGlobalRankings(c *gin.Context) {
	// Get period from query parameter
	period := c.DefaultQuery("period", "daily")
	
	// Get limit from query parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}
	
	// Get rankings
	rankings, err := h.service.GetGlobalRankings(c.Request.Context(), period, limit)
	if err != nil {
		h.logger.Errorf("Failed to get global rankings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get global rankings"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"rankings": rankings,
		"period":   period,
	})
}

// GetUserRanking retrieves a user's ranking
func (h *GifterRankingHandler) GetUserRanking(c *gin.Context) {
	// Get user ID from URL parameter
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}
	
	// Get period from query parameter
	period := c.DefaultQuery("period", "daily")
	
	// Get stream ID from query parameter (optional)
	var streamID *uint
	streamIDStr := c.Query("streamId")
	if streamIDStr != "" {
		streamIDVal, err := strconv.ParseUint(streamIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
			return
		}
		streamIDUint := uint(streamIDVal)
		streamID = &streamIDUint
	}
	
	// Get ranking
	ranking, err := h.service.GetUserRanking(c.Request.Context(), uint(userID), streamID, period)
	if err != nil {
		h.logger.Errorf("Failed to get ranking for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user ranking"})
		return
	}
	
	c.JSON(http.StatusOK, ranking)
}

// UpdateStreamRankings updates rankings for a stream
func (h *GifterRankingHandler) UpdateStreamRankings(c *gin.Context) {
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Update rankings
	err = h.service.UpdateStreamRankings(c.Request.Context(), uint(streamID))
	if err != nil {
		h.logger.Errorf("Failed to update rankings for stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update rankings"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Rankings updated successfully",
	})
}

// UpdateGlobalRankings updates global rankings
func (h *GifterRankingHandler) UpdateGlobalRankings(c *gin.Context) {
	// Update rankings
	err := h.service.UpdateGlobalRankings(c.Request.Context())
	if err != nil {
		h.logger.Errorf("Failed to update global rankings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update global rankings"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Global rankings updated successfully",
	})
}

// CalculateBadgeLevels calculates badge levels for all rankings
func (h *GifterRankingHandler) CalculateBadgeLevels(c *gin.Context) {
	// Calculate badge levels
	err := h.service.CalculateBadgeLevels(c.Request.Context())
	if err != nil {
		h.logger.Errorf("Failed to calculate badge levels: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to calculate badge levels"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Badge levels calculated successfully",
	})
}
