import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  ArrowForward as ArrowForwardIcon,
  Book as BookIcon,
  CheckCircle as CheckCircleIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Movie as MovieIcon,
  OndemandVideo as OndemandVideoIcon,
  PlayCircleOutline as PlayCircleOutlineIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import {
  fetchPersonalizedPaths,
  fetchPersonalizedPathWithItems,
  markPathItemComplete,
  deletePersonalizedPath,
  selectPersonalizedPaths,
  selectCurrentPath,
  selectCurrentPathItems,
  selectPersonalizationLoading,
  selectPersonalizationError,
  selectUserHasStyle,
} from '../features/personalization/personalizationSlice';
import { AppDispatch } from '../store';
import { useNavigate } from 'react-router-dom';
import { styled } from '@mui/system';
import { PathItem } from '../api/personalizationService';

// Styled components
const PathCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
  },
}));

const PathCardContent = styled(CardContent)({
  flexGrow: 1,
});

const ItemTypeIcon = ({ type }: { type: string }) => {
  switch (type.toLowerCase()) {
    case 'book':
      return <BookIcon />;
    case 'video':
      return <OndemandVideoIcon />;
    case 'course':
      return <SchoolIcon />;
    case 'tutorial':
      return <MovieIcon />;
    default:
      return <PlayCircleOutlineIcon />;
  }
};

const PersonalizedPathsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const theme = useTheme();
  
  const paths = useSelector(selectPersonalizedPaths);
  const currentPath = useSelector(selectCurrentPath);
  const pathItems = useSelector(selectCurrentPathItems);
  const loading = useSelector(selectPersonalizationLoading);
  const error = useSelector(selectPersonalizationError);
  const userHasStyle = useSelector(selectUserHasStyle);
  
  const [pathDetailsOpen, setPathDetailsOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pathToDelete, setPathToDelete] = useState<number | null>(null);
  
  useEffect(() => {
    dispatch(fetchPersonalizedPaths());
  }, [dispatch]);
  
  useEffect(() => {
    if (!userHasStyle) {
      // Redirect to assessment if user hasn't taken it
      navigate('/learning-style-assessment');
    }
  }, [userHasStyle, navigate]);
  
  const handleViewPath = (pathId: number) => {
    dispatch(fetchPersonalizedPathWithItems(pathId));
    setPathDetailsOpen(true);
  };
  
  const handleClosePathDetails = () => {
    setPathDetailsOpen(false);
  };
  
  const handleToggleComplete = (itemId: number, completed: boolean) => {
    dispatch(markPathItemComplete({ itemId, completed: !completed }));
  };
  
  const handleDeletePath = (pathId: number) => {
    setPathToDelete(pathId);
    setDeleteDialogOpen(true);
  };
  
  const confirmDeletePath = () => {
    if (pathToDelete) {
      dispatch(deletePersonalizedPath(pathToDelete));
      setDeleteDialogOpen(false);
      setPathToDelete(null);
      if (pathDetailsOpen && currentPath && currentPath.id === pathToDelete) {
        setPathDetailsOpen(false);
      }
    }
  };
  
  const cancelDeletePath = () => {
    setDeleteDialogOpen(false);
    setPathToDelete(null);
  };
  
  const handleCreatePath = () => {
    navigate('/create-personalized-path');
  };
  
  const handleEditPath = (pathId: number) => {
    navigate(`/edit-personalized-path/${pathId}`);
  };
  
  const handleNavigateToItem = (item: PathItem) => {
    // Navigate to the appropriate page based on item type
    switch (item.itemType.toLowerCase()) {
      case 'book':
        navigate(`/books/${item.itemId}`);
        break;
      case 'video':
        navigate(`/videos/${item.itemId}`);
        break;
      case 'course':
        navigate(`/courses/${item.itemId}`);
        break;
      case 'tutorial':
        navigate(`/tutorials/${item.itemId}`);
        break;
      default:
        navigate(`/${item.itemType.toLowerCase()}s/${item.itemId}`);
    }
  };
  
  const handleTakeAssessment = () => {
    navigate('/learning-style-assessment');
  };
  
  if (loading && paths.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <Typography color="error" variant="h6">
          Error: {error}
        </Typography>
      </Box>
    );
  }
  
  if (!userHasStyle) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Take the Learning Style Assessment
          </Typography>
          <Typography variant="body1" paragraph>
            To get personalized learning paths, you need to take the learning style assessment first.
          </Typography>
          <Button variant="contained" color="primary" onClick={handleTakeAssessment}>
            Take Assessment
          </Button>
        </Paper>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1">
          Your Personalized Learning Paths
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreatePath}
        >
          Create New Path
        </Button>
      </Box>
      
      {paths.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No personalized paths found
          </Typography>
          <Typography variant="body1" paragraph>
            You don't have any personalized learning paths yet. Create one to get started!
          </Typography>
          <Button variant="contained" color="primary" onClick={handleCreatePath}>
            Create Your First Path
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {paths.map((path) => (
            <Grid item xs={12} sm={6} md={4} key={path.id}>
              <PathCard>
                <PathCardContent>
                  <Typography variant="h6" gutterBottom>
                    {path.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    {path.description}
                  </Typography>
                  <Box sx={{ mt: 2, mb: 1 }}>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Completion: {Math.round(path.completionRate)}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={path.completionRate}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </PathCardContent>
                <CardActions>
                  <Button size="small" onClick={() => handleViewPath(path.id!)}>
                    View Details
                  </Button>
                  <Button size="small" onClick={() => handleEditPath(path.id!)}>
                    Edit
                  </Button>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDeletePath(path.id!)}
                    sx={{ ml: 'auto' }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </CardActions>
              </PathCard>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Path Details Dialog */}
      <Dialog
        open={pathDetailsOpen}
        onClose={handleClosePathDetails}
        maxWidth="md"
        fullWidth
      >
        {currentPath && (
          <>
            <DialogTitle>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{currentPath.name}</Typography>
                <Chip
                  label={`${Math.round(currentPath.completionRate)}% Complete`}
                  color={currentPath.completionRate === 100 ? 'success' : 'primary'}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                {currentPath.description}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Learning Path Items
              </Typography>
              
              {pathItems.length === 0 ? (
                <Typography variant="body2" color="textSecondary">
                  No items in this learning path yet.
                </Typography>
              ) : (
                <List>
                  {pathItems.map((item) => (
                    <ListItem
                      key={item.id}
                      button
                      onClick={() => handleNavigateToItem(item)}
                      sx={{
                        mb: 1,
                        bgcolor: item.isCompleted ? 'action.selected' : 'background.paper',
                        borderRadius: 1,
                      }}
                    >
                      <ListItemIcon>
                        <ItemTypeIcon type={item.itemType} />
                      </ListItemIcon>
                      <ListItemText
                        primary={item.title}
                        secondary={
                          <>
                            <Typography variant="body2" component="span" color="textSecondary">
                              {item.description}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={0.5}>
                              <Typography variant="caption" color="textSecondary">
                                {item.estimatedDuration} min
                              </Typography>
                              {item.isCompleted && (
                                <Chip
                                  label="Completed"
                                  size="small"
                                  color="success"
                                  sx={{ ml: 1 }}
                                />
                              )}
                            </Box>
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Tooltip title={item.isCompleted ? 'Mark as incomplete' : 'Mark as complete'}>
                          <IconButton
                            edge="end"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleComplete(item.id!, item.isCompleted);
                            }}
                          >
                            <CheckCircleIcon
                              color={item.isCompleted ? 'success' : 'action'}
                            />
                          </IconButton>
                        </Tooltip>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClosePathDetails}>Close</Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleEditPath(currentPath.id!)}
                startIcon={<EditIcon />}
              >
                Edit Path
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={cancelDeletePath}>
        <DialogTitle>Delete Learning Path</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete this learning path? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDeletePath}>Cancel</Button>
          <Button onClick={confirmDeletePath} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PersonalizedPathsPage;
