import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Event as EventIcon,
  Public as PublicIcon,
  Lock as PrivateIcon,
  LockOutlined as SecretIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  MyLocation as MyLocationIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchGroups,
  fetchNearbyGroups,
  searchGroups,
  fetchUserGroups,
  createGroup,
  Group,
} from '../store/slices/groupsSlice';

// Helper components
const GroupCard: React.FC<{
  group: Group;
  onView: () => void;
}> = ({ group, onView }) => {
  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <PublicIcon fontSize="small" />;
      case 'private':
        return <PrivateIcon fontSize="small" />;
      case 'secret':
        return <SecretIcon fontSize="small" />;
      default:
        return <PublicIcon fontSize="small" />;
    }
  };

  const getVisibilityText = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return 'Public';
      case 'private':
        return 'Private';
      case 'secret':
        return 'Secret';
      default:
        return 'Public';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'local_action':
        return 'Local Action';
      case 'interest':
        return 'Interest';
      case 'project':
        return 'Project';
      case 'study':
        return 'Study';
      default:
        return type;
    }
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {group.bannerImage && (
        <Box
          sx={{
            height: 120,
            backgroundImage: `url(${group.bannerImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            src={group.avatarImage}
            sx={{ width: 56, height: 56, mr: 2 }}
          >
            {group.name.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="h6" component="div">
              {group.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title={getVisibilityText(group.visibility)}>
                <Box sx={{ mr: 1 }}>{getVisibilityIcon(group.visibility)}</Box>
              </Tooltip>
              <Typography variant="body2" color="text.secondary">
                {getTypeText(group.type)}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {group.description.length > 150
            ? `${group.description.substring(0, 150)}...`
            : group.description}
        </Typography>

        {group.location && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              {group.location} {group.isVirtual && '(Virtual Available)'}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <PeopleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {group.members?.length || 0} members
            {group.maxMembers > 0 && ` (max: ${group.maxMembers})`}
          </Typography>
        </Box>

        {group.tags && (
          <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {group.tags.split(',').map((tag, index) => (
              <Chip key={index} label={tag.trim()} size="small" variant="outlined" />
            ))}
          </Box>
        )}
      </CardContent>
      <CardActions>
        <Button size="small" onClick={onView}>
          View Group
        </Button>
      </CardActions>
    </Card>
  );
};

const CreateGroupDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onSubmit: (group: Partial<Group>) => void;
  loading: boolean;
}> = ({ open, onClose, onSubmit, loading }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'local_action' | 'interest' | 'project' | 'study'>('local_action');
  const [visibility, setVisibility] = useState<'public' | 'private' | 'secret'>('public');
  const [location, setLocation] = useState('');
  const [isVirtual, setIsVirtual] = useState(false);
  const [joinApprovalRequired, setJoinApprovalRequired] = useState(true);
  const [membershipCode, setMembershipCode] = useState('');
  const [maxMembers, setMaxMembers] = useState(0);
  const [tags, setTags] = useState('');

  const handleSubmit = () => {
    const group: Partial<Group> = {
      name,
      description,
      type,
      visibility,
      location,
      isVirtual,
      joinApprovalRequired,
      membershipCode: membershipCode || undefined,
      maxMembers,
      tags: tags || undefined,
    };

    onSubmit(group);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Group</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Group Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              multiline
              rows={4}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <InputLabel>Group Type</InputLabel>
              <Select
                value={type}
                onChange={(e) => setType(e.target.value as any)}
                label="Group Type"
              >
                <MenuItem value="local_action">Local Action</MenuItem>
                <MenuItem value="interest">Interest</MenuItem>
                <MenuItem value="project">Project</MenuItem>
                <MenuItem value="study">Study</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <InputLabel>Visibility</InputLabel>
              <Select
                value={visibility}
                onChange={(e) => setVisibility(e.target.value as any)}
                label="Visibility"
              >
                <MenuItem value="public">Public (Anyone can find and join)</MenuItem>
                <MenuItem value="private">Private (Visible but requires approval)</MenuItem>
                <MenuItem value="secret">Secret (Invitation only)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Maximum Members (0 for unlimited)"
              type="number"
              value={maxMembers}
              onChange={(e) => setMaxMembers(parseInt(e.target.value) || 0)}
              InputProps={{ inputProps: { min: 0 } }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={isVirtual}
                  onChange={(e) => setIsVirtual(e.target.checked)}
                />
              }
              label="Virtual Participation Available"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={joinApprovalRequired}
                  onChange={(e) => setJoinApprovalRequired(e.target.checked)}
                />
              }
              label="Require Approval to Join"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Membership Code (Optional)"
              value={membershipCode}
              onChange={(e) => setMembershipCode(e.target.value)}
              helperText="If provided, users will need this code to join the group"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Tags (Comma separated)"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              helperText="e.g. education, technology, community"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={!name || !description || loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Create Group'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Main component
const LocalGroupsInterface: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    groups: { items: allGroups, loading: allGroupsLoading, error: allGroupsError },
    userGroups: { items: myGroups, loading: myGroupsLoading, error: myGroupsError },
    nearbyGroups: { items: nearbyGroupsItems, loading: nearbyGroupsLoading, error: nearbyGroupsError },
  } = useSelector((state: RootState) => state.groups);

  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [groupTypeFilter, setGroupTypeFilter] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [useCurrentLocation, setUseCurrentLocation] = useState(false);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);

  useEffect(() => {
    if (user) {
      dispatch(fetchUserGroups({ userId: user.id }));
      dispatch(fetchGroups());
    }
  }, [dispatch, user]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    if (newValue === 2 && useCurrentLocation && userLocation) {
      dispatch(fetchNearbyGroups(userLocation));
    }
  };

  const handleSearch = () => {
    dispatch(
      searchGroups({
        q: searchQuery,
        type: groupTypeFilter,
      })
    );
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setGroupTypeFilter('');
    dispatch(fetchGroups());
  };

  const handleGetLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
          setUserLocation(location);
          setUseCurrentLocation(true);
          dispatch(fetchNearbyGroups(location));
        },
        (error) => {
          console.error('Error getting location:', error);
          setUseCurrentLocation(false);
        }
      );
    } else {
      console.error('Geolocation is not supported by this browser.');
      setUseCurrentLocation(false);
    }
  };

  const handleCreateGroup = async (group: Partial<Group>) => {
    try {
      await dispatch(createGroup(group)).unwrap();
      setCreateDialogOpen(false);
      dispatch(fetchUserGroups({ userId: user!.id }));
    } catch (error) {
      console.error('Failed to create group:', error);
    }
  };

  const handleViewGroup = (groupId: number) => {
    navigate(`/groups/${groupId}`);
  };

  const loading = allGroupsLoading || myGroupsLoading || nearbyGroupsLoading;

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to access local groups. Please log in and try again.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Local Groups
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Group
        </Button>
      </Box>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="My Groups" />
          <Tab label="Discover Groups" />
          <Tab label="Nearby Groups" />
        </Tabs>
      </Paper>

      {(allGroupsError || myGroupsError || nearbyGroupsError) && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {allGroupsError || myGroupsError || nearbyGroupsError}
        </Alert>
      )}

      {tabValue === 1 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={5}>
              <TextField
                fullWidth
                label="Search Groups"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  endAdornment: searchQuery && (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setSearchQuery('')} edge="end">
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={5}>
              <FormControl fullWidth>
                <InputLabel>Group Type</InputLabel>
                <Select
                  value={groupTypeFilter}
                  onChange={(e) => setGroupTypeFilter(e.target.value)}
                  label="Group Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="local_action">Local Action</MenuItem>
                  <MenuItem value="interest">Interest</MenuItem>
                  <MenuItem value="project">Project</MenuItem>
                  <MenuItem value="study">Study</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6} md={1}>
              <Button
                fullWidth
                variant="contained"
                color="primary"
                onClick={handleSearch}
                startIcon={<SearchIcon />}
              >
                Search
              </Button>
            </Grid>
            <Grid item xs={6} md={1}>
              <Button
                fullWidth
                variant="outlined"
                onClick={handleClearSearch}
                startIcon={<ClearIcon />}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {tabValue === 2 && (
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<MyLocationIcon />}
            onClick={handleGetLocation}
            disabled={loading}
          >
            {useCurrentLocation ? 'Refresh Nearby Groups' : 'Find Groups Near Me'}
          </Button>
        </Box>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* My Groups Tab */}
          {tabValue === 0 && (
            <>
              {myGroups.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <PeopleIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    You haven't joined any groups yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Join existing groups or create your own to connect with others.
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => setTabValue(1)}
                      sx={{ mr: 2 }}
                    >
                      Discover Groups
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setCreateDialogOpen(true)}
                    >
                      Create Group
                    </Button>
                  </Box>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {myGroups.map((group) => (
                    <Grid item key={group.id} xs={12} sm={6} md={4}>
                      <GroupCard
                        group={group}
                        onView={() => handleViewGroup(group.id)}
                      />
                    </Grid>
                  ))}
                </Grid>
              )}
            </>
          )}

          {/* Discover Groups Tab */}
          {tabValue === 1 && (
            <>
              {allGroups.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <SearchIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No groups found
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Try adjusting your search criteria or create a new group.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    Create Group
                  </Button>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {allGroups.map((group) => (
                    <Grid item key={group.id} xs={12} sm={6} md={4}>
                      <GroupCard
                        group={group}
                        onView={() => handleViewGroup(group.id)}
                      />
                    </Grid>
                  ))}
                </Grid>
              )}
            </>
          )}

          {/* Nearby Groups Tab */}
          {tabValue === 2 && (
            <>
              {!useCurrentLocation ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <LocationIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Find groups near you
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Allow location access to discover groups in your area.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<MyLocationIcon />}
                    onClick={handleGetLocation}
                  >
                    Use My Location
                  </Button>
                </Paper>
              ) : nearbyGroupsItems.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <LocationIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No groups found nearby
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    There are no groups in your area yet. Why not create one?
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    Create Group
                  </Button>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {nearbyGroupsItems.map((group) => (
                    <Grid item key={group.id} xs={12} sm={6} md={4}>
                      <GroupCard
                        group={group}
                        onView={() => handleViewGroup(group.id)}
                      />
                    </Grid>
                  ))}
                </Grid>
              )}
            </>
          )}
        </>
      )}

      <CreateGroupDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateGroup}
        loading={loading}
      />
    </Container>
  );
};

export default LocalGroupsInterface;
