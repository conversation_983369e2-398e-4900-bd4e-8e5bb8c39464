import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import marketplaceService, {
  Product,
  Service,
  Job,
  Category,
  SearchParams,
  SearchResult,
  ProductCreateRequest,
  ProductUpdateRequest,
  ServiceCreateRequest,
  ServiceUpdateRequest,
  JobCreateRequest,
  JobUpdateRequest
} from '../api/marketplaceService';

interface MarketplaceState {
  products: {
    items: Product[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  services: {
    items: Service[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  jobs: {
    items: Job[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  categories: {
    items: Category[];
    loading: boolean;
    error: string | null;
  };
  currentProduct: {
    item: Product | null;
    loading: boolean;
    error: string | null;
  };
  currentService: {
    item: Service | null;
    loading: boolean;
    error: string | null;
  };
  currentJob: {
    item: Job | null;
    loading: boolean;
    error: string | null;
  };
  searchParams: SearchParams;
}

const initialState: MarketplaceState = {
  products: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  services: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  jobs: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  categories: {
    items: [],
    loading: false,
    error: null
  },
  currentProduct: {
    item: null,
    loading: false,
    error: null
  },
  currentService: {
    item: null,
    loading: false,
    error: null
  },
  currentJob: {
    item: null,
    loading: false,
    error: null
  },
  searchParams: {
    page: 1,
    limit: 12,
    sortBy: 'newest'
  }
};

// Products thunks
export const fetchProducts = createAsyncThunk(
  'marketplace/fetchProducts',
  async (params: SearchParams, { rejectWithValue }) => {
    try {
      return await marketplaceService.getProducts(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch products');
    }
  }
);

export const fetchProductById = createAsyncThunk(
  'marketplace/fetchProductById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await marketplaceService.getProductById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch product');
    }
  }
);

export const createProduct = createAsyncThunk(
  'marketplace/createProduct',
  async (product: ProductCreateRequest, { rejectWithValue }) => {
    try {
      return await marketplaceService.createProduct(product);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create product');
    }
  }
);

export const updateProduct = createAsyncThunk(
  'marketplace/updateProduct',
  async ({ id, product }: { id: string; product: ProductUpdateRequest }, { rejectWithValue }) => {
    try {
      return await marketplaceService.updateProduct(id, product);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update product');
    }
  }
);

export const deleteProduct = createAsyncThunk(
  'marketplace/deleteProduct',
  async (id: string, { rejectWithValue }) => {
    try {
      await marketplaceService.deleteProduct(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete product');
    }
  }
);

// Services thunks
export const fetchServices = createAsyncThunk(
  'marketplace/fetchServices',
  async (params: SearchParams, { rejectWithValue }) => {
    try {
      return await marketplaceService.getServices(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch services');
    }
  }
);

export const fetchServiceById = createAsyncThunk(
  'marketplace/fetchServiceById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await marketplaceService.getServiceById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch service');
    }
  }
);

export const createService = createAsyncThunk(
  'marketplace/createService',
  async (service: ServiceCreateRequest, { rejectWithValue }) => {
    try {
      return await marketplaceService.createService(service);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create service');
    }
  }
);

export const updateService = createAsyncThunk(
  'marketplace/updateService',
  async ({ id, service }: { id: string; service: ServiceUpdateRequest }, { rejectWithValue }) => {
    try {
      return await marketplaceService.updateService(id, service);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update service');
    }
  }
);

export const deleteService = createAsyncThunk(
  'marketplace/deleteService',
  async (id: string, { rejectWithValue }) => {
    try {
      await marketplaceService.deleteService(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete service');
    }
  }
);

// Jobs thunks
export const fetchJobs = createAsyncThunk(
  'marketplace/fetchJobs',
  async (params: SearchParams, { rejectWithValue }) => {
    try {
      return await marketplaceService.getJobs(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch jobs');
    }
  }
);

export const fetchJobById = createAsyncThunk(
  'marketplace/fetchJobById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await marketplaceService.getJobById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch job');
    }
  }
);

export const createJob = createAsyncThunk(
  'marketplace/createJob',
  async (job: JobCreateRequest, { rejectWithValue }) => {
    try {
      return await marketplaceService.createJob(job);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create job');
    }
  }
);

export const updateJob = createAsyncThunk(
  'marketplace/updateJob',
  async ({ id, job }: { id: string; job: JobUpdateRequest }, { rejectWithValue }) => {
    try {
      return await marketplaceService.updateJob(id, job);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update job');
    }
  }
);

export const deleteJob = createAsyncThunk(
  'marketplace/deleteJob',
  async (id: string, { rejectWithValue }) => {
    try {
      await marketplaceService.deleteJob(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete job');
    }
  }
);

// Categories thunk
export const fetchCategories = createAsyncThunk(
  'marketplace/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await marketplaceService.getCategories();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch categories');
    }
  }
);

const marketplaceSlice = createSlice({
  name: 'marketplace',
  initialState,
  reducers: {
    setSearchParams: (state, action: PayloadAction<SearchParams>) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    resetSearchParams: (state) => {
      state.searchParams = initialState.searchParams;
    },
    clearCurrentProduct: (state) => {
      state.currentProduct.item = null;
      state.currentProduct.error = null;
    },
    clearCurrentService: (state) => {
      state.currentService.item = null;
      state.currentService.error = null;
    },
    clearCurrentJob: (state) => {
      state.currentJob.item = null;
      state.currentJob.error = null;
    }
  },
  extraReducers: (builder) => {
    // Products reducers
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.products.loading = true;
        state.products.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action: PayloadAction<SearchResult<Product>>) => {
        state.products.items = action.payload.items;
        state.products.total = action.payload.total;
        state.products.page = action.payload.page;
        state.products.totalPages = action.payload.totalPages;
        state.products.loading = false;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.products.loading = false;
        state.products.error = action.payload as string;
      })
      
      .addCase(fetchProductById.pending, (state) => {
        state.currentProduct.loading = true;
        state.currentProduct.error = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action: PayloadAction<Product>) => {
        state.currentProduct.item = action.payload;
        state.currentProduct.loading = false;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.currentProduct.loading = false;
        state.currentProduct.error = action.payload as string;
      })
      
      .addCase(createProduct.fulfilled, (state, action: PayloadAction<Product>) => {
        state.products.items.unshift(action.payload);
        state.products.total += 1;
      })
      
      .addCase(updateProduct.fulfilled, (state, action: PayloadAction<Product>) => {
        const index = state.products.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.products.items[index] = action.payload;
        }
        if (state.currentProduct.item?.id === action.payload.id) {
          state.currentProduct.item = action.payload;
        }
      })
      
      .addCase(deleteProduct.fulfilled, (state, action: PayloadAction<string>) => {
        state.products.items = state.products.items.filter(item => item.id !== action.payload);
        state.products.total -= 1;
        if (state.currentProduct.item?.id === action.payload) {
          state.currentProduct.item = null;
        }
      })
      
      // Services reducers
      .addCase(fetchServices.pending, (state) => {
        state.services.loading = true;
        state.services.error = null;
      })
      .addCase(fetchServices.fulfilled, (state, action: PayloadAction<SearchResult<Service>>) => {
        state.services.items = action.payload.items;
        state.services.total = action.payload.total;
        state.services.page = action.payload.page;
        state.services.totalPages = action.payload.totalPages;
        state.services.loading = false;
      })
      .addCase(fetchServices.rejected, (state, action) => {
        state.services.loading = false;
        state.services.error = action.payload as string;
      })
      
      .addCase(fetchServiceById.pending, (state) => {
        state.currentService.loading = true;
        state.currentService.error = null;
      })
      .addCase(fetchServiceById.fulfilled, (state, action: PayloadAction<Service>) => {
        state.currentService.item = action.payload;
        state.currentService.loading = false;
      })
      .addCase(fetchServiceById.rejected, (state, action) => {
        state.currentService.loading = false;
        state.currentService.error = action.payload as string;
      })
      
      .addCase(createService.fulfilled, (state, action: PayloadAction<Service>) => {
        state.services.items.unshift(action.payload);
        state.services.total += 1;
      })
      
      .addCase(updateService.fulfilled, (state, action: PayloadAction<Service>) => {
        const index = state.services.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.services.items[index] = action.payload;
        }
        if (state.currentService.item?.id === action.payload.id) {
          state.currentService.item = action.payload;
        }
      })
      
      .addCase(deleteService.fulfilled, (state, action: PayloadAction<string>) => {
        state.services.items = state.services.items.filter(item => item.id !== action.payload);
        state.services.total -= 1;
        if (state.currentService.item?.id === action.payload) {
          state.currentService.item = null;
        }
      })
      
      // Jobs reducers
      .addCase(fetchJobs.pending, (state) => {
        state.jobs.loading = true;
        state.jobs.error = null;
      })
      .addCase(fetchJobs.fulfilled, (state, action: PayloadAction<SearchResult<Job>>) => {
        state.jobs.items = action.payload.items;
        state.jobs.total = action.payload.total;
        state.jobs.page = action.payload.page;
        state.jobs.totalPages = action.payload.totalPages;
        state.jobs.loading = false;
      })
      .addCase(fetchJobs.rejected, (state, action) => {
        state.jobs.loading = false;
        state.jobs.error = action.payload as string;
      })
      
      .addCase(fetchJobById.pending, (state) => {
        state.currentJob.loading = true;
        state.currentJob.error = null;
      })
      .addCase(fetchJobById.fulfilled, (state, action: PayloadAction<Job>) => {
        state.currentJob.item = action.payload;
        state.currentJob.loading = false;
      })
      .addCase(fetchJobById.rejected, (state, action) => {
        state.currentJob.loading = false;
        state.currentJob.error = action.payload as string;
      })
      
      .addCase(createJob.fulfilled, (state, action: PayloadAction<Job>) => {
        state.jobs.items.unshift(action.payload);
        state.jobs.total += 1;
      })
      
      .addCase(updateJob.fulfilled, (state, action: PayloadAction<Job>) => {
        const index = state.jobs.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.jobs.items[index] = action.payload;
        }
        if (state.currentJob.item?.id === action.payload.id) {
          state.currentJob.item = action.payload;
        }
      })
      
      .addCase(deleteJob.fulfilled, (state, action: PayloadAction<string>) => {
        state.jobs.items = state.jobs.items.filter(item => item.id !== action.payload);
        state.jobs.total -= 1;
        if (state.currentJob.item?.id === action.payload) {
          state.currentJob.item = null;
        }
      })
      
      // Categories reducers
      .addCase(fetchCategories.pending, (state) => {
        state.categories.loading = true;
        state.categories.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action: PayloadAction<Category[]>) => {
        state.categories.items = action.payload;
        state.categories.loading = false;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.categories.loading = false;
        state.categories.error = action.payload as string;
      });
  }
});

export const {
  setSearchParams,
  resetSearchParams,
  clearCurrentProduct,
  clearCurrentService,
  clearCurrentJob
} = marketplaceSlice.actions;

export default marketplaceSlice.reducer;
