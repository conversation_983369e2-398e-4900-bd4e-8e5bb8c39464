import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  Chip,
  Divider,
  Button,
  CircularProgress,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormGroup,
  FormControlLabel,
  TextField,
  IconButton,
  useTheme,
  useMediaQuery,
  Drawer,
  AppBar,
  Toolbar,
} from '@mui/material';
import {
  FilterList as FilterListIcon,
  Close as CloseIcon,
  Book as BookIcon,
  VideoLibrary as VideoIcon,
  School as CourseIcon,
  Assignment as TutorialIcon,
  Event as EventIcon,
  Forum as ForumIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { AppDispatch } from '../store';
import {
  searchContent,
  setQuery,
  setTypes,
  setPage,
  setSortBy,
  setSortOrder,
  setTags,
  setDateRange,
  resetFilters,
  selectSearchResults,
  selectTotalResults,
  selectHasMore,
  selectPage,
  selectQuery,
  selectTypes,
  selectSortBy,
  selectSortOrder,
  selectTags,
  selectDateRange,
  selectSearchLoading,
  selectSearchError,
} from '../features/search/searchSlice';
import SearchBar from '../components/search/SearchBar';
import { SearchRequest, SearchResult } from '../api/searchService';
import { format } from 'date-fns';

const SearchResultsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const results = useSelector(selectSearchResults);
  const totalResults = useSelector(selectTotalResults);
  const hasMore = useSelector(selectHasMore);
  const page = useSelector(selectPage);
  const query = useSelector(selectQuery);
  const types = useSelector(selectTypes);
  const sortBy = useSelector(selectSortBy);
  const sortOrder = useSelector(selectSortOrder);
  const tags = useSelector(selectTags);
  const dateRange = useSelector(selectDateRange);
  const loading = useSelector(selectSearchLoading);
  const error = useSelector(selectSearchError);
  
  const [filtersOpen, setFiltersOpen] = useState(!isMobile);
  
  // Parse query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const queryParam = params.get('q');
    
    if (queryParam && queryParam !== query) {
      dispatch(setQuery(queryParam));
    }
    
    const typesParam = params.get('types');
    if (typesParam) {
      dispatch(setTypes(typesParam.split(',')));
    }
    
    const sortByParam = params.get('sortBy');
    if (sortByParam) {
      dispatch(setSortBy(sortByParam));
    }
    
    const sortOrderParam = params.get('sortOrder') as 'asc' | 'desc';
    if (sortOrderParam) {
      dispatch(setSortOrder(sortOrderParam));
    }
    
    const tagsParam = params.get('tags');
    if (tagsParam) {
      dispatch(setTags(tagsParam.split(',')));
    }
    
    const dateFromParam = params.get('dateFrom');
    const dateToParam = params.get('dateTo');
    if (dateFromParam || dateToParam) {
      dispatch(setDateRange({
        from: dateFromParam,
        to: dateToParam,
      }));
    }
  }, [dispatch, location.search]);
  
  // Perform search when parameters change
  useEffect(() => {
    if (query) {
      const searchRequest: SearchRequest = {
        query,
        types: types.length > 0 ? types : undefined,
        page,
        pageSize: 10,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder || undefined,
        tags: tags.length > 0 ? tags : undefined,
        dateFrom: dateRange.from || undefined,
        dateTo: dateRange.to || undefined,
      };
      
      dispatch(searchContent(searchRequest));
      
      // Update URL with search parameters
      const params = new URLSearchParams();
      params.set('q', query);
      
      if (types.length > 0) {
        params.set('types', types.join(','));
      }
      
      if (sortBy) {
        params.set('sortBy', sortBy);
      }
      
      if (sortOrder) {
        params.set('sortOrder', sortOrder);
      }
      
      if (tags.length > 0) {
        params.set('tags', tags.join(','));
      }
      
      if (dateRange.from) {
        params.set('dateFrom', dateRange.from);
      }
      
      if (dateRange.to) {
        params.set('dateTo', dateRange.to);
      }
      
      navigate(`/search?${params.toString()}`, { replace: true });
    }
  }, [dispatch, query, types, page, sortBy, sortOrder, tags, dateRange]);
  
  const handleLoadMore = () => {
    if (hasMore && !loading) {
      dispatch(setPage(page + 1));
    }
  };
  
  const handleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const checked = event.target.checked;
    
    if (checked) {
      dispatch(setTypes([...types, value]));
    } else {
      dispatch(setTypes(types.filter(type => type !== value)));
    }
    
    // Reset to page 1 when filters change
    dispatch(setPage(1));
  };
  
  const handleSortByChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    dispatch(setSortBy(event.target.value as string));
    dispatch(setPage(1));
  };
  
  const handleSortOrderChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    dispatch(setSortOrder(event.target.value as 'asc' | 'desc'));
    dispatch(setPage(1));
  };
  
  const handleDateFromChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setDateRange({
      ...dateRange,
      from: event.target.value,
    }));
    dispatch(setPage(1));
  };
  
  const handleDateToChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setDateRange({
      ...dateRange,
      to: event.target.value,
    }));
    dispatch(setPage(1));
  };
  
  const handleResetFilters = () => {
    dispatch(resetFilters());
    dispatch(setPage(1));
  };
  
  const handleToggleFilters = () => {
    setFiltersOpen(!filtersOpen);
  };
  
  const getIconForType = (type: string) => {
    switch (type.toLowerCase()) {
      case 'book':
        return <BookIcon />;
      case 'video':
        return <VideoIcon />;
      case 'course':
        return <CourseIcon />;
      case 'tutorial':
        return <TutorialIcon />;
      case 'event':
        return <EventIcon />;
      case 'forum':
        return <ForumIcon />;
      case 'user':
        return <PersonIcon />;
      default:
        return null;
    }
  };
  
  const renderResultItem = (result: SearchResult) => (
    <Grid item xs={12} sm={6} md={4} key={`${result.type}-${result.id}`}>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
          },
        }}
      >
        <CardActionArea
          component="a"
          href={result.url}
          sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
        >
          {result.imageUrl && (
            <CardMedia
              component="img"
              height="140"
              image={result.imageUrl}
              alt={result.title}
            />
          )}
          <CardContent sx={{ flexGrow: 1 }}>
            <Box display="flex" alignItems="center" mb={1}>
              <Chip
                icon={getIconForType(result.type)}
                label={result.type}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ mr: 1 }}
              />
              {result.relevanceScore && result.relevanceScore > 90 && (
                <Chip
                  label="Highly Relevant"
                  size="small"
                  color="secondary"
                />
              )}
            </Box>
            <Typography variant="h6" component="h2" gutterBottom noWrap>
              {result.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                mb: 2,
              }}
            >
              {result.description}
            </Typography>
            {result.author && (
              <Typography variant="caption" color="text.secondary" display="block">
                By {result.author}
              </Typography>
            )}
            {result.createdAt && (
              <Typography variant="caption" color="text.secondary" display="block">
                {format(new Date(result.createdAt), 'MMM d, yyyy')}
              </Typography>
            )}
            {result.tags && result.tags.length > 0 && (
              <Box mt={1} display="flex" flexWrap="wrap" gap={0.5}>
                {result.tags.slice(0, 3).map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                ))}
                {result.tags.length > 3 && (
                  <Typography variant="caption" color="text.secondary">
                    +{result.tags.length - 3} more
                  </Typography>
                )}
              </Box>
            )}
          </CardContent>
        </CardActionArea>
      </Card>
    </Grid>
  );
  
  const renderFilters = () => (
    <Paper
      sx={{
        p: 2,
        height: '100%',
        position: isMobile ? 'static' : 'sticky',
        top: theme.spacing(2),
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">Filters</Typography>
        {isMobile && (
          <IconButton onClick={handleToggleFilters}>
            <CloseIcon />
          </IconButton>
        )}
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Content Type
      </Typography>
      <FormGroup>
        <FormControlLabel
          control={<Checkbox checked={types.includes('book')} onChange={handleTypeChange} value="book" />}
          label="Books"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('video')} onChange={handleTypeChange} value="video" />}
          label="Videos"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('course')} onChange={handleTypeChange} value="course" />}
          label="Courses"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('tutorial')} onChange={handleTypeChange} value="tutorial" />}
          label="Tutorials"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('event')} onChange={handleTypeChange} value="event" />}
          label="Events"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('forum')} onChange={handleTypeChange} value="forum" />}
          label="Forum Posts"
        />
        <FormControlLabel
          control={<Checkbox checked={types.includes('user')} onChange={handleTypeChange} value="user" />}
          label="Users"
        />
      </FormGroup>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Sort By
      </Typography>
      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
        <InputLabel id="sort-by-label">Sort By</InputLabel>
        <Select
          labelId="sort-by-label"
          value={sortBy}
          label="Sort By"
          onChange={handleSortByChange}
        >
          <MenuItem value="relevance">Relevance</MenuItem>
          <MenuItem value="date">Date</MenuItem>
          <MenuItem value="title">Title</MenuItem>
          <MenuItem value="popularity">Popularity</MenuItem>
        </Select>
      </FormControl>
      
      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
        <InputLabel id="sort-order-label">Order</InputLabel>
        <Select
          labelId="sort-order-label"
          value={sortOrder}
          label="Order"
          onChange={handleSortOrderChange}
        >
          <MenuItem value="desc">Descending</MenuItem>
          <MenuItem value="asc">Ascending</MenuItem>
        </Select>
      </FormControl>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Date Range
      </Typography>
      <TextField
        label="From"
        type="date"
        size="small"
        fullWidth
        value={dateRange.from || ''}
        onChange={handleDateFromChange}
        InputLabelProps={{ shrink: true }}
        sx={{ mb: 2 }}
      />
      <TextField
        label="To"
        type="date"
        size="small"
        fullWidth
        value={dateRange.to || ''}
        onChange={handleDateToChange}
        InputLabelProps={{ shrink: true }}
        sx={{ mb: 2 }}
      />
      
      <Button
        variant="outlined"
        color="primary"
        fullWidth
        onClick={handleResetFilters}
        sx={{ mt: 2 }}
      >
        Reset Filters
      </Button>
    </Paper>
  );
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Search Results
        </Typography>
        <SearchBar fullWidth autoFocus />
      </Box>
      
      {query && (
        <Box mb={4}>
          <Typography variant="body1">
            {loading && page === 1 ? 'Searching...' : `Found ${totalResults} results for "${query}"`}
          </Typography>
        </Box>
      )}
      
      {isMobile && (
        <Box mb={2}>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleToggleFilters}
            fullWidth
          >
            Filters
          </Button>
        </Box>
      )}
      
      <Grid container spacing={3}>
        {isMobile ? (
          <Drawer
            anchor="left"
            open={filtersOpen}
            onClose={handleToggleFilters}
            sx={{
              '& .MuiDrawer-paper': {
                width: '80%',
                maxWidth: 300,
                boxSizing: 'border-box',
                p: 2,
              },
            }}
          >
            {renderFilters()}
          </Drawer>
        ) : (
          <Grid item xs={12} md={3}>
            {renderFilters()}
          </Grid>
        )}
        
        <Grid item xs={12} md={9}>
          {error ? (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="error" variant="h6" gutterBottom>
                Error
              </Typography>
              <Typography variant="body1">{error}</Typography>
              <Button
                variant="contained"
                color="primary"
                onClick={() => window.location.reload()}
                sx={{ mt: 2 }}
              >
                Try Again
              </Button>
            </Paper>
          ) : loading && page === 1 ? (
            <Box display="flex" justifyContent="center" py={8}>
              <CircularProgress />
            </Box>
          ) : results.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                No results found
              </Typography>
              <Typography variant="body1">
                Try adjusting your search terms or filters to find what you're looking for.
              </Typography>
            </Paper>
          ) : (
            <>
              <Grid container spacing={3}>
                {results.map(renderResultItem)}
              </Grid>
              
              {hasMore && (
                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    variant="outlined"
                    onClick={handleLoadMore}
                    disabled={loading}
                    startIcon={loading && <CircularProgress size={20} />}
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </Button>
                </Box>
              )}
            </>
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

export default SearchResultsPage;
