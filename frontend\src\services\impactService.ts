import api from './api';

export interface ImpactMetric {
  id: number;
  name: string;
  description: string;
  category: string;
  unit: string;
  targetValue: number;
  currentValue: number;
  startDate: string;
  endDate: string;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  status: 'active' | 'completed' | 'archived';
}

export interface ImpactDataPoint {
  id: number;
  metricId: number;
  value: number;
  date: string;
  notes: string;
  location?: {
    latitude: number;
    longitude: number;
    name: string;
  };
  createdBy: number;
  createdAt: string;
}

export interface ImpactReport {
  id: number;
  title: string;
  description: string;
  metrics: number[];
  startDate: string;
  endDate: string;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  status: 'draft' | 'published' | 'archived';
  tags: string[];
}

export interface ImpactCategory {
  id: number;
  name: string;
  description: string;
  color: string;
}

export interface ImpactDashboardConfig {
  id: number;
  userId: number;
  layout: {
    widgets: {
      id: string;
      type: 'metric' | 'chart' | 'map' | 'summary';
      metricIds?: number[];
      chartType?: 'line' | 'bar' | 'pie' | 'area';
      timeRange?: 'day' | 'week' | 'month' | 'year' | 'all';
      position: {
        x: number;
        y: number;
        w: number;
        h: number;
      };
      title?: string;
    }[];
  };
  createdAt: string;
  updatedAt: string;
}

const impactService = {
  // Metrics CRUD operations
  getMetrics: async (params?: { category?: string; status?: string; isPublic?: boolean }) => {
    const response = await api.get('/impact/metrics', { params });
    return response.data;
  },

  getMetricById: async (id: number) => {
    const response = await api.get(`/impact/metrics/${id}`);
    return response.data;
  },

  getUserMetrics: async (userId: number) => {
    const response = await api.get(`/impact/metrics/user/${userId}`);
    return response.data;
  },

  createMetric: async (metric: Partial<ImpactMetric>) => {
    const response = await api.post('/impact/metrics', metric);
    return response.data;
  },

  updateMetric: async (id: number, metric: Partial<ImpactMetric>) => {
    const response = await api.put(`/impact/metrics/${id}`, metric);
    return response.data;
  },

  deleteMetric: async (id: number) => {
    const response = await api.delete(`/impact/metrics/${id}`);
    return response.data;
  },

  // Data points operations
  getDataPoints: async (metricId: number) => {
    const response = await api.get(`/impact/metrics/${metricId}/data-points`);
    return response.data;
  },

  createDataPoint: async (metricId: number, dataPoint: Partial<ImpactDataPoint>) => {
    const response = await api.post(`/impact/metrics/${metricId}/data-points`, dataPoint);
    return response.data;
  },

  updateDataPoint: async (id: number, dataPoint: Partial<ImpactDataPoint>) => {
    const response = await api.put(`/impact/data-points/${id}`, dataPoint);
    return response.data;
  },

  deleteDataPoint: async (id: number) => {
    const response = await api.delete(`/impact/data-points/${id}`);
    return response.data;
  },

  // Reports operations
  getReports: async (params?: { status?: string; isPublic?: boolean }) => {
    const response = await api.get('/impact/reports', { params });
    return response.data;
  },

  getReportById: async (id: number) => {
    const response = await api.get(`/impact/reports/${id}`);
    return response.data;
  },

  getUserReports: async (userId: number) => {
    const response = await api.get(`/impact/reports/user/${userId}`);
    return response.data;
  },

  createReport: async (report: Partial<ImpactReport>) => {
    const response = await api.post('/impact/reports', report);
    return response.data;
  },

  updateReport: async (id: number, report: Partial<ImpactReport>) => {
    const response = await api.put(`/impact/reports/${id}`, report);
    return response.data;
  },

  deleteReport: async (id: number) => {
    const response = await api.delete(`/impact/reports/${id}`);
    return response.data;
  },

  publishReport: async (id: number) => {
    const response = await api.put(`/impact/reports/${id}/publish`);
    return response.data;
  },

  archiveReport: async (id: number) => {
    const response = await api.put(`/impact/reports/${id}/archive`);
    return response.data;
  },

  // Categories operations
  getCategories: async () => {
    const response = await api.get('/impact/categories');
    return response.data;
  },

  getCategoryById: async (id: number) => {
    const response = await api.get(`/impact/categories/${id}`);
    return response.data;
  },

  createCategory: async (category: Partial<ImpactCategory>) => {
    const response = await api.post('/impact/categories', category);
    return response.data;
  },

  updateCategory: async (id: number, category: Partial<ImpactCategory>) => {
    const response = await api.put(`/impact/categories/${id}`, category);
    return response.data;
  },

  deleteCategory: async (id: number) => {
    const response = await api.delete(`/impact/categories/${id}`);
    return response.data;
  },

  // Dashboard configuration operations
  getDashboardConfig: async (userId: number) => {
    const response = await api.get(`/impact/dashboard/user/${userId}`);
    return response.data;
  },

  updateDashboardConfig: async (userId: number, config: Partial<ImpactDashboardConfig>) => {
    const response = await api.put(`/impact/dashboard/user/${userId}`, config);
    return response.data;
  },

  // Analytics operations
  getMetricAnalytics: async (metricId: number, timeRange: string) => {
    const response = await api.get(`/impact/analytics/metrics/${metricId}`, {
      params: { timeRange },
    });
    return response.data;
  },

  getCategoryAnalytics: async (categoryId: number, timeRange: string) => {
    const response = await api.get(`/impact/analytics/categories/${categoryId}`, {
      params: { timeRange },
    });
    return response.data;
  },

  getUserAnalytics: async (userId: number) => {
    const response = await api.get(`/impact/analytics/users/${userId}`);
    return response.data;
  },

  // Export operations
  exportMetricData: async (metricId: number, format: 'csv' | 'excel' | 'pdf') => {
    const response = await api.get(`/impact/export/metrics/${metricId}`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },

  exportReportData: async (reportId: number, format: 'csv' | 'excel' | 'pdf') => {
    const response = await api.get(`/impact/export/reports/${reportId}`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },
};

export default impactService;
