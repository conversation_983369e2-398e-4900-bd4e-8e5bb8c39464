import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import walletService, {
  Wallet,
  Transaction,
  PaymentMethod,
  DepositRequest,
  WithdrawalRequest,
  TransferRequest,
  PaymentRequest,
  AddPaymentMethodRequest
} from '../api/walletService';

interface WalletState {
  wallet: {
    data: Wallet | null;
    loading: boolean;
    error: string | null;
  };
  transactions: {
    items: Transaction[];
    total: number;
    page: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
  };
  paymentMethods: {
    items: PaymentMethod[];
    loading: boolean;
    error: string | null;
  };
  deposit: {
    redirectUrl: string | null;
    reference: string | null;
    loading: boolean;
    error: string | null;
  };
  withdrawal: {
    reference: string | null;
    status: string | null;
    loading: boolean;
    error: string | null;
  };
  transfer: {
    transaction: Transaction | null;
    loading: boolean;
    error: string | null;
  };
  payment: {
    transaction: Transaction | null;
    loading: boolean;
    error: string | null;
  };
}

const initialState: WalletState = {
  wallet: {
    data: null,
    loading: false,
    error: null
  },
  transactions: {
    items: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: false,
    error: null
  },
  paymentMethods: {
    items: [],
    loading: false,
    error: null
  },
  deposit: {
    redirectUrl: null,
    reference: null,
    loading: false,
    error: null
  },
  withdrawal: {
    reference: null,
    status: null,
    loading: false,
    error: null
  },
  transfer: {
    transaction: null,
    loading: false,
    error: null
  },
  payment: {
    transaction: null,
    loading: false,
    error: null
  }
};

// Wallet thunks
export const fetchWallet = createAsyncThunk(
  'wallet/fetchWallet',
  async (_, { rejectWithValue }) => {
    try {
      return await walletService.getWallet();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch wallet');
    }
  }
);

export const fetchTransactions = createAsyncThunk(
  'wallet/fetchTransactions',
  async (
    {
      page = 1,
      limit = 20,
      type,
      status,
      startDate,
      endDate
    }: {
      page?: number;
      limit?: number;
      type?: string;
      status?: string;
      startDate?: string;
      endDate?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      return await walletService.getTransactions(page, limit, type, status, startDate, endDate);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch transactions');
    }
  }
);

export const initiateDeposit = createAsyncThunk(
  'wallet/initiateDeposit',
  async (request: DepositRequest, { rejectWithValue }) => {
    try {
      return await walletService.initiateDeposit(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to initiate deposit');
    }
  }
);

export const requestWithdrawal = createAsyncThunk(
  'wallet/requestWithdrawal',
  async (request: WithdrawalRequest, { rejectWithValue }) => {
    try {
      return await walletService.requestWithdrawal(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to request withdrawal');
    }
  }
);

export const transferFunds = createAsyncThunk(
  'wallet/transferFunds',
  async (request: TransferRequest, { rejectWithValue }) => {
    try {
      return await walletService.transferFunds(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to transfer funds');
    }
  }
);

export const makePayment = createAsyncThunk(
  'wallet/makePayment',
  async (request: PaymentRequest, { rejectWithValue }) => {
    try {
      return await walletService.makePayment(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to make payment');
    }
  }
);

// Payment methods thunks
export const fetchPaymentMethods = createAsyncThunk(
  'wallet/fetchPaymentMethods',
  async (_, { rejectWithValue }) => {
    try {
      return await walletService.getPaymentMethods();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch payment methods');
    }
  }
);

export const addPaymentMethod = createAsyncThunk(
  'wallet/addPaymentMethod',
  async (request: AddPaymentMethodRequest, { rejectWithValue }) => {
    try {
      return await walletService.addPaymentMethod(request);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add payment method');
    }
  }
);

export const deletePaymentMethod = createAsyncThunk(
  'wallet/deletePaymentMethod',
  async (id: string, { rejectWithValue }) => {
    try {
      await walletService.deletePaymentMethod(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete payment method');
    }
  }
);

export const setDefaultPaymentMethod = createAsyncThunk(
  'wallet/setDefaultPaymentMethod',
  async (id: string, { rejectWithValue }) => {
    try {
      return await walletService.setDefaultPaymentMethod(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to set default payment method');
    }
  }
);

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    resetDepositState: (state) => {
      state.deposit = initialState.deposit;
    },
    resetWithdrawalState: (state) => {
      state.withdrawal = initialState.withdrawal;
    },
    resetTransferState: (state) => {
      state.transfer = initialState.transfer;
    },
    resetPaymentState: (state) => {
      state.payment = initialState.payment;
    }
  },
  extraReducers: (builder) => {
    // Wallet reducers
    builder
      .addCase(fetchWallet.pending, (state) => {
        state.wallet.loading = true;
        state.wallet.error = null;
      })
      .addCase(fetchWallet.fulfilled, (state, action: PayloadAction<Wallet>) => {
        state.wallet.data = action.payload;
        state.wallet.loading = false;
      })
      .addCase(fetchWallet.rejected, (state, action) => {
        state.wallet.loading = false;
        state.wallet.error = action.payload as string;
      })
      
      // Transactions reducers
      .addCase(fetchTransactions.pending, (state) => {
        state.transactions.loading = true;
        state.transactions.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action: PayloadAction<{
        transactions: Transaction[];
        total: number;
        page: number;
        totalPages: number;
      }>) => {
        state.transactions.items = action.payload.transactions;
        state.transactions.total = action.payload.total;
        state.transactions.page = action.payload.page;
        state.transactions.totalPages = action.payload.totalPages;
        state.transactions.loading = false;
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.transactions.loading = false;
        state.transactions.error = action.payload as string;
      })
      
      // Deposit reducers
      .addCase(initiateDeposit.pending, (state) => {
        state.deposit.loading = true;
        state.deposit.error = null;
      })
      .addCase(initiateDeposit.fulfilled, (state, action: PayloadAction<{
        redirectUrl: string;
        reference: string;
      }>) => {
        state.deposit.redirectUrl = action.payload.redirectUrl;
        state.deposit.reference = action.payload.reference;
        state.deposit.loading = false;
      })
      .addCase(initiateDeposit.rejected, (state, action) => {
        state.deposit.loading = false;
        state.deposit.error = action.payload as string;
      })
      
      // Withdrawal reducers
      .addCase(requestWithdrawal.pending, (state) => {
        state.withdrawal.loading = true;
        state.withdrawal.error = null;
      })
      .addCase(requestWithdrawal.fulfilled, (state, action: PayloadAction<{
        reference: string;
        status: string;
      }>) => {
        state.withdrawal.reference = action.payload.reference;
        state.withdrawal.status = action.payload.status;
        state.withdrawal.loading = false;
      })
      .addCase(requestWithdrawal.rejected, (state, action) => {
        state.withdrawal.loading = false;
        state.withdrawal.error = action.payload as string;
      })
      
      // Transfer reducers
      .addCase(transferFunds.pending, (state) => {
        state.transfer.loading = true;
        state.transfer.error = null;
      })
      .addCase(transferFunds.fulfilled, (state, action: PayloadAction<Transaction>) => {
        state.transfer.transaction = action.payload;
        state.transfer.loading = false;
        
        // Update wallet balance
        if (state.wallet.data) {
          state.wallet.data.balance -= action.payload.amount;
        }
        
        // Add to transactions list if it exists
        if (state.transactions.items.length > 0) {
          state.transactions.items.unshift(action.payload);
        }
      })
      .addCase(transferFunds.rejected, (state, action) => {
        state.transfer.loading = false;
        state.transfer.error = action.payload as string;
      })
      
      // Payment reducers
      .addCase(makePayment.pending, (state) => {
        state.payment.loading = true;
        state.payment.error = null;
      })
      .addCase(makePayment.fulfilled, (state, action: PayloadAction<Transaction>) => {
        state.payment.transaction = action.payload;
        state.payment.loading = false;
        
        // Update wallet balance
        if (state.wallet.data) {
          state.wallet.data.balance -= action.payload.amount;
        }
        
        // Add to transactions list if it exists
        if (state.transactions.items.length > 0) {
          state.transactions.items.unshift(action.payload);
        }
      })
      .addCase(makePayment.rejected, (state, action) => {
        state.payment.loading = false;
        state.payment.error = action.payload as string;
      })
      
      // Payment methods reducers
      .addCase(fetchPaymentMethods.pending, (state) => {
        state.paymentMethods.loading = true;
        state.paymentMethods.error = null;
      })
      .addCase(fetchPaymentMethods.fulfilled, (state, action: PayloadAction<PaymentMethod[]>) => {
        state.paymentMethods.items = action.payload;
        state.paymentMethods.loading = false;
      })
      .addCase(fetchPaymentMethods.rejected, (state, action) => {
        state.paymentMethods.loading = false;
        state.paymentMethods.error = action.payload as string;
      })
      
      .addCase(addPaymentMethod.fulfilled, (state, action: PayloadAction<PaymentMethod>) => {
        state.paymentMethods.items.push(action.payload);
        
        // If this is the default payment method, update all others
        if (action.payload.isDefault) {
          state.paymentMethods.items = state.paymentMethods.items.map(method => 
            method.id !== action.payload.id ? { ...method, isDefault: false } : method
          );
        }
      })
      
      .addCase(deletePaymentMethod.fulfilled, (state, action: PayloadAction<string>) => {
        state.paymentMethods.items = state.paymentMethods.items.filter(method => method.id !== action.payload);
      })
      
      .addCase(setDefaultPaymentMethod.fulfilled, (state, action: PayloadAction<PaymentMethod>) => {
        state.paymentMethods.items = state.paymentMethods.items.map(method => 
          method.id === action.payload.id
            ? action.payload
            : { ...method, isDefault: false }
        );
      });
  }
});

export const {
  resetDepositState,
  resetWithdrawalState,
  resetTransferState,
  resetPaymentState
} = walletSlice.actions;

export default walletSlice.reducer;
