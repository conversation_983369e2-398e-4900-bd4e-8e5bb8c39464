import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CoursesService } from '../../api';
import {
  Course,
  Module,
  Lesson,
  Quiz,
  Question,
  Assignment,
  Enrollment,
  Progress,
  QuizAttempt,
  AssignmentSubmission,
  Review,
  Certificate
} from '../../services/coursesService';

// State types
interface CoursesState {
  courses: {
    items: Course[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  currentCourse: {
    data: Course | null;
    loading: boolean;
    error: string | null;
  };
  modules: {
    items: Module[];
    loading: boolean;
    error: string | null;
  };
  currentModule: {
    data: Module | null;
    loading: boolean;
    error: string | null;
  };
  lessons: {
    items: Lesson[];
    loading: boolean;
    error: string | null;
  };
  currentLesson: {
    data: Lesson | null;
    loading: boolean;
    error: string | null;
  };
  quiz: {
    data: Quiz | null;
    questions: Question[];
    loading: boolean;
    error: string | null;
  };
  assignment: {
    data: Assignment | null;
    loading: boolean;
    error: string | null;
  };
  enrollment: {
    data: Enrollment | null;
    loading: boolean;
    error: string | null;
  };
  progress: {
    items: Progress[];
    loading: boolean;
    error: string | null;
  };
  quizAttempts: {
    items: QuizAttempt[];
    currentAttempt: QuizAttempt | null;
    loading: boolean;
    error: string | null;
  };
  submissions: {
    items: AssignmentSubmission[];
    currentSubmission: AssignmentSubmission | null;
    loading: boolean;
    error: string | null;
  };
  reviews: {
    items: Review[];
    currentReview: Review | null;
    loading: boolean;
    error: string | null;
  };
  certificates: {
    items: Certificate[];
    currentCertificate: Certificate | null;
    loading: boolean;
    error: string | null;
  };
}

// Initial state
const initialState: CoursesState = {
  courses: {
    items: [],
    total: 0,
    loading: false,
    error: null,
  },
  currentCourse: {
    data: null,
    loading: false,
    error: null,
  },
  modules: {
    items: [],
    loading: false,
    error: null,
  },
  currentModule: {
    data: null,
    loading: false,
    error: null,
  },
  lessons: {
    items: [],
    loading: false,
    error: null,
  },
  currentLesson: {
    data: null,
    loading: false,
    error: null,
  },
  quiz: {
    data: null,
    questions: [],
    loading: false,
    error: null,
  },
  assignment: {
    data: null,
    loading: false,
    error: null,
  },
  enrollment: {
    data: null,
    loading: false,
    error: null,
  },
  progress: {
    items: [],
    loading: false,
    error: null,
  },
  quizAttempts: {
    items: [],
    currentAttempt: null,
    loading: false,
    error: null,
  },
  submissions: {
    items: [],
    currentSubmission: null,
    loading: false,
    error: null,
  },
  reviews: {
    items: [],
    currentReview: null,
    loading: false,
    error: null,
  },
  certificates: {
    items: [],
    currentCertificate: null,
    loading: false,
    error: null,
  },
};

// Async thunks
export const fetchCourses = createAsyncThunk(
  'courses/fetchCourses',
  async ({ page = 1, pageSize = 10, filters = {} }: { page?: number; pageSize?: number; filters?: any }, { rejectWithValue }) => {
    try {
      return await CoursesService.getCourses(page, pageSize, filters);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch courses');
    }
  }
);

export const fetchCourseByID = createAsyncThunk(
  'courses/fetchCourseByID',
  async (id: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getCourseByID(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch course');
    }
  }
);

export const fetchCourseBySlug = createAsyncThunk(
  'courses/fetchCourseBySlug',
  async (slug: string, { rejectWithValue }) => {
    try {
      return await CoursesService.getCourseBySlug(slug);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch course');
    }
  }
);

export const createCourse = createAsyncThunk(
  'courses/createCourse',
  async (course: Partial<Course>, { rejectWithValue }) => {
    try {
      return await CoursesService.createCourse(course);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create course');
    }
  }
);

export const updateCourse = createAsyncThunk(
  'courses/updateCourse',
  async ({ id, course }: { id: number; course: Partial<Course> }, { rejectWithValue }) => {
    try {
      return await CoursesService.updateCourse(id, course);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update course');
    }
  }
);

export const deleteCourse = createAsyncThunk(
  'courses/deleteCourse',
  async (id: number, { rejectWithValue }) => {
    try {
      await CoursesService.deleteCourse(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete course');
    }
  }
);

export const fetchModulesByCourseID = createAsyncThunk(
  'courses/fetchModulesByCourseID',
  async (courseID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getModulesByCourseID(courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch modules');
    }
  }
);

export const fetchModuleByID = createAsyncThunk(
  'courses/fetchModuleByID',
  async (id: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getModuleByID(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch module');
    }
  }
);

export const createModule = createAsyncThunk(
  'courses/createModule',
  async ({ courseID, module }: { courseID: number; module: Partial<Module> }, { rejectWithValue }) => {
    try {
      return await CoursesService.createModule(courseID, module);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create module');
    }
  }
);

export const updateModule = createAsyncThunk(
  'courses/updateModule',
  async ({ id, module }: { id: number; module: Partial<Module> }, { rejectWithValue }) => {
    try {
      return await CoursesService.updateModule(id, module);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update module');
    }
  }
);

export const deleteModule = createAsyncThunk(
  'courses/deleteModule',
  async (id: number, { rejectWithValue }) => {
    try {
      await CoursesService.deleteModule(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete module');
    }
  }
);

export const fetchLessonsByModuleID = createAsyncThunk(
  'courses/fetchLessonsByModuleID',
  async (moduleID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getLessonsByModuleID(moduleID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch lessons');
    }
  }
);

export const fetchLessonByID = createAsyncThunk(
  'courses/fetchLessonByID',
  async (id: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getLessonByID(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch lesson');
    }
  }
);

export const createLesson = createAsyncThunk(
  'courses/createLesson',
  async ({ moduleID, lesson }: { moduleID: number; lesson: Partial<Lesson> }, { rejectWithValue }) => {
    try {
      return await CoursesService.createLesson(moduleID, lesson);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create lesson');
    }
  }
);

export const updateLesson = createAsyncThunk(
  'courses/updateLesson',
  async ({ id, lesson }: { id: number; lesson: Partial<Lesson> }, { rejectWithValue }) => {
    try {
      return await CoursesService.updateLesson(id, lesson);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update lesson');
    }
  }
);

export const deleteLesson = createAsyncThunk(
  'courses/deleteLesson',
  async (id: number, { rejectWithValue }) => {
    try {
      await CoursesService.deleteLesson(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete lesson');
    }
  }
);

export const fetchQuizByLessonID = createAsyncThunk(
  'courses/fetchQuizByLessonID',
  async (lessonID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getQuizByLessonID(lessonID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch quiz');
    }
  }
);

export const fetchQuestionsByQuizID = createAsyncThunk(
  'courses/fetchQuestionsByQuizID',
  async (quizID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getQuestionsByQuizID(quizID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch questions');
    }
  }
);

export const fetchAssignmentByLessonID = createAsyncThunk(
  'courses/fetchAssignmentByLessonID',
  async (lessonID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getAssignmentByLessonID(lessonID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch assignment');
    }
  }
);

export const fetchEnrollment = createAsyncThunk(
  'courses/fetchEnrollment',
  async ({ userID, courseID }: { userID: number; courseID: number }, { rejectWithValue }) => {
    try {
      return await CoursesService.getEnrollment(userID, courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch enrollment');
    }
  }
);

export const createEnrollment = createAsyncThunk(
  'courses/createEnrollment',
  async (enrollment: Partial<Enrollment>, { rejectWithValue }) => {
    try {
      return await CoursesService.createEnrollment(enrollment);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create enrollment');
    }
  }
);

export const fetchProgressByUserAndCourse = createAsyncThunk(
  'courses/fetchProgressByUserAndCourse',
  async ({ userID, courseID }: { userID: number; courseID: number }, { rejectWithValue }) => {
    try {
      return await CoursesService.getProgressByUserAndCourse(userID, courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch progress');
    }
  }
);

export const createProgress = createAsyncThunk(
  'courses/createProgress',
  async (progress: Partial<Progress>, { rejectWithValue }) => {
    try {
      return await CoursesService.createProgress(progress);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create progress');
    }
  }
);

export const updateProgress = createAsyncThunk(
  'courses/updateProgress',
  async ({ id, progress }: { id: number; progress: Partial<Progress> }, { rejectWithValue }) => {
    try {
      return await CoursesService.updateProgress(id, progress);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update progress');
    }
  }
);

export const fetchReviewsByCourseID = createAsyncThunk(
  'courses/fetchReviewsByCourseID',
  async (courseID: number, { rejectWithValue }) => {
    try {
      return await CoursesService.getReviewsByCourseID(courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch reviews');
    }
  }
);

export const createReview = createAsyncThunk(
  'courses/createReview',
  async (review: Partial<Review>, { rejectWithValue }) => {
    try {
      return await CoursesService.createReview(review);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create review');
    }
  }
);

export const checkCourseCompletion = createAsyncThunk(
  'courses/checkCourseCompletion',
  async ({ userID, courseID }: { userID: number; courseID: number }, { rejectWithValue }) => {
    try {
      return await CoursesService.checkCourseCompletion(userID, courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to check course completion');
    }
  }
);

export const generateCertificate = createAsyncThunk(
  'courses/generateCertificate',
  async ({ userID, courseID }: { userID: number; courseID: number }, { rejectWithValue }) => {
    try {
      return await CoursesService.generateCourseCompletionCertificate(userID, courseID);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to generate certificate');
    }
  }
);

// Slice
const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    resetCurrentCourse: (state) => {
      state.currentCourse.data = null;
      state.currentCourse.error = null;
    },
    resetCurrentModule: (state) => {
      state.currentModule.data = null;
      state.currentModule.error = null;
    },
    resetCurrentLesson: (state) => {
      state.currentLesson.data = null;
      state.currentLesson.error = null;
    },
    resetQuiz: (state) => {
      state.quiz.data = null;
      state.quiz.questions = [];
      state.quiz.error = null;
    },
    resetAssignment: (state) => {
      state.assignment.data = null;
      state.assignment.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch courses
    builder.addCase(fetchCourses.pending, (state) => {
      state.courses.loading = true;
      state.courses.error = null;
    });
    builder.addCase(fetchCourses.fulfilled, (state, action) => {
      state.courses.loading = false;
      state.courses.items = action.payload.data;
      state.courses.total = action.payload.meta.total;
    });
    builder.addCase(fetchCourses.rejected, (state, action) => {
      state.courses.loading = false;
      state.courses.error = action.payload as string;
    });

    // Fetch course by ID
    builder.addCase(fetchCourseByID.pending, (state) => {
      state.currentCourse.loading = true;
      state.currentCourse.error = null;
    });
    builder.addCase(fetchCourseByID.fulfilled, (state, action) => {
      state.currentCourse.loading = false;
      state.currentCourse.data = action.payload;
    });
    builder.addCase(fetchCourseByID.rejected, (state, action) => {
      state.currentCourse.loading = false;
      state.currentCourse.error = action.payload as string;
    });

    // Fetch course by slug
    builder.addCase(fetchCourseBySlug.pending, (state) => {
      state.currentCourse.loading = true;
      state.currentCourse.error = null;
    });
    builder.addCase(fetchCourseBySlug.fulfilled, (state, action) => {
      state.currentCourse.loading = false;
      state.currentCourse.data = action.payload;
    });
    builder.addCase(fetchCourseBySlug.rejected, (state, action) => {
      state.currentCourse.loading = false;
      state.currentCourse.error = action.payload as string;
    });

    // Create course
    builder.addCase(createCourse.fulfilled, (state, action) => {
      state.courses.items.push(action.payload);
      state.currentCourse.data = action.payload;
    });

    // Update course
    builder.addCase(updateCourse.fulfilled, (state, action) => {
      const index = state.courses.items.findIndex(course => course.id === action.payload.id);
      if (index !== -1) {
        state.courses.items[index] = action.payload;
      }
      state.currentCourse.data = action.payload;
    });

    // Delete course
    builder.addCase(deleteCourse.fulfilled, (state, action) => {
      state.courses.items = state.courses.items.filter(course => course.id !== action.payload);
      if (state.currentCourse.data?.id === action.payload) {
        state.currentCourse.data = null;
      }
    });

    // Fetch modules by course ID
    builder.addCase(fetchModulesByCourseID.pending, (state) => {
      state.modules.loading = true;
      state.modules.error = null;
    });
    builder.addCase(fetchModulesByCourseID.fulfilled, (state, action) => {
      state.modules.loading = false;
      state.modules.items = action.payload;
    });
    builder.addCase(fetchModulesByCourseID.rejected, (state, action) => {
      state.modules.loading = false;
      state.modules.error = action.payload as string;
    });

    // Fetch module by ID
    builder.addCase(fetchModuleByID.pending, (state) => {
      state.currentModule.loading = true;
      state.currentModule.error = null;
    });
    builder.addCase(fetchModuleByID.fulfilled, (state, action) => {
      state.currentModule.loading = false;
      state.currentModule.data = action.payload;
    });
    builder.addCase(fetchModuleByID.rejected, (state, action) => {
      state.currentModule.loading = false;
      state.currentModule.error = action.payload as string;
    });

    // Create module
    builder.addCase(createModule.fulfilled, (state, action) => {
      state.modules.items.push(action.payload);
      state.currentModule.data = action.payload;
    });

    // Update module
    builder.addCase(updateModule.fulfilled, (state, action) => {
      const index = state.modules.items.findIndex(module => module.id === action.payload.id);
      if (index !== -1) {
        state.modules.items[index] = action.payload;
      }
      state.currentModule.data = action.payload;
    });

    // Delete module
    builder.addCase(deleteModule.fulfilled, (state, action) => {
      state.modules.items = state.modules.items.filter(module => module.id !== action.payload);
      if (state.currentModule.data?.id === action.payload) {
        state.currentModule.data = null;
      }
    });

    // Fetch lessons by module ID
    builder.addCase(fetchLessonsByModuleID.pending, (state) => {
      state.lessons.loading = true;
      state.lessons.error = null;
    });
    builder.addCase(fetchLessonsByModuleID.fulfilled, (state, action) => {
      state.lessons.loading = false;
      state.lessons.items = action.payload;
    });
    builder.addCase(fetchLessonsByModuleID.rejected, (state, action) => {
      state.lessons.loading = false;
      state.lessons.error = action.payload as string;
    });

    // Fetch lesson by ID
    builder.addCase(fetchLessonByID.pending, (state) => {
      state.currentLesson.loading = true;
      state.currentLesson.error = null;
    });
    builder.addCase(fetchLessonByID.fulfilled, (state, action) => {
      state.currentLesson.loading = false;
      state.currentLesson.data = action.payload;
    });
    builder.addCase(fetchLessonByID.rejected, (state, action) => {
      state.currentLesson.loading = false;
      state.currentLesson.error = action.payload as string;
    });

    // Create lesson
    builder.addCase(createLesson.fulfilled, (state, action) => {
      state.lessons.items.push(action.payload);
      state.currentLesson.data = action.payload;
    });

    // Update lesson
    builder.addCase(updateLesson.fulfilled, (state, action) => {
      const index = state.lessons.items.findIndex(lesson => lesson.id === action.payload.id);
      if (index !== -1) {
        state.lessons.items[index] = action.payload;
      }
      state.currentLesson.data = action.payload;
    });

    // Delete lesson
    builder.addCase(deleteLesson.fulfilled, (state, action) => {
      state.lessons.items = state.lessons.items.filter(lesson => lesson.id !== action.payload);
      if (state.currentLesson.data?.id === action.payload) {
        state.currentLesson.data = null;
      }
    });

    // Fetch quiz by lesson ID
    builder.addCase(fetchQuizByLessonID.pending, (state) => {
      state.quiz.loading = true;
      state.quiz.error = null;
    });
    builder.addCase(fetchQuizByLessonID.fulfilled, (state, action) => {
      state.quiz.loading = false;
      state.quiz.data = action.payload;
    });
    builder.addCase(fetchQuizByLessonID.rejected, (state, action) => {
      state.quiz.loading = false;
      state.quiz.error = action.payload as string;
    });

    // Fetch questions by quiz ID
    builder.addCase(fetchQuestionsByQuizID.pending, (state) => {
      state.quiz.loading = true;
      state.quiz.error = null;
    });
    builder.addCase(fetchQuestionsByQuizID.fulfilled, (state, action) => {
      state.quiz.loading = false;
      state.quiz.questions = action.payload;
    });
    builder.addCase(fetchQuestionsByQuizID.rejected, (state, action) => {
      state.quiz.loading = false;
      state.quiz.error = action.payload as string;
    });

    // Fetch assignment by lesson ID
    builder.addCase(fetchAssignmentByLessonID.pending, (state) => {
      state.assignment.loading = true;
      state.assignment.error = null;
    });
    builder.addCase(fetchAssignmentByLessonID.fulfilled, (state, action) => {
      state.assignment.loading = false;
      state.assignment.data = action.payload;
    });
    builder.addCase(fetchAssignmentByLessonID.rejected, (state, action) => {
      state.assignment.loading = false;
      state.assignment.error = action.payload as string;
    });

    // Fetch enrollment
    builder.addCase(fetchEnrollment.pending, (state) => {
      state.enrollment.loading = true;
      state.enrollment.error = null;
    });
    builder.addCase(fetchEnrollment.fulfilled, (state, action) => {
      state.enrollment.loading = false;
      state.enrollment.data = action.payload;
    });
    builder.addCase(fetchEnrollment.rejected, (state, action) => {
      state.enrollment.loading = false;
      state.enrollment.error = action.payload as string;
    });

    // Create enrollment
    builder.addCase(createEnrollment.fulfilled, (state, action) => {
      state.enrollment.data = action.payload;
    });

    // Fetch progress by user and course
    builder.addCase(fetchProgressByUserAndCourse.pending, (state) => {
      state.progress.loading = true;
      state.progress.error = null;
    });
    builder.addCase(fetchProgressByUserAndCourse.fulfilled, (state, action) => {
      state.progress.loading = false;
      state.progress.items = action.payload;
    });
    builder.addCase(fetchProgressByUserAndCourse.rejected, (state, action) => {
      state.progress.loading = false;
      state.progress.error = action.payload as string;
    });

    // Fetch reviews by course ID
    builder.addCase(fetchReviewsByCourseID.pending, (state) => {
      state.reviews.loading = true;
      state.reviews.error = null;
    });
    builder.addCase(fetchReviewsByCourseID.fulfilled, (state, action) => {
      state.reviews.loading = false;
      state.reviews.items = action.payload;
    });
    builder.addCase(fetchReviewsByCourseID.rejected, (state, action) => {
      state.reviews.loading = false;
      state.reviews.error = action.payload as string;
    });

    // Create review
    builder.addCase(createReview.fulfilled, (state, action) => {
      state.reviews.items.push(action.payload);
      state.reviews.currentReview = action.payload;
    });

    // Generate certificate
    builder.addCase(generateCertificate.fulfilled, (state, action) => {
      state.certificates.currentCertificate = action.payload;
      state.certificates.items.push(action.payload);
    });
  },
});

// Export actions and reducer
export const {
  resetCurrentCourse,
  resetCurrentModule,
  resetCurrentLesson,
  resetQuiz,
  resetAssignment
} = coursesSlice.actions;

export default coursesSlice.reducer;
