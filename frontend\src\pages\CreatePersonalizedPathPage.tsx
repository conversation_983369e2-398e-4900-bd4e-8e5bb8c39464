import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  Container,
  Paper,
  TextField,
  Typography,
  Divider,
  Grid,
  MenuItem,
  IconButton,
  Card,
  CardContent,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AppDispatch } from '../store';
import {
  createPersonalizedPath,
  selectPersonalizationLoading,
  selectPersonalizationError,
} from '../features/personalization/personalizationSlice';
import { PersonalizedPath, PathItem } from '../api/personalizationService';

const CreatePersonalizedPathPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  
  const loading = useSelector(selectPersonalizationLoading);
  const error = useSelector(selectPersonalizationError);
  
  const [path, setPath] = useState<PersonalizedPath>({
    name: '',
    description: '',
    isActive: true,
    completionRate: 0,
  });
  
  const [items, setItems] = useState<Omit<PathItem, 'id' | 'pathId'>[]>([
    {
      itemType: 'book',
      itemId: 0,
      title: '',
      description: '',
      order: 1,
      isCompleted: false,
      estimatedDuration: 30,
    },
  ]);
  
  const handlePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPath({
      ...path,
      [name]: value,
    });
  };
  
  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newItems = [...items];
    
    if (name === 'itemId' || name === 'order' || name === 'estimatedDuration') {
      newItems[index] = {
        ...newItems[index],
        [name]: parseInt(value) || 0,
      };
    } else {
      newItems[index] = {
        ...newItems[index],
        [name]: value,
      };
    }
    
    setItems(newItems);
  };
  
  const handleAddItem = () => {
    setItems([
      ...items,
      {
        itemType: 'book',
        itemId: 0,
        title: '',
        description: '',
        order: items.length + 1,
        isCompleted: false,
        estimatedDuration: 30,
      },
    ]);
  };
  
  const handleRemoveItem = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    
    // Update order for remaining items
    newItems.forEach((item, i) => {
      item.order = i + 1;
    });
    
    setItems(newItems);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!path.name) {
      alert('Please enter a path name');
      return;
    }
    
    if (items.length === 0) {
      alert('Please add at least one item to the path');
      return;
    }
    
    // Validate items
    for (const item of items) {
      if (!item.title || !item.itemType || !item.itemId) {
        alert('Please fill in all required fields for each item');
        return;
      }
    }
    
    try {
      await dispatch(createPersonalizedPath({ path, items: items as PathItem[] })).unwrap();
      navigate('/personalized-paths');
    } catch (err) {
      console.error('Failed to create path:', err);
    }
  };
  
  const handleCancel = () => {
    navigate('/personalized-paths');
  };
  
  return (
    <Container maxWidth="md">
      <Box py={4}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Create Personalized Learning Path
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  required
                  label="Path Name"
                  name="name"
                  value={path.name}
                  onChange={handlePathChange}
                  disabled={loading}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={path.description}
                  onChange={handlePathChange}
                  multiline
                  rows={3}
                  disabled={loading}
                />
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 4 }} />
            
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h5">Path Items</Typography>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={handleAddItem}
                disabled={loading}
              >
                Add Item
              </Button>
            </Box>
            
            {items.map((item, index) => (
              <Card key={index} sx={{ mb: 3 }}>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Title"
                        name="title"
                        value={item.title}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        select
                        label="Item Type"
                        name="itemType"
                        value={item.itemType}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      >
                        <MenuItem value="book">Book</MenuItem>
                        <MenuItem value="video">Video</MenuItem>
                        <MenuItem value="course">Course</MenuItem>
                        <MenuItem value="tutorial">Tutorial</MenuItem>
                      </TextField>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Item ID"
                        name="itemId"
                        type="number"
                        value={item.itemId}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Estimated Duration (minutes)"
                        name="estimatedDuration"
                        type="number"
                        value={item.estimatedDuration}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={item.description}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        multiline
                        rows={2}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} display="flex" justifyContent="flex-end">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={items.length === 1 || loading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            ))}
            
            <Box display="flex" justifyContent="space-between" mt={4}>
              <Button variant="outlined" onClick={handleCancel} disabled={loading}>
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                {loading ? 'Creating...' : 'Create Path'}
              </Button>
            </Box>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default CreatePersonalizedPathPage;
