package repository

import (
	"database/sql"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/models"
)

// ProgressRepository defines the interface for progress data access
type ProgressRepository interface {
	GetUserProgress(userID int) (*models.UserProgress, error)
	GetRecentActivities(userID int, limit int) ([]models.Activity, error)
	GetMilestones(userID int) ([]models.Milestone, error)
	GetAchievements(userID int) ([]models.Achievement, error)
	GetHistoricalData(userID int) ([]models.HistoricalData, error)
	GetSkillsData(userID int) ([]models.SkillData, error)
	UpdateMilestoneProgress(milestoneID, progress int) error
	UpdateAchievementProgress(achievementID, progress int) error
	LogActivity(activity *models.Activity) error
	GetMilestoneDefinitions() ([]models.MilestoneDefinition, error)
	GetAchievementDefinitions() ([]models.AchievementDefinition, error)
}

// SQLProgressRepository implements ProgressRepository using SQL
type SQLProgressRepository struct {
	db *sqlx.DB
}

// NewSQLProgressRepository creates a new SQLProgressRepository
func NewSQLProgressRepository(db *sqlx.DB) *SQLProgressRepository {
	return &SQLProgressRepository{db: db}
}

// GetUserProgress retrieves a user's progress
func (r *SQLProgressRepository) GetUserProgress(userID int) (*models.UserProgress, error) {
	var progress models.UserProgress
	err := r.db.Get(&progress, `
		SELECT id, user_id, overall_completion, points_earned, streak, level
		FROM user_progress
		WHERE user_id = ?
	`, userID)
	if err != nil {
		return nil, err
	}

	// Get recent activities
	activities, err := r.GetRecentActivities(userID, 5)
	if err != nil {
		return nil, err
	}
	progress.RecentActivities = activities

	return &progress, nil
}

// GetRecentActivities retrieves a user's recent activities
func (r *SQLProgressRepository) GetRecentActivities(userID int, limit int) ([]models.Activity, error) {
	var activities []models.Activity
	err := r.db.Select(&activities, `
		SELECT id, user_id, type, name, progress, date
		FROM activities
		WHERE user_id = ?
		ORDER BY date DESC
		LIMIT ?
	`, userID, limit)
	if err != nil {
		return nil, err
	}
	return activities, nil
}

// GetMilestones retrieves a user's milestones
func (r *SQLProgressRepository) GetMilestones(userID int) ([]models.Milestone, error) {
	var milestones []models.Milestone
	err := r.db.Select(&milestones, `
		SELECT id, name, description, completed, date, progress, icon, user_id
		FROM milestones
		WHERE user_id = ?
		ORDER BY id
	`, userID)
	if err != nil {
		return nil, err
	}
	return milestones, nil
}

// GetAchievements retrieves a user's achievements
func (r *SQLProgressRepository) GetAchievements(userID int) ([]models.Achievement, error) {
	var achievements []models.Achievement
	err := r.db.Select(&achievements, `
		SELECT id, name, description, earned, date, progress, icon, user_id
		FROM achievements
		WHERE user_id = ?
		ORDER BY id
	`, userID)
	if err != nil {
		return nil, err
	}
	return achievements, nil
}

// GetHistoricalData retrieves a user's historical progress data
func (r *SQLProgressRepository) GetHistoricalData(userID int) ([]models.HistoricalData, error) {
	var historicalData []models.HistoricalData
	err := r.db.Select(&historicalData, `
		SELECT id, user_id, month, progress, activities, date
		FROM historical_data
		WHERE user_id = ?
		ORDER BY date
	`, userID)
	if err != nil {
		return nil, err
	}
	return historicalData, nil
}

// GetSkillsData retrieves a user's skills data
func (r *SQLProgressRepository) GetSkillsData(userID int) ([]models.SkillData, error) {
	var skillsData []models.SkillData
	err := r.db.Select(&skillsData, `
		SELECT id, user_id, name, value
		FROM skills_data
		WHERE user_id = ?
		ORDER BY value DESC
	`, userID)
	if err != nil {
		return nil, err
	}
	return skillsData, nil
}

// UpdateMilestoneProgress updates a milestone's progress
func (r *SQLProgressRepository) UpdateMilestoneProgress(milestoneID, progress int) error {
	var completed bool
	var date *time.Time

	if progress >= 100 {
		completed = true
		now := time.Now()
		date = &now
	}

	_, err := r.db.Exec(`
		UPDATE milestones
		SET progress = ?, completed = ?, date = ?
		WHERE id = ?
	`, progress, completed, date, milestoneID)
	return err
}

// UpdateAchievementProgress updates an achievement's progress
func (r *SQLProgressRepository) UpdateAchievementProgress(achievementID, progress int) error {
	var earned bool
	var date *time.Time

	if progress >= 100 {
		earned = true
		now := time.Now()
		date = &now
	}

	_, err := r.db.Exec(`
		UPDATE achievements
		SET progress = ?, earned = ?, date = ?
		WHERE id = ?
	`, progress, earned, date, achievementID)
	return err
}

// LogActivity logs a user activity
func (r *SQLProgressRepository) LogActivity(activity *models.Activity) error {
	_, err := r.db.Exec(`
		INSERT INTO activities (user_id, type, name, progress, date)
		VALUES (?, ?, ?, ?, ?)
	`, activity.UserID, activity.Type, activity.Name, activity.Progress, activity.Date)
	return err
}

// GetMilestoneDefinitions retrieves all milestone definitions
func (r *SQLProgressRepository) GetMilestoneDefinitions() ([]models.MilestoneDefinition, error) {
	var definitions []models.MilestoneDefinition
	err := r.db.Select(&definitions, `
		SELECT id, name, description, criteria, icon
		FROM milestone_definitions
		ORDER BY id
	`)
	if err != nil {
		return nil, err
	}
	return definitions, nil
}

// GetAchievementDefinitions retrieves all achievement definitions
func (r *SQLProgressRepository) GetAchievementDefinitions() ([]models.AchievementDefinition, error) {
	var definitions []models.AchievementDefinition
	err := r.db.Select(&definitions, `
		SELECT id, name, description, criteria, icon
		FROM achievement_definitions
		ORDER BY id
	`)
	if err != nil {
		return nil, err
	}
	return definitions, nil
}
