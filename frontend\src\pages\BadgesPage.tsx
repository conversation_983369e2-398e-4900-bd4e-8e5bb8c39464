import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress, 
  Alert, 
  Button,
  Divider,
  useTheme
} from '@mui/material';
import { 
  EmojiEvents as TrophyIcon,
  School as EducationIcon,
  Whatshot as StreakIcon,
  Forum as CommunityIcon,
  Create as CreativeIcon,
  Psychology as KnowledgeIcon,
  Lightbulb as InsightIcon,
  Favorite as ContributionIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { RootState } from '../store';
import { fetchUserBadges, markBadgeViewed } from '../features/badges/badgesSlice';
import BadgeSystem, { UserBadge } from '../components/progress/BadgeSystem';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`badges-tabpanel-${index}`}
      aria-labelledby={`badges-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const BadgesPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const { userBadges, loading, error } = useSelector((state: RootState) => state.badges);
  
  const [tabValue, setTabValue] = useState(0);
  const [showNewBadgeNotification, setShowNewBadgeNotification] = useState(false);
  
  // Fetch user badges when component mounts
  useEffect(() => {
    if (user?.id) {
      dispatch(fetchUserBadges(user.id) as any);
    }
  }, [dispatch, user]);
  
  // Check for new badges
  useEffect(() => {
    const hasNewBadge = userBadges.some(badge => badge.isNew);
    setShowNewBadgeNotification(hasNewBadge);
  }, [userBadges]);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle badge click
  const handleBadgeClick = (badge: UserBadge) => {
    console.log('Badge clicked:', badge);
    
    // If the badge is new, mark it as viewed
    if (badge.isNew && user?.id) {
      dispatch(markBadgeViewed({ userId: user.id, badgeId: badge.id }) as any);
    }
  };
  
  // Handle new badge notification dismiss
  const handleNewBadgeDismiss = () => {
    setShowNewBadgeNotification(false);
    
    // Mark all new badges as viewed
    if (user?.id) {
      userBadges.forEach(badge => {
        if (badge.isNew) {
          dispatch(markBadgeViewed({ userId: user.id, badgeId: badge.id }) as any);
        }
      });
    }
  };
  
  // Transform backend badges to component format
  const transformBadges = (): UserBadge[] => {
    return userBadges.map(badge => ({
      id: badge.id,
      name: badge.badge.name,
      description: badge.badge.description,
      category: badge.badge.category.toLowerCase() as any,
      level: badge.badge.isRare ? 'platinum' : badge.badge.isPublic ? 'gold' : 'silver',
      earnedAt: badge.awardedAt,
      progress: badge.progress,
      requiredProgress: badge.requiredProgress,
      isNew: badge.isNew
    }));
  };
  
  // Get badge stats
  const getBadgeStats = () => {
    const total = userBadges.length;
    const earned = userBadges.filter(badge => badge.awardedAt).length;
    const categories = new Set(userBadges.map(badge => badge.badge.category));
    
    return {
      total,
      earned,
      remaining: total - earned,
      categories: categories.size
    };
  };
  
  const stats = getBadgeStats();
  const badges = transformBadges();
  
  if (loading && userBadges.length === 0) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => user?.id && dispatch(fetchUserBadges(user.id) as any)}>
          Try Again
        </Button>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          My Badges & Achievements
        </Typography>
        
        <Paper elevation={2} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
          <Box display="flex" flexWrap="wrap" justifyContent="space-between" alignItems="center" mb={3}>
            <Box>
              <Typography variant="h6" gutterBottom>
                Badge Collection Progress
              </Typography>
              <Typography variant="body1">
                You've earned {stats.earned} out of {stats.total} available badges across {stats.categories} categories.
              </Typography>
            </Box>
            
            <Box display="flex" gap={2} mt={{ xs: 2, md: 0 }}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center', 
                  minWidth: 100,
                  bgcolor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText
                }}
              >
                <Typography variant="h4" fontWeight="bold">
                  {stats.earned}
                </Typography>
                <Typography variant="body2">
                  Earned
                </Typography>
              </Paper>
              
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center', 
                  minWidth: 100,
                  bgcolor: theme.palette.grey[200]
                }}
              >
                <Typography variant="h4" fontWeight="bold" color="text.secondary">
                  {stats.remaining}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Remaining
                </Typography>
              </Paper>
            </Box>
          </Box>
          
          <Box 
            sx={{ 
              height: 8, 
              width: '100%', 
              bgcolor: theme.palette.grey[200],
              borderRadius: 4,
              overflow: 'hidden',
              mb: 2
            }}
          >
            <Box 
              sx={{ 
                height: '100%', 
                width: `${(stats.earned / stats.total) * 100}%`,
                bgcolor: theme.palette.primary.main,
                borderRadius: 4
              }} 
            />
          </Box>
          
          <Typography variant="body2" color="text.secondary" textAlign="right">
            {Math.round((stats.earned / stats.total) * 100)}% Complete
          </Typography>
        </Paper>
        
        <Paper elevation={2} sx={{ borderRadius: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<StarIcon />} label="All Badges" />
            <Tab icon={<TrophyIcon />} label="Achievements" />
            <Tab icon={<EducationIcon />} label="Education" />
            <Tab icon={<StreakIcon />} label="Streaks" />
            <Tab icon={<CommunityIcon />} label="Community" />
            <Tab icon={<CreativeIcon />} label="Creative" />
            <Tab icon={<KnowledgeIcon />} label="Knowledge" />
          </Tabs>
          
          <TabPanel value={tabValue} index={0}>
            <BadgeSystem 
              badges={badges} 
              onBadgeClick={handleBadgeClick}
              showNewBadgeNotification={showNewBadgeNotification}
              onNewBadgeDismiss={handleNewBadgeDismiss}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={1}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'achievement')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={2}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'education')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={3}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'streak')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={4}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'community')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={5}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'creative')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
          
          <TabPanel value={tabValue} index={6}>
            <BadgeSystem 
              badges={badges.filter(badge => badge.category === 'knowledge')} 
              onBadgeClick={handleBadgeClick}
            />
          </TabPanel>
        </Paper>
      </motion.div>
    </Container>
  );
};

export default BadgesPage;
