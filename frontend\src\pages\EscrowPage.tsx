import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Tabs,
  Tab,
  Paper,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme
} from '@mui/material';
import {
  AccountBalance as EscrowIcon,
  Gavel as DisputeIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { RootState } from '../../store';
import {
  fetchEscrowTransactions,
  fetchDisputes,
  releaseEscrowFunds,
  refundEscrowFunds,
  cancelEscrowTransaction
} from '../../features/escrow/escrowSlice';
import EscrowTransactionCard from '../../components/escrow/EscrowTransactionCard';
import { useNavigate } from 'react-router-dom';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`escrow-tabpanel-${index}`}
      aria-labelledby={`escrow-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const EscrowPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { 
    transactions, 
    disputes,
    operations
  } = useSelector((state: RootState) => state.escrow);
  
  const [tabValue, setTabValue] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  
  // Fetch data when component mounts
  useEffect(() => {
    loadData();
  }, [dispatch, tabValue, statusFilter, page]);
  
  const loadData = () => {
    if (tabValue === 0) {
      dispatch(fetchEscrowTransactions({ page, status: statusFilter }) as any);
    } else {
      dispatch(fetchDisputes({ page, status: statusFilter }) as any);
    }
  };
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(1);
    setStatusFilter('');
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setStatusFilter(event.target.value as string);
    setPage(1);
  };
  
  // Handle page change
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };
  
  // Handle transaction actions
  const handleReleaseEscrowFunds = (transaction: any) => {
    dispatch(releaseEscrowFunds(transaction.id) as any);
  };
  
  const handleRefundEscrowFunds = (transaction: any) => {
    dispatch(refundEscrowFunds(transaction.id) as any);
  };
  
  const handleCancelEscrowTransaction = (transaction: any) => {
    dispatch(cancelEscrowTransaction(transaction.id) as any);
  };
  
  const handleOpenDispute = (transaction: any) => {
    navigate(`/escrow/disputes/create?transaction=${transaction.id}`);
  };
  
  const handleViewTransactionDetails = (transaction: any) => {
    navigate(`/escrow/transactions/${transaction.id}`);
  };
  
  const handleViewDisputeDetails = (dispute: any) => {
    navigate(`/escrow/disputes/${dispute.id}`);
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Escrow & Dispute Resolution
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Securely manage your marketplace transactions and resolve disputes.
        </Typography>
      </Box>
      
      <Paper elevation={2} sx={{ borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="escrow tabs"
          >
            <Tab icon={<EscrowIcon />} label="Escrow Transactions" />
            <Tab icon={<DisputeIcon />} label="Disputes" />
          </Tabs>
          
          {tabValue === 0 && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => navigate('/escrow/transactions/create')}
              sx={{ my: 1 }}
            >
              New Escrow
            </Button>
          )}
        </Box>
        
        <Box sx={{ px: 3, pt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              value={statusFilter}
              label="Status"
              onChange={handleStatusFilterChange}
            >
              <MenuItem value="">All Statuses</MenuItem>
              {tabValue === 0 ? (
                <>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="held">Funds Held</MenuItem>
                  <MenuItem value="released">Released</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                  <MenuItem value="disputed">Disputed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </>
              ) : (
                <>
                  <MenuItem value="open">Open</MenuItem>
                  <MenuItem value="under_review">Under Review</MenuItem>
                  <MenuItem value="resolved">Resolved</MenuItem>
                  <MenuItem value="closed">Closed</MenuItem>
                </>
              )}
            </Select>
          </FormControl>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          {transactions.loading && transactions.items.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress />
            </Box>
          ) : transactions.error ? (
            <Alert severity="error" sx={{ mx: 3, mb: 3 }}>
              {transactions.error}
            </Alert>
          ) : transactions.items.length === 0 ? (
            <Box sx={{ py: 8, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No escrow transactions found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {statusFilter ? 'Try changing your filter' : 'Create a new escrow transaction to get started'}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => navigate('/escrow/transactions/create')}
                sx={{ mt: 2 }}
              >
                New Escrow
              </Button>
            </Box>
          ) : (
            <Box sx={{ p: 3 }}>
              <Grid container spacing={3}>
                {transactions.items.map((transaction) => (
                  <Grid item xs={12} key={transaction.id}>
                    <EscrowTransactionCard
                      transaction={transaction}
                      isBuyer={user?.id === transaction.buyerId}
                      isSeller={user?.id === transaction.sellerId}
                      onRelease={handleReleaseEscrowFunds}
                      onRefund={handleRefundEscrowFunds}
                      onCancel={handleCancelEscrowTransaction}
                      onDispute={handleOpenDispute}
                      onViewDetails={handleViewTransactionDetails}
                      loading={
                        operations.releasing || 
                        operations.refunding || 
                        operations.cancelling
                      }
                    />
                  </Grid>
                ))}
              </Grid>
              
              {transactions.totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={transactions.totalPages}
                    page={transactions.page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </Box>
          )}
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          {disputes.loading && disputes.items.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress />
            </Box>
          ) : disputes.error ? (
            <Alert severity="error" sx={{ mx: 3, mb: 3 }}>
              {disputes.error}
            </Alert>
          ) : disputes.items.length === 0 ? (
            <Box sx={{ py: 8, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No disputes found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {statusFilter ? 'Try changing your filter' : 'You have no active disputes'}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ p: 3 }}>
              <Grid container spacing={3}>
                {disputes.items.map((dispute) => (
                  <Grid item xs={12} md={6} key={dispute.id}>
                    <Paper 
                      elevation={2} 
                      sx={{ 
                        p: 3, 
                        borderRadius: 2,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: theme.shadows[8]
                        },
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                      onClick={() => handleViewDisputeDetails(dispute)}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6">
                          Dispute #{dispute.id}
                        </Typography>
                        <Chip 
                          size="small" 
                          label={dispute.status} 
                          color={
                            dispute.status === 'open' ? 'warning' :
                            dispute.status === 'under_review' ? 'info' :
                            dispute.status === 'resolved' ? 'success' :
                            'default'
                          }
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Order:</strong> #{dispute.orderId}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Created:</strong> {new Date(dispute.createdAt).toLocaleDateString()}
                      </Typography>
                      
                      <Typography variant="body2" sx={{ mb: 2, flexGrow: 1 }}>
                        <strong>Reason:</strong> {dispute.reason.length > 100 ? `${dispute.reason.substring(0, 100)}...` : dispute.reason}
                      </Typography>
                      
                      {dispute.resolution && (
                        <Box sx={{ mt: 'auto' }}>
                          <Divider sx={{ my: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            <strong>Resolution:</strong> {dispute.resolution}
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
              
              {disputes.totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={disputes.totalPages}
                    page={disputes.page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </Box>
          )}
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default EscrowPage;
