import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Stepper,
  Step,
  StepLabel,
  StepButton,
  Card,
  CardContent,
  IconButton,
  Drawer,
  useMediaQuery,
  useTheme,
  LinearProgress,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Rating,
  TextField,
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Home as HomeIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import { AppDispatch, RootState } from '../store';
import {
  fetchCourseByID,
  fetchModulesByCourseID,
  fetchLessonByID,
  fetchEnrollment,
  fetchProgressByUserAndCourse,
  createProgress,
  updateProgress,
  createReview,
} from '../store/slices/coursesSlice';

const CourseLessonPage: React.FC = () => {
  const { courseId, lessonId } = useParams<{ courseId: string; lessonId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { data: course, loading: courseLoading } = useSelector(
    (state: RootState) => state.courses.currentCourse
  );
  const { items: modules, loading: modulesLoading } = useSelector(
    (state: RootState) => state.courses.modules
  );
  const { data: currentLesson, loading: lessonLoading } = useSelector(
    (state: RootState) => state.courses.currentLesson
  );
  const { data: enrollment } = useSelector((state: RootState) => state.courses.enrollment);
  const { items: progress, loading: progressLoading } = useSelector(
    (state: RootState) => state.courses.progress
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [activeStep, setActiveStep] = useState(0);
  const [activeModuleIndex, setActiveModuleIndex] = useState(0);
  const [completionDialogOpen, setCompletionDialogOpen] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewData, setReviewData] = useState({
    rating: 5,
    comment: '',
  });
  
  // Flatten all lessons for navigation
  const allLessons = modules.flatMap((module) => 
    (module.lessons || []).map((lesson) => ({
      ...lesson,
      moduleId: module.id,
      moduleTitle: module.title,
    }))
  );
  
  // Find current lesson index
  const currentLessonIndex = allLessons.findIndex(
    (lesson) => lesson.id === (currentLesson ? currentLesson.id : parseInt(lessonId || '0'))
  );
  
  // Calculate course progress
  const completedLessons = progress.filter((p) => p.completed).length;
  const totalLessons = allLessons.length;
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
  const isLastLesson = currentLessonIndex === allLessons.length - 1;
  
  useEffect(() => {
    if (courseId) {
      dispatch(fetchCourseByID(parseInt(courseId)));
      dispatch(fetchModulesByCourseID(parseInt(courseId)));
      
      if (user) {
        dispatch(fetchEnrollment({ userID: user.id, courseID: parseInt(courseId) }));
        dispatch(fetchProgressByUserAndCourse({ userID: user.id, courseID: parseInt(courseId) }));
      }
    }
  }, [dispatch, courseId, user]);
  
  useEffect(() => {
    if (lessonId) {
      dispatch(fetchLessonByID(parseInt(lessonId)));
    }
  }, [dispatch, lessonId]);
  
  useEffect(() => {
    if (modules.length > 0 && currentLesson) {
      // Find the module that contains the current lesson
      const moduleIndex = modules.findIndex((module) =>
        module.lessons && module.lessons.some((lesson) => lesson.id === currentLesson.id)
      );
      
      if (moduleIndex !== -1) {
        setActiveModuleIndex(moduleIndex);
      }
    }
  }, [modules, currentLesson]);
  
  useEffect(() => {
    // Mark lesson as viewed when loaded
    if (user && currentLesson && !progressLoading) {
      const existingProgress = progress.find((p) => p.lessonID === currentLesson.id);
      
      if (existingProgress) {
        if (!existingProgress.completed) {
          dispatch(
            updateProgress({
              id: existingProgress.id,
              progress: {
                ...existingProgress,
                completed: true,
                completionDate: new Date().toISOString(),
                lastAccessedAt: new Date().toISOString(),
              },
            })
          );
        }
      } else {
        dispatch(
          createProgress({
            userID: user.id,
            lessonID: currentLesson.id,
            completed: true,
            completionDate: new Date().toISOString(),
            lastAccessedAt: new Date().toISOString(),
          })
        );
      }
    }
  }, [dispatch, user, currentLesson, progress, progressLoading]);
  
  useEffect(() => {
    // Check if course is completed
    if (
      user &&
      course &&
      enrollment &&
      !enrollment.isCompleted &&
      completedLessons === totalLessons &&
      totalLessons > 0
    ) {
      setCompletionDialogOpen(true);
    }
  }, [user, course, enrollment, completedLessons, totalLessons]);
  
  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };
  
  const handleLessonClick = (lessonId: number) => {
    navigate(`/courses/${courseId}/lessons/${lessonId}`);
    if (isMobile) {
      setDrawerOpen(false);
    }
  };
  
  const handleNextLesson = () => {
    if (currentLessonIndex < allLessons.length - 1) {
      const nextLesson = allLessons[currentLessonIndex + 1];
      navigate(`/courses/${courseId}/lessons/${nextLesson.id}`);
    }
  };
  
  const handlePreviousLesson = () => {
    if (currentLessonIndex > 0) {
      const prevLesson = allLessons[currentLessonIndex - 1];
      navigate(`/courses/${courseId}/lessons/${prevLesson.id}`);
    }
  };
  
  const handleCompletionDialogClose = () => {
    setCompletionDialogOpen(false);
  };
  
  const handleReviewDialogOpen = () => {
    setReviewDialogOpen(true);
  };
  
  const handleReviewDialogClose = () => {
    setReviewDialogOpen(false);
  };
  
  const handleReviewChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReviewData({
      ...reviewData,
      [name]: value,
    });
  };
  
  const handleRatingChange = (event: React.SyntheticEvent, newValue: number | null) => {
    setReviewData({
      ...reviewData,
      rating: newValue || 5,
    });
  };
  
  const handleReviewSubmit = async () => {
    if (user && course) {
      try {
        await dispatch(
          createReview({
            userID: user.id,
            courseID: course.id,
            rating: reviewData.rating,
            comment: reviewData.comment,
          })
        ).unwrap();
        
        setReviewDialogOpen(false);
        setCompletionDialogOpen(false);
      } catch (error) {
        console.error('Failed to submit review:', error);
      }
    }
  };
  
  const isLessonCompleted = (lessonId: number) => {
    return progress.some((p) => p.lessonID === lessonId && p.completed);
  };
  
  const renderLessonContent = () => {
    if (!currentLesson) return null;
    
    switch (currentLesson.contentType) {
      case 'text':
        return (
          <Box sx={{ p: 3 }}>
            <ReactMarkdown>{currentLesson.content}</ReactMarkdown>
          </Box>
        );
      case 'video':
        return (
          <Box sx={{ p: 3 }}>
            {currentLesson.videoURL ? (
              <Box sx={{ position: 'relative', paddingTop: '56.25%', mb: 3 }}>
                <iframe
                  src={currentLesson.videoURL}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  title={currentLesson.title}
                />
              </Box>
            ) : (
              <Alert severity="warning">Video URL is not available.</Alert>
            )}
            {currentLesson.content && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Notes
                </Typography>
                <ReactMarkdown>{currentLesson.content}</ReactMarkdown>
              </Box>
            )}
          </Box>
        );
      case 'quiz':
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quiz: {currentLesson.title}
            </Typography>
            <Alert severity="info" sx={{ mb: 3 }}>
              Complete this quiz to test your knowledge.
            </Alert>
            <Button
              variant="contained"
              color="primary"
              component={Link}
              to={`/courses/${courseId}/lessons/${currentLesson.id}/quiz`}
            >
              Start Quiz
            </Button>
          </Box>
        );
      case 'assignment':
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Assignment: {currentLesson.title}
            </Typography>
            <ReactMarkdown>{currentLesson.content}</ReactMarkdown>
            <Button
              variant="contained"
              color="primary"
              component={Link}
              to={`/courses/${courseId}/lessons/${currentLesson.id}/assignment`}
              sx={{ mt: 3 }}
            >
              Submit Assignment
            </Button>
          </Box>
        );
      default:
        return (
          <Box sx={{ p: 3 }}>
            <Alert severity="warning">
              Content type not supported: {currentLesson.contentType}
            </Alert>
          </Box>
        );
    }
  };
  
  if (courseLoading || modulesLoading || lessonLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (!course || !currentLesson) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Course or lesson not found. Please try again later.
        </Alert>
      </Container>
    );
  }
  
  const drawer = (
    <Box sx={{ width: 320, overflow: 'auto' }}>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" noWrap>
          Course Content
        </Typography>
        {isMobile && (
          <IconButton onClick={handleDrawerToggle}>
            <ChevronLeftIcon />
          </IconButton>
        )}
      </Box>
      
      <Divider />
      
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Your Progress
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <LinearProgress
            variant="determinate"
            value={progressPercentage}
            sx={{ flexGrow: 1, mr: 1 }}
          />
          <Typography variant="body2" color="text.secondary">
            {Math.round(progressPercentage)}%
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {completedLessons}/{totalLessons} lessons completed
        </Typography>
      </Box>
      
      <Divider />
      
      <List sx={{ p: 0 }}>
        {modules.map((module, moduleIndex) => (
          <React.Fragment key={module.id}>
            <ListItem
              button
              onClick={() => setActiveModuleIndex(moduleIndex === activeModuleIndex ? -1 : moduleIndex)}
              sx={{ bgcolor: 'grey.100' }}
            >
              <ListItemText
                primary={`Module ${moduleIndex + 1}: ${module.title}`}
                secondary={`${module.lessons ? module.lessons.length : 0} lessons`}
              />
              {moduleIndex === activeModuleIndex ? <ChevronLeftIcon /> : <ChevronRightIcon />}
            </ListItem>
            
            {moduleIndex === activeModuleIndex && module.lessons && (
              <List disablePadding>
                {module.lessons.map((lesson) => {
                  const isActive = currentLesson.id === lesson.id;
                  const isCompleted = isLessonCompleted(lesson.id);
                  
                  return (
                    <ListItem
                      key={lesson.id}
                      button
                      selected={isActive}
                      onClick={() => handleLessonClick(lesson.id)}
                      sx={{
                        pl: 4,
                        bgcolor: isActive ? 'primary.light' : 'transparent',
                        color: isActive ? 'primary.contrastText' : 'inherit',
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {isCompleted ? (
                          <CheckCircleIcon color="success" fontSize="small" />
                        ) : (
                          <RadioButtonUncheckedIcon fontSize="small" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={lesson.title}
                        secondary={`${lesson.contentType} • ${lesson.duration} min`}
                        primaryTypographyProps={{
                          variant: 'body2',
                          color: isActive ? 'inherit' : isCompleted ? 'text.primary' : 'text.secondary',
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                        }}
                      />
                    </ListItem>
                  );
                })}
              </List>
            )}
            
            <Divider />
          </React.Fragment>
        ))}
      </List>
    </Box>
  );
  
  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Sidebar */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            '& .MuiDrawer-paper': { width: 320 },
          }}
        >
          {drawer}
        </Drawer>
      ) : (
        <Drawer
          variant="persistent"
          open={drawerOpen}
          sx={{
            width: drawerOpen ? 320 : 0,
            flexShrink: 0,
            '& .MuiDrawer-paper': { width: 320 },
          }}
        >
          {drawer}
        </Drawer>
      )}
      
      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          overflow: 'auto',
          transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          marginLeft: isMobile ? 0 : drawerOpen ? '320px' : 0,
        }}
      >
        {/* Top navigation */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            borderBottom: '1px solid',
            borderColor: 'divider',
          }}
        >
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Breadcrumbs sx={{ flexGrow: 1 }}>
            <Button
              component={Link}
              to="/"
              size="small"
              startIcon={<HomeIcon />}
              sx={{ textTransform: 'none' }}
            >
              Home
            </Button>
            <Button
              component={Link}
              to="/courses"
              size="small"
              startIcon={<SchoolIcon />}
              sx={{ textTransform: 'none' }}
            >
              Courses
            </Button>
            <Button
              component={Link}
              to={`/courses/${courseId}`}
              size="small"
              sx={{ textTransform: 'none' }}
            >
              {course.title}
            </Button>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              {currentLesson.title}
            </Typography>
          </Breadcrumbs>
        </Box>
        
        {/* Lesson content */}
        <Box sx={{ p: 0 }}>
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
              <Typography variant="h5" gutterBottom>
                {currentLesson.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {currentLesson.description}
              </Typography>
            </Box>
            
            {renderLessonContent()}
          </Paper>
          
          {/* Navigation buttons */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              p: 2,
              borderTop: '1px solid',
              borderColor: 'divider',
              bgcolor: 'background.paper',
              position: 'sticky',
              bottom: 0,
            }}
          >
            <Button
              variant="outlined"
              startIcon={<ChevronLeftIcon />}
              onClick={handlePreviousLesson}
              disabled={currentLessonIndex === 0}
            >
              Previous
            </Button>
            
            <Button
              variant="contained"
              endIcon={<ChevronRightIcon />}
              onClick={handleNextLesson}
              disabled={currentLessonIndex === allLessons.length - 1}
            >
              Next
            </Button>
          </Box>
        </Box>
      </Box>
      
      {/* Course completion dialog */}
      <Dialog open={completionDialogOpen} onClose={handleCompletionDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Congratulations!</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
            <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              You've completed the course!
            </Typography>
            <Typography variant="body1" align="center">
              You've successfully completed all lessons in "{course.title}". Would you like to leave a review?
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleReviewDialogOpen}
            >
              Leave a Review
            </Button>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              component={Link}
              to={`/courses/${courseId}/certificate`}
              variant="outlined"
            >
              View Certificate
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCompletionDialogClose}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Review dialog */}
      <Dialog open={reviewDialogOpen} onClose={handleReviewDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Review Course</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 3 }}>
            Please share your experience with this course. Your feedback helps other students and the instructor.
          </DialogContentText>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              How would you rate this course?
            </Typography>
            <Rating
              name="rating"
              value={reviewData.rating}
              onChange={handleRatingChange}
              size="large"
              precision={0.5}
            />
          </Box>
          
          <TextField
            autoFocus
            margin="dense"
            name="comment"
            label="Your Review"
            type="text"
            fullWidth
            variant="outlined"
            multiline
            rows={4}
            value={reviewData.comment}
            onChange={handleReviewChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleReviewDialogClose}>Cancel</Button>
          <Button
            onClick={handleReviewSubmit}
            variant="contained"
            disabled={!reviewData.comment}
          >
            Submit Review
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseLessonPage;
