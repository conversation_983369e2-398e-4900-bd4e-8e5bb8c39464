import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Container, Grid, Typography, Box, Tabs, Tab, TextField, Button, FormControl, InputLabel, Select, MenuItem, Divider, Paper } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';

// Import marketplace components
import ProductCard from '../components/marketplace/ProductCard';
import ServiceCard from '../components/marketplace/ServiceCard';
import JobCard from '../components/marketplace/JobCard';

// Import from marketplace slice
import { 
  fetchProducts, 
  fetchServices, 
  fetchJobs,
  selectProducts,
  selectServices,
  selectJobs,
  selectLoading
} from '../features/marketplace/marketplaceSlice';

// Define tab panel interface
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab Panel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`marketplace-tabpanel-${index}`}
      aria-labelledby={`marketplace-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Tab props
function a11yProps(index: number) {
  return {
    id: `marketplace-tab-${index}`,
    'aria-controls': `marketplace-tabpanel-${index}`,
  };
}

const MarketplacePage: React.FC = () => {
  const dispatch = useDispatch();
  const products = useSelector(selectProducts);
  const services = useSelector(selectServices);
  const jobs = useSelector(selectJobs);
  const loading = useSelector(selectLoading);

  // State for tabs
  const [tabValue, setTabValue] = useState(0);
  
  // State for filters
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [sortBy, setSortBy] = useState('newest');

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchProducts());
    dispatch(fetchServices());
    dispatch(fetchJobs());
  }, [dispatch]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle search
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Toggle filters
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Filter products based on search term and filters
  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (categoryFilter === 'all' || product.category === categoryFilter) &&
    product.price >= priceRange[0] && product.price <= priceRange[1]
  );

  // Filter services based on search term and filters
  const filteredServices = services.filter(service => 
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (categoryFilter === 'all' || service.category === categoryFilter) &&
    service.price >= priceRange[0] && service.price <= priceRange[1]
  );

  // Filter jobs based on search term and filters
  const filteredJobs = jobs.filter(job => 
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (categoryFilter === 'all' || job.category === categoryFilter)
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (sortBy === 'newest') return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    if (sortBy === 'oldest') return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    if (sortBy === 'price-low') return a.price - b.price;
    if (sortBy === 'price-high') return b.price - a.price;
    return 0;
  });

  // Sort services
  const sortedServices = [...filteredServices].sort((a, b) => {
    if (sortBy === 'newest') return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    if (sortBy === 'oldest') return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    if (sortBy === 'price-low') return a.price - b.price;
    if (sortBy === 'price-high') return b.price - a.price;
    return 0;
  });

  // Sort jobs
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    if (sortBy === 'newest') return new Date(b.postedDate).getTime() - new Date(a.postedDate).getTime();
    if (sortBy === 'oldest') return new Date(a.postedDate).getTime() - new Date(b.postedDate).getTime();
    return 0;
  });

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Marketplace
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" paragraph>
        Discover products, services, and job opportunities from our community
      </Typography>

      {/* Search and Filter Bar */}
      <Paper elevation={3} sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search marketplace..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Button 
              variant="outlined" 
              startIcon={<FilterListIcon />}
              onClick={toggleFilters}
              sx={{ mr: 2 }}
            >
              Filters
            </Button>
            <FormControl variant="outlined" sx={{ minWidth: 120 }}>
              <InputLabel id="sort-by-label">Sort By</InputLabel>
              <Select
                labelId="sort-by-label"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                label="Sort By"
              >
                <MenuItem value="newest">Newest</MenuItem>
                <MenuItem value="oldest">Oldest</MenuItem>
                <MenuItem value="price-low">Price: Low to High</MenuItem>
                <MenuItem value="price-high">Price: High to Low</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Expanded Filters */}
        {showFilters && (
          <Box sx={{ mt: 2 }}>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel id="category-label">Category</InputLabel>
                  <Select
                    labelId="category-label"
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    <MenuItem value="books">Books</MenuItem>
                    <MenuItem value="art">Art</MenuItem>
                    <MenuItem value="crafts">Crafts</MenuItem>
                    <MenuItem value="digital">Digital Products</MenuItem>
                    <MenuItem value="services">Services</MenuItem>
                    <MenuItem value="courses">Courses</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography gutterBottom>Price Range</Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    label="Min"
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <TextField
                    label="Max"
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  sx={{ mt: 2 }}
                  onClick={() => {
                    setSearchTerm('');
                    setCategoryFilter('all');
                    setPriceRange([0, 1000]);
                    setSortBy('newest');
                  }}
                >
                  Reset Filters
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="marketplace tabs">
          <Tab label="Products" {...a11yProps(0)} />
          <Tab label="Services" {...a11yProps(1)} />
          <Tab label="Jobs" {...a11yProps(2)} />
        </Tabs>
      </Box>

      {/* Products Tab */}
      <TabPanel value={tabValue} index={0}>
        {loading ? (
          <Typography>Loading products...</Typography>
        ) : sortedProducts.length > 0 ? (
          <Grid container spacing={3}>
            {sortedProducts.map((product) => (
              <Grid item key={product.id} xs={12} sm={6} md={4}>
                <ProductCard product={product} />
              </Grid>
            ))}
          </Grid>
        ) : (
          <Typography>No products found matching your criteria.</Typography>
        )}
      </TabPanel>

      {/* Services Tab */}
      <TabPanel value={tabValue} index={1}>
        {loading ? (
          <Typography>Loading services...</Typography>
        ) : sortedServices.length > 0 ? (
          <Grid container spacing={3}>
            {sortedServices.map((service) => (
              <Grid item key={service.id} xs={12} sm={6} md={4}>
                <ServiceCard service={service} />
              </Grid>
            ))}
          </Grid>
        ) : (
          <Typography>No services found matching your criteria.</Typography>
        )}
      </TabPanel>

      {/* Jobs Tab */}
      <TabPanel value={tabValue} index={2}>
        {loading ? (
          <Typography>Loading job opportunities...</Typography>
        ) : sortedJobs.length > 0 ? (
          <Grid container spacing={3}>
            {sortedJobs.map((job) => (
              <Grid item key={job.id} xs={12}>
                <JobCard job={job} />
              </Grid>
            ))}
          </Grid>
        ) : (
          <Typography>No job opportunities found matching your criteria.</Typography>
        )}
      </TabPanel>
    </Container>
  );
};

export default MarketplacePage;
