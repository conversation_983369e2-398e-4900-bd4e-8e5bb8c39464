package handlers

import (
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"
        "github.com/yerenwgventures/GreatNigeriaLibrary/internal/project/models"
        "github.com/yerenwgventures/GreatNigeriaLibrary/internal/project/service"
)

// ProjectHandlers contains all the handlers for project-related endpoints
type ProjectHandlers struct {
        ProjectService *service.ProjectService
}

// NewProjectHandlers creates a new ProjectHandlers
func NewProjectHandlers(projectService *service.ProjectService) *ProjectHandlers {
        return &ProjectHandlers{
                ProjectService: projectService,
        }
}

// GetProjects handles GET /api/projects
func (h *ProjectHandlers) GetProjects(c *gin.Context) {
        // Parse filter parameters
        params := make(map[string]interface{})

        if status := c.Query("status"); status != "" {
                params["status"] = status
        }

        if ownerIDStr := c.Query("owner_id"); ownerIDStr != "" {
                ownerID, err := strconv.ParseUint(ownerIDStr, 10, 64)
                if err == nil {
                        params["owner_id"] = ownerID
                }
        }

        if isPublicStr := c.Query("is_public"); isPublicStr != "" {
                isPublic := isPublicStr == "true"
                params["is_public"] = isPublic
        }

        if search := c.Query("search"); search != "" {
                params["search"] = search
        }

        if location := c.Query("location"); location != "" {
                params["location"] = location
        }

        projects, err := h.ProjectService.GetProjects(params)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, projects)
}

// GetProjectByID handles GET /api/projects/:id
func (h *ProjectHandlers) GetProjectByID(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        project, err := h.ProjectService.GetProjectByID(id)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
                return
        }

        c.JSON(http.StatusOK, project)
}

// CreateProject handles POST /api/projects
func (h *ProjectHandlers) CreateProject(c *gin.Context) {
        var project models.Project
        if err := c.ShouldBindJSON(&project); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        // Set owner ID from authenticated user
        // userID, exists := c.Get("user_id")
        // if !exists {
        //      c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        //      return
        // }
        // project.OwnerID = userID.(uint64)

        if err := h.ProjectService.CreateProject(&project); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusCreated, project)
}

// UpdateProject handles PUT /api/projects/:id
func (h *ProjectHandlers) UpdateProject(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        // Check if the project exists
        existingProject, err := h.ProjectService.GetProjectByID(id)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
                return
        }

        // Check if the user is the owner of the project
        // userID, exists := c.Get("user_id")
        // if !exists {
        //      c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        //      return
        // }
        // if existingProject.OwnerID != userID.(uint64) {
        //      c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to update this project"})
        //      return
        // }

        var project models.Project
        if err := c.ShouldBindJSON(&project); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        project.ID = id
        project.OwnerID = existingProject.OwnerID // Ensure the owner remains the same

        if err := h.ProjectService.UpdateProject(&project); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, project)
}

// DeleteProject handles DELETE /api/projects/:id
func (h *ProjectHandlers) DeleteProject(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        // Check if the project exists
        _, err = h.ProjectService.GetProjectByID(id)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
                return
        }

        // Check if the user is the owner of the project
        // userID, exists := c.Get("user_id")
        // if !exists {
        //      c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        //      return
        // }
        // if existingProject.OwnerID != userID.(uint64) {
        //      c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to delete this project"})
        //      return
        // }

        if err := h.ProjectService.DeleteProject(id); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

// GetProjectMembers handles GET /api/projects/:id/members
func (h *ProjectHandlers) GetProjectMembers(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        members, err := h.ProjectService.GetProjectMembers(id)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, members)
}

// AddMemberToProject handles POST /api/projects/:id/members
func (h *ProjectHandlers) AddMemberToProject(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        var request struct {
                UserID uint64 `json:"user_id" binding:"required"`
                Role   string `json:"role" binding:"required"`
        }

        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        if err := h.ProjectService.AddMemberToProject(projectID, request.UserID, request.Role); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Member added to project successfully"})
}

// UpdateMemberRole handles PUT /api/projects/:id/members/:userID
func (h *ProjectHandlers) UpdateMemberRole(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        userID, err := strconv.ParseUint(c.Param("userID"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID format"})
                return
        }

        var request struct {
                Role string `json:"role" binding:"required"`
        }

        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        if err := h.ProjectService.UpdateMemberRole(projectID, userID, request.Role); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Member role updated successfully"})
}

// RemoveMemberFromProject handles DELETE /api/projects/:id/members/:userID
func (h *ProjectHandlers) RemoveMemberFromProject(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        userID, err := strconv.ParseUint(c.Param("userID"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID format"})
                return
        }

        if err := h.ProjectService.RemoveMemberFromProject(projectID, userID); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Member removed from project successfully"})
}

// GetProjectsByMember handles GET /api/users/:id/projects
func (h *ProjectHandlers) GetProjectsByMember(c *gin.Context) {
        userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID format"})
                return
        }

        projects, err := h.ProjectService.GetProjectsByMember(userID)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, projects)
}

// CreateTask handles POST /api/projects/:id/tasks
func (h *ProjectHandlers) CreateTask(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        var task models.Task
        if err := c.ShouldBindJSON(&task); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        task.ProjectID = projectID

        // Set creator ID from authenticated user
        // userID, exists := c.Get("user_id")
        // if !exists {
        //      c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        //      return
        // }
        // task.CreatorID = userID.(uint64)

        if err := h.ProjectService.CreateTask(&task); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusCreated, task)
}

// GetTasksByProject handles GET /api/projects/:id/tasks
func (h *ProjectHandlers) GetTasksByProject(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        // Parse filter parameters
        params := make(map[string]interface{})

        if status := c.Query("status"); status != "" {
                params["status"] = status
        }

        if priority := c.Query("priority"); priority != "" {
                params["priority"] = priority
        }

        if assigneeIDStr := c.Query("assignee_id"); assigneeIDStr != "" {
                assigneeID, err := strconv.ParseUint(assigneeIDStr, 10, 64)
                if err == nil {
                        params["assignee_id"] = assigneeID
                }
        }

        tasks, err := h.ProjectService.GetTasksByProject(projectID, params)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, tasks)
}

// GetTaskByID handles GET /api/tasks/:id
func (h *ProjectHandlers) GetTaskByID(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        task, err := h.ProjectService.GetTaskByID(id)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Task not found"})
                return
        }

        c.JSON(http.StatusOK, task)
}

// UpdateTask handles PUT /api/tasks/:id
func (h *ProjectHandlers) UpdateTask(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        // Check if the task exists
        existingTask, err := h.ProjectService.GetTaskByID(id)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Task not found"})
                return
        }

        var task models.Task
        if err := c.ShouldBindJSON(&task); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        task.ID = id
        task.ProjectID = existingTask.ProjectID // Ensure project remains the same
        task.CreatorID = existingTask.CreatorID // Ensure creator remains the same

        if err := h.ProjectService.UpdateTask(&task); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, task)
}

// DeleteTask handles DELETE /api/tasks/:id
func (h *ProjectHandlers) DeleteTask(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        if err := h.ProjectService.DeleteTask(id); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Task deleted successfully"})
}

// AssignTask handles PUT /api/tasks/:id/assign
func (h *ProjectHandlers) AssignTask(c *gin.Context) {
        id, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
                return
        }

        var request struct {
                AssigneeID uint64 `json:"assignee_id" binding:"required"`
        }

        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        if err := h.ProjectService.AssignTask(id, request.AssigneeID); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Task assigned successfully"})
}

// CreateProjectUpdate handles POST /api/projects/:id/updates
func (h *ProjectHandlers) CreateProjectUpdate(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        var update models.ProjectUpdate
        if err := c.ShouldBindJSON(&update); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        update.ProjectID = projectID

        // Set user ID from authenticated user
        // userID, exists := c.Get("user_id")
        // if !exists {
        //      c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        //      return
        // }
        // update.UserID = userID.(uint64)

        if err := h.ProjectService.CreateProjectUpdate(&update); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusCreated, update)
}

// GetProjectUpdates handles GET /api/projects/:id/updates
func (h *ProjectHandlers) GetProjectUpdates(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        updates, err := h.ProjectService.GetProjectUpdates(projectID)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, updates)
}

// AddTagToProject handles POST /api/projects/:id/tags
func (h *ProjectHandlers) AddTagToProject(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        var request struct {
                TagName string `json:"tag_name" binding:"required"`
        }

        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        if err := h.ProjectService.AddTagToProject(projectID, request.TagName); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Tag added to project successfully"})
}

// RemoveTagFromProject handles DELETE /api/projects/:id/tags/:tagName
func (h *ProjectHandlers) RemoveTagFromProject(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        tagName := c.Param("tagName")

        if err := h.ProjectService.RemoveTagFromProject(projectID, tagName); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Tag removed from project successfully"})
}

// GetProjectsByBookSection handles GET /api/books/:bookID/chapters/:chapterID/sections/:sectionID/projects
func (h *ProjectHandlers) GetProjectsByBookSection(c *gin.Context) {
        bookID, err := strconv.ParseUint(c.Param("bookID"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID format"})
                return
        }

        chapterID, err := strconv.ParseUint(c.Param("chapterID"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chapter ID format"})
                return
        }

        sectionID, err := strconv.ParseUint(c.Param("sectionID"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid section ID format"})
                return
        }

        projects, err := h.ProjectService.GetProjectsByBookSection(bookID, chapterID, sectionID)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, projects)
}

// AddProjectToBookSection handles POST /api/projects/:id/booksections
func (h *ProjectHandlers) AddProjectToBookSection(c *gin.Context) {
        projectID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
                return
        }

        var request struct {
                BookID    uint64 `json:"book_id" binding:"required"`
                ChapterID uint64 `json:"chapter_id" binding:"required"`
                SectionID uint64 `json:"section_id" binding:"required"`
        }

        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }

        if err := h.ProjectService.AddProjectToBookSection(projectID, request.BookID, request.ChapterID, request.SectionID); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Project added to book section successfully"})
}