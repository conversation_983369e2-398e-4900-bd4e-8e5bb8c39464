import api from './api';

export interface Skill {
  id: number;
  name: string;
  description: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  tags: string[];
  createdBy?: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserSkill {
  id: number;
  userId: number;
  skillId: number;
  skill: Skill;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  yearsOfExperience: number;
  isVerified: boolean;
  endorsements: number;
  createdAt: string;
  updatedAt: string;
}

export interface SkillEndorsement {
  id: number;
  userSkillId: number;
  endorserId: number;
  endorserName: string;
  comment?: string;
  createdAt: string;
}

export interface SkillAssessment {
  id: number;
  skillId: number;
  title: string;
  description: string;
  questions: SkillAssessmentQuestion[];
  passingScore: number;
  timeLimit?: number; // in minutes
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface SkillAssessmentQuestion {
  id: number;
  assessmentId: number;
  questionText: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
  points: number;
}

export interface SkillAssessmentAttempt {
  id: number;
  assessmentId: number;
  userId: number;
  score: number;
  isPassed: boolean;
  startTime: string;
  endTime?: string;
  answers: SkillAssessmentAnswer[];
}

export interface SkillAssessmentAnswer {
  questionId: number;
  userAnswer: string;
  isCorrect: boolean;
}

export interface SkillNeed {
  id: number;
  userId: number;
  title: string;
  description: string;
  requiredSkills: RequiredSkill[];
  projectType: 'one-time' | 'ongoing' | 'mentorship';
  location?: string;
  isRemote: boolean;
  compensation?: string;
  status: 'open' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

export interface RequiredSkill {
  skillId: number;
  skill: Skill;
  minimumLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  isRequired: boolean;
  weight: number; // 1-10, importance of this skill
}

export interface SkillMatch {
  needId: number;
  need: SkillNeed;
  userId: number;
  userName: string;
  userProfileImage?: string;
  matchScore: number; // 0-100
  matchedSkills: MatchedSkill[];
  status: 'pending' | 'accepted' | 'rejected' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface MatchedSkill {
  skillId: number;
  skillName: string;
  requiredLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  userLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  matchScore: number; // 0-100
}

export interface SkillCategory {
  id: number;
  name: string;
  description: string;
  parentId?: number;
}

const skillsService = {
  // Skills CRUD operations
  getSkills: async (params?: { category?: string; search?: string }) => {
    const response = await api.get('/skills', { params });
    return response.data;
  },

  getSkillById: async (id: number) => {
    const response = await api.get(`/skills/${id}`);
    return response.data;
  },

  createSkill: async (skill: Partial<Skill>) => {
    const response = await api.post('/skills', skill);
    return response.data;
  },

  updateSkill: async (id: number, skill: Partial<Skill>) => {
    const response = await api.put(`/skills/${id}`, skill);
    return response.data;
  },

  deleteSkill: async (id: number) => {
    const response = await api.delete(`/skills/${id}`);
    return response.data;
  },

  // User skills operations
  getUserSkills: async (userId: number) => {
    const response = await api.get(`/skills/users/${userId}`);
    return response.data;
  },

  addUserSkill: async (userId: number, userSkill: Partial<UserSkill>) => {
    const response = await api.post(`/skills/users/${userId}`, userSkill);
    return response.data;
  },

  updateUserSkill: async (userId: number, skillId: number, userSkill: Partial<UserSkill>) => {
    const response = await api.put(`/skills/users/${userId}/skills/${skillId}`, userSkill);
    return response.data;
  },

  deleteUserSkill: async (userId: number, skillId: number) => {
    const response = await api.delete(`/skills/users/${userId}/skills/${skillId}`);
    return response.data;
  },

  // Endorsements
  getSkillEndorsements: async (userSkillId: number) => {
    const response = await api.get(`/skills/endorsements/${userSkillId}`);
    return response.data;
  },

  endorseSkill: async (userSkillId: number, endorserId: number, comment?: string) => {
    const response = await api.post(`/skills/endorsements`, {
      userSkillId,
      endorserId,
      comment,
    });
    return response.data;
  },

  // Skill assessments
  getSkillAssessments: async (skillId?: number) => {
    const params = skillId ? { skillId } : undefined;
    const response = await api.get('/skills/assessments', { params });
    return response.data;
  },

  getSkillAssessmentById: async (id: number) => {
    const response = await api.get(`/skills/assessments/${id}`);
    return response.data;
  },

  createSkillAssessment: async (assessment: Partial<SkillAssessment>) => {
    const response = await api.post('/skills/assessments', assessment);
    return response.data;
  },

  updateSkillAssessment: async (id: number, assessment: Partial<SkillAssessment>) => {
    const response = await api.put(`/skills/assessments/${id}`, assessment);
    return response.data;
  },

  deleteSkillAssessment: async (id: number) => {
    const response = await api.delete(`/skills/assessments/${id}`);
    return response.data;
  },

  // Assessment attempts
  startAssessmentAttempt: async (assessmentId: number, userId: number) => {
    const response = await api.post('/skills/assessment-attempts', {
      assessmentId,
      userId,
    });
    return response.data;
  },

  submitAssessmentAttempt: async (attemptId: number, answers: SkillAssessmentAnswer[]) => {
    const response = await api.put(`/skills/assessment-attempts/${attemptId}/submit`, {
      answers,
    });
    return response.data;
  },

  getUserAssessmentAttempts: async (userId: number) => {
    const response = await api.get(`/skills/assessment-attempts/user/${userId}`);
    return response.data;
  },

  // Skill needs
  getSkillNeeds: async (params?: { status?: string; location?: string }) => {
    const response = await api.get('/skills/needs', { params });
    return response.data;
  },

  getSkillNeedById: async (id: number) => {
    const response = await api.get(`/skills/needs/${id}`);
    return response.data;
  },

  getUserSkillNeeds: async (userId: number) => {
    const response = await api.get(`/skills/needs/user/${userId}`);
    return response.data;
  },

  createSkillNeed: async (need: Partial<SkillNeed>) => {
    const response = await api.post('/skills/needs', need);
    return response.data;
  },

  updateSkillNeed: async (id: number, need: Partial<SkillNeed>) => {
    const response = await api.put(`/skills/needs/${id}`, need);
    return response.data;
  },

  deleteSkillNeed: async (id: number) => {
    const response = await api.delete(`/skills/needs/${id}`);
    return response.data;
  },

  // Skill matching
  getSkillMatches: async (needId: number) => {
    const response = await api.get(`/skills/matches/need/${needId}`);
    return response.data;
  },

  getUserSkillMatches: async (userId: number) => {
    const response = await api.get(`/skills/matches/user/${userId}`);
    return response.data;
  },

  updateMatchStatus: async (matchId: number, status: 'accepted' | 'rejected' | 'completed') => {
    const response = await api.put(`/skills/matches/${matchId}/status`, { status });
    return response.data;
  },

  // Run matching algorithm
  findMatches: async (needId: number) => {
    const response = await api.post(`/skills/matches/find`, { needId });
    return response.data;
  },

  // Skill categories
  getSkillCategories: async () => {
    const response = await api.get('/skills/categories');
    return response.data;
  },

  createSkillCategory: async (category: Partial<SkillCategory>) => {
    const response = await api.post('/skills/categories', category);
    return response.data;
  },

  updateSkillCategory: async (id: number, category: Partial<SkillCategory>) => {
    const response = await api.put(`/skills/categories/${id}`, category);
    return response.data;
  },

  deleteSkillCategory: async (id: number) => {
    const response = await api.delete(`/skills/categories/${id}`);
    return response.data;
  },
};

export default skillsService;
