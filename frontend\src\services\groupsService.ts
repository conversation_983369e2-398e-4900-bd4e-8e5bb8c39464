import api from './api';

export interface Group {
  id: number;
  name: string;
  description: string;
  type: 'local_action' | 'interest' | 'project' | 'study';
  visibility: 'public' | 'private' | 'secret';
  location: string;
  latitude: number;
  longitude: number;
  isVirtual: boolean;
  joinApprovalRequired: boolean;
  membershipCode?: string;
  maxMembers: number;
  createdById: number;
  bannerImage?: string;
  avatarImage?: string;
  tags?: string;
  createdAt: string;
  updatedAt: string;
  members?: GroupMember[];
  events?: LocalEvent[];
  resources?: SharedResource[];
  discussions?: GroupDiscussion[];
  actions?: GroupAction[];
}

export interface GroupMember {
  id: number;
  groupId: number;
  userId: number;
  role: 'owner' | 'admin' | 'moderator' | 'member';
  status: 'active' | 'pending' | 'invited' | 'blocked';
  joinedAt?: string;
  invitedBy?: number;
  approvedBy?: number;
  lastActive?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    name: string;
    email: string;
    profileImage?: string;
  };
}

export interface LocalEvent {
  id: number;
  groupId: number;
  title: string;
  description: string;
  location: string;
  latitude: number;
  longitude: number;
  isVirtual: boolean;
  virtualLink?: string;
  startTime: string;
  endTime: string;
  status: 'planned' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  createdById: number;
  maxAttendees: number;
  createdAt: string;
  updatedAt: string;
  attendees?: EventAttendee[];
  resources?: SharedResource[];
}

export interface EventAttendee {
  id: number;
  eventId: number;
  userId: number;
  status: 'going' | 'maybe' | 'not_going';
  rsvpTime: string;
  checkedIn: boolean;
  checkInTime?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    name: string;
    email: string;
    profileImage?: string;
  };
}

export interface SharedResource {
  id: number;
  groupId?: number;
  eventId?: number;
  actionId?: number;
  title: string;
  description: string;
  type: 'document' | 'image' | 'video' | 'audio' | 'link' | 'book';
  url?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
  createdById: number;
  isPublic: boolean;
  tags?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: number;
    name: string;
    profileImage?: string;
  };
}

export interface GroupDiscussion {
  id: number;
  groupId: number;
  title: string;
  content: string;
  createdById: number;
  isPinned: boolean;
  isLocked: boolean;
  createdAt: string;
  updatedAt: string;
  comments?: GroupComment[];
  createdBy?: {
    id: number;
    name: string;
    profileImage?: string;
  };
}

export interface GroupComment {
  id: number;
  discussionId: number;
  content: string;
  createdById: number;
  parentId?: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: number;
    name: string;
    profileImage?: string;
  };
  replies?: GroupComment[];
}

export interface GroupAction {
  id: number;
  groupId: number;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: string;
  completedAt?: string;
  createdById: number;
  assignedToId?: number;
  createdAt: string;
  updatedAt: string;
  resources?: SharedResource[];
  createdBy?: {
    id: number;
    name: string;
    profileImage?: string;
  };
  assignedTo?: {
    id: number;
    name: string;
    profileImage?: string;
  };
}

export interface GroupInvitation {
  id: number;
  groupId: number;
  email: string;
  userId?: number;
  invitedById: number;
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'cancelled';
  expiresAt: string;
  code: string;
  message?: string;
  createdAt: string;
  updatedAt: string;
  group?: Group;
  invitedBy?: {
    id: number;
    name: string;
    profileImage?: string;
  };
}

export interface GroupJoinRequest {
  id: number;
  groupId: number;
  userId: number;
  status: 'pending' | 'approved' | 'rejected';
  message?: string;
  approvedBy?: number;
  approvedAt?: string;
  rejectedBy?: number;
  rejectedAt?: string;
  code?: string;
  createdAt: string;
  updatedAt: string;
  group?: Group;
  user?: {
    id: number;
    name: string;
    email: string;
    profileImage?: string;
  };
}

const groupsService = {
  // Group operations
  getGroups: async (params?: { page?: number; pageSize?: number }) => {
    const response = await api.get('/groups', { params });
    return response.data;
  },

  getGroupById: async (id: number) => {
    const response = await api.get(`/groups/${id}`);
    return response.data;
  },

  createGroup: async (group: Partial<Group>) => {
    const response = await api.post('/groups', group);
    return response.data;
  },

  updateGroup: async (id: number, group: Partial<Group>) => {
    const response = await api.put(`/groups/${id}`, group);
    return response.data;
  },

  deleteGroup: async (id: number) => {
    const response = await api.delete(`/groups/${id}`);
    return response.data;
  },

  getNearbyGroups: async (latitude: number, longitude: number, radius: number = 10) => {
    const response = await api.get('/groups/nearby', {
      params: { latitude, longitude, radius },
    });
    return response.data;
  },

  searchGroups: async (params: {
    q?: string;
    type?: string;
    tags?: string;
    page?: number;
    pageSize?: number;
  }) => {
    const response = await api.get('/groups/search', { params });
    return response.data;
  },

  getUserGroups: async (userId: number, includeInvited: boolean = false) => {
    const response = await api.get(`/groups/user/${userId}`, {
      params: { includeInvited },
    });
    return response.data;
  },

  // Member operations
  getGroupMembers: async (
    groupId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/members`, { params });
    return response.data;
  },

  addMember: async (
    groupId: number,
    data: { userId: number; role: string; status: string }
  ) => {
    const response = await api.post(`/groups/${groupId}/members`, data);
    return response.data;
  },

  getGroupMember: async (groupId: number, userId: number) => {
    const response = await api.get(`/groups/${groupId}/members/${userId}`);
    return response.data;
  },

  updateMember: async (
    groupId: number,
    userId: number,
    data: { role: string; status: string }
  ) => {
    const response = await api.put(`/groups/${groupId}/members/${userId}`, data);
    return response.data;
  },

  removeMember: async (groupId: number, userId: number) => {
    const response = await api.delete(`/groups/${groupId}/members/${userId}`);
    return response.data;
  },

  // Event operations
  getGroupEvents: async (
    groupId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/events`, { params });
    return response.data;
  },

  createEvent: async (groupId: number, event: Partial<LocalEvent>) => {
    const response = await api.post(`/groups/${groupId}/events`, event);
    return response.data;
  },

  getEventById: async (eventId: number) => {
    const response = await api.get(`/groups/events/${eventId}`);
    return response.data;
  },

  updateEvent: async (eventId: number, event: Partial<LocalEvent>) => {
    const response = await api.put(`/groups/events/${eventId}`, event);
    return response.data;
  },

  deleteEvent: async (eventId: number) => {
    const response = await api.delete(`/groups/events/${eventId}`);
    return response.data;
  },

  getUserUpcomingEvents: async (userId: number, params?: { page?: number; pageSize?: number }) => {
    const response = await api.get(`/groups/user/${userId}/events/upcoming`, { params });
    return response.data;
  },

  // Attendee operations
  getEventAttendees: async (
    eventId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/events/${eventId}/attendees`, { params });
    return response.data;
  },

  addAttendee: async (eventId: number, data: { status: string }) => {
    const response = await api.post(`/groups/events/${eventId}/attendees`, data);
    return response.data;
  },

  getEventAttendee: async (eventId: number, userId: number) => {
    const response = await api.get(`/groups/events/${eventId}/attendees/${userId}`);
    return response.data;
  },

  updateAttendee: async (eventId: number, userId: number, data: { status: string }) => {
    const response = await api.put(`/groups/events/${eventId}/attendees/${userId}`, data);
    return response.data;
  },

  removeAttendee: async (eventId: number, userId: number) => {
    const response = await api.delete(`/groups/events/${eventId}/attendees/${userId}`);
    return response.data;
  },

  checkInAttendee: async (eventId: number, userId: number) => {
    const response = await api.post(`/groups/events/${eventId}/attendees/${userId}/checkin`);
    return response.data;
  },

  // Resource operations
  getGroupResources: async (
    groupId: number,
    params?: { type?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/resources`, { params });
    return response.data;
  },

  createGroupResource: async (groupId: number, resource: Partial<SharedResource>) => {
    const response = await api.post(`/groups/${groupId}/resources`, resource);
    return response.data;
  },

  getEventResources: async (eventId: number, params?: { page?: number; pageSize?: number }) => {
    const response = await api.get(`/groups/events/${eventId}/resources`, { params });
    return response.data;
  },

  createEventResource: async (eventId: number, resource: Partial<SharedResource>) => {
    const response = await api.post(`/groups/events/${eventId}/resources`, resource);
    return response.data;
  },

  getActionResources: async (actionId: number, params?: { page?: number; pageSize?: number }) => {
    const response = await api.get(`/groups/actions/${actionId}/resources`, { params });
    return response.data;
  },

  createActionResource: async (actionId: number, resource: Partial<SharedResource>) => {
    const response = await api.post(`/groups/actions/${actionId}/resources`, resource);
    return response.data;
  },

  getResourceById: async (resourceId: number) => {
    const response = await api.get(`/groups/resources/${resourceId}`);
    return response.data;
  },

  updateResource: async (resourceId: number, resource: Partial<SharedResource>) => {
    const response = await api.put(`/groups/resources/${resourceId}`, resource);
    return response.data;
  },

  deleteResource: async (resourceId: number) => {
    const response = await api.delete(`/groups/resources/${resourceId}`);
    return response.data;
  },

  // Discussion operations
  getGroupDiscussions: async (groupId: number, params?: { page?: number; pageSize?: number }) => {
    const response = await api.get(`/groups/${groupId}/discussions`, { params });
    return response.data;
  },

  createDiscussion: async (groupId: number, discussion: Partial<GroupDiscussion>) => {
    const response = await api.post(`/groups/${groupId}/discussions`, discussion);
    return response.data;
  },

  getDiscussionById: async (discussionId: number) => {
    const response = await api.get(`/groups/discussions/${discussionId}`);
    return response.data;
  },

  updateDiscussion: async (discussionId: number, discussion: Partial<GroupDiscussion>) => {
    const response = await api.put(`/groups/discussions/${discussionId}`, discussion);
    return response.data;
  },

  deleteDiscussion: async (discussionId: number) => {
    const response = await api.delete(`/groups/discussions/${discussionId}`);
    return response.data;
  },

  // Comment operations
  getDiscussionComments: async (
    discussionId: number,
    params?: { page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/discussions/${discussionId}/comments`, { params });
    return response.data;
  },

  createComment: async (discussionId: number, comment: Partial<GroupComment>) => {
    const response = await api.post(`/groups/discussions/${discussionId}/comments`, comment);
    return response.data;
  },

  getCommentById: async (commentId: number) => {
    const response = await api.get(`/groups/comments/${commentId}`);
    return response.data;
  },

  updateComment: async (commentId: number, content: string) => {
    const response = await api.put(`/groups/comments/${commentId}`, { content });
    return response.data;
  },

  deleteComment: async (commentId: number) => {
    const response = await api.delete(`/groups/comments/${commentId}`);
    return response.data;
  },

  // Action operations
  getGroupActions: async (
    groupId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/actions`, { params });
    return response.data;
  },

  createAction: async (groupId: number, action: Partial<GroupAction>) => {
    const response = await api.post(`/groups/${groupId}/actions`, action);
    return response.data;
  },

  getActionById: async (actionId: number) => {
    const response = await api.get(`/groups/actions/${actionId}`);
    return response.data;
  },

  updateAction: async (actionId: number, action: Partial<GroupAction>) => {
    const response = await api.put(`/groups/actions/${actionId}`, action);
    return response.data;
  },

  deleteAction: async (actionId: number) => {
    const response = await api.delete(`/groups/actions/${actionId}`);
    return response.data;
  },

  getUserActions: async (
    userId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/user/${userId}/actions`, { params });
    return response.data;
  },

  // Invitation operations
  getGroupInvitations: async (
    groupId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/invitations`, { params });
    return response.data;
  },

  createInvitation: async (
    groupId: number,
    data: { email: string; userId?: number; message?: string }
  ) => {
    const response = await api.post(`/groups/${groupId}/invitations`, data);
    return response.data;
  },

  getInvitationById: async (invitationId: number) => {
    const response = await api.get(`/groups/invitations/${invitationId}`);
    return response.data;
  },

  getInvitationByCode: async (code: string) => {
    const response = await api.get(`/groups/invitations/code/${code}`);
    return response.data;
  },

  acceptInvitation: async (code: string) => {
    const response = await api.post(`/groups/invitations/code/${code}/accept`);
    return response.data;
  },

  declineInvitation: async (code: string) => {
    const response = await api.post(`/groups/invitations/code/${code}/decline`);
    return response.data;
  },

  cancelInvitation: async (invitationId: number) => {
    const response = await api.delete(`/groups/invitations/${invitationId}`);
    return response.data;
  },

  getUserInvitations: async (
    userId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/user/${userId}/invitations`, { params });
    return response.data;
  },

  // Join request operations
  getGroupJoinRequests: async (
    groupId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/${groupId}/join-requests`, { params });
    return response.data;
  },

  createJoinRequest: async (
    groupId: number,
    data: { message?: string; code?: string }
  ) => {
    const response = await api.post(`/groups/${groupId}/join-requests`, data);
    return response.data;
  },

  getJoinRequestById: async (requestId: number) => {
    const response = await api.get(`/groups/join-requests/${requestId}`);
    return response.data;
  },

  approveJoinRequest: async (requestId: number) => {
    const response = await api.post(`/groups/join-requests/${requestId}/approve`);
    return response.data;
  },

  rejectJoinRequest: async (requestId: number) => {
    const response = await api.post(`/groups/join-requests/${requestId}/reject`);
    return response.data;
  },

  cancelJoinRequest: async (requestId: number) => {
    const response = await api.delete(`/groups/join-requests/${requestId}`);
    return response.data;
  },

  getUserJoinRequests: async (
    userId: number,
    params?: { status?: string; page?: number; pageSize?: number }
  ) => {
    const response = await api.get(`/groups/user/${userId}/join-requests`, { params });
    return response.data;
  },
};

export default groupsService;
