package gateway

import (
	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/middleware"
)

// SetupRoutes configures all routes for the API Gateway
func SetupRoutes(router *gin.Engine) {
	// API routes
	api := router.Group("/api")
	{
		// Health check endpoint
		api.GET("/health", HealthCheck)

		// Authentication routes
		api.POST("/auth/register", ForwardToAuthService)
		api.POST("/auth/login", ForwardToAuthService)
		api.POST("/auth/logout", ForwardToAuthService)
		api.POST("/auth/refresh-token", ForwardToAuthService)
		api.POST("/auth/reset-password", ForwardToAuthService)
		api.POST("/auth/confirm-reset", ForwardToAuthService)
		api.POST("/auth/verify-email", ForwardToAuthService)

		// Content routes
		api.GET("/books", ForwardToContentService)
		api.GET("/books/:id", ForwardToContentService)
		api.GET("/books/:id/chapters", ForwardToContentService)
		api.GET("/books/:id/chapters/:chapterId", ForwardToContentService)
		api.GET("/books/:id/chapters/:chapterId/sections/:sectionId", ForwardToContentService)
		api.GET("/books/search", ForwardToContentService)

		// Discussion routes
		api.GET("/forum/categories", ForwardToDiscussionService)
		api.GET("/forum/topics", ForwardToDiscussionService)
		api.GET("/forum/topics/:id", ForwardToDiscussionService)
		api.GET("/forum/topics/:id/comments", ForwardToDiscussionService)

		// Celebrate routes
		api.GET("/celebrate", ForwardToCelebrateService)
		api.GET("/celebrate/:id", ForwardToCelebrateService)
		api.GET("/celebrate/search", ForwardToCelebrateService)

		// Resources routes
		api.GET("/resources/categories", ForwardToResourcesService)
		api.GET("/resources", ForwardToResourcesService)
		api.GET("/resources/:id", ForwardToResourcesService)

		// Live Streaming routes
		api.GET("/livestream/active", ForwardToLiveStreamService)
		api.GET("/livestream/:id", ForwardToLiveStreamService)
		api.GET("/livestream/search", ForwardToLiveStreamService)

		// Virtual Currency routes
		api.GET("/currency/packages", ForwardToLiveStreamService)
		api.GET("/currency/balance/:userId", ForwardToLiveStreamService)

		// WebSocket endpoint
		api.GET("/ws", ForwardToLiveStreamService)

		// Progress routes
		api.GET("/progress/user", ForwardToProgressService)
		api.GET("/progress/milestones", ForwardToProgressService)
		api.GET("/progress/achievements", ForwardToProgressService)
		api.GET("/progress/history", ForwardToProgressService)
		api.GET("/progress/skills", ForwardToProgressService)

		// Tips routes
		api.GET("/tips", ForwardToTipsService)
		api.GET("/tips/:id", ForwardToTipsService)
		api.GET("/tips/category/:category", ForwardToTipsService)
		api.POST("/tips/contextual", ForwardToTipsService)

		// Personalization routes
		api.GET("/personalization/assessment/questions", ForwardToPersonalizationService)

		// Course routes
		api.GET("/courses", ForwardToCoursesService)
		api.GET("/courses/:id", ForwardToCoursesService)
		api.GET("/courses/slug/:slug", ForwardToCoursesService)
		api.GET("/courses/:id/modules", ForwardToCoursesService)
		api.GET("/courses/modules/:id", ForwardToCoursesService)
		api.GET("/courses/modules/:id/lessons", ForwardToCoursesService)
		api.GET("/courses/lessons/:id", ForwardToCoursesService)
		api.GET("/courses/lessons/:id/quiz", ForwardToCoursesService)
		api.GET("/courses/quizzes/:id", ForwardToCoursesService)
		api.GET("/courses/quizzes/:id/questions", ForwardToCoursesService)
		api.GET("/courses/questions/:id", ForwardToCoursesService)
		api.GET("/courses/lessons/:id/assignment", ForwardToCoursesService)
		api.GET("/courses/assignments/:id", ForwardToCoursesService)
		api.GET("/courses/reviews/course/:courseId", ForwardToCoursesService)

		// Tutorial routes
		api.GET("/tutorials", ForwardToTutorialsService)
		api.GET("/tutorials/:id", ForwardToTutorialsService)
		api.GET("/tutorials/slug/:slug", ForwardToTutorialsService)
		api.GET("/tutorials/:id/steps", ForwardToTutorialsService)
		api.GET("/tutorials/steps/:id", ForwardToTutorialsService)
		api.GET("/tutorials/categories", ForwardToTutorialsService)
		api.GET("/tutorials/categories/:id", ForwardToTutorialsService)
		api.GET("/tutorials/:id/ratings", ForwardToTutorialsService)

		// Quiz routes
		api.GET("/quizzes", ForwardToQuizzesService)
		api.GET("/quizzes/:id", ForwardToQuizzesService)
		api.GET("/quizzes/category/:categoryId", ForwardToQuizzesService)
		api.GET("/quizzes/course/:courseId", ForwardToQuizzesService)
		api.GET("/quizzes/lesson/:lessonId", ForwardToQuizzesService)
		api.GET("/quizzes/tutorial/:tutorialId", ForwardToQuizzesService)
		api.GET("/quizzes/:id/questions", ForwardToQuizzesService)
		api.GET("/questions/:id", ForwardToQuizzesService)

		// Impact routes
		api.GET("/impact/metrics", ForwardToImpactService)
		api.GET("/impact/metrics/:id", ForwardToImpactService)
		api.GET("/impact/categories", ForwardToImpactService)
		api.GET("/impact/categories/:id", ForwardToImpactService)
		api.GET("/impact/reports", ForwardToImpactService)
		api.GET("/impact/reports/:id", ForwardToImpactService)

		// Rewards routes
		api.GET("/rewards/achievements", ForwardToRewardsService)
		api.GET("/rewards/rewards", ForwardToRewardsService)
		api.GET("/rewards/tiers", ForwardToRewardsService)
		api.GET("/rewards/leaderboard", ForwardToRewardsService)

		// Skills routes
		api.GET("/skills", ForwardToSkillsService)
		api.GET("/skills/:id", ForwardToSkillsService)
		api.GET("/skills/categories", ForwardToSkillsService)
		api.GET("/skills/assessments", ForwardToSkillsService)
		api.GET("/skills/needs", ForwardToSkillsService)
		api.GET("/skills/needs/:id", ForwardToSkillsService)

		// Search routes
		api.POST("/search", ForwardToSearchService)
		api.GET("/search/popular", ForwardToSearchService)

		// Protected routes - require authentication
		authorized := api.Group("")
		authorized.Use(middleware.AuthMiddleware())
		{
			// User routes
			authorized.GET("/auth/me", ForwardToAuthService)
			authorized.PUT("/auth/me", ForwardToAuthService)
			authorized.GET("/auth/sessions", ForwardToAuthService)
			authorized.DELETE("/auth/sessions/:id", ForwardToAuthService)

			// Content interaction routes
			authorized.POST("/books/:id/progress", ForwardToContentService)
			authorized.GET("/books/bookmarks", ForwardToContentService)
			authorized.POST("/books/:id/bookmarks", ForwardToContentService)
			authorized.DELETE("/books/:id/bookmarks/:bookmarkId", ForwardToContentService)
			authorized.GET("/books/notes", ForwardToContentService)
			authorized.POST("/books/:id/notes", ForwardToContentService)
			authorized.PUT("/books/:id/notes/:noteId", ForwardToContentService)
			authorized.DELETE("/books/:id/notes/:noteId", ForwardToContentService)

			// Discussion interaction routes
			authorized.POST("/forum/topics", ForwardToDiscussionService)
			authorized.PUT("/forum/topics/:id", ForwardToDiscussionService)
			authorized.DELETE("/forum/topics/:id", ForwardToDiscussionService)
			authorized.POST("/forum/topics/:id/comments", ForwardToDiscussionService)
			authorized.PUT("/forum/topics/:id/comments/:commentId", ForwardToDiscussionService)
			authorized.DELETE("/forum/topics/:id/comments/:commentId", ForwardToDiscussionService)
			authorized.POST("/forum/topics/:id/reactions", ForwardToDiscussionService)
			authorized.DELETE("/forum/topics/:id/reactions", ForwardToDiscussionService)

			// Celebrate interaction routes
			authorized.POST("/celebrate", ForwardToCelebrateService)
			authorized.PUT("/celebrate/:id", ForwardToCelebrateService)
			authorized.DELETE("/celebrate/:id", ForwardToCelebrateService)
			authorized.POST("/celebrate/:id/vote", ForwardToCelebrateService)

			// Points routes
			authorized.GET("/points/balance", ForwardToPointsService)
			authorized.GET("/points/history", ForwardToPointsService)
			authorized.GET("/points/leaderboard", ForwardToPointsService)
			authorized.GET("/points/achievements", ForwardToPointsService)

			// Payment routes
			authorized.POST("/payments/create", ForwardToPaymentService)
			authorized.GET("/payments/history", ForwardToPaymentService)
			authorized.GET("/payments/subscriptions", ForwardToPaymentService)
			authorized.POST("/payments/verify", ForwardToPaymentService)

			// Live Streaming routes
			authorized.POST("/livestream", ForwardToLiveStreamService)
			authorized.PUT("/livestream/:id", ForwardToLiveStreamService)
			authorized.DELETE("/livestream/:id", ForwardToLiveStreamService)
			authorized.POST("/livestream/:id/start", ForwardToLiveStreamService)
			authorized.POST("/livestream/:id/end", ForwardToLiveStreamService)

			// Virtual Currency routes
			authorized.POST("/currency/purchase", ForwardToLiveStreamService)
			authorized.GET("/currency/transactions/:userId", ForwardToLiveStreamService)

			// Gift routes
			authorized.POST("/gifts/send", ForwardToLiveStreamService)
			authorized.GET("/gifts/user/:userId/sent", ForwardToLiveStreamService)
			authorized.GET("/gifts/user/:userId/received", ForwardToLiveStreamService)

			// Ranking routes
			authorized.GET("/rankings/user/:userId", ForwardToLiveStreamService)

			// Revenue routes
			authorized.GET("/revenue/creator/:userId", ForwardToLiveStreamService)
			authorized.GET("/revenue/summary/:userId", ForwardToLiveStreamService)
			authorized.POST("/revenue/withdraw", ForwardToLiveStreamService)

			// Progress routes
			authorized.PUT("/progress/milestones/:id", ForwardToProgressService)
			authorized.PUT("/progress/achievements/:id", ForwardToProgressService)
			authorized.POST("/progress/activities", ForwardToProgressService)

			// Tips routes
			authorized.POST("/tips", ForwardToTipsService)
			authorized.PUT("/tips/:id", ForwardToTipsService)
			authorized.DELETE("/tips/:id", ForwardToTipsService)
			authorized.GET("/tips/rules/:tipID", ForwardToTipsService)
			authorized.POST("/tips/rules", ForwardToTipsService)
			authorized.PUT("/tips/rules/:id", ForwardToTipsService)
			authorized.DELETE("/tips/rules/:id", ForwardToTipsService)
			authorized.POST("/tips/view/:id", ForwardToTipsService)
			authorized.POST("/tips/dismiss/:id", ForwardToTipsService)
			authorized.POST("/tips/click/:id", ForwardToTipsService)
			authorized.POST("/tips/feedback", ForwardToTipsService)
			authorized.GET("/tips/statistics/:id", ForwardToTipsService)
			authorized.GET("/tips/statistics", ForwardToTipsService)

			// Personalization routes
			authorized.GET("/personalization/learning-style", ForwardToPersonalizationService)
			authorized.POST("/personalization/learning-style", ForwardToPersonalizationService)
			authorized.GET("/personalization/preferences", ForwardToPersonalizationService)
			authorized.POST("/personalization/preferences", ForwardToPersonalizationService)
			authorized.POST("/personalization/assessment/submit", ForwardToPersonalizationService)
			authorized.GET("/personalization/paths", ForwardToPersonalizationService)
			authorized.GET("/personalization/paths/:id", ForwardToPersonalizationService)
			authorized.POST("/personalization/paths", ForwardToPersonalizationService)
			authorized.PUT("/personalization/paths/:id", ForwardToPersonalizationService)
			authorized.DELETE("/personalization/paths/:id", ForwardToPersonalizationService)
			authorized.POST("/personalization/paths/:pathId/items", ForwardToPersonalizationService)
			authorized.PUT("/personalization/paths/items/:id", ForwardToPersonalizationService)
			authorized.DELETE("/personalization/paths/items/:id", ForwardToPersonalizationService)
			authorized.PUT("/personalization/paths/items/:id/complete", ForwardToPersonalizationService)
			authorized.POST("/personalization/recommendations", ForwardToPersonalizationService)
			authorized.PUT("/personalization/recommendations/:id/status", ForwardToPersonalizationService)
			authorized.GET("/personalization/performance", ForwardToPersonalizationService)
			authorized.PUT("/personalization/performance", ForwardToPersonalizationService)

			// Course routes
			authorized.POST("/courses", ForwardToCoursesService)
			authorized.PUT("/courses/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/:id", ForwardToCoursesService)
			authorized.POST("/courses/:id/modules", ForwardToCoursesService)
			authorized.PUT("/courses/modules/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/modules/:id", ForwardToCoursesService)
			authorized.POST("/courses/modules/:id/lessons", ForwardToCoursesService)
			authorized.PUT("/courses/lessons/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/lessons/:id", ForwardToCoursesService)
			authorized.POST("/courses/lessons/:id/quiz", ForwardToCoursesService)
			authorized.PUT("/courses/quizzes/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/quizzes/:id", ForwardToCoursesService)
			authorized.POST("/courses/quizzes/:id/questions", ForwardToCoursesService)
			authorized.PUT("/courses/questions/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/questions/:id", ForwardToCoursesService)
			authorized.POST("/courses/lessons/:id/assignment", ForwardToCoursesService)
			authorized.PUT("/courses/assignments/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/assignments/:id", ForwardToCoursesService)
			authorized.GET("/courses/enrollments/user/:userId", ForwardToCoursesService)
			authorized.GET("/courses/enrollments/course/:courseId", ForwardToCoursesService)
			authorized.GET("/courses/enrollments/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.POST("/courses/enrollments", ForwardToCoursesService)
			authorized.PUT("/courses/enrollments/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/enrollments/:id", ForwardToCoursesService)
			authorized.GET("/courses/progress/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.GET("/courses/progress/user/:userId/lesson/:lessonId", ForwardToCoursesService)
			authorized.POST("/courses/progress", ForwardToCoursesService)
			authorized.PUT("/courses/progress/:id", ForwardToCoursesService)
			authorized.GET("/courses/quiz-attempts/user/:userId/quiz/:quizId", ForwardToCoursesService)
			authorized.GET("/courses/quiz-attempts/:id", ForwardToCoursesService)
			authorized.POST("/courses/quiz-attempts", ForwardToCoursesService)
			authorized.PUT("/courses/quiz-attempts/:id", ForwardToCoursesService)
			authorized.GET("/courses/submissions/user/:userId/assignment/:assignmentId", ForwardToCoursesService)
			authorized.GET("/courses/submissions/:id", ForwardToCoursesService)
			authorized.POST("/courses/submissions", ForwardToCoursesService)
			authorized.PUT("/courses/submissions/:id", ForwardToCoursesService)
			authorized.GET("/courses/reviews/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.POST("/courses/reviews", ForwardToCoursesService)
			authorized.PUT("/courses/reviews/:id", ForwardToCoursesService)
			authorized.DELETE("/courses/reviews/:id", ForwardToCoursesService)
			authorized.GET("/courses/certificates/user/:userId", ForwardToCoursesService)
			authorized.GET("/courses/certificates/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.POST("/courses/certificates", ForwardToCoursesService)
			authorized.GET("/courses/completion/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.POST("/courses/completion/user/:userId/course/:courseId", ForwardToCoursesService)
			authorized.POST("/courses/certificates/generate/user/:userId/course/:courseId", ForwardToCoursesService)

			// Tutorial routes
			authorized.GET("/tutorials/user/:userId", ForwardToTutorialsService)
			authorized.POST("/tutorials", ForwardToTutorialsService)
			authorized.PUT("/tutorials/:id", ForwardToTutorialsService)
			authorized.DELETE("/tutorials/:id", ForwardToTutorialsService)
			authorized.POST("/tutorials/:id/steps", ForwardToTutorialsService)
			authorized.PUT("/tutorials/steps/:id", ForwardToTutorialsService)
			authorized.DELETE("/tutorials/steps/:id", ForwardToTutorialsService)
			authorized.POST("/tutorials/categories", ForwardToTutorialsService)
			authorized.PUT("/tutorials/categories/:id", ForwardToTutorialsService)
			authorized.DELETE("/tutorials/categories/:id", ForwardToTutorialsService)
			authorized.POST("/tutorials/ratings", ForwardToTutorialsService)
			authorized.GET("/tutorials/ratings/user/:userId/tutorial/:tutorialId", ForwardToTutorialsService)
			authorized.PUT("/tutorials/ratings/:id", ForwardToTutorialsService)
			authorized.DELETE("/tutorials/ratings/:id", ForwardToTutorialsService)
			authorized.GET("/tutorials/progress/user/:userId/tutorial/:tutorialId", ForwardToTutorialsService)
			authorized.POST("/tutorials/progress/complete-step", ForwardToTutorialsService)
			authorized.DELETE("/tutorials/progress/user/:userId/tutorial/:tutorialId", ForwardToTutorialsService)

			// Quiz routes
			authorized.GET("/quizzes/user/:userId/created", ForwardToQuizzesService)
			authorized.GET("/quizzes/user/:userId/attempted", ForwardToQuizzesService)
			authorized.POST("/quizzes", ForwardToQuizzesService)
			authorized.PUT("/quizzes/:id", ForwardToQuizzesService)
			authorized.DELETE("/quizzes/:id", ForwardToQuizzesService)
			authorized.PUT("/quizzes/:id/publish", ForwardToQuizzesService)
			authorized.PUT("/quizzes/:id/unpublish", ForwardToQuizzesService)
			authorized.POST("/quizzes/:id/questions", ForwardToQuizzesService)
			authorized.PUT("/questions/:id", ForwardToQuizzesService)
			authorized.DELETE("/questions/:id", ForwardToQuizzesService)
			authorized.PUT("/quizzes/:id/questions/reorder", ForwardToQuizzesService)
			authorized.POST("/quiz-attempts", ForwardToQuizzesService)
			authorized.PUT("/quiz-attempts/:id/submit", ForwardToQuizzesService)
			authorized.GET("/quiz-attempts/:id", ForwardToQuizzesService)
			authorized.GET("/quiz-attempts/user/:userId/quiz/:quizId", ForwardToQuizzesService)
			authorized.GET("/quizzes/:id/analytics", ForwardToQuizzesService)
			authorized.GET("/questions/:id/analytics", ForwardToQuizzesService)
			authorized.GET("/users/:userId/quiz-analytics", ForwardToQuizzesService)

			// Impact routes
			authorized.GET("/impact/metrics/user/:userId", ForwardToImpactService)
			authorized.POST("/impact/metrics", ForwardToImpactService)
			authorized.PUT("/impact/metrics/:id", ForwardToImpactService)
			authorized.DELETE("/impact/metrics/:id", ForwardToImpactService)
			authorized.GET("/impact/metrics/:id/data-points", ForwardToImpactService)
			authorized.POST("/impact/metrics/:id/data-points", ForwardToImpactService)
			authorized.PUT("/impact/data-points/:id", ForwardToImpactService)
			authorized.DELETE("/impact/data-points/:id", ForwardToImpactService)
			authorized.GET("/impact/reports/user/:userId", ForwardToImpactService)
			authorized.POST("/impact/reports", ForwardToImpactService)
			authorized.PUT("/impact/reports/:id", ForwardToImpactService)
			authorized.DELETE("/impact/reports/:id", ForwardToImpactService)
			authorized.PUT("/impact/reports/:id/publish", ForwardToImpactService)
			authorized.PUT("/impact/reports/:id/archive", ForwardToImpactService)
			authorized.POST("/impact/categories", ForwardToImpactService)
			authorized.PUT("/impact/categories/:id", ForwardToImpactService)
			authorized.DELETE("/impact/categories/:id", ForwardToImpactService)
			authorized.GET("/impact/dashboard/user/:userId", ForwardToImpactService)
			authorized.PUT("/impact/dashboard/user/:userId", ForwardToImpactService)
			authorized.GET("/impact/analytics/metrics/:id", ForwardToImpactService)
			authorized.GET("/impact/analytics/categories/:id", ForwardToImpactService)
			authorized.GET("/impact/analytics/users/:userId", ForwardToImpactService)
			authorized.GET("/impact/export/metrics/:id", ForwardToImpactService)
			authorized.GET("/impact/export/reports/:id", ForwardToImpactService)

			// Rewards routes
			authorized.GET("/rewards/users/:userId/summary", ForwardToRewardsService)
			authorized.GET("/rewards/users/:userId/points", ForwardToRewardsService)
			authorized.GET("/rewards/users/:userId/activities", ForwardToRewardsService)
			authorized.POST("/rewards/activities", ForwardToRewardsService)
			authorized.GET("/rewards/users/:userId/achievements", ForwardToRewardsService)
			authorized.GET("/rewards/users/:userId/redemptions", ForwardToRewardsService)
			authorized.POST("/rewards/users/:userId/redeem", ForwardToRewardsService)

			// Rewards admin routes
			authorized.POST("/rewards/admin/rewards", ForwardToRewardsService)
			authorized.PUT("/rewards/admin/rewards/:id", ForwardToRewardsService)
			authorized.DELETE("/rewards/admin/rewards/:id", ForwardToRewardsService)
			authorized.POST("/rewards/admin/achievements", ForwardToRewardsService)
			authorized.PUT("/rewards/admin/achievements/:id", ForwardToRewardsService)
			authorized.DELETE("/rewards/admin/achievements/:id", ForwardToRewardsService)
			authorized.GET("/rewards/admin/rules", ForwardToRewardsService)
			authorized.GET("/rewards/admin/rules/:id", ForwardToRewardsService)
			authorized.POST("/rewards/admin/rules", ForwardToRewardsService)
			authorized.PUT("/rewards/admin/rules/:id", ForwardToRewardsService)
			authorized.DELETE("/rewards/admin/rules/:id", ForwardToRewardsService)
			authorized.POST("/rewards/admin/tiers", ForwardToRewardsService)
			authorized.PUT("/rewards/admin/tiers/:id", ForwardToRewardsService)
			authorized.DELETE("/rewards/admin/tiers/:id", ForwardToRewardsService)
			authorized.GET("/rewards/admin/redemptions", ForwardToRewardsService)
			authorized.PUT("/rewards/admin/redemptions/:id", ForwardToRewardsService)
			authorized.POST("/rewards/admin/users/:userId/adjust-points", ForwardToRewardsService)

			// Skills routes
			authorized.GET("/skills/users/:userId", ForwardToSkillsService)
			authorized.POST("/skills", ForwardToSkillsService)
			authorized.PUT("/skills/:id", ForwardToSkillsService)
			authorized.DELETE("/skills/:id", ForwardToSkillsService)
			authorized.POST("/skills/users/:userId", ForwardToSkillsService)
			authorized.PUT("/skills/users/:userId/skills/:skillId", ForwardToSkillsService)
			authorized.DELETE("/skills/users/:userId/skills/:skillId", ForwardToSkillsService)
			authorized.GET("/skills/endorsements/:userSkillId", ForwardToSkillsService)
			authorized.POST("/skills/endorsements", ForwardToSkillsService)
			authorized.GET("/skills/assessments/:id", ForwardToSkillsService)
			authorized.POST("/skills/assessments", ForwardToSkillsService)
			authorized.PUT("/skills/assessments/:id", ForwardToSkillsService)
			authorized.DELETE("/skills/assessments/:id", ForwardToSkillsService)
			authorized.POST("/skills/assessment-attempts", ForwardToSkillsService)
			authorized.PUT("/skills/assessment-attempts/:id/submit", ForwardToSkillsService)
			authorized.GET("/skills/assessment-attempts/user/:userId", ForwardToSkillsService)
			authorized.GET("/skills/needs/user/:userId", ForwardToSkillsService)
			authorized.POST("/skills/needs", ForwardToSkillsService)
			authorized.PUT("/skills/needs/:id", ForwardToSkillsService)
			authorized.DELETE("/skills/needs/:id", ForwardToSkillsService)
			authorized.GET("/skills/matches/need/:needId", ForwardToSkillsService)
			authorized.GET("/skills/matches/user/:userId", ForwardToSkillsService)
			authorized.PUT("/skills/matches/:id/status", ForwardToSkillsService)
			authorized.POST("/skills/matches/find", ForwardToSkillsService)
			authorized.POST("/skills/categories", ForwardToSkillsService)
			authorized.PUT("/skills/categories/:id", ForwardToSkillsService)
			authorized.DELETE("/skills/categories/:id", ForwardToSkillsService)

			// Search routes
			authorized.GET("/search/recent", ForwardToSearchService)
			authorized.POST("/search/recent", ForwardToSearchService)
			authorized.DELETE("/search/recent", ForwardToSearchService)

			// Admin routes
			admin := authorized.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				// User management
				admin.GET("/users", ForwardToAuthService)
				admin.GET("/users/:id", ForwardToAuthService)
				admin.PUT("/users/:id", ForwardToAuthService)
				admin.DELETE("/users/:id", ForwardToAuthService)

				// Content management
				admin.POST("/books", ForwardToContentService)
				admin.PUT("/books/:id", ForwardToContentService)
				admin.DELETE("/books/:id", ForwardToContentService)
				admin.POST("/books/:id/chapters", ForwardToContentService)
				admin.PUT("/books/:id/chapters/:chapterId", ForwardToContentService)
				admin.DELETE("/books/:id/chapters/:chapterId", ForwardToContentService)

				// Discussion management
				admin.POST("/forum/categories", ForwardToDiscussionService)
				admin.PUT("/forum/categories/:id", ForwardToDiscussionService)
				admin.DELETE("/forum/categories/:id", ForwardToDiscussionService)
				admin.POST("/forum/topics/:id/pin", ForwardToDiscussionService)
				admin.POST("/forum/topics/:id/lock", ForwardToDiscussionService)

				// Payment management
				admin.GET("/payments/all", ForwardToPaymentService)
				admin.PUT("/payments/:id", ForwardToPaymentService)

				// Live Streaming management
				admin.GET("/livestream/all", ForwardToLiveStreamService)
				admin.POST("/livestream/:id/feature", ForwardToLiveStreamService)

				// Virtual Currency management
				admin.POST("/currency/packages", ForwardToLiveStreamService)
				admin.PUT("/currency/packages/:id", ForwardToLiveStreamService)
				admin.DELETE("/currency/packages/:id", ForwardToLiveStreamService)

				// Revenue management
				admin.GET("/revenue/withdrawals", ForwardToLiveStreamService)
				admin.PUT("/revenue/withdrawals/:id", ForwardToLiveStreamService)

				// Personalization management
				admin.GET("/personalization/templates", ForwardToPersonalizationService)
				admin.GET("/personalization/templates/:id", ForwardToPersonalizationService)
			}
		}
	}
}

// HealthCheck handles the health check endpoint
func HealthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status": "ok",
	})
}

// ForwardToAuthService forwards requests to the Auth Service
func ForwardToAuthService(c *gin.Context) {
	// Implementation would proxy the request to the Auth Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Auth Service",
	})
}

// ForwardToContentService forwards requests to the Content Service
func ForwardToContentService(c *gin.Context) {
	// Implementation would proxy the request to the Content Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Content Service",
	})
}

// ForwardToDiscussionService forwards requests to the Discussion Service
func ForwardToDiscussionService(c *gin.Context) {
	// Implementation would proxy the request to the Discussion Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Discussion Service",
	})
}

// ForwardToCelebrateService forwards requests to the Celebrate Service
func ForwardToCelebrateService(c *gin.Context) {
	// Implementation would proxy the request to the Celebrate Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Celebrate Service",
	})
}

// ForwardToResourcesService forwards requests to the Resources Service
func ForwardToResourcesService(c *gin.Context) {
	// Implementation would proxy the request to the Resources Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Resources Service",
	})
}

// ForwardToPointsService forwards requests to the Points Service
func ForwardToPointsService(c *gin.Context) {
	// Implementation would proxy the request to the Points Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Points Service",
	})
}

// ForwardToPaymentService forwards requests to the Payment Service
func ForwardToPaymentService(c *gin.Context) {
	// Implementation would proxy the request to the Payment Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Payment Service",
	})
}

// ForwardToLiveStreamService forwards requests to the Live Stream Service
func ForwardToLiveStreamService(c *gin.Context) {
	// Implementation would proxy the request to the Live Stream Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Live Stream Service",
	})
}

// ForwardToProgressService forwards requests to the Progress Service
func ForwardToProgressService(c *gin.Context) {
	// Implementation would proxy the request to the Progress Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Progress Service",
	})
}

// ForwardToTipsService forwards requests to the Tips Service
func ForwardToTipsService(c *gin.Context) {
	// Implementation would proxy the request to the Tips Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Tips Service",
	})
}

// ForwardToPersonalizationService forwards requests to the Personalization Service
func ForwardToPersonalizationService(c *gin.Context) {
	// Implementation would proxy the request to the Personalization Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Personalization Service",
	})
}

// ForwardToSearchService forwards requests to the Search Service
func ForwardToSearchService(c *gin.Context) {
	// Implementation would proxy the request to the Search Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Search Service",
	})
}

// ForwardToCoursesService forwards requests to the Courses Service
func ForwardToCoursesService(c *gin.Context) {
	// Implementation would proxy the request to the Courses Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Courses Service",
	})
}

// ForwardToTutorialsService forwards requests to the Tutorials Service
func ForwardToTutorialsService(c *gin.Context) {
	// Implementation would proxy the request to the Tutorials Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Tutorials Service",
	})
}

// ForwardToQuizzesService forwards requests to the Quizzes Service
func ForwardToQuizzesService(c *gin.Context) {
	// Implementation would proxy the request to the Quizzes Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Quizzes Service",
	})
}

// ForwardToImpactService forwards requests to the Impact Service
func ForwardToImpactService(c *gin.Context) {
	// Implementation would proxy the request to the Impact Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Impact Service",
	})
}

// ForwardToRewardsService forwards requests to the Rewards Service
func ForwardToRewardsService(c *gin.Context) {
	// Implementation would proxy the request to the Rewards Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Rewards Service",
	})
}

// ForwardToSkillsService forwards requests to the Skills Service
func ForwardToSkillsService(c *gin.Context) {
	// Implementation would proxy the request to the Skills Service
	// For now, we'll just return a placeholder response
	c.JSON(200, gin.H{
		"message": "Request would be forwarded to Skills Service",
	})
}
