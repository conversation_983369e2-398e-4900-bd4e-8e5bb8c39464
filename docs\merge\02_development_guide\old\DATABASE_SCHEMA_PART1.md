# Great Nigeria Platform - Database Schema (Part 1)

## Overview

This document outlines the database schema for the Great Nigeria platform using PostgreSQL with GORM as the ORM. The database is designed to support all aspects of the platform, including content management, user interactions, and payment processing.

## Table of Contents

1. [Overview](#overview)
2. [Entity Relationship Diagram](#entity-relationship-diagram)
3. [Core Tables](#core-tables)
   - [Users](#users)
   - [Books](#books)
   - [Chapters](#chapters)
   - [Sections](#sections)
4. [User Progress and Engagement](#user-progress-and-engagement)
   - [UserProgress](#userprogress)
   - [Bookmarks](#bookmarks)
5. [Discussion System](#discussion-system)
   - [Discussions](#discussions)
   - [Comments](#comments)
   - [UserLikes](#userlikes)
6. [Points and Rewards System](#points-and-rewards-system)
   - [UserActivities](#useractivities)
   - [TopicCompletions](#topiccompletions)
   - [MembershipLevels](#membershiplevels)
7. [Payment System](#payment-system)
   - [Purchases](#purchases)
   - [Plans](#plans)
   - [Subscriptions](#subscriptions)
8. [Citation System](#citation-system)
9. [Database Management](#database-management)
10. [Backup and Restoration](#backup-and-restoration)

## Entity Relationship Diagram

```
+-------------+     +-------------+     +-------------+
|    Users    |<--->| UserProgress|<--->|    Books    |
+-------------+     +-------------+     +-------------+
      ^  ^                                  ^
      |  |                                  |
      |  v                                  v
+-------------+     +-------------+     +-------------+
|   Comments  |<--->| Discussions |<--->|  Bookmarks  |
+-------------+     +-------------+     +-------------+
      ^                   ^
      |                   |
      v                   v
+-------------+     +-------------+
|UserActivities|     | UserLikes  |
+-------------+     +-------------+
      ^
      |
      v
+-------------+     +-------------+
|TopicCompletion|    | Purchases  |
+-------------+     +-------------+
```

## Core Tables

### Users

The Users table stores all user account information, including authentication details, profile information, and membership status.

```go
type User struct {
    ID              uint `gorm:"primaryKey"`
    Username        string `gorm:"size:50;not null;unique"`
    Email           string `gorm:"size:100;not null;unique"`
    Password        string `gorm:"size:255;not null"`
    FullName        string `gorm:"size:100"`
    Bio             string `gorm:"type:text"`
    ProfileImage    string `gorm:"size:255"`
    MembershipLevel int `gorm:"default:1"` // 1=Basic, 2=Engaged, 3=Active, 4=Premium
    PointsBalance   int `gorm:"default:0"`
    IsActive        bool `gorm:"default:true"`
    IsAdmin         bool `gorm:"default:false"`
    LastLogin       time.Time
    CreatedAt       time.Time
    UpdatedAt       time.Time
    DeletedAt       gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    Progress        []UserProgress `gorm:"foreignKey:UserID"`
    Bookmarks       []Bookmark `gorm:"foreignKey:UserID"`
    Discussions     []Discussion `gorm:"foreignKey:UserID"`
    Comments        []Comment `gorm:"foreignKey:UserID"`
    Activities      []UserActivity `gorm:"foreignKey:UserID"`
    Likes           []UserLike `gorm:"foreignKey:UserID"`
    Completions     []TopicCompletion `gorm:"foreignKey:UserID"`
    Purchases       []Purchase `gorm:"foreignKey:UserID"`
}
```

**Key Features:**
- Unique username and email for identification
- Secure password storage (hashed)
- Profile information (full name, bio, profile image)
- Membership level tracking
- Points balance for the rewards system
- Soft delete support via DeletedAt

### Books

The Books table stores information about the books available on the platform, including metadata and access control settings.

```go
type Book struct {
    ID          uint `gorm:"primaryKey"`
    Title       string `gorm:"size:255;not null"`
    Description string `gorm:"type:text"`
    Author      string `gorm:"size:100"`
    CoverImage  string `gorm:"size:255"`
    AccessLevel int `gorm:"default:1"` // 1=Free, 2=Points, 3=Premium
    PointsRequired int `gorm:"default:0"`
    PublishedAt time.Time
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    Chapters    []Chapter `gorm:"foreignKey:BookID"`
    Bookmarks   []Bookmark `gorm:"foreignKey:BookID"`
}
```

**Key Features:**
- Book metadata (title, description, author)
- Cover image URL
- Access level control (free, points-based, premium)
- Points required for access (if applicable)
- Publication tracking
- Relationship to chapters

### Chapters

The Chapters table organizes books into chapters, providing a hierarchical structure for content.

```go
type Chapter struct {
    ID          uint `gorm:"primaryKey"`
    BookID      uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    OrderIndex  int `gorm:"not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Book        Book
    Sections    []Section `gorm:"foreignKey:ChapterID"`
}
```

**Key Features:**
- Association with a specific book
- Title for the chapter
- Order index for proper sequencing
- Relationship to sections

### Sections

The Sections table contains the actual content of the books, organized within chapters.

```go
type Section struct {
    ID          uint `gorm:"primaryKey"`
    ChapterID   uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    Content     string `gorm:"type:text"`
    OrderIndex  int `gorm:"not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Chapter     Chapter
}
```

**Key Features:**
- Association with a specific chapter
- Title for the section
- Content storage (text/HTML)
- Order index for proper sequencing

## User Progress and Engagement

### UserProgress

The UserProgress table tracks a user's progress through the books, including their last position and completion percentage.

```go
type UserProgress struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    LastPosition int
    PercentComplete float64 `gorm:"default:0"`
    LastUpdated time.Time
    CreatedAt   time.Time
    
    // Relationships
    User        User
    Book        Book
}
```

**Key Features:**
- Tracks which user is reading which book
- Records the specific chapter and section
- Stores the last position within a section
- Calculates overall completion percentage
- Timestamps for progress tracking

### Bookmarks

The Bookmarks table allows users to save specific locations within books for later reference.

```go
type Bookmark struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    Position    int
    Note        string `gorm:"type:text"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    User        User
    Book        Book
}
```

**Key Features:**
- Associates a bookmark with a specific user
- Records the book, chapter, and section
- Stores the exact position within a section
- Allows users to add notes to bookmarks
