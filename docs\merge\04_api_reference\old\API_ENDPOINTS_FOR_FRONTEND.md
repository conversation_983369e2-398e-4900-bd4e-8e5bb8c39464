# API Endpoints for Frontend Integration

This document outlines the API endpoints that the React frontend will need to interact with. It serves as a reference for frontend developers implementing the React TypeScript frontend.

## Authentication Endpoints

### Register User

- **URL**: `/api/auth/register`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "name": "User Name",
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
  ```

### Login User

- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
  ```

### Get Current User

- **URL**: `/api/auth/me`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>"
  }
  ```

## Book Endpoints

### Get All Books

- **URL**: `/api/books`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Book 1: Awakening the Giant",
      "description": "Book description",
      "cover_image": "/static/images/book1_cover.jpg",
      "author": "Author Name",
      "published_date": "2023-01-01"
    },
    {
      "id": "2",
      "title": "Book 2: Building the Future",
      "description": "Book description",
      "cover_image": "/static/images/book2_cover.jpg",
      "author": "Author Name",
      "published_date": "2023-02-01"
    }
  ]
  ```

### Get Book by ID

- **URL**: `/api/books/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Book 1: Awakening the Giant",
    "description": "Book description",
    "cover_image": "/static/images/book1_cover.jpg",
    "author": "Author Name",
    "published_date": "2023-01-01",
    "chapters_count": 10,
    "total_pages": 250
  }
  ```

### Get Book Chapters

- **URL**: `/api/books/:id/chapters`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Chapter 1: Introduction",
      "order": 1,
      "sections": [
        {
          "id": "1",
          "title": "Section 1.1",
          "order": 1
        },
        {
          "id": "2",
          "title": "Section 1.2",
          "order": 2
        }
      ]
    },
    {
      "id": "2",
      "title": "Chapter 2: Getting Started",
      "order": 2,
      "sections": [
        {
          "id": "3",
          "title": "Section 2.1",
          "order": 1
        },
        {
          "id": "4",
          "title": "Section 2.2",
          "order": 2
        }
      ]
    }
  ]
  ```

### Get Chapter by ID

- **URL**: `/api/books/chapters/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Chapter 1: Introduction",
    "order": 1,
    "book_id": "1",
    "sections": [
      {
        "id": "1",
        "title": "Section 1.1",
        "order": 1
      },
      {
        "id": "2",
        "title": "Section 1.2",
        "order": 2
      }
    ]
  }
  ```

### Get Section by ID

- **URL**: `/api/books/sections/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Section 1.1",
    "order": 1,
    "chapter_id": "1",
    "content": "<p>Section content in HTML format</p>",
    "next_section_id": "2",
    "prev_section_id": null
  }
  ```

### Save Reading Progress

- **URL**: `/api/books/:id/progress`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "sectionId": "1"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Reading progress saved"
  }
  ```

## User Profile Endpoints

### Get User Profile

- **URL**: `/api/users/:id/profile`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "bio": "User bio",
    "avatar": "/static/images/avatars/user_avatar.jpg",
    "createdAt": "2023-01-01T00:00:00Z",
    "points": 100,
    "level": 2
  }
  ```

### Get Reading Statistics

- **URL**: `/api/users/:id/reading-stats`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "books_started": 3,
    "books_completed": 1,
    "total_pages_read": 150,
    "reading_streak": 5,
    "average_reading_time": 30,
    "reading_history": [
      {
        "date": "2023-04-01",
        "pages_read": 10
      },
      {
        "date": "2023-04-02",
        "pages_read": 15
      }
    ]
  }
  ```

### Get User Bookmarks

- **URL**: `/api/users/:id/bookmarks`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "book_id": "1",
      "book_title": "Book 1: Awakening the Giant",
      "section_id": "1",
      "section_title": "Section 1.1",
      "created_at": "2023-04-01T00:00:00Z",
      "note": "Important section about leadership"
    },
    {
      "id": "2",
      "book_id": "2",
      "book_title": "Book 2: Building the Future",
      "section_id": "3",
      "section_title": "Section 2.1",
      "created_at": "2023-04-02T00:00:00Z",
      "note": "Key concepts about community building"
    }
  ]
  ```

### Get User Activities

- **URL**: `/api/users/:id/activities`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "reading",
      "description": "Started reading Book 1",
      "created_at": "2023-04-01T00:00:00Z",
      "book_id": "1",
      "book_title": "Book 1: Awakening the Giant"
    },
    {
      "id": "2",
      "type": "forum",
      "description": "Posted in forum topic: Community Building",
      "created_at": "2023-04-02T00:00:00Z",
      "topic_id": "1",
      "topic_title": "Community Building"
    }
  ]
  ```

## Forum Endpoints

### Get Forum Categories

- **URL**: `/api/forum/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "General Discussion",
      "description": "General discussion about Nigeria",
      "topics_count": 10
    },
    {
      "id": "2",
      "name": "Book Discussion",
      "description": "Discuss the books and their content",
      "topics_count": 5
    }
  ]
  ```

### Get Topics by Category

- **URL**: `/api/forum/categories/:id/topics`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Community Building",
      "author": {
        "id": "user_id",
        "name": "User Name"
      },
      "created_at": "2023-04-01T00:00:00Z",
      "replies_count": 5,
      "last_reply_at": "2023-04-02T00:00:00Z"
    },
    {
      "id": "2",
      "title": "Leadership in Nigeria",
      "author": {
        "id": "user_id",
        "name": "User Name"
      },
      "created_at": "2023-04-02T00:00:00Z",
      "replies_count": 3,
      "last_reply_at": "2023-04-03T00:00:00Z"
    }
  ]
  ```

### Get Topic by ID

- **URL**: `/api/forum/topics/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Community Building",
    "content": "<p>Topic content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-01T00:00:00Z",
    "category": {
      "id": "1",
      "name": "General Discussion"
    },
    "replies": [
      {
        "id": "1",
        "content": "<p>Reply content in HTML format</p>",
        "author": {
          "id": "user_id",
          "name": "User Name",
          "avatar": "/static/images/avatars/user_avatar.jpg"
        },
        "created_at": "2023-04-02T00:00:00Z",
        "votes": 5
      }
    ]
  }
  ```

### Create Topic

- **URL**: `/api/forum/topics`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "title": "New Topic",
    "content": "<p>Topic content in HTML format</p>",
    "category_id": "1"
  }
  ```
- **Response**:
  ```json
  {
    "id": "3",
    "title": "New Topic",
    "content": "<p>Topic content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-03T00:00:00Z",
    "category": {
      "id": "1",
      "name": "General Discussion"
    }
  }
  ```

### Create Reply

- **URL**: `/api/forum/topics/:id/replies`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "content": "<p>Reply content in HTML format</p>"
  }
  ```
- **Response**:
  ```json
  {
    "id": "2",
    "content": "<p>Reply content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-03T00:00:00Z",
    "votes": 0
  }
  ```

## Resources Endpoints

### Get Resource Categories

- **URL**: `/api/resources/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "Implementation Tools",
      "description": "Tools for implementing the ideas from the books",
      "resources_count": 5
    },
    {
      "id": "2",
      "name": "Templates",
      "description": "Templates for various documents and plans",
      "resources_count": 3
    }
  ]
  ```

### Get Resources by Category

- **URL**: `/api/resources/categories/:id/resources`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Community Building Toolkit",
      "description": "A toolkit for building communities",
      "file_type": "pdf",
      "file_size": 1024,
      "download_url": "/api/resources/1/download",
      "created_at": "2023-04-01T00:00:00Z",
      "downloads_count": 100
    },
    {
      "id": "2",
      "title": "Leadership Workshop Guide",
      "description": "A guide for conducting leadership workshops",
      "file_type": "pdf",
      "file_size": 2048,
      "download_url": "/api/resources/2/download",
      "created_at": "2023-04-02T00:00:00Z",
      "downloads_count": 50
    }
  ]
  ```

### Get Resource by ID

- **URL**: `/api/resources/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Community Building Toolkit",
    "description": "A toolkit for building communities",
    "content": "<p>Resource description in HTML format</p>",
    "file_type": "pdf",
    "file_size": 1024,
    "download_url": "/api/resources/1/download",
    "created_at": "2023-04-01T00:00:00Z",
    "downloads_count": 100,
    "category": {
      "id": "1",
      "name": "Implementation Tools"
    },
    "related_resources": [
      {
        "id": "2",
        "title": "Leadership Workshop Guide"
      }
    ]
  }
  ```

### Download Resource

- **URL**: `/api/resources/:id/download`
- **Method**: `GET`
- **Response**: Binary file download

## Celebrate Nigeria Endpoints

### Get Featured Entries

- **URL**: `/api/celebrate/featured`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "person",
      "name": "Chinua Achebe",
      "slug": "chinua-achebe",
      "image_url": "/static/images/celebrate/chinua-achebe.jpg",
      "summary": "Renowned Nigerian novelist, poet, and critic",
      "featured": true
    },
    {
      "id": "2",
      "type": "place",
      "name": "Zuma Rock",
      "slug": "zuma-rock",
      "image_url": "/static/images/celebrate/zuma-rock.jpg",
      "summary": "Monolith located in Niger State, Nigeria",
      "featured": true
    }
  ]
  ```

### Get Entry by Type and Slug

- **URL**: `/api/celebrate/:type/:slug`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "type": "person",
    "name": "Chinua Achebe",
    "slug": "chinua-achebe",
    "image_url": "/static/images/celebrate/chinua-achebe.jpg",
    "summary": "Renowned Nigerian novelist, poet, and critic",
    "description": "<p>Detailed description in HTML format</p>",
    "birth_date": "1930-11-16",
    "death_date": "2013-03-21",
    "achievements": [
      "Published 'Things Fall Apart' in 1958",
      "Won the Man Booker International Prize in 2007"
    ],
    "facts": [
      {
        "title": "Education",
        "content": "University College, Ibadan"
      },
      {
        "title": "Notable Works",
        "content": "Things Fall Apart, No Longer at Ease, Arrow of God"
      }
    ],
    "media": [
      {
        "type": "image",
        "url": "/static/images/celebrate/chinua-achebe-1.jpg",
        "caption": "Chinua Achebe in 1960"
      },
      {
        "type": "video",
        "url": "https://www.youtube.com/watch?v=example",
        "caption": "Interview with Chinua Achebe"
      }
    ],
    "related_entries": [
      {
        "id": "3",
        "type": "person",
        "name": "Wole Soyinka",
        "slug": "wole-soyinka"
      }
    ]
  }
  ```

### Search Entries

- **URL**: `/api/celebrate/search`
- **Method**: `GET`
- **Query Parameters**:
  - `q`: Search query
  - `type`: Entry type (person, place, event)
  - `category`: Category ID
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "person",
      "name": "Chinua Achebe",
      "slug": "chinua-achebe",
      "image_url": "/static/images/celebrate/chinua-achebe.jpg",
      "summary": "Renowned Nigerian novelist, poet, and critic"
    },
    {
      "id": "3",
      "type": "person",
      "name": "Wole Soyinka",
      "slug": "wole-soyinka",
      "image_url": "/static/images/celebrate/wole-soyinka.jpg",
      "summary": "Nigerian playwright, novelist, and poet"
    }
  ]
  ```

### Get Categories

- **URL**: `/api/celebrate/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "Literature",
      "entries_count": 5
    },
    {
      "id": "2",
      "name": "Natural Landmarks",
      "entries_count": 3
    }
  ]
  ```

### Submit Entry

- **URL**: `/api/celebrate/submit`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "type": "person",
    "name": "New Person",
    "summary": "Summary of the person",
    "description": "<p>Detailed description in HTML format</p>",
    "birth_date": "1980-01-01",
    "achievements": [
      "Achievement 1",
      "Achievement 2"
    ],
    "facts": [
      {
        "title": "Education",
        "content": "University of Lagos"
      }
    ],
    "category_ids": [1, 2]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Entry submitted for moderation",
    "entry_id": "4"
  }
  ```

## Error Responses

All endpoints return standard error responses in the following format:

```json
{
  "error": {
    "code": "error_code",
    "message": "Error message",
    "details": "Additional error details (optional)"
  }
}
```

Common error codes:
- `unauthorized`: Authentication required or token invalid
- `forbidden`: User does not have permission to access the resource
- `not_found`: Resource not found
- `validation_error`: Request validation failed
- `server_error`: Internal server error
