import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Typography, Button, Box, CircularProgress } from '@mui/material';
import StreamerProfile from '../components/livestream/StreamerProfile';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';

const StreamerProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();

  if (!userId || isNaN(Number(userId))) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Invalid user ID
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/livestream')}
        >
          Back to Streams
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Streamer Profile
      </Typography>

      <LivestreamNavigation />

      <StreamerProfile userId={Number(userId)} />
    </Container>
  );
};

export default StreamerProfilePage;
