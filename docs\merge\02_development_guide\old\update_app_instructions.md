# Instructions for Updating App.tsx

To add the ProgressDashboardPage to your application, you need to update the App.tsx file in your frontend repository. Here are the steps:

1. **Copy the following files to your frontend repository**:
   - Copy `frontend_files/ProgressDashboardPage.tsx` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\pages\ProgressDashboardPage.tsx`
   - Copy `frontend_files/progressSlice.ts` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\features\progress\progressSlice.ts`
   - Copy `frontend_files/progressService.ts` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\api\progressService.ts`

2. **Update your App.tsx file**:
   - Add the import for ProgressDashboardPage:
     ```tsx
     import ProgressDashboardPage from './pages/ProgressDashboardPage';
     ```
   - Add a new route for the ProgressDashboardPage:
     ```tsx
     <Route path="/progress" element={<ProgressDashboardPage />} />
     ```

3. **Update your store/index.ts file**:
   - Add the import for progressReducer:
     ```tsx
     import progressReducer from '../features/progress/progressSlice';
     ```
   - Add progressReducer to the reducer object:
     ```tsx
     reducer: {
       // existing reducers...
       progress: progressReducer,
     },
     ```

4. **Update your api/index.ts file**:
   - Add the import for progressService:
     ```tsx
     import progressService from './progressService';
     ```
   - Export progressService:
     ```tsx
     export { 
       // existing exports...
       progressService 
     };
     ```

5. **Add a link to the ProgressDashboardPage in your navigation**:
   - Find your navigation component (likely in Header.tsx or similar)
   - Add a link to the progress dashboard:
     ```tsx
     <Link to="/progress">Progress</Link>
     ```

## Backend Implementation

For the backend implementation, copy the following files to your backend repository:

1. **Copy the progress models**:
   - Copy `internal/progress/models/progress.go` to your backend repository

2. **Copy the progress repository**:
   - Copy `internal/progress/repository/progress_repository.go` to your backend repository

3. **Copy the progress service**:
   - Copy `internal/progress/service/progress_service.go` to your backend repository

4. **Copy the progress handlers**:
   - Copy `internal/progress/handlers/progress_handler.go` to your backend repository

5. **Update your API Gateway router**:
   - Find your router.go file (likely in internal/gateway/router.go)
   - Initialize the progress repository, service, and handler
   - Register the progress routes

Example router update:
```go
// Initialize progress components
progressRepo := progress_repository.NewSQLProgressRepository(db)
progressService := progress_service.NewProgressService(progressRepo)
progressHandler := progress_handlers.NewProgressHandler(progressService)

// Register progress routes
progressHandler.RegisterRoutes(apiGroup)
```

## Testing

After implementing these changes, test the progress dashboard by:

1. Starting your backend server
2. Starting your frontend development server
3. Navigating to the /progress route in your browser
4. Verifying that the progress dashboard loads correctly
5. Testing the different tabs and features of the dashboard
