import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  Button,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  CardActions,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  AccountBalanceWallet as WalletIcon,
  History as HistoryIcon,
  ShoppingCart as ShoppingCartIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import {
  fetchUserBalance,
  fetchCoinPackages,
  fetchUserTransactions
} from '../features/livestream/livestreamSlice';
import { CoinPackage } from '../api/livestreamService';
import PurchaseCoinsModal from '../components/livestream/PurchaseCoinsModal';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`currency-tabpanel-${index}`}
      aria-labelledby={`currency-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const VirtualCurrencyPage: React.FC = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    balance,
    packages: coinPackages,
    transactions,
    loading,
    error
  } = useSelector((state: RootState) => state.livestream.virtualCurrency);

  const [tabValue, setTabValue] = useState(0);
  const [selectedPackage, setSelectedPackage] = useState<CoinPackage | null>(null);
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchUserBalance(user.id) as any);
      dispatch(fetchCoinPackages() as any);
      dispatch(fetchUserTransactions({ userId: user.id, page: 1, limit: 20 }) as any);
    }
  }, [dispatch, user]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePurchaseClick = (pkg: CoinPackage) => {
    setSelectedPackage(pkg);
    setIsPurchaseModalOpen(true);
  };

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h5" align="center" sx={{ my: 4 }}>
          Please log in to access your virtual currency wallet
        </Typography>
      </Container>
    );
  }

  if (loading && !balance) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Virtual Currency
      </Typography>

      <LivestreamNavigation />

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      <Grid container spacing={3}>
        {/* Wallet Summary */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box display="flex" alignItems="center">
                  <WalletIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6" color="text.secondary">
                      Your Balance
                    </Typography>
                    <Typography variant="h3" fontWeight="bold">
                      {balance?.balance || 0} Coins
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Purchased
                      </Typography>
                      <Typography variant="h6">
                        {balance?.totalBought || 0} Coins
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Spent
                      </Typography>
                      <Typography variant="h6">
                        {balance?.totalSpent || 0} Coins
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Tabs */}
        <Grid item xs={12}>
          <Paper elevation={2}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="currency tabs"
              >
                <Tab
                  icon={<ShoppingCartIcon />}
                  label="Buy Coins"
                  id="currency-tab-0"
                  aria-controls="currency-tabpanel-0"
                />
                <Tab
                  icon={<HistoryIcon />}
                  label="Transaction History"
                  id="currency-tab-1"
                  aria-controls="currency-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Buy Coins Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Select a Coin Package
              </Typography>

              <Grid container spacing={3}>
                {coinPackages.map((pkg) => (
                  <Grid item xs={12} sm={6} md={4} key={pkg.id}>
                    <Card
                      elevation={3}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-5px)'
                        }
                      }}
                    >
                      {pkg.isPromotional && (
                        <Chip
                          label="Special Offer"
                          color="secondary"
                          sx={{
                            position: 'absolute',
                            top: 10,
                            right: 10,
                            zIndex: 1
                          }}
                        />
                      )}

                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography variant="h5" component="div" gutterBottom>
                          {pkg.name}
                        </Typography>

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="h4" color="primary" fontWeight="bold">
                            {pkg.coinsAmount} Coins
                          </Typography>
                          {pkg.bonusCoins > 0 && (
                            <Typography variant="body1" color="secondary">
                              +{pkg.bonusCoins} Bonus Coins
                            </Typography>
                          )}
                        </Box>

                        <Typography variant="body2" color="text.secondary" paragraph>
                          {pkg.description || `Purchase ${pkg.coinsAmount} coins to use for gifting during live streams.`}
                        </Typography>

                        <Typography variant="h6" color="text.primary">
                          ₦{pkg.priceNaira.toLocaleString()}
                        </Typography>
                      </CardContent>

                      <CardActions>
                        <Button
                          variant="contained"
                          color="primary"
                          fullWidth
                          onClick={() => handlePurchaseClick(pkg)}
                        >
                          Purchase
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </TabPanel>

            {/* Transaction History Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Transaction History
              </Typography>

              {loading ? (
                <Box display="flex" justifyContent="center" my={4}>
                  <CircularProgress />
                </Box>
              ) : transactions.length > 0 ? (
                <List>
                  {transactions.map((transaction) => (
                    <React.Fragment key={transaction.id}>
                      <ListItem>
                        <ListItemText
                          primary={
                            <Typography variant="body1">
                              {transaction.description}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {new Date(transaction.createdAt).toLocaleString()}
                            </Typography>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Typography
                            variant="body1"
                            color={transaction.amount > 0 ? 'success.main' : 'error.main'}
                            fontWeight="bold"
                          >
                            {transaction.amount > 0 ? '+' : ''}{transaction.amount} Coins
                          </Typography>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography variant="body1" color="text.secondary" align="center" sx={{ my: 4 }}>
                  No transactions found
                </Typography>
              )}
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      <PurchaseCoinsModal
        open={isPurchaseModalOpen}
        onClose={() => setIsPurchaseModalOpen(false)}
        coinPackage={selectedPackage}
      />
    </Container>
  );
};

export default VirtualCurrencyPage;
