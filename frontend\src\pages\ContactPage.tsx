import React, { useState } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const Subtitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const Paragraph = styled.p`
  margin-bottom: 1.5rem;
  line-height: 1.8;
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  
  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const ContactForm = styled.form`
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #16213e;
    box-shadow: 0 0 0 2px rgba(22, 33, 62, 0.2);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 150px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #16213e;
    box-shadow: 0 0 0 2px rgba(22, 33, 62, 0.2);
  }
`;

const Button = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ContactInfo = styled.div`
  background-color: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
`;

const ContactItem = styled.div`
  margin-bottom: 1.5rem;
`;

const ContactLabel = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #16213e;
`;

const ContactValue = styled.p`
  margin: 0;
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const SocialLink = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #16213e;
  color: white;
  border-radius: 50%;
  text-decoration: none;
  font-size: 1.2rem;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const SuccessMessage = styled.div`
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
`;

const ContactPage: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      setName('');
      setEmail('');
      setSubject('');
      setMessage('');
    }, 1500);
  };
  
  return (
    <Container>
      <Title>Contact Us</Title>
      
      <Paragraph>
        We'd love to hear from you! Whether you have a question about our resources, need help with
        the platform, or want to collaborate with us, please don't hesitate to reach out.
      </Paragraph>
      
      <ContactGrid>
        <ContactForm onSubmit={handleSubmit}>
          <Subtitle>Send Us a Message</Subtitle>
          
          {isSubmitted && (
            <SuccessMessage>
              Thank you for your message! We'll get back to you as soon as possible.
            </SuccessMessage>
          )}
          
          <FormGroup>
            <Label htmlFor="name">Your Name</Label>
            <Input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="email">Your Email</Label>
            <Input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="subject">Subject</Label>
            <Input
              type="text"
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="message">Message</Label>
            <TextArea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              required
            />
          </FormGroup>
          
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </Button>
        </ContactForm>
        
        <ContactInfo>
          <Subtitle>Contact Information</Subtitle>
          
          <ContactItem>
            <ContactLabel>Email</ContactLabel>
            <ContactValue><EMAIL></ContactValue>
          </ContactItem>
          
          <ContactItem>
            <ContactLabel>Phone</ContactLabel>
            <ContactValue>+234 ************</ContactValue>
          </ContactItem>
          
          <ContactItem>
            <ContactLabel>Address</ContactLabel>
            <ContactValue>
              Great Nigeria Headquarters
              <br />
              123 Awolowo Road
              <br />
              Lagos, Nigeria
            </ContactValue>
          </ContactItem>
          
          <ContactItem>
            <ContactLabel>Office Hours</ContactLabel>
            <ContactValue>
              Monday - Friday: 9:00 AM - 5:00 PM
              <br />
              Saturday: 10:00 AM - 2:00 PM
              <br />
              Sunday: Closed
            </ContactValue>
          </ContactItem>
          
          <ContactItem>
            <ContactLabel>Follow Us</ContactLabel>
            <SocialLinks>
              <SocialLink href="#" title="Facebook">
                f
              </SocialLink>
              <SocialLink href="#" title="Twitter">
                t
              </SocialLink>
              <SocialLink href="#" title="Instagram">
                i
              </SocialLink>
              <SocialLink href="#" title="LinkedIn">
                in
              </SocialLink>
            </SocialLinks>
          </ContactItem>
        </ContactInfo>
      </ContactGrid>
    </Container>
  );
};

export default ContactPage;
