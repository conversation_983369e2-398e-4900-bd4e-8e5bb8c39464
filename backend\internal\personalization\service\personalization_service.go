package service

import (
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/repository"
	"github.com/sirupsen/logrus"
	"sort"
	"time"
)

// PersonalizationService defines the interface for personalization business logic
type PersonalizationService interface {
	// Learning Style methods
	GetLearningStyleByUserID(userID uint) (*models.LearningStyle, error)
	SaveLearningStyle(style *models.LearningStyle) error
	
	// Learning Preference methods
	GetLearningPreferenceByUserID(userID uint) (*models.LearningPreference, error)
	SaveLearningPreference(pref *models.LearningPreference) error
	
	// Personalized Path methods
	GetPersonalizedPathsByUserID(userID uint) ([]models.PersonalizedPath, error)
	GetPersonalizedPathWithItems(pathID uint) (*models.PersonalizedPath, []models.PathItem, error)
	CreatePersonalizedPath(path *models.PersonalizedPath, items []models.PathItem) error
	UpdatePersonalizedPath(path *models.PersonalizedPath) error
	DeletePersonalizedPath(pathID uint) error
	
	// Path Item methods
	AddPathItem(item *models.PathItem) error
	UpdatePathItem(item *models.PathItem) error
	DeletePathItem(itemID uint) error
	MarkPathItemComplete(itemID uint, completed bool) error
	
	// Assessment methods
	GetAssessmentQuestions() ([]models.AssessmentQuestion, error)
	SubmitAssessmentResponses(userID uint, responses []models.AssessmentResponse) (*models.AssessmentResult, error)
	
	// Recommendation methods
	GetRecommendationsForUser(req models.PersonalizationRequest) (*models.PersonalizationResponse, error)
	UpdateRecommendationStatus(recID uint, viewed, saved, rejected bool) error
	
	// Learning Path Template methods
	GetLearningPathTemplates() ([]models.LearningPathTemplate, error)
	GetLearningPathTemplateWithItems(templateID uint) (*models.LearningPathTemplate, []models.TemplateItem, error)
	
	// User Performance methods
	GetUserPerformance(userID uint) (*models.UserPerformance, error)
	UpdateUserPerformance(perf *models.UserPerformance) error
	
	// Generate personalized paths based on learning style
	GeneratePersonalizedPaths(userID uint) ([]models.PersonalizedPath, error)
}

// DefaultPersonalizationService implements PersonalizationService
type DefaultPersonalizationService struct {
	repo   repository.PersonalizationRepository
	logger *logrus.Logger
}

// NewPersonalizationService creates a new personalization service
func NewPersonalizationService(repo repository.PersonalizationRepository, logger *logrus.Logger) PersonalizationService {
	return &DefaultPersonalizationService{
		repo:   repo,
		logger: logger,
	}
}

// GetLearningStyleByUserID retrieves a user's learning style
func (s *DefaultPersonalizationService) GetLearningStyleByUserID(userID uint) (*models.LearningStyle, error) {
	return s.repo.GetLearningStyleByUserID(userID)
}

// SaveLearningStyle saves a user's learning style
func (s *DefaultPersonalizationService) SaveLearningStyle(style *models.LearningStyle) error {
	// Determine primary and secondary learning styles
	styles := map[string]int{
		"visual":      style.Visual,
		"auditory":    style.Auditory,
		"readWrite":   style.ReadWrite,
		"kinesthetic": style.Kinesthetic,
		"social":      style.Social,
		"solitary":    style.Solitary,
		"logical":     style.Logical,
	}
	
	// Sort styles by score
	type styleScore struct {
		name  string
		score int
	}
	
	var sortedStyles []styleScore
	for name, score := range styles {
		sortedStyles = append(sortedStyles, styleScore{name, score})
	}
	
	sort.Slice(sortedStyles, func(i, j int) bool {
		return sortedStyles[i].score > sortedStyles[j].score
	})
	
	// Set primary and secondary styles
	if len(sortedStyles) > 0 {
		style.PrimaryStyle = sortedStyles[0].name
	}
	
	if len(sortedStyles) > 1 {
		style.SecondaryStyle = sortedStyles[1].name
	}
	
	style.AssessmentTaken = true
	style.AssessmentDate = time.Now()
	
	return s.repo.SaveLearningStyle(style)
}

// GetLearningPreferenceByUserID retrieves a user's learning preferences
func (s *DefaultPersonalizationService) GetLearningPreferenceByUserID(userID uint) (*models.LearningPreference, error) {
	return s.repo.GetLearningPreferenceByUserID(userID)
}

// SaveLearningPreference saves a user's learning preferences
func (s *DefaultPersonalizationService) SaveLearningPreference(pref *models.LearningPreference) error {
	return s.repo.SaveLearningPreference(pref)
}

// GetPersonalizedPathsByUserID retrieves all personalized paths for a user
func (s *DefaultPersonalizationService) GetPersonalizedPathsByUserID(userID uint) ([]models.PersonalizedPath, error) {
	return s.repo.GetPersonalizedPathsByUserID(userID)
}

// GetPersonalizedPathWithItems retrieves a personalized path with its items
func (s *DefaultPersonalizationService) GetPersonalizedPathWithItems(pathID uint) (*models.PersonalizedPath, []models.PathItem, error) {
	path, err := s.repo.GetPersonalizedPathByID(pathID)
	if err != nil {
		return nil, nil, err
	}
	
	items, err := s.repo.GetPathItemsByPathID(pathID)
	if err != nil {
		return nil, nil, err
	}
	
	return path, items, nil
}

// CreatePersonalizedPath creates a new personalized path with items
func (s *DefaultPersonalizationService) CreatePersonalizedPath(path *models.PersonalizedPath, items []models.PathItem) error {
	// Create the path first
	if err := s.repo.CreatePersonalizedPath(path); err != nil {
		return err
	}
	
	// Add items to the path
	for i := range items {
		items[i].PathID = path.ID
		items[i].Order = i + 1
		if err := s.repo.AddPathItem(&items[i]); err != nil {
			return err
		}
	}
	
	return nil
}

// UpdatePersonalizedPath updates an existing personalized path
func (s *DefaultPersonalizationService) UpdatePersonalizedPath(path *models.PersonalizedPath) error {
	return s.repo.UpdatePersonalizedPath(path)
}

// DeletePersonalizedPath deletes a personalized path
func (s *DefaultPersonalizationService) DeletePersonalizedPath(pathID uint) error {
	return s.repo.DeletePersonalizedPath(pathID)
}

// AddPathItem adds an item to a personalized path
func (s *DefaultPersonalizationService) AddPathItem(item *models.PathItem) error {
	return s.repo.AddPathItem(item)
}

// UpdatePathItem updates an existing path item
func (s *DefaultPersonalizationService) UpdatePathItem(item *models.PathItem) error {
	return s.repo.UpdatePathItem(item)
}

// DeletePathItem deletes a path item
func (s *DefaultPersonalizationService) DeletePathItem(itemID uint) error {
	return s.repo.DeletePathItem(itemID)
}

// MarkPathItemComplete marks a path item as complete or incomplete
func (s *DefaultPersonalizationService) MarkPathItemComplete(itemID uint, completed bool) error {
	return s.repo.MarkPathItemComplete(itemID, completed)
}

// GetAssessmentQuestions retrieves all active assessment questions
func (s *DefaultPersonalizationService) GetAssessmentQuestions() ([]models.AssessmentQuestion, error) {
	return s.repo.GetActiveAssessmentQuestions()
}

// SubmitAssessmentResponses processes assessment responses and calculates learning style
func (s *DefaultPersonalizationService) SubmitAssessmentResponses(userID uint, responses []models.AssessmentResponse) (*models.AssessmentResult, error) {
	// Save responses
	if err := s.repo.SaveAssessmentResponses(responses); err != nil {
		return nil, err
	}
	
	// Get questions to calculate scores
	questions, err := s.repo.GetAllAssessmentQuestions()
	if err != nil {
		return nil, err
	}
	
	// Create a map of question IDs to questions
	questionMap := make(map[uint]models.AssessmentQuestion)
	for _, q := range questions {
		questionMap[q.ID] = q
	}
	
	// Calculate learning style scores
	scores := map[string]int{
		"visual":      0,
		"auditory":    0,
		"readWrite":   0,
		"kinesthetic": 0,
		"social":      0,
		"solitary":    0,
		"logical":     0,
	}
	
	totalWeights := map[string]int{
		"visual":      0,
		"auditory":    0,
		"readWrite":   0,
		"kinesthetic": 0,
		"social":      0,
		"solitary":    0,
		"logical":     0,
	}
	
	for _, response := range responses {
		question, exists := questionMap[response.QuestionID]
		if !exists {
			continue
		}
		
		dimension := question.StyleDimension
		weight := question.Weight
		
		// Calculate score based on selected option (assuming options are scored 0-4)
		optionScore := response.SelectedOption
		
		// Add weighted score
		scores[dimension] += optionScore * weight
		totalWeights[dimension] += weight
	}
	
	// Normalize scores to 0-100 scale
	learningStyle := &models.LearningStyle{
		UserID: userID,
	}
	
	if totalWeights["visual"] > 0 {
		learningStyle.Visual = scores["visual"] * 100 / (totalWeights["visual"] * 4) // 4 is max option score
	}
	
	if totalWeights["auditory"] > 0 {
		learningStyle.Auditory = scores["auditory"] * 100 / (totalWeights["auditory"] * 4)
	}
	
	if totalWeights["readWrite"] > 0 {
		learningStyle.ReadWrite = scores["readWrite"] * 100 / (totalWeights["readWrite"] * 4)
	}
	
	if totalWeights["kinesthetic"] > 0 {
		learningStyle.Kinesthetic = scores["kinesthetic"] * 100 / (totalWeights["kinesthetic"] * 4)
	}
	
	if totalWeights["social"] > 0 {
		learningStyle.Social = scores["social"] * 100 / (totalWeights["social"] * 4)
	}
	
	if totalWeights["solitary"] > 0 {
		learningStyle.Solitary = scores["solitary"] * 100 / (totalWeights["solitary"] * 4)
	}
	
	if totalWeights["logical"] > 0 {
		learningStyle.Logical = scores["logical"] * 100 / (totalWeights["logical"] * 4)
	}
	
	// Save learning style
	if err := s.SaveLearningStyle(learningStyle); err != nil {
		return nil, err
	}
	
	// Generate personalized paths
	paths, err := s.GeneratePersonalizedPaths(userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate personalized paths")
		// Continue anyway, as we still want to return the learning style
	}
	
	// Generate recommendations
	recommendations, err := s.repo.GetRecommendationsForUser(userID, "", 5)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get recommendations")
		// Continue anyway
	}
	
	// Create and return assessment result
	result := &models.AssessmentResult{
		UserID:          userID,
		LearningStyle:   *learningStyle,
		Recommendations: recommendations,
		SuggestedPaths:  paths,
	}
	
	return result, nil
}

// GetRecommendationsForUser retrieves personalized content recommendations
func (s *DefaultPersonalizationService) GetRecommendationsForUser(req models.PersonalizationRequest) (*models.PersonalizationResponse, error) {
	// Check if user has a learning style
	style, err := s.repo.GetLearningStyleByUserID(req.UserID)
	if err != nil {
		return nil, err
	}
	
	hasStyle := style != nil && style.AssessmentTaken
	
	// Get recommendations
	count := req.Count
	if count <= 0 {
		count = 10 // Default limit
	}
	
	recommendations, err := s.repo.GetRecommendationsForUser(req.UserID, req.ContentType, count+1) // Get one extra to check if there are more
	if err != nil {
		return nil, err
	}
	
	hasMore := len(recommendations) > count
	if hasMore {
		recommendations = recommendations[:count]
	}
	
	return &models.PersonalizationResponse{
		Recommendations: recommendations,
		HasMoreResults:  hasMore,
		UserHasStyle:    hasStyle,
	}, nil
}

// UpdateRecommendationStatus updates the status of a recommendation
func (s *DefaultPersonalizationService) UpdateRecommendationStatus(recID uint, viewed, saved, rejected bool) error {
	return s.repo.UpdateRecommendationStatus(recID, viewed, saved, rejected)
}

// GetLearningPathTemplates retrieves all active learning path templates
func (s *DefaultPersonalizationService) GetLearningPathTemplates() ([]models.LearningPathTemplate, error) {
	return s.repo.GetActiveLearningPathTemplates()
}

// GetLearningPathTemplateWithItems retrieves a learning path template with its items
func (s *DefaultPersonalizationService) GetLearningPathTemplateWithItems(templateID uint) (*models.LearningPathTemplate, []models.TemplateItem, error) {
	template, err := s.repo.GetLearningPathTemplateByID(templateID)
	if err != nil {
		return nil, nil, err
	}
	
	items, err := s.repo.GetTemplateItemsByTemplateID(templateID)
	if err != nil {
		return nil, nil, err
	}
	
	return template, items, nil
}

// GetUserPerformance retrieves a user's performance metrics
func (s *DefaultPersonalizationService) GetUserPerformance(userID uint) (*models.UserPerformance, error) {
	return s.repo.GetUserPerformance(userID)
}

// UpdateUserPerformance updates a user's performance metrics
func (s *DefaultPersonalizationService) UpdateUserPerformance(perf *models.UserPerformance) error {
	return s.repo.UpdateUserPerformance(perf)
}

// GeneratePersonalizedPaths generates personalized learning paths based on learning style
func (s *DefaultPersonalizationService) GeneratePersonalizedPaths(userID uint) ([]models.PersonalizedPath, error) {
	// Get user's learning style
	style, err := s.repo.GetLearningStyleByUserID(userID)
	if err != nil {
		return nil, err
	}
	
	if style == nil || !style.AssessmentTaken {
		return nil, nil // User hasn't taken assessment yet
	}
	
	// Get user's preferences
	pref, err := s.repo.GetLearningPreferenceByUserID(userID)
	if err != nil {
		return nil, err
	}
	
	// Get all templates
	templates, err := s.repo.GetActiveLearningPathTemplates()
	if err != nil {
		return nil, err
	}
	
	// Find matching templates based on learning style
	var matchingTemplates []models.LearningPathTemplate
	for _, template := range templates {
		// Check if template targets user's primary or secondary style
		for _, targetStyle := range template.TargetStyles {
			if targetStyle == style.PrimaryStyle || targetStyle == style.SecondaryStyle {
				matchingTemplates = append(matchingTemplates, template)
				break
			}
		}
	}
	
	// If no matching templates, use any active template
	if len(matchingTemplates) == 0 {
		matchingTemplates = templates
	}
	
	// Limit to top 3 templates
	if len(matchingTemplates) > 3 {
		matchingTemplates = matchingTemplates[:3]
	}
	
	// Generate paths from templates
	var paths []models.PersonalizedPath
	for _, template := range matchingTemplates {
		// Create path from template
		path := models.PersonalizedPath{
			UserID:      userID,
			Name:        template.Name,
			Description: template.Description,
			IsActive:    true,
		}
		
		// Get template items
		templateItems, err := s.repo.GetTemplateItemsByTemplateID(template.ID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get template items")
			continue
		}
		
		// Create path items from template items
		var pathItems []models.PathItem
		for i, templateItem := range templateItems {
			// TODO: Implement dynamic content selection based on criteria
			// For now, just copy the template item
			pathItem := models.PathItem{
				ItemType:          templateItem.ItemType,
				ItemID:            templateItem.ItemID,
				Title:             templateItem.ItemCriteria, // Placeholder
				Description:       "Generated from template",
				Order:             i + 1,
				IsCompleted:       false,
				EstimatedDuration: 30, // Default 30 minutes
			}
			
			pathItems = append(pathItems, pathItem)
		}
		
		// Create the path with items
		if err := s.CreatePersonalizedPath(&path, pathItems); err != nil {
			s.logger.WithError(err).Error("Failed to create personalized path")
			continue
		}
		
		paths = append(paths, path)
	}
	
	return paths, nil
}
