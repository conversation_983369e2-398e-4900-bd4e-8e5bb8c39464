import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Box,
  Typography,
  Tabs,
  Tab,
  Avatar,
  Button,
  Divider,
  CircularProgress,
  Alert,
  TextField,
  IconButton,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Bookmark as BookmarkIcon,
  Note as NoteIcon,
  Timeline as TimelineIcon,
  People as PeopleIcon,
  EmojiEvents as AchievementsIcon,
  Payments as PaymentsIcon,
  Tune as TuneIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import AnimatedProgressBar from '../components/progress/AnimatedProgressBar';
import MilestoneAchievement from '../components/progress/MilestoneAchievement';
import FeatureTogglePanel from '../components/features/FeatureTogglePanel';
import TipsManager, { Tip } from '../components/tips/TipsManager';
import { Feature } from '../components/features/FeatureToggle';
import { fetchFeatures, toggleFeature, resetFeaturesToDefault } from '../features/features/featuresSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const EnhancedUserProfilePage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { user, loading: authLoading } = useSelector((state: RootState) => state.auth);
  const { features, loading: featuresLoading, error: featuresError } = useSelector((state: RootState) => state.features);
  
  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [profileData, setProfileData] = useState({
    displayName: '',
    bio: '',
    location: '',
    website: '',
    interests: [] as string[]
  });
  const [showMilestone, setShowMilestone] = useState(false);
  const [currentMilestone, setCurrentMilestone] = useState({
    title: 'Reading Milestone Achieved!',
    description: 'You have completed 50% of Book 1. Keep up the great work!',
    type: 'chapter' as 'chapter' | 'book' | 'points' | 'streak' | 'custom',
    level: 'silver' as 'bronze' | 'silver' | 'gold' | 'platinum',
    pointsAwarded: 100
  });
  const [currentTrigger, setCurrentTrigger] = useState<string | undefined>(undefined);
  
  // Sample tips for demonstration
  const tips: Tip[] = [
    {
      id: 'profile-completion',
      type: 'suggestion',
      title: 'Complete Your Profile',
      content: 'Add a bio and profile picture to help others get to know you better.',
      trigger: 'profile-page-loaded',
      position: 'bottom-right',
      priority: 'medium'
    },
    {
      id: 'feature-toggle',
      type: 'info',
      title: 'Customize Your Experience',
      content: 'You can enable or disable features to personalize your experience on the platform.',
      trigger: 'features-tab-opened',
      position: 'top-right',
      priority: 'high'
    },
    {
      id: 'reading-progress',
      type: 'tip',
      title: 'Track Your Progress',
      content: 'Check your reading progress to see how far you\'ve come and what\'s next.',
      trigger: 'progress-tab-opened',
      position: 'bottom-left',
      priority: 'medium'
    }
  ];
  
  // Sample reading progress data
  const readingProgress = {
    book1: {
      chaptersRead: 5,
      totalChapters: 10,
      lastRead: new Date().toISOString(),
      milestones: [25, 50, 75, 100]
    },
    book2: {
      chaptersRead: 2,
      totalChapters: 8,
      lastRead: new Date().toISOString(),
      milestones: [25, 50, 75, 100]
    },
    book3: {
      chaptersRead: 0,
      totalChapters: 12,
      lastRead: null,
      milestones: [25, 50, 75, 100]
    }
  };
  
  // Sample achievements data
  const achievements = [
    {
      id: 1,
      title: 'Book 1 Starter',
      description: 'Started reading Book 1',
      icon: '📚',
      earnedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      points: 10
    },
    {
      id: 2,
      title: 'Engaged Reader',
      description: 'Read for 5 consecutive days',
      icon: '🔥',
      earnedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      points: 50
    },
    {
      id: 3,
      title: 'Discussion Starter',
      description: 'Created your first forum topic',
      icon: '💬',
      earnedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      points: 25
    }
  ];
  
  // Load user data when component mounts
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    
    // Set profile data from user object
    setProfileData({
      displayName: user.name || '',
      bio: user.bio || '',
      location: user.location || '',
      website: user.website || '',
      interests: user.interests || []
    });
    
    // Fetch features
    dispatch(fetchFeatures() as any);
    
    // Show profile tip when page loads
    setCurrentTrigger('profile-page-loaded');
  }, [user, navigate, dispatch]);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    
    // Set trigger based on selected tab
    if (newValue === 1) {
      setCurrentTrigger('progress-tab-opened');
    } else if (newValue === 6) {
      setCurrentTrigger('features-tab-opened');
    } else {
      setCurrentTrigger(undefined);
    }
  };
  
  // Handle profile edit mode toggle
  const handleEditToggle = () => {
    setEditMode(!editMode);
  };
  
  // Handle profile data changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData({
      ...profileData,
      [name]: value
    });
  };
  
  // Handle profile save
  const handleProfileSave = () => {
    // In a real app, this would dispatch an action to update the user profile
    console.log('Saving profile:', profileData);
    setEditMode(false);
    
    // Show milestone achievement as a demo
    setCurrentMilestone({
      title: 'Profile Updated!',
      description: 'You\'ve completed your profile information. Your profile is now more visible to other users.',
      type: 'custom',
      level: 'bronze',
      pointsAwarded: 25
    });
    setShowMilestone(true);
  };
  
  // Handle milestone achievement
  const handleMilestoneReached = (milestone: number) => {
    console.log(`Milestone reached: ${milestone}%`);
    
    // Show milestone achievement
    setCurrentMilestone({
      title: `${milestone}% Milestone Reached!`,
      description: `You've completed ${milestone}% of Book 1. Keep up the great work!`,
      type: 'chapter',
      level: milestone >= 100 ? 'gold' : milestone >= 75 ? 'silver' : 'bronze',
      pointsAwarded: milestone >= 100 ? 200 : milestone >= 75 ? 100 : 50
    });
    setShowMilestone(true);
  };
  
  // Handle feature toggle
  const handleFeatureToggle = (featureId: string, enabled: boolean) => {
    dispatch(toggleFeature({ featureId, enabled }) as any);
  };
  
  // Handle reset features to default
  const handleResetFeatures = () => {
    dispatch(resetFeaturesToDefault() as any);
  };
  
  // Handle tip dismissal
  const handleTipDismiss = (tipId: string, dontShowAgain: boolean) => {
    console.log(`Tip dismissed: ${tipId}, Don't show again: ${dontShowAgain}`);
    // In a real app, this would save the user's preference
  };
  
  if (authLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }
  
  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          You must be logged in to view this page.
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <TipsManager
        tips={tips}
        currentTrigger={currentTrigger}
        onTipDismiss={handleTipDismiss}
      />
      
      <MilestoneAchievement
        open={showMilestone}
        onClose={() => setShowMilestone(false)}
        title={currentMilestone.title}
        description={currentMilestone.description}
        type={currentMilestone.type}
        level={currentMilestone.level}
        pointsAwarded={currentMilestone.pointsAwarded}
      />
      
      <Grid container spacing={4}>
        {/* Profile Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
            <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
              <Avatar
                src={user.avatar || undefined}
                alt={user.name}
                sx={{ width: 120, height: 120, mb: 2, bgcolor: theme.palette.primary.main }}
              >
                {user.name?.charAt(0).toUpperCase()}
              </Avatar>
              
              {editMode ? (
                <TextField
                  fullWidth
                  label="Display Name"
                  name="displayName"
                  value={profileData.displayName}
                  onChange={handleProfileChange}
                  variant="outlined"
                  size="small"
                  sx={{ mb: 1 }}
                />
              ) : (
                <Typography variant="h5" fontWeight="bold" textAlign="center">
                  {profileData.displayName || user.name}
                </Typography>
              )}
              
              <Typography variant="body2" color="text.secondary" textAlign="center" gutterBottom>
                @{user.username}
              </Typography>
              
              <Box display="flex" gap={1} mt={1}>
                <Chip 
                  label="Active Member" 
                  color="primary" 
                  size="small" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${user.points || 0} Points`} 
                  color="secondary" 
                  size="small" 
                />
              </Box>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            {editMode ? (
              <Box mb={3}>
                <TextField
                  fullWidth
                  label="Bio"
                  name="bio"
                  value={profileData.bio}
                  onChange={handleProfileChange}
                  variant="outlined"
                  size="small"
                  multiline
                  rows={3}
                  sx={{ mb: 2 }}
                />
                
                <TextField
                  fullWidth
                  label="Location"
                  name="location"
                  value={profileData.location}
                  onChange={handleProfileChange}
                  variant="outlined"
                  size="small"
                  sx={{ mb: 2 }}
                />
                
                <TextField
                  fullWidth
                  label="Website"
                  name="website"
                  value={profileData.website}
                  onChange={handleProfileChange}
                  variant="outlined"
                  size="small"
                  sx={{ mb: 2 }}
                />
                
                <Box display="flex" gap={1} mt={2}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleProfileSave}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<CancelIcon />}
                    onClick={handleEditToggle}
                  >
                    Cancel
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box mb={3}>
                <Typography variant="body1" paragraph>
                  {profileData.bio || 'No bio provided yet.'}
                </Typography>
                
                {profileData.location && (
                  <Typography variant="body2" color="text.secondary" paragraph>
                    📍 {profileData.location}
                  </Typography>
                )}
                
                {profileData.website && (
                  <Typography variant="body2" color="text.secondary" paragraph>
                    🔗 {profileData.website}
                  </Typography>
                )}
                
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={handleEditToggle}
                  sx={{ mt: 1 }}
                >
                  Edit Profile
                </Button>
              </Box>
            )}
            
            <Divider sx={{ my: 2 }} />
            
            <Box>
              <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                Reading Stats
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h6" color="primary">
                      {readingProgress.book1.chaptersRead + readingProgress.book2.chaptersRead + readingProgress.book3.chaptersRead}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Chapters Read
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h6" color="primary">
                      {achievements.length}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Achievements
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h6" color="primary">
                      {user.points || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Points
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              
              <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                Book 1 Progress
              </Typography>
              
              <AnimatedProgressBar
                value={readingProgress.book1.chaptersRead}
                total={readingProgress.book1.totalChapters}
                milestones={readingProgress.book1.milestones}
                onMilestoneReached={handleMilestoneReached}
                height={8}
                color="primary"
              />
            </Box>
          </Paper>
        </Grid>
        
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ borderRadius: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab icon={<TimelineIcon />} label="Activity" />
              <Tab icon={<BookmarkIcon />} label="Reading" />
              <Tab icon={<NoteIcon />} label="Notes" />
              <Tab icon={<AchievementsIcon />} label="Achievements" />
              <Tab icon={<PeopleIcon />} label="Connections" />
              <Tab icon={<PaymentsIcon />} label="Wallet" />
              <Tab icon={<TuneIcon />} label="Features" />
              <Tab icon={<SettingsIcon />} label="Settings" />
            </Tabs>
            
            {/* Activity Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                This is a placeholder for the activity feed. In a real implementation, this would show the user's recent actions, interactions, and achievements.
              </Alert>
              
              <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Activity feed coming soon...
                </Typography>
              </Box>
            </TabPanel>
            
            {/* Reading Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Reading Progress
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Book 1: Great Nigeria
                    </Typography>
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" color="text.secondary">
                        {readingProgress.book1.chaptersRead} of {readingProgress.book1.totalChapters} chapters
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {Math.round((readingProgress.book1.chaptersRead / readingProgress.book1.totalChapters) * 100)}%
                      </Typography>
                    </Box>
                    
                    <AnimatedProgressBar
                      value={readingProgress.book1.chaptersRead}
                      total={readingProgress.book1.totalChapters}
                      milestones={readingProgress.book1.milestones}
                      onMilestoneReached={handleMilestoneReached}
                      height={10}
                      color="primary"
                    />
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                      <Button variant="outlined" size="small">
                        Continue Reading
                      </Button>
                      <Typography variant="caption" color="text.secondary">
                        Last read: {new Date(readingProgress.book1.lastRead).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Book 2: Building Nigeria
                    </Typography>
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" color="text.secondary">
                        {readingProgress.book2.chaptersRead} of {readingProgress.book2.totalChapters} chapters
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {Math.round((readingProgress.book2.chaptersRead / readingProgress.book2.totalChapters) * 100)}%
                      </Typography>
                    </Box>
                    
                    <AnimatedProgressBar
                      value={readingProgress.book2.chaptersRead}
                      total={readingProgress.book2.totalChapters}
                      milestones={readingProgress.book2.milestones}
                      onMilestoneReached={handleMilestoneReached}
                      height={10}
                      color="secondary"
                    />
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                      <Button variant="outlined" size="small">
                        Continue Reading
                      </Button>
                      <Typography variant="caption" color="text.secondary">
                        Last read: {new Date(readingProgress.book2.lastRead).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Book 3: Advanced Nigeria
                    </Typography>
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" color="text.secondary">
                        {readingProgress.book3.chaptersRead} of {readingProgress.book3.totalChapters} chapters
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {Math.round((readingProgress.book3.chaptersRead / readingProgress.book3.totalChapters) * 100)}%
                      </Typography>
                    </Box>
                    
                    <AnimatedProgressBar
                      value={readingProgress.book3.chaptersRead}
                      total={readingProgress.book3.totalChapters}
                      milestones={readingProgress.book3.milestones}
                      onMilestoneReached={handleMilestoneReached}
                      height={10}
                      color="info"
                    />
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                      <Button variant="outlined" size="small" disabled>
                        Premium Content
                      </Button>
                      <Typography variant="caption" color="text.secondary">
                        Unlock with 1500+ points
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
            </TabPanel>
            
            {/* Notes Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                My Notes
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                This is a placeholder for the notes section. In a real implementation, this would show the user's notes organized by book and chapter.
              </Alert>
              
              <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Notes feature coming soon...
                </Typography>
              </Box>
            </TabPanel>
            
            {/* Achievements Tab */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                My Achievements
              </Typography>
              
              <Grid container spacing={2}>
                {achievements.map((achievement) => (
                  <Grid item xs={12} sm={6} md={4} key={achievement.id}>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        textAlign: 'center',
                        borderRadius: 2,
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: theme.shadows[3]
                        }
                      }}
                    >
                      <Box
                        sx={{
                          fontSize: '2rem',
                          mb: 1
                        }}
                      >
                        {achievement.icon}
                      </Box>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {achievement.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {achievement.description}
                      </Typography>
                      <Box
                        sx={{
                          mt: 'auto',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1
                        }}
                      >
                        <Chip
                          label={`+${achievement.points} Points`}
                          color="primary"
                          size="small"
                        />
                        <Typography variant="caption" color="text.secondary">
                          {new Date(achievement.earnedAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </TabPanel>
            
            {/* Connections Tab */}
            <TabPanel value={tabValue} index={4}>
              <Typography variant="h6" gutterBottom>
                My Connections
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                This is a placeholder for the connections section. In a real implementation, this would show the user's friends, followers, and groups.
              </Alert>
              
              <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Connections feature coming soon...
                </Typography>
              </Box>
            </TabPanel>
            
            {/* Wallet Tab */}
            <TabPanel value={tabValue} index={5}>
              <Typography variant="h6" gutterBottom>
                My Wallet
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                This is a placeholder for the wallet section. In a real implementation, this would show the user's virtual currency, transaction history, and payment methods.
              </Alert>
              
              <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Wallet feature coming soon...
                </Typography>
              </Box>
            </TabPanel>
            
            {/* Features Tab */}
            <TabPanel value={tabValue} index={6}>
              <Typography variant="h6" gutterBottom>
                Feature Settings
              </Typography>
              
              {featuresLoading ? (
                <Box display="flex" justifyContent="center" py={4}>
                  <CircularProgress />
                </Box>
              ) : (
                <FeatureTogglePanel
                  features={features}
                  onFeatureToggle={handleFeatureToggle}
                  onRefresh={() => dispatch(fetchFeatures() as any)}
                  onResetToDefault={handleResetFeatures}
                  loading={featuresLoading}
                  error={featuresError}
                />
              )}
            </TabPanel>
            
            {/* Settings Tab */}
            <TabPanel value={tabValue} index={7}>
              <Typography variant="h6" gutterBottom>
                Account Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <SecurityIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        Security
                      </Typography>
                    </Box>
                    <Typography variant="body2" paragraph>
                      Manage your password, two-factor authentication, and security preferences.
                    </Typography>
                    <Button variant="outlined" size="small">
                      Security Settings
                    </Button>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <NotificationsIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        Notifications
                      </Typography>
                    </Box>
                    <Typography variant="body2" paragraph>
                      Configure email, push, and in-app notification preferences.
                    </Typography>
                    <Button variant="outlined" size="small">
                      Notification Settings
                    </Button>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <SettingsIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        Account Preferences
                      </Typography>
                    </Box>
                    <Typography variant="body2" paragraph>
                      Manage your account settings, privacy preferences, and data export options.
                    </Typography>
                    <Button variant="outlined" size="small">
                      Account Settings
                    </Button>
                  </Paper>
                </Grid>
              </Grid>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default EnhancedUserProfilePage;
