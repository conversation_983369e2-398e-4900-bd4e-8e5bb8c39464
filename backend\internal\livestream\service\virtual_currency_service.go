package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// VirtualCurrencyService defines the interface for virtual currency business logic
type VirtualCurrencyService interface {
	// Balance operations
	GetUserBalance(ctx context.Context, userID uint) (*repository.VirtualCurrency, error)
	
	// Transaction operations
	GetUserTransactions(ctx context.Context, userID uint, page, limit int) ([]repository.VirtualCurrencyTransaction, int, error)
	
	// Coin package operations
	GetCoinPackages(ctx context.Context) ([]repository.CoinPackage, error)
	GetCoinPackageByID(ctx context.Context, id uint) (*repository.CoinPackage, error)
	
	// Purchase operations
	PurchaseCoins(ctx context.Context, userID, packageID uint, paymentID uint) error
	
	// Gift operations
	DeductCoinsForGift(ctx context.Context, userID uint, amount float64, giftID uint) error
	
	// Bonus operations
	AwardBonusCoins(ctx context.Context, userID uint, amount float64, reason string) error
}

// VirtualCurrencyServiceImpl implements the VirtualCurrencyService interface
type VirtualCurrencyServiceImpl struct {
	repo   repository.VirtualCurrencyRepository
	logger *logger.Logger
}

// NewVirtualCurrencyService creates a new instance of the virtual currency service
func NewVirtualCurrencyService(repo repository.VirtualCurrencyRepository, logger *logger.Logger) VirtualCurrencyService {
	return &VirtualCurrencyServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// GetUserBalance retrieves a user's virtual currency balance
func (s *VirtualCurrencyServiceImpl) GetUserBalance(ctx context.Context, userID uint) (*repository.VirtualCurrency, error) {
	return s.repo.GetUserBalance(ctx, userID)
}

// GetUserTransactions retrieves a user's virtual currency transactions
func (s *VirtualCurrencyServiceImpl) GetUserTransactions(ctx context.Context, userID uint, page, limit int) ([]repository.VirtualCurrencyTransaction, int, error) {
	return s.repo.GetUserTransactions(ctx, userID, page, limit)
}

// GetCoinPackages retrieves all available coin packages
func (s *VirtualCurrencyServiceImpl) GetCoinPackages(ctx context.Context) ([]repository.CoinPackage, error) {
	packages, err := s.repo.GetCoinPackages(ctx)
	if err != nil {
		return nil, err
	}
	
	// Filter out expired promotional packages
	now := time.Now()
	var validPackages []repository.CoinPackage
	
	for _, pkg := range packages {
		if pkg.IsPromotional && pkg.PromotionEnds != nil && now.After(*pkg.PromotionEnds) {
			continue
		}
		validPackages = append(validPackages, pkg)
	}
	
	return validPackages, nil
}

// GetCoinPackageByID retrieves a coin package by its ID
func (s *VirtualCurrencyServiceImpl) GetCoinPackageByID(ctx context.Context, id uint) (*repository.CoinPackage, error) {
	pkg, err := s.repo.GetCoinPackageByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Check if promotional package is expired
	if pkg.IsPromotional && pkg.PromotionEnds != nil && time.Now().After(*pkg.PromotionEnds) {
		return nil, errors.New("promotional package has expired")
	}
	
	return pkg, nil
}

// PurchaseCoins processes a coin purchase
func (s *VirtualCurrencyServiceImpl) PurchaseCoins(ctx context.Context, userID, packageID uint, paymentID uint) error {
	// Get the coin package
	pkg, err := s.repo.GetCoinPackageByID(ctx, packageID)
	if err != nil {
		return err
	}
	
	// Check if package is active
	if !pkg.IsActive {
		return errors.New("coin package is not active")
	}
	
	// Check if promotional package is expired
	if pkg.IsPromotional && pkg.PromotionEnds != nil && time.Now().After(*pkg.PromotionEnds) {
		return errors.New("promotional package has expired")
	}
	
	// Calculate total coins (including bonus)
	totalCoins := pkg.CoinsAmount + pkg.BonusCoins
	
	// Generate reference ID
	referenceID := fmt.Sprintf("COIN-PURCHASE-%s", uuid.New().String())
	
	// Add coins to user's balance
	description := fmt.Sprintf("Purchase of %s coin package", pkg.Name)
	err = s.repo.AddCoinsToUser(ctx, userID, totalCoins, "purchase", description, referenceID, &paymentID)
	if err != nil {
		return err
	}
	
	s.logger.Infof("User %d purchased %s coin package for %.2f Naira, received %.2f coins (including %.2f bonus coins)",
		userID, pkg.Name, pkg.PriceNaira, totalCoins, pkg.BonusCoins)
	
	return nil
}

// DeductCoinsForGift deducts coins from a user's balance for a gift
func (s *VirtualCurrencyServiceImpl) DeductCoinsForGift(ctx context.Context, userID uint, amount float64, giftID uint) error {
	// Check if user has sufficient balance
	balance, err := s.repo.GetUserBalance(ctx, userID)
	if err != nil {
		return err
	}
	
	if balance.Balance < amount {
		return errors.New("insufficient balance")
	}
	
	// Generate reference ID
	referenceID := fmt.Sprintf("GIFT-%d-%s", giftID, uuid.New().String())
	
	// Deduct coins from user's balance
	description := fmt.Sprintf("Gift sent (ID: %d)", giftID)
	err = s.repo.DeductCoinsFromUser(ctx, userID, amount, "gift_sent", description, referenceID)
	if err != nil {
		return err
	}
	
	s.logger.Infof("User %d sent gift %d, deducted %.2f coins", userID, giftID, amount)
	
	return nil
}

// AwardBonusCoins awards bonus coins to a user
func (s *VirtualCurrencyServiceImpl) AwardBonusCoins(ctx context.Context, userID uint, amount float64, reason string) error {
	// Generate reference ID
	referenceID := fmt.Sprintf("BONUS-%s", uuid.New().String())
	
	// Add bonus coins to user's balance
	description := fmt.Sprintf("Bonus coins: %s", reason)
	err := s.repo.AddCoinsToUser(ctx, userID, amount, "bonus", description, referenceID, nil)
	if err != nil {
		return err
	}
	
	s.logger.Infof("User %d awarded %.2f bonus coins for: %s", userID, amount, reason)
	
	return nil
}
