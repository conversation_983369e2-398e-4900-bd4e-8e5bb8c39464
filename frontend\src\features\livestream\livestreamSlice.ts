import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import livestreamService, {
  Stream,
  CoinPackage,
  VirtualCurrency,
  Gift,
  GifterRanking,
  CreatorRevenue,
  CreateStreamRequest,
  PurchaseCoinsRequest,
  SendGiftRequest,
  WithdrawalRequestData
} from '../../api/livestreamService';

// Define the state interface
interface LivestreamState {
  streams: {
    items: Stream[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  currentStream: {
    data: Stream | null;
    viewers: any[];
    gifts: Gift[];
    rankings: GifterRanking[];
    loading: boolean;
    error: string | null;
  };
  virtualCurrency: {
    balance: VirtualCurrency | null;
    packages: CoinPackage[];
    transactions: any[];
    loading: boolean;
    error: string | null;
  };
  gifts: {
    sent: Gift[];
    received: Gift[];
    loading: boolean;
    error: string | null;
  };
  rankings: {
    global: GifterRanking[];
    userRanking: GifterRanking | null;
    loading: boolean;
    error: string | null;
  };
  revenue: {
    data: CreatorRevenue[];
    summary: any | null;
    loading: boolean;
    error: string | null;
  };
}

// Define the initial state
const initialState: LivestreamState = {
  streams: {
    items: [],
    total: 0,
    loading: false,
    error: null
  },
  currentStream: {
    data: null,
    viewers: [],
    gifts: [],
    rankings: [],
    loading: false,
    error: null
  },
  virtualCurrency: {
    balance: null,
    packages: [],
    transactions: [],
    loading: false,
    error: null
  },
  gifts: {
    sent: [],
    received: [],
    loading: false,
    error: null
  },
  rankings: {
    global: [],
    userRanking: null,
    loading: false,
    error: null
  },
  revenue: {
    data: [],
    summary: null,
    loading: false,
    error: null
  }
};

// Define async thunks
export const fetchActiveStreams = createAsyncThunk(
  'livestream/fetchActiveStreams',
  async ({ page, limit }: { page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getActiveStreams(page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch active streams');
    }
  }
);

export const fetchStreamById = createAsyncThunk(
  'livestream/fetchStreamById',
  async (streamId: number, { rejectWithValue }) => {
    try {
      return await livestreamService.getStreamById(streamId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch stream');
    }
  }
);

export const createStream = createAsyncThunk(
  'livestream/createStream',
  async (streamData: CreateStreamRequest, { rejectWithValue }) => {
    try {
      return await livestreamService.createStream(streamData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create stream');
    }
  }
);

export const updateStream = createAsyncThunk(
  'livestream/updateStream',
  async ({ streamId, streamData }: { streamId: number; streamData: Partial<CreateStreamRequest> }, { rejectWithValue }) => {
    try {
      return await livestreamService.updateStream(streamId, streamData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update stream');
    }
  }
);

export const startStream = createAsyncThunk(
  'livestream/startStream',
  async (streamId: number, { rejectWithValue }) => {
    try {
      return await livestreamService.startStream(streamId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to start stream');
    }
  }
);

export const endStream = createAsyncThunk(
  'livestream/endStream',
  async (streamId: number, { rejectWithValue }) => {
    try {
      return await livestreamService.endStream(streamId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to end stream');
    }
  }
);

export const fetchStreamViewers = createAsyncThunk(
  'livestream/fetchStreamViewers',
  async ({ streamId, page, limit }: { streamId: number; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getStreamViewers(streamId, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch stream viewers');
    }
  }
);

export const fetchCoinPackages = createAsyncThunk(
  'livestream/fetchCoinPackages',
  async (_, { rejectWithValue }) => {
    try {
      return await livestreamService.getCoinPackages();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch coin packages');
    }
  }
);

export const fetchUserBalance = createAsyncThunk(
  'livestream/fetchUserBalance',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await livestreamService.getUserBalance(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user balance');
    }
  }
);

export const purchaseCoins = createAsyncThunk(
  'livestream/purchaseCoins',
  async (data: PurchaseCoinsRequest, { rejectWithValue }) => {
    try {
      return await livestreamService.purchaseCoins(data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to purchase coins');
    }
  }
);

export const fetchUserTransactions = createAsyncThunk(
  'livestream/fetchUserTransactions',
  async ({ userId, page, limit }: { userId: number; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getUserTransactions(userId, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user transactions');
    }
  }
);

export const sendGift = createAsyncThunk(
  'livestream/sendGift',
  async (data: SendGiftRequest, { rejectWithValue }) => {
    try {
      return await livestreamService.sendGift(data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to send gift');
    }
  }
);

export const fetchStreamGifts = createAsyncThunk(
  'livestream/fetchStreamGifts',
  async ({ streamId, page, limit }: { streamId: number; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getStreamGifts(streamId, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch stream gifts');
    }
  }
);

export const fetchUserSentGifts = createAsyncThunk(
  'livestream/fetchUserSentGifts',
  async ({ userId, page, limit }: { userId: number; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getUserSentGifts(userId, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user sent gifts');
    }
  }
);

export const fetchUserReceivedGifts = createAsyncThunk(
  'livestream/fetchUserReceivedGifts',
  async ({ userId, page, limit }: { userId: number; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getUserReceivedGifts(userId, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user received gifts');
    }
  }
);

export const fetchStreamRankings = createAsyncThunk(
  'livestream/fetchStreamRankings',
  async ({ streamId, period, limit }: { streamId: number; period: string; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getStreamRankings(streamId, period, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch stream rankings');
    }
  }
);

export const fetchGlobalRankings = createAsyncThunk(
  'livestream/fetchGlobalRankings',
  async ({ period, limit }: { period: string; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getGlobalRankings(period, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch global rankings');
    }
  }
);

export const fetchUserRanking = createAsyncThunk(
  'livestream/fetchUserRanking',
  async ({ userId, streamId, period }: { userId: number; streamId?: number; period: string }, { rejectWithValue }) => {
    try {
      return await livestreamService.getUserRanking(userId, streamId, period);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user ranking');
    }
  }
);

export const fetchCreatorRevenue = createAsyncThunk(
  'livestream/fetchCreatorRevenue',
  async ({ userId, period, page, limit }: { userId: number; period?: string; page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await livestreamService.getCreatorRevenue(userId, period, page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch creator revenue');
    }
  }
);

export const fetchRevenueSummary = createAsyncThunk(
  'livestream/fetchRevenueSummary',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await livestreamService.getRevenueSummary(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch revenue summary');
    }
  }
);

export const requestWithdrawal = createAsyncThunk(
  'livestream/requestWithdrawal',
  async (data: WithdrawalRequestData, { rejectWithValue }) => {
    try {
      return await livestreamService.requestWithdrawal(data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to request withdrawal');
    }
  }
);

// Create the slice
const livestreamSlice = createSlice({
  name: 'livestream',
  initialState,
  reducers: {
    resetStreamState: (state) => {
      state.currentStream = {
        data: null,
        viewers: [],
        gifts: [],
        rankings: [],
        loading: false,
        error: null
      };
    },
    addStreamGift: (state, action: PayloadAction<Gift>) => {
      state.currentStream.gifts.unshift(action.payload);
    },
    updateViewerCount: (state, action: PayloadAction<number>) => {
      if (state.currentStream.data) {
        state.currentStream.data.viewerCount = action.payload;
      }
    },
    updateStreamRankings: (state, action: PayloadAction<GifterRanking[]>) => {
      state.currentStream.rankings = action.payload;
    }
  },
  extraReducers: (builder) => {
    // Active Streams
    builder
      .addCase(fetchActiveStreams.pending, (state) => {
        state.streams.loading = true;
        state.streams.error = null;
      })
      .addCase(fetchActiveStreams.fulfilled, (state, action) => {
        state.streams.loading = false;
        state.streams.items = action.payload.streams;
        state.streams.total = action.payload.pagination.total;
      })
      .addCase(fetchActiveStreams.rejected, (state, action) => {
        state.streams.loading = false;
        state.streams.error = action.payload as string;
      });

    // Stream by ID
    builder
      .addCase(fetchStreamById.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(fetchStreamById.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.data = action.payload;
      })
      .addCase(fetchStreamById.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // Create Stream
    builder
      .addCase(createStream.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(createStream.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.data = action.payload;
        state.streams.items.unshift(action.payload);
      })
      .addCase(createStream.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // Update Stream
    builder
      .addCase(updateStream.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(updateStream.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.data = action.payload;
        
        // Update in streams list if present
        const index = state.streams.items.findIndex(stream => stream.id === action.payload.id);
        if (index !== -1) {
          state.streams.items[index] = action.payload;
        }
      })
      .addCase(updateStream.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // Start Stream
    builder
      .addCase(startStream.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(startStream.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.data = action.payload;
        
        // Update in streams list if present
        const index = state.streams.items.findIndex(stream => stream.id === action.payload.id);
        if (index !== -1) {
          state.streams.items[index] = action.payload;
        }
      })
      .addCase(startStream.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // End Stream
    builder
      .addCase(endStream.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(endStream.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.data = action.payload;
        
        // Update in streams list if present
        const index = state.streams.items.findIndex(stream => stream.id === action.payload.id);
        if (index !== -1) {
          state.streams.items[index] = action.payload;
        }
      })
      .addCase(endStream.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // Stream Viewers
    builder
      .addCase(fetchStreamViewers.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(fetchStreamViewers.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.viewers = action.payload.viewers;
      })
      .addCase(fetchStreamViewers.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // Coin Packages
    builder
      .addCase(fetchCoinPackages.pending, (state) => {
        state.virtualCurrency.loading = true;
        state.virtualCurrency.error = null;
      })
      .addCase(fetchCoinPackages.fulfilled, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.packages = action.payload;
      })
      .addCase(fetchCoinPackages.rejected, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.error = action.payload as string;
      });

    // User Balance
    builder
      .addCase(fetchUserBalance.pending, (state) => {
        state.virtualCurrency.loading = true;
        state.virtualCurrency.error = null;
      })
      .addCase(fetchUserBalance.fulfilled, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.balance = action.payload;
      })
      .addCase(fetchUserBalance.rejected, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.error = action.payload as string;
      });

    // Purchase Coins
    builder
      .addCase(purchaseCoins.pending, (state) => {
        state.virtualCurrency.loading = true;
        state.virtualCurrency.error = null;
      })
      .addCase(purchaseCoins.fulfilled, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.balance = action.payload.balance;
      })
      .addCase(purchaseCoins.rejected, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.error = action.payload as string;
      });

    // User Transactions
    builder
      .addCase(fetchUserTransactions.pending, (state) => {
        state.virtualCurrency.loading = true;
        state.virtualCurrency.error = null;
      })
      .addCase(fetchUserTransactions.fulfilled, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.transactions = action.payload.transactions;
      })
      .addCase(fetchUserTransactions.rejected, (state, action) => {
        state.virtualCurrency.loading = false;
        state.virtualCurrency.error = action.payload as string;
      });

    // Send Gift
    builder
      .addCase(sendGift.pending, (state) => {
        state.gifts.loading = true;
        state.gifts.error = null;
      })
      .addCase(sendGift.fulfilled, (state, action) => {
        state.gifts.loading = false;
        state.gifts.sent.unshift(action.payload);
        
        // Add to current stream gifts if applicable
        if (state.currentStream.data && state.currentStream.data.id === action.payload.streamId) {
          state.currentStream.gifts.unshift(action.payload);
        }
        
        // Update balance if available
        if (state.virtualCurrency.balance) {
          state.virtualCurrency.balance.balance -= action.payload.coinsAmount;
          state.virtualCurrency.balance.totalSpent += action.payload.coinsAmount;
        }
      })
      .addCase(sendGift.rejected, (state, action) => {
        state.gifts.loading = false;
        state.gifts.error = action.payload as string;
      });

    // Stream Gifts
    builder
      .addCase(fetchStreamGifts.pending, (state) => {
        state.currentStream.loading = true;
        state.currentStream.error = null;
      })
      .addCase(fetchStreamGifts.fulfilled, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.gifts = action.payload.gifts;
      })
      .addCase(fetchStreamGifts.rejected, (state, action) => {
        state.currentStream.loading = false;
        state.currentStream.error = action.payload as string;
      });

    // User Sent Gifts
    builder
      .addCase(fetchUserSentGifts.pending, (state) => {
        state.gifts.loading = true;
        state.gifts.error = null;
      })
      .addCase(fetchUserSentGifts.fulfilled, (state, action) => {
        state.gifts.loading = false;
        state.gifts.sent = action.payload.gifts;
      })
      .addCase(fetchUserSentGifts.rejected, (state, action) => {
        state.gifts.loading = false;
        state.gifts.error = action.payload as string;
      });

    // User Received Gifts
    builder
      .addCase(fetchUserReceivedGifts.pending, (state) => {
        state.gifts.loading = true;
        state.gifts.error = null;
      })
      .addCase(fetchUserReceivedGifts.fulfilled, (state, action) => {
        state.gifts.loading = false;
        state.gifts.received = action.payload.gifts;
      })
      .addCase(fetchUserReceivedGifts.rejected, (state, action) => {
        state.gifts.loading = false;
        state.gifts.error = action.payload as string;
      });

    // Stream Rankings
    builder
      .addCase(fetchStreamRankings.pending, (state) => {
        state.rankings.loading = true;
        state.rankings.error = null;
      })
      .addCase(fetchStreamRankings.fulfilled, (state, action) => {
        state.rankings.loading = false;
        state.currentStream.rankings = action.payload.rankings;
      })
      .addCase(fetchStreamRankings.rejected, (state, action) => {
        state.rankings.loading = false;
        state.rankings.error = action.payload as string;
      });

    // Global Rankings
    builder
      .addCase(fetchGlobalRankings.pending, (state) => {
        state.rankings.loading = true;
        state.rankings.error = null;
      })
      .addCase(fetchGlobalRankings.fulfilled, (state, action) => {
        state.rankings.loading = false;
        state.rankings.global = action.payload.rankings;
      })
      .addCase(fetchGlobalRankings.rejected, (state, action) => {
        state.rankings.loading = false;
        state.rankings.error = action.payload as string;
      });

    // User Ranking
    builder
      .addCase(fetchUserRanking.pending, (state) => {
        state.rankings.loading = true;
        state.rankings.error = null;
      })
      .addCase(fetchUserRanking.fulfilled, (state, action) => {
        state.rankings.loading = false;
        state.rankings.userRanking = action.payload;
      })
      .addCase(fetchUserRanking.rejected, (state, action) => {
        state.rankings.loading = false;
        state.rankings.error = action.payload as string;
      });

    // Creator Revenue
    builder
      .addCase(fetchCreatorRevenue.pending, (state) => {
        state.revenue.loading = true;
        state.revenue.error = null;
      })
      .addCase(fetchCreatorRevenue.fulfilled, (state, action) => {
        state.revenue.loading = false;
        state.revenue.data = action.payload.revenues;
      })
      .addCase(fetchCreatorRevenue.rejected, (state, action) => {
        state.revenue.loading = false;
        state.revenue.error = action.payload as string;
      });

    // Revenue Summary
    builder
      .addCase(fetchRevenueSummary.pending, (state) => {
        state.revenue.loading = true;
        state.revenue.error = null;
      })
      .addCase(fetchRevenueSummary.fulfilled, (state, action) => {
        state.revenue.loading = false;
        state.revenue.summary = action.payload;
      })
      .addCase(fetchRevenueSummary.rejected, (state, action) => {
        state.revenue.loading = false;
        state.revenue.error = action.payload as string;
      });

    // Request Withdrawal
    builder
      .addCase(requestWithdrawal.pending, (state) => {
        state.revenue.loading = true;
        state.revenue.error = null;
      })
      .addCase(requestWithdrawal.fulfilled, (state) => {
        state.revenue.loading = false;
      })
      .addCase(requestWithdrawal.rejected, (state, action) => {
        state.revenue.loading = false;
        state.revenue.error = action.payload as string;
      });
  }
});

// Export actions and reducer
export const { resetStreamState, addStreamGift, updateViewerCount, updateStreamRankings } = livestreamSlice.actions;
export default livestreamSlice.reducer;
