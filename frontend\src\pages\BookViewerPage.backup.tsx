import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import { RootState } from '../store';
import {
  fetchBookById,
  fetchBookChapters,
  fetchChapterById,
  fetchSectionById,
  fetchSubsectionsBySection,
  fetchSubsectionById,
  saveReadingProgress,
  fetchBookmarks,
  addBookmark,
  deleteBookmark,
} from '../features/books/booksSlice';
import { renderMarkdown } from '../utils/markdownRenderer';

const BookViewerContainer = styled.div`
  display: flex;
  flex-direction: column;

  @media (min-width: 992px) {
    flex-direction: row;
    min-height: calc(100vh - 200px);
  }
`;

const Sidebar = styled.div`
  width: 100%;
  background-color: #f8f9fa;
  border-right: 1px solid #ddd;
  overflow-y: auto;

  @media (min-width: 992px) {
    width: 300px;
    max-height: calc(100vh - 200px);
  }
`;

const BookInfo = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #ddd;
`;

const BookTitle = styled.h1`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const BookAuthor = styled.p`
  color: #666;
  margin-bottom: 1rem;
`;

const ChapterList = styled.div`
  padding: 1rem 0;
`;

const ChapterItem = styled.div<{ isActive: boolean }>`
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  background-color: ${(props) => (props.isActive ? '#e6e6e6' : 'transparent')};
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};

  &:hover {
    background-color: ${(props) => (props.isActive ? '#e6e6e6' : '#f0f0f0')};
  }
`;

const SectionList = styled.div`
  padding-left: 1.5rem;
`;

const SectionItem = styled.div<{ isActive: boolean }>`
  padding: 0.5rem 1.5rem;
  cursor: pointer;
  color: ${(props) => (props.isActive ? '#16213e' : '#333')};
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};

  &:hover {
    background-color: #f0f0f0;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
`;

const SectionContent = styled.div`
  line-height: 1.8;
  font-size: 1.1rem;

  p {
    margin-bottom: 1.5rem;
  }

  h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
  }

  li {
    margin-bottom: 0.5rem;
  }
`;

const NavigationButtons = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 3rem;
  padding-top: 1rem;
  border-top: 1px solid #ddd;
`;

const NavButton = styled.button<{ disabled: boolean }>`
  background-color: ${(props) => (props.disabled ? '#ccc' : '#16213e')};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};

  &:hover {
    background-color: ${(props) => (props.disabled ? '#ccc' : '#0f3460')};
  }
`;

const BookmarkButton = styled.button<{ isBookmarked: boolean }>`
  background-color: transparent;
  border: none;
  color: ${(props) => (props.isBookmarked ? '#e94560' : '#16213e')};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;

  span {
    font-size: 1rem;
    margin-left: 0.5rem;
  }
`;

const BookViewerPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const bookId = searchParams.get('book') || '1';
  const [expandedChapters, setExpandedChapters] = useState<Record<string, boolean>>({});

  const dispatch = useDispatch();
  const {
    currentBook,
    chapters,
    currentChapter,
    currentSection,
    currentSubsections,
    currentSubsection,
    bookmarks,
    isLoading,
    error,
  } = useSelector((state: RootState) => state.books);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Fetch book data when component mounts or bookId changes
    dispatch(fetchBookById(bookId));
    dispatch(fetchBookChapters(bookId));

    // If user is authenticated, fetch bookmarks
    if (isAuthenticated) {
      dispatch(fetchBookmarks(bookId));
    }
  }, [dispatch, bookId, isAuthenticated]);

  // Toggle chapter expansion
  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) => ({
      ...prev,
      [chapterId]: !prev[chapterId],
    }));
  };

  // Load chapter
  const handleChapterSelect = (chapterId: string) => {
    dispatch(fetchChapterById(chapterId));
    toggleChapter(chapterId);

    // If chapter has sections, load the first section
    const chapter = chapters.find((c) => c.id === chapterId);
    if (chapter && chapter.sections.length > 0) {
      dispatch(fetchSectionById(chapter.sections[0].id));
    }
  };

  // Load section
  const handleSectionSelect = (sectionId: string) => {
    dispatch(fetchSectionById(sectionId));

    // Save reading progress if authenticated
    if (isAuthenticated) {
      dispatch(saveReadingProgress({ bookId, sectionId }));
    }

    // If section has subsections, load them
    const section = currentChapter?.sections.find(s => s.id === sectionId);
    if (section?.has_subsections) {
      dispatch(fetchSubsectionsBySection(sectionId));
    }
  };

  // Load subsection
  const handleSubsectionSelect = (subsectionId: string) => {
    dispatch(fetchSubsectionById(subsectionId));

    // Save reading progress if authenticated
    if (isAuthenticated) {
      dispatch(saveReadingProgress({ bookId, sectionId: currentSection?.id || '' }));
    }
  };

  // Navigate to previous section
  const handlePrevSection = () => {
    if (currentSection?.prev_section_id) {
      dispatch(fetchSectionById(currentSection.prev_section_id));

      // Save reading progress if authenticated
      if (isAuthenticated) {
        dispatch(saveReadingProgress({ bookId, sectionId: currentSection.prev_section_id }));
      }
    }
  };

  // Navigate to next section
  const handleNextSection = () => {
    if (currentSection?.next_section_id) {
      dispatch(fetchSectionById(currentSection.next_section_id));

      // Save reading progress if authenticated
      if (isAuthenticated) {
        dispatch(saveReadingProgress({ bookId, sectionId: currentSection.next_section_id }));
      }
    }
  };

  // Check if current section is bookmarked
  const isBookmarked = () => {
    return bookmarks.some((bookmark) => bookmark.section_id === currentSection?.id);
  };

  // Toggle bookmark
  const toggleBookmark = () => {
    if (!isAuthenticated || !currentSection) return;

    const bookmarked = isBookmarked();

    if (bookmarked) {
      // Find bookmark ID and delete it
      const bookmark = bookmarks.find((b) => b.section_id === currentSection.id);
      if (bookmark) {
        dispatch(deleteBookmark(bookmark.id));
      }
    } else {
      // Add new bookmark
      dispatch(addBookmark({ bookId, sectionId: currentSection.id }));
    }
  };

  if (isLoading && !currentBook) {
    return <div>Loading book...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!currentBook) {
    return <div>Book not found</div>;
  }

  return (
    <BookViewerContainer>
      <Sidebar>
        <BookInfo>
          <BookTitle>{currentBook.title}</BookTitle>
          <BookAuthor>By {currentBook.author}</BookAuthor>
        </BookInfo>

        <ChapterList>
          {chapters.map((chapter) => (
            <div key={chapter.id}>
              <ChapterItem
                isActive={currentChapter?.id === chapter.id}
                onClick={() => handleChapterSelect(chapter.id)}
              >
                {chapter.title}
              </ChapterItem>

              {(expandedChapters[chapter.id] || currentChapter?.id === chapter.id) && (
                <SectionList>
                  {chapter.sections.map((section) => (
                    <React.Fragment key={section.id}>
                      <SectionItem
                        isActive={currentSection?.id === section.id}
                        onClick={() => handleSectionSelect(section.id)}
                      >
                        {section.title}
                      </SectionItem>

                      {/* Display subsections if this section has them */}
                      {section.has_subsections && currentSection?.id === section.id && (
                        <div style={{ paddingLeft: '1.5rem' }}>
                          {currentSubsections.length > 0 ? (
                            currentSubsections.map((subsection) => (
                              <div
                                key={subsection.id}
                                onClick={() => handleSubsectionSelect(subsection.id)}
                                style={{
                                  padding: '0.4rem 0.5rem',
                                  fontSize: '0.9rem',
                                  cursor: 'pointer',
                                  backgroundColor: currentSubsection?.id === subsection.id ? '#f0f0f0' : 'transparent',
                                  fontWeight: currentSubsection?.id === subsection.id ? 'bold' : 'normal',
                                }}
                              >
                                {subsection.title}
                              </div>
                            ))
                          ) : (
                            <div style={{ fontSize: '0.9rem', color: '#666', padding: '0.5rem 0' }}>
                              Loading subsections...
                            </div>
                          )}
                        </div>
                      )}
                    </React.Fragment>
                  ))}
                </SectionList>
              )}
            </div>
          ))}
        </ChapterList>
      </Sidebar>

      <ContentArea>
        {currentSubsection ? (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>
                  {currentSection?.title}
                </div>
                <SectionTitle>{currentSubsection.title}</SectionTitle>
              </div>

              {isAuthenticated && (
                <BookmarkButton
                  isBookmarked={isBookmarked()}
                  onClick={toggleBookmark}
                  title={isBookmarked() ? 'Remove bookmark' : 'Add bookmark'}
                >
                  {isBookmarked() ? '★' : '☆'}
                  <span>{isBookmarked() ? 'Bookmarked' : 'Bookmark'}</span>
                </BookmarkButton>
              )}
            </div>

            <SectionContent
              dangerouslySetInnerHTML={{ __html: renderMarkdown(currentSubsection.content || '') }}
            />

            <NavigationButtons>
              <NavButton
                onClick={() => currentSubsection.prev_subsection_id && handleSubsectionSelect(currentSubsection.prev_subsection_id)}
                disabled={!currentSubsection.prev_subsection_id}
              >
                Previous
              </NavButton>

              <NavButton
                onClick={() => currentSubsection.next_subsection_id && handleSubsectionSelect(currentSubsection.next_subsection_id)}
                disabled={!currentSubsection.next_subsection_id}
              >
                Next
              </NavButton>
            </NavigationButtons>
          </>
        ) : currentSection ? (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <SectionTitle>{currentSection.title}</SectionTitle>

              {isAuthenticated && (
                <BookmarkButton
                  isBookmarked={isBookmarked()}
                  onClick={toggleBookmark}
                  title={isBookmarked() ? 'Remove bookmark' : 'Add bookmark'}
                >
                  {isBookmarked() ? '★' : '☆'}
                  <span>{isBookmarked() ? 'Bookmarked' : 'Bookmark'}</span>
                </BookmarkButton>
              )}
            </div>

            {currentSection.has_subsections ? (
              <div>
                <p>This section contains subsections. Please select a subsection from the sidebar to view its content.</p>
                {currentSubsections.length > 0 && (
                  <div style={{ marginTop: '1rem' }}>
                    <h3>Available Subsections:</h3>
                    <ul>
                      {currentSubsections.map(subsection => (
                        <li key={subsection.id} style={{ margin: '0.5rem 0' }}>
                          <a
                            href="#"
                            onClick={(e) => { e.preventDefault(); handleSubsectionSelect(subsection.id); }}
                            style={{ color: '#16213e', textDecoration: 'none' }}
                          >
                            {subsection.title}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <SectionContent
                dangerouslySetInnerHTML={{ __html: renderMarkdown(currentSection.content || '') }}
              />
            )}

            <NavigationButtons>
              <NavButton
                onClick={handlePrevSection}
                disabled={!currentSection.prev_section_id}
              >
                Previous
              </NavButton>

              <NavButton
                onClick={handleNextSection}
                disabled={!currentSection.next_section_id}
              >
                Next
              </NavButton>
            </NavigationButtons>
          </>
        ) : (
          <div>
            <h2>Welcome to {currentBook.title}</h2>
            <p>Select a chapter and section from the sidebar to start reading.</p>
          </div>
        )}
      </ContentArea>
    </BookViewerContainer>
  );
};

export default BookViewerPage;
