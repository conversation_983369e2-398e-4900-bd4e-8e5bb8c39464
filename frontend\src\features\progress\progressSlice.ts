import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import axios from 'axios';

// Define types for the progress state
interface Activity {
  id: number;
  type: string;
  name: string;
  progress: number;
  date: string;
}

interface UserProgress {
  overallCompletion: number;
  pointsEarned: number;
  streak: number;
  level: number;
  recentActivities: Activity[];
}

interface Milestone {
  id: number;
  name: string;
  description: string;
  completed: boolean;
  date?: string;
  progress?: number;
  icon: string;
}

interface Achievement {
  id: number;
  name: string;
  description: string;
  earned: boolean;
  date?: string;
  progress?: number;
  icon: string;
}

interface HistoricalData {
  month: string;
  progress: number;
  activities: number;
}

interface SkillData {
  name: string;
  value: number;
}

interface ProgressState {
  userProgress: UserProgress | null;
  milestones: Milestone[];
  achievements: Achievement[];
  historicalData: HistoricalData[];
  skillsData: SkillData[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: ProgressState = {
  userProgress: null,
  milestones: [],
  achievements: [],
  historicalData: [],
  skillsData: [],
  loading: false,
  error: null
};

// Async thunks for API calls
export const fetchUserProgress = createAsyncThunk(
  'progress/fetchUserProgress',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/progress/user');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchMilestones = createAsyncThunk(
  'progress/fetchMilestones',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/progress/milestones');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchAchievements = createAsyncThunk(
  'progress/fetchAchievements',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/progress/achievements');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchHistoricalData = createAsyncThunk(
  'progress/fetchHistoricalData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/progress/history');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const fetchSkillsData = createAsyncThunk(
  'progress/fetchSkillsData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/api/progress/skills');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Create the progress slice
const progressSlice = createSlice({
  name: 'progress',
  initialState,
  reducers: {
    resetProgress: (state) => {
      state.error = null;
    },
    updateMilestoneProgress: (state, action: PayloadAction<{ id: number, progress: number }>) => {
      const { id, progress } = action.payload;
      const milestone = state.milestones.find(m => m.id === id);
      if (milestone) {
        milestone.progress = progress;
        if (progress >= 100) {
          milestone.completed = true;
          milestone.date = new Date().toISOString().split('T')[0];
        }
      }
    },
    updateAchievementProgress: (state, action: PayloadAction<{ id: number, progress: number }>) => {
      const { id, progress } = action.payload;
      const achievement = state.achievements.find(a => a.id === id);
      if (achievement) {
        achievement.progress = progress;
        if (progress >= 100) {
          achievement.earned = true;
          achievement.date = new Date().toISOString().split('T')[0];
        }
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // User Progress
      .addCase(fetchUserProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.userProgress = action.payload;
      })
      .addCase(fetchUserProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch user progress';
      })
      
      // Milestones
      .addCase(fetchMilestones.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMilestones.fulfilled, (state, action) => {
        state.loading = false;
        state.milestones = action.payload;
      })
      .addCase(fetchMilestones.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch milestones';
      })
      
      // Achievements
      .addCase(fetchAchievements.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAchievements.fulfilled, (state, action) => {
        state.loading = false;
        state.achievements = action.payload;
      })
      .addCase(fetchAchievements.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch achievements';
      })
      
      // Historical Data
      .addCase(fetchHistoricalData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHistoricalData.fulfilled, (state, action) => {
        state.loading = false;
        state.historicalData = action.payload;
      })
      .addCase(fetchHistoricalData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch historical data';
      })
      
      // Skills Data
      .addCase(fetchSkillsData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSkillsData.fulfilled, (state, action) => {
        state.loading = false;
        state.skillsData = action.payload;
      })
      .addCase(fetchSkillsData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch skills data';
      });
  }
});

// Export actions
export const { resetProgress, updateMilestoneProgress, updateAchievementProgress } = progressSlice.actions;

// Export selectors
export const selectUserProgress = (state: RootState) => state.progress.userProgress;
export const selectMilestones = (state: RootState) => state.progress.milestones;
export const selectAchievements = (state: RootState) => state.progress.achievements;
export const selectHistoricalData = (state: RootState) => state.progress.historicalData;
export const selectSkillsData = (state: RootState) => state.progress.skillsData;
export const selectLoading = (state: RootState) => state.progress.loading;
export const selectError = (state: RootState) => state.progress.error;

// Export reducer
export default progressSlice.reducer;
