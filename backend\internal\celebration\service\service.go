package service

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	"github.com/gosimple/slug"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/celebration/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/celebration/repository"
)

// ValidationError represents an error during input validation
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("%s: %s", e.Field, e.Message)
}

// CelebrationService handles business logic for the Celebrate Nigeria feature
type CelebrationService struct {
	repo *repository.CelebrationRepository
}

// NewCelebrationService creates a new service instance
func NewCelebrationService(repo *repository.CelebrationRepository) *CelebrationService {
	return &CelebrationService{
		repo: repo,
	}
}

// GetEntryBySlug retrieves an entry by its slug
func (s *CelebrationService) GetEntryBySlug(ctx context.Context, slug string) (*models.CelebrationEntry, error) {
	return s.repo.FindEntryBySlug(ctx, slug)
}

// GetPersonBySlug retrieves a person entry by its slug
func (s *CelebrationService) GetPersonBySlug(ctx context.Context, slug string) (*models.PersonEntry, error) {
	return s.repo.FindPersonBySlug(ctx, slug)
}

// GetPlaceBySlug retrieves a place entry by its slug
func (s *CelebrationService) GetPlaceBySlug(ctx context.Context, slug string) (*models.PlaceEntry, error) {
	return s.repo.FindPlaceBySlug(ctx, slug)
}

// GetEventBySlug retrieves an event entry by its slug
func (s *CelebrationService) GetEventBySlug(ctx context.Context, slug string) (*models.EventEntry, error) {
	return s.repo.FindEventBySlug(ctx, slug)
}

// ListCategorizedEntries retrieves entries by category slug
func (s *CelebrationService) ListCategorizedEntries(ctx context.Context, categorySlug, entryType string, page, pageSize int) ([]models.CelebrationEntry, int64, error) {
	// Find the category by slug
	category, err := s.repo.FindCategoryBySlug(ctx, categorySlug)
	if err != nil {
		return nil, 0, err
	}
	if category == nil {
		return nil, 0, fmt.Errorf("category not found")
	}

	// Calculate offset
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// Get entries for this category
	return s.repo.ListEntries(ctx, entryType, &category.ID, pageSize, offset)
}

// ListEntries retrieves a paginated list of entries
func (s *CelebrationService) ListEntries(ctx context.Context, entryType string, page, pageSize int) ([]models.CelebrationEntry, int64, error) {
	// Calculate offset
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	return s.repo.ListEntries(ctx, entryType, nil, pageSize, offset)
}

// ListFeaturedEntries retrieves featured entries
func (s *CelebrationService) ListFeaturedEntries(ctx context.Context, entryType string, limit int) ([]models.CelebrationEntry, error) {
	return s.repo.ListFeaturedEntries(ctx, entryType, limit)
}

// ListCategories retrieves all categories or subcategories
func (s *CelebrationService) ListCategories(ctx context.Context, parentSlug string) ([]models.Category, error) {
	var parentID *int64

	if parentSlug != "" {
		// Find the parent category
		parent, err := s.repo.FindCategoryBySlug(ctx, parentSlug)
		if err != nil {
			return nil, err
		}
		if parent == nil {
			return nil, fmt.Errorf("parent category not found")
		}
		parentID = &parent.ID
	}

	return s.repo.ListCategories(ctx, parentID)
}

// GetCategoryBySlug retrieves a category by its slug
func (s *CelebrationService) GetCategoryBySlug(ctx context.Context, slug string) (*models.Category, error) {
	return s.repo.FindCategoryBySlug(ctx, slug)
}

// ListEntryComments retrieves comments for an entry
func (s *CelebrationService) ListEntryComments(ctx context.Context, entryID int64, page, pageSize int) ([]models.EntryComment, int64, error) {
	// Calculate offset
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	return s.repo.ListEntryComments(ctx, entryID, pageSize, offset)
}

// CreatePersonEntry creates a new person entry
func (s *CelebrationService) CreatePersonEntry(ctx context.Context, entry *models.PersonEntry, categoryIDs []int64, userID int64) error {
	// Validate required fields
	if entry.Title == "" {
		return ValidationError{Field: "title", Message: "Title is required"}
	}
	if entry.ShortDesc == "" {
		return ValidationError{Field: "shortDesc", Message: "Short description is required"}
	}
	if len(categoryIDs) == 0 {
		return ValidationError{Field: "categories", Message: "At least one category is required"}
	}

	// Generate slug if not provided
	if entry.Slug == "" {
		entry.Slug = slug.Make(entry.Title)
	} else {
		// Validate slug format
		slugRegex := regexp.MustCompile("^[a-z0-9]+(?:-[a-z0-9]+)*$")
		if !slugRegex.MatchString(entry.Slug) {
			return ValidationError{
				Field:   "slug",
				Message: "Slug must contain only lowercase letters, numbers, and hyphens",
			}
		}

		// Check if slug is already in use
		existing, err := s.repo.FindEntryBySlug(ctx, entry.Slug)
		if err != nil {
			return err
		}
		if existing != nil {
			return ValidationError{Field: "slug", Message: "This slug is already in use"}
		}
	}

	// Set entry type
	entry.EntryType = "person"

	// Set default status to pending for user submissions
	if userID != 0 {
		entry.Status = "pending"
		entry.SubmittedBy = &userID
	}

	// Set creation time
	now := time.Now()
	entry.CreatedAt = now
	entry.UpdatedAt = now

	// Add categories
	categories := make([]models.Category, 0, len(categoryIDs))
	for _, catID := range categoryIDs {
		category, err := s.repo.FindCategoryByID(ctx, catID)
		if err != nil {
			return err
		}
		if category == nil {
			return fmt.Errorf("category with ID %d not found", catID)
		}
		categories = append(categories, *category)
	}
	entry.Categories = categories

	// Create the entry
	return s.repo.CreatePersonEntry(ctx, entry)
}

// CreatePlaceEntry creates a new place entry
func (s *CelebrationService) CreatePlaceEntry(ctx context.Context, entry *models.PlaceEntry, categoryIDs []int64, userID int64) error {
	// Validate required fields
	if entry.Title == "" {
		return ValidationError{Field: "title", Message: "Title is required"}
	}
	if entry.ShortDesc == "" {
		return ValidationError{Field: "shortDesc", Message: "Short description is required"}
	}
	if len(categoryIDs) == 0 {
		return ValidationError{Field: "categories", Message: "At least one category is required"}
	}
	if entry.PlaceType == nil || *entry.PlaceType == "" {
		return ValidationError{Field: "placeType", Message: "Place type is required"}
	}

	// Generate slug if not provided
	if entry.Slug == "" {
		entry.Slug = slug.Make(entry.Title)
	} else {
		// Validate slug format
		slugRegex := regexp.MustCompile("^[a-z0-9]+(?:-[a-z0-9]+)*$")
		if !slugRegex.MatchString(entry.Slug) {
			return ValidationError{
				Field:   "slug",
				Message: "Slug must contain only lowercase letters, numbers, and hyphens",
			}
		}

		// Check if slug is already in use
		existing, err := s.repo.FindEntryBySlug(ctx, entry.Slug)
		if err != nil {
			return err
		}
		if existing != nil {
			return ValidationError{Field: "slug", Message: "This slug is already in use"}
		}
	}

	// Set entry type
	entry.EntryType = "place"

	// Set default status to pending for user submissions
	if userID != 0 {
		entry.Status = "pending"
		entry.SubmittedBy = &userID
	}

	// Set creation time
	now := time.Now()
	entry.CreatedAt = now
	entry.UpdatedAt = now

	// Add categories
	categories := make([]models.Category, 0, len(categoryIDs))
	for _, catID := range categoryIDs {
		category, err := s.repo.FindCategoryByID(ctx, catID)
		if err != nil {
			return err
		}
		if category == nil {
			return fmt.Errorf("category with ID %d not found", catID)
		}
		categories = append(categories, *category)
	}
	entry.Categories = categories

	// Create the entry
	return s.repo.CreatePlaceEntry(ctx, entry)
}

// CreateEventEntry creates a new event entry
func (s *CelebrationService) CreateEventEntry(ctx context.Context, entry *models.EventEntry, categoryIDs []int64, userID int64) error {
	// Validate required fields
	if entry.Title == "" {
		return ValidationError{Field: "title", Message: "Title is required"}
	}
	if entry.ShortDesc == "" {
		return ValidationError{Field: "shortDesc", Message: "Short description is required"}
	}
	if len(categoryIDs) == 0 {
		return ValidationError{Field: "categories", Message: "At least one category is required"}
	}
	if entry.EventType == nil || *entry.EventType == "" {
		return ValidationError{Field: "eventType", Message: "Event type is required"}
	}

	// Generate slug if not provided
	if entry.Slug == "" {
		entry.Slug = slug.Make(entry.Title)
	} else {
		// Validate slug format
		slugRegex := regexp.MustCompile("^[a-z0-9]+(?:-[a-z0-9]+)*$")
		if !slugRegex.MatchString(entry.Slug) {
			return ValidationError{
				Field:   "slug",
				Message: "Slug must contain only lowercase letters, numbers, and hyphens",
			}
		}

		// Check if slug is already in use
		existing, err := s.repo.FindEntryBySlug(ctx, entry.Slug)
		if err != nil {
			return err
		}
		if existing != nil {
			return ValidationError{Field: "slug", Message: "This slug is already in use"}
		}
	}

	// Set entry type
	entry.EntryType = "event"

	// Set default status to pending for user submissions
	if userID != 0 {
		entry.Status = "pending"
		entry.SubmittedBy = &userID
	}

	// Set creation time
	now := time.Now()
	entry.CreatedAt = now
	entry.UpdatedAt = now

	// Add categories
	categories := make([]models.Category, 0, len(categoryIDs))
	for _, catID := range categoryIDs {
		category, err := s.repo.FindCategoryByID(ctx, catID)
		if err != nil {
			return err
		}
		if category == nil {
			return fmt.Errorf("category with ID %d not found", catID)
		}
		categories = append(categories, *category)
	}
	entry.Categories = categories

	// Create the entry
	return s.repo.CreateEventEntry(ctx, entry)
}

// CreateEntryComment adds a comment to an entry
func (s *CelebrationService) CreateEntryComment(ctx context.Context, entryID, userID int64, content string, parentID *int64) (*models.EntryComment, error) {
	// Validate
	if content == "" {
		return nil, ValidationError{Field: "content", Message: "Comment content is required"}
	}

	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		return nil, fmt.Errorf("entry not found")
	}

	// Check if parent comment exists if provided
	if parentID != nil {
		parentComments, count, err := s.repo.ListEntryComments(ctx, entryID, 1, 0)
		if err != nil {
			return nil, err
		}
		if count == 0 || len(parentComments) == 0 {
			return nil, fmt.Errorf("parent comment not found")
		}
	}

	// Create comment
	comment := &models.EntryComment{
		CelebrationEntryID: entryID,
		UserID:             userID,
		Content:            content,
		Status:             "approved", // Auto-approve for now, can add moderation later
		ParentID:           parentID,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	if err := s.repo.CreateEntryComment(ctx, comment); err != nil {
		return nil, err
	}

	return comment, nil
}

// CreateEntryMedia adds media to an entry
func (s *CelebrationService) CreateEntryMedia(ctx context.Context, entryID int64, mediaType, url, caption string) (*models.EntryMedia, error) {
	// Validate
	if url == "" {
		return nil, ValidationError{Field: "url", Message: "Media URL is required"}
	}
	if !isValidMediaType(mediaType) {
		return nil, ValidationError{Field: "mediaType", Message: "Invalid media type"}
	}

	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		return nil, fmt.Errorf("entry not found")
	}

	// Create media
	var captionPtr *string
	if caption != "" {
		captionPtr = &caption
	}

	media := &models.EntryMedia{
		CelebrationEntryID: entryID,
		MediaType:          mediaType,
		URL:                url,
		Caption:            captionPtr,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	if err := s.repo.CreateEntryMedia(ctx, media); err != nil {
		return nil, err
	}

	return media, nil
}

// isValidMediaType checks if the provided media type is valid
func isValidMediaType(mediaType string) bool {
	validTypes := map[string]bool{
		"image":    true,
		"video":    true,
		"audio":    true,
		"document": true,
	}
	return validTypes[mediaType]
}

// CreateEntryFact adds a fact to an entry
func (s *CelebrationService) CreateEntryFact(ctx context.Context, entryID int64, label, value string, sortOrder int) (*models.EntryFact, error) {
	// Validate
	if label == "" {
		return nil, ValidationError{Field: "label", Message: "Fact label is required"}
	}
	if value == "" {
		return nil, ValidationError{Field: "value", Message: "Fact value is required"}
	}

	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		return nil, fmt.Errorf("entry not found")
	}

	// Create fact
	fact := &models.EntryFact{
		CelebrationEntryID: entryID,
		Label:              label,
		Value:              value,
		SortOrder:          sortOrder,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	if err := s.repo.CreateEntryFact(ctx, fact); err != nil {
		return nil, err
	}

	return fact, nil
}

// CreateCategory creates a new category
func (s *CelebrationService) CreateCategory(ctx context.Context, name, description, slug string, parentID *int64, iconSVG string) (*models.Category, error) {
	// Convert iconSVG to *string
	var iconSVGPtr *string
	if iconSVG != "" {
		iconSVGPtr = &iconSVG
	}
	// Validate
	if name == "" {
		return nil, ValidationError{Field: "name", Message: "Category name is required"}
	}

	// Generate slug if not provided
	if slug == "" {
		slug = GenerateSlug(name)
	} else {
		// Validate slug format
		slugRegex := regexp.MustCompile("^[a-z0-9]+(?:-[a-z0-9]+)*$")
		if !slugRegex.MatchString(slug) {
			return nil, ValidationError{
				Field:   "slug",
				Message: "Slug must contain only lowercase letters, numbers, and hyphens",
			}
		}
	}

	// Check if slug is already in use
	existingCategory, err := s.repo.FindCategoryBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	if existingCategory != nil {
		return nil, ValidationError{Field: "slug", Message: "This slug is already in use"}
	}

	// Check if parent exists if provided
	if parentID != nil {
		parent, err := s.repo.FindCategoryByID(ctx, *parentID)
		if err != nil {
			return nil, err
		}
		if parent == nil {
			return nil, fmt.Errorf("parent category not found")
		}
	}

	// Create category
	var descriptionPtr *string
	if description != "" {
		descriptionPtr = &description
	}

	category := &models.Category{
		Name:        name,
		Description: descriptionPtr,
		Slug:        slug,
		ParentID:    parentID,
		IconSVG:     iconSVGPtr,
		Visible:     true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.repo.CreateCategory(ctx, category); err != nil {
		return nil, err
	}

	return category, nil
}

// GenerateSlug creates a URL-friendly slug from a string
func GenerateSlug(s string) string {
	return slug.Make(s)
}

// UpdateCategory updates an existing category
func (s *CelebrationService) UpdateCategory(ctx context.Context, id int64, name, description, slug string, parentID *int64, iconSVG string, visible bool) (*models.Category, error) {
	// Convert iconSVG to *string
	var iconSVGPtr *string
	if iconSVG != "" {
		iconSVGPtr = &iconSVG
	}
	// Get existing category
	category, err := s.repo.FindCategoryByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if category == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Update fields if provided
	if name != "" {
		category.Name = name
	}

	if description != "" {
		descriptionPtr := &description
		category.Description = descriptionPtr
	}

	if slug != "" && slug != category.Slug {
		// Validate slug format
		slugRegex := regexp.MustCompile("^[a-z0-9]+(?:-[a-z0-9]+)*$")
		if !slugRegex.MatchString(slug) {
			return nil, ValidationError{
				Field:   "slug",
				Message: "Slug must contain only lowercase letters, numbers, and hyphens",
			}
		}

		// Check if slug is already in use
		existingCategory, err := s.repo.FindCategoryBySlug(ctx, slug)
		if err != nil {
			return nil, err
		}
		if existingCategory != nil && existingCategory.ID != id {
			return nil, ValidationError{Field: "slug", Message: "This slug is already in use"}
		}

		category.Slug = slug
	}

	if iconSVG != "" {
		category.IconSVG = iconSVGPtr
	}

	if parentID != nil && *parentID != 0 {
		// Check if parent exists
		parent, err := s.repo.FindCategoryByID(ctx, *parentID)
		if err != nil {
			return nil, err
		}
		if parent == nil {
			return nil, fmt.Errorf("parent category not found")
		}

		// Prevent category from being its own parent
		if *parentID == id {
			return nil, ValidationError{Field: "parentID", Message: "Category cannot be its own parent"}
		}

		category.ParentID = parentID
	} else if parentID != nil && *parentID == 0 {
		// If parent ID is explicitly set to 0, make it a top-level category
		category.ParentID = nil
	}

	// Always update visibility
	category.Visible = visible

	// Update timestamp
	category.UpdatedAt = time.Now()

	// Save changes
	if err := s.repo.UpdateCategory(ctx, category); err != nil {
		return nil, err
	}

	return category, nil
}

// DeleteCategory deletes a category
func (s *CelebrationService) DeleteCategory(ctx context.Context, id int64) error {
	// Get existing category
	category, err := s.repo.FindCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return fmt.Errorf("category not found")
	}

	// Delete category
	return s.repo.DeleteCategory(ctx, id)
}

// SearchEntriesParams contains parameters for the SearchEntries function
type SearchEntriesParams struct {
	Query        string
	EntryType    string
	CategorySlug string
	SortBy       string
	Page         int
	PageSize     int
}

// SearchEntries performs a search on entries with advanced filtering
func (s *CelebrationService) SearchEntries(ctx context.Context, params SearchEntriesParams) ([]models.CelebrationEntry, int64, int, error) {
	// Default values
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.SortBy == "" {
		params.SortBy = "relevance"
	}

	// Calculate offset
	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}

	// Clean and normalize the query if provided
	if params.Query != "" {
		params.Query = strings.TrimSpace(params.Query)
	}

	// Prepare repository options
	options := repository.SearchEntriesOptions{
		Query:        params.Query,
		EntryType:    params.EntryType,
		CategorySlug: params.CategorySlug,
		SortBy:       params.SortBy,
		Limit:        params.PageSize,
		Offset:       offset,
	}

	// Perform search
	entries, totalCount, err := s.repo.SearchEntries(ctx, options)
	if err != nil {
		return nil, 0, 0, err
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(totalCount) / float64(params.PageSize)))
	if totalPages < 1 {
		totalPages = 1
	}

	return entries, totalCount, totalPages, nil
}

// CreateEntrySubmission creates a new submission for an entry
func (s *CelebrationService) CreateEntrySubmission(ctx context.Context, userID int64, entryType, title, content string, targetEntryID *int64) (*models.EntrySubmission, error) {
	// Validate
	if userID == 0 {
		return nil, ValidationError{Field: "userID", Message: "User ID is required"}
	}
	if title == "" {
		return nil, ValidationError{Field: "title", Message: "Submission title is required"}
	}
	if content == "" {
		return nil, ValidationError{Field: "content", Message: "Submission content is required"}
	}
	if !isValidEntryType(entryType) {
		return nil, ValidationError{Field: "entryType", Message: "Invalid entry type"}
	}

	// If this is an edit to an existing entry, check if it exists
	if targetEntryID != nil {
		entry, err := s.repo.FindEntryByID(ctx, *targetEntryID)
		if err != nil {
			return nil, err
		}
		if entry == nil {
			return nil, fmt.Errorf("target entry not found")
		}
	}

	// Create submission
	submission := &models.EntrySubmission{
		UserID:        userID,
		EntryType:     entryType,
		TargetEntryID: targetEntryID,
		Title:         title,
		Content:       content,
		Status:        "pending",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.repo.CreateSubmission(ctx, submission); err != nil {
		return nil, err
	}

	return submission, nil
}

// isValidEntryType checks if the provided entry type is valid
func isValidEntryType(entryType string) bool {
	validTypes := map[string]bool{
		"person": true,
		"place":  true,
		"event":  true,
	}
	return validTypes[entryType]
}

// ListPendingSubmissions retrieves pending submissions for review
func (s *CelebrationService) ListPendingSubmissions(ctx context.Context, entryType string, page, pageSize int) ([]models.EntrySubmission, int64, error) {
	// Calculate offset
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	return s.repo.ListPendingSubmissions(ctx, entryType, pageSize, offset)
}

// ReviewSubmission updates the status of a submission
func (s *CelebrationService) ReviewSubmission(ctx context.Context, submissionID, adminID int64, status, adminNotes string) error {
	// Validate
	if submissionID == 0 {
		return ValidationError{Field: "submissionID", Message: "Submission ID is required"}
	}
	if adminID == 0 {
		return ValidationError{Field: "adminID", Message: "Admin ID is required"}
	}
	if status != "approved" && status != "rejected" {
		return ValidationError{Field: "status", Message: "Status must be 'approved' or 'rejected'"}
	}

	// TODO: Check if user has admin rights

	// Get the submission
	submissions, _, err := s.repo.ListPendingSubmissions(ctx, "", 1, 0)
	if err != nil {
		return err
	}
	if len(submissions) == 0 {
		return fmt.Errorf("submission not found")
	}

	submission := submissions[0]

	// Update the submission
	submission.Status = status

	// Convert adminNotes string to pointer
	var adminNotesPtr *string
	if adminNotes != "" {
		adminNotesPtr = &adminNotes
	}
	submission.AdminNotes = adminNotesPtr

	submission.ReviewedBy = &adminID
	submission.ReviewedAt = sql.NullTime{Time: time.Now(), Valid: true}
	submission.UpdatedAt = time.Now()

	// Save changes
	if err := s.repo.UpdateSubmission(ctx, &submission); err != nil {
		return err
	}

	// If approved, create the actual entry
	if status == "approved" {
		// TODO: Implement entry creation from submission
		// This would involve parsing the JSON content and creating the entry
	}

	return nil
}

// VoteForSubmission adds a vote to a submission
func (s *CelebrationService) VoteForSubmission(ctx context.Context, submissionID, userID int64, voteType string) error {
	// Validate
	if submissionID == 0 {
		return ValidationError{Field: "submissionID", Message: "Submission ID is required"}
	}
	if userID == 0 {
		return ValidationError{Field: "userID", Message: "User ID is required"}
	}
	if voteType != "support" && voteType != "oppose" {
		return ValidationError{Field: "voteType", Message: "Vote type must be 'support' or 'oppose'"}
	}

	// Submit the vote
	return s.repo.VoteForSubmission(ctx, submissionID, userID, voteType)
}

// VoteForEntry adds or updates a vote for an entry
func (s *CelebrationService) VoteForEntry(ctx context.Context, entryID, userID int64, voteType string) error {
	// Validate
	if entryID == 0 {
		return ValidationError{Field: "entryID", Message: "Entry ID is required"}
	}
	if userID == 0 {
		return ValidationError{Field: "userID", Message: "User ID is required"}
	}
	if voteType != "upvote" && voteType != "downvote" {
		return ValidationError{Field: "voteType", Message: "Vote type must be 'upvote' or 'downvote'"}
	}

	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return err
	}
	if entry == nil {
		return ValidationError{Field: "entryID", Message: "Entry not found"}
	}

	// Only allow voting on published entries
	if entry.Status != "published" {
		return ValidationError{Field: "entryID", Message: "Cannot vote on unpublished entries"}
	}

	// Submit the vote
	return s.repo.VoteForEntry(ctx, entryID, userID, voteType)
}

// GetEntryVoteCounts gets the upvote and downvote counts for an entry
func (s *CelebrationService) GetEntryVoteCounts(ctx context.Context, entryID int64) (upvotes int, downvotes int, err error) {
	// Validate
	if entryID == 0 {
		return 0, 0, ValidationError{Field: "entryID", Message: "Entry ID is required"}
	}

	return s.repo.GetEntryVoteCounts(ctx, entryID)
}

// GetUserVoteForEntry gets a user's vote for an entry
func (s *CelebrationService) GetUserVoteForEntry(ctx context.Context, entryID, userID int64) (string, error) {
	// Validate
	if entryID == 0 {
		return "", ValidationError{Field: "entryID", Message: "Entry ID is required"}
	}
	if userID == 0 {
		return "", ValidationError{Field: "userID", Message: "User ID is required"}
	}

	return s.repo.GetUserVoteForEntry(ctx, entryID, userID)
}

// DeleteVoteForEntry removes a user's vote for an entry
func (s *CelebrationService) DeleteVoteForEntry(ctx context.Context, entryID, userID int64) error {
	// Validate
	if entryID == 0 {
		return ValidationError{Field: "entryID", Message: "Entry ID is required"}
	}
	if userID == 0 {
		return ValidationError{Field: "userID", Message: "User ID is required"}
	}

	return s.repo.DeleteVoteForEntry(ctx, entryID, userID)
}
