package service

import (
	"encoding/json"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/project/models"
	"gorm.io/gorm"
)

// ProjectService handles business logic for projects
type ProjectService struct {
	DB *gorm.DB
}

// NewProjectService creates a new ProjectService
func NewProjectService(db *gorm.DB) *ProjectService {
	return &ProjectService{
		DB: db,
	}
}

// CreateProject creates a new project
func (s *ProjectService) CreateProject(project *models.Project) error {
	return s.DB.Create(project).Error
}

// GetProjects gets projects with optional filtering
func (s *ProjectService) GetProjects(params map[string]interface{}) ([]models.Project, error) {
	var projects []models.Project
	
	query := s.DB
	
	// Apply filters
	if status, ok := params["status"].(string); ok {
		query = query.Where("status = ?", status)
	}
	
	if ownerID, ok := params["owner_id"].(uint64); ok {
		query = query.Where("owner_id = ?", ownerID)
	}
	
	if isPublic, ok := params["is_public"].(bool); ok {
		query = query.Where("is_public = ?", isPublic)
	}
	
	if search, ok := params["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
	
	if location, ok := params["location"].(string); ok && location != "" {
		query = query.Where("location ILIKE ?", "%"+location+"%")
	}
	
	// Execute query
	err := query.Preload("Tags").Find(&projects).Error
	return projects, err
}

// GetProjectByID gets a project by ID
func (s *ProjectService) GetProjectByID(id uint64) (*models.Project, error) {
	var project models.Project
	if err := s.DB.Preload("Tags").Preload("Members").First(&project, id).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

// UpdateProject updates a project
func (s *ProjectService) UpdateProject(project *models.Project) error {
	return s.DB.Save(project).Error
}

// DeleteProject deletes a project
func (s *ProjectService) DeleteProject(id uint64) error {
	return s.DB.Delete(&models.Project{}, id).Error
}

// AddMemberToProject adds a member to a project
func (s *ProjectService) AddMemberToProject(projectID, userID uint64, role string) error {
	member := models.ProjectMember{
		ProjectID: projectID,
		UserID:    userID,
		Role:      role,
		JoinedAt:  time.Now(),
	}
	
	return s.DB.Create(&member).Error
}

// RemoveMemberFromProject removes a member from a project
func (s *ProjectService) RemoveMemberFromProject(projectID, userID uint64) error {
	return s.DB.Where("project_id = ? AND user_id = ?", projectID, userID).
		Delete(&models.ProjectMember{}).Error
}

// UpdateMemberRole updates a member's role in a project
func (s *ProjectService) UpdateMemberRole(projectID, userID uint64, role string) error {
	return s.DB.Model(&models.ProjectMember{}).
		Where("project_id = ? AND user_id = ?", projectID, userID).
		Update("role", role).Error
}

// GetProjectMembers gets all members of a project
func (s *ProjectService) GetProjectMembers(projectID uint64) ([]models.ProjectMember, error) {
	var members []models.ProjectMember
	err := s.DB.Where("project_id = ?", projectID).Find(&members).Error
	return members, err
}

// GetProjectsByMember gets all projects a user is a member of
func (s *ProjectService) GetProjectsByMember(userID uint64) ([]models.Project, error) {
	var projects []models.Project
	
	err := s.DB.Joins("JOIN project_members pm ON pm.project_id = projects.id").
		Where("pm.user_id = ?", userID).
		Preload("Tags").
		Find(&projects).Error
	
	return projects, err
}

// CreateTask creates a new task
func (s *ProjectService) CreateTask(task *models.Task) error {
	return s.DB.Create(task).Error
}

// GetTasksByProject gets tasks for a project with optional filtering
func (s *ProjectService) GetTasksByProject(projectID uint64, params map[string]interface{}) ([]models.Task, error) {
	var tasks []models.Task
	
	query := s.DB.Where("project_id = ?", projectID)
	
	// Apply filters
	if status, ok := params["status"].(string); ok {
		query = query.Where("status = ?", status)
	}
	
	if priority, ok := params["priority"].(string); ok {
		query = query.Where("priority = ?", priority)
	}
	
	if assigneeID, ok := params["assignee_id"].(uint64); ok {
		query = query.Where("assignee_id = ?", assigneeID)
	}
	
	// Execute query
	err := query.Find(&tasks).Error
	return tasks, err
}

// GetTaskByID gets a task by ID
func (s *ProjectService) GetTaskByID(id uint64) (*models.Task, error) {
	var task models.Task
	if err := s.DB.First(&task, id).Error; err != nil {
		return nil, err
	}
	return &task, nil
}

// UpdateTask updates a task
func (s *ProjectService) UpdateTask(task *models.Task) error {
	// If task status is changed to completed, set completed_at timestamp
	if task.Status == models.TaskStatusCompleted && task.CompletedAt == nil {
		now := time.Now()
		task.CompletedAt = &now
	}
	
	return s.DB.Save(task).Error
}

// DeleteTask deletes a task
func (s *ProjectService) DeleteTask(id uint64) error {
	return s.DB.Delete(&models.Task{}, id).Error
}

// AssignTask assigns a task to a user
func (s *ProjectService) AssignTask(taskID, assigneeID uint64) error {
	return s.DB.Model(&models.Task{}).Where("id = ?", taskID).
		Update("assignee_id", assigneeID).Error
}

// CreateProjectUpdate creates a new project update
func (s *ProjectService) CreateProjectUpdate(update *models.ProjectUpdate) error {
	return s.DB.Create(update).Error
}

// GetProjectUpdates gets updates for a project
func (s *ProjectService) GetProjectUpdates(projectID uint64) ([]models.ProjectUpdate, error) {
	var updates []models.ProjectUpdate
	err := s.DB.Where("project_id = ?", projectID).
		Order("created_at DESC").
		Find(&updates).Error
	return updates, err
}

// AddTagToProject adds a tag to a project
func (s *ProjectService) AddTagToProject(projectID uint64, tagName string) error {
	// Begin transaction
	tx := s.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	
	// Rollback transaction on error
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// Find or create tag
	var tag models.ProjectTag
	err := tx.Where("name = ?", tagName).FirstOrCreate(&tag, models.ProjectTag{
		Name: tagName,
	}).Error
	
	if err != nil {
		tx.Rollback()
		return err
	}
	
	// Add association between project and tag
	err = tx.Exec("INSERT INTO project_tag_mappings (project_id, project_tag_id) VALUES (?, ?) ON CONFLICT DO NOTHING", 
		projectID, tag.ID).Error
	
	if err != nil {
		tx.Rollback()
		return err
	}
	
	// Commit transaction
	return tx.Commit().Error
}

// RemoveTagFromProject removes a tag from a project
func (s *ProjectService) RemoveTagFromProject(projectID uint64, tagName string) error {
	var tag models.ProjectTag
	
	// Find tag by name
	if err := s.DB.Where("name = ?", tagName).First(&tag).Error; err != nil {
		return err
	}
	
	// Remove association
	return s.DB.Exec("DELETE FROM project_tag_mappings WHERE project_id = ? AND project_tag_id = ?", 
		projectID, tag.ID).Error
}

// GetProjectsByBookSection gets projects associated with a book section
func (s *ProjectService) GetProjectsByBookSection(bookID, chapterID, sectionID uint64) ([]models.Project, error) {
	var projects []models.Project
	
	// Parse BookSectionIDs JSON array for each project and check if it contains the target section
	// This is a simplified approach - in production you'd use a proper JSON query
	// or a many-to-many mapping table for better performance
	err := s.DB.Where("book_section_ids LIKE ?", 
		"%"+string(bookID)+"-"+string(chapterID)+"-"+string(sectionID)+"%").
		Preload("Tags").
		Find(&projects).Error
	
	return projects, err
}

// AddProjectToBookSection associates a project with a book section
func (s *ProjectService) AddProjectToBookSection(projectID, bookID, chapterID, sectionID uint64) error {
	// Get the project
	var project models.Project
	if err := s.DB.First(&project, projectID).Error; err != nil {
		return err
	}
	
	// Create section identifier
	sectionIdentifier := string(bookID) + "-" + string(chapterID) + "-" + string(sectionID)
	
	// Parse existing book section IDs or create new array
	var sectionIDs []string
	if project.BookSectionIDs != "" {
		if err := json.Unmarshal([]byte(project.BookSectionIDs), &sectionIDs); err != nil {
			return err
		}
	}
	
	// Check if section already exists
	for _, id := range sectionIDs {
		if id == sectionIdentifier {
			return nil // Already associated
		}
	}
	
	// Add new section ID
	sectionIDs = append(sectionIDs, sectionIdentifier)
	
	// Serialize back to JSON
	sectionIDsJSON, err := json.Marshal(sectionIDs)
	if err != nil {
		return err
	}
	
	// Update project
	return s.DB.Model(&models.Project{}).Where("id = ?", projectID).
		Update("book_section_ids", string(sectionIDsJSON)).Error
}