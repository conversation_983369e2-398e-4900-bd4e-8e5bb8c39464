package service

import (
	"encoding/json"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/report/models"
	"gorm.io/gorm"
)

// ReportService handles business logic for reports and templates
type ReportService struct {
	DB *gorm.DB
}

// NewReportService creates a new ReportService
func NewReportService(db *gorm.DB) *ReportService {
	return &ReportService{
		DB: db,
	}
}

// CreateReportTemplate creates a new report template
func (s *ReportService) CreateReportTemplate(template *models.ReportTemplate) error {
	return s.DB.Create(template).Error
}

// GetReportTemplates gets report templates with optional filtering
func (s *ReportService) GetReportTemplates(params map[string]interface{}) ([]models.ReportTemplate, error) {
	var templates []models.ReportTemplate
	
	query := s.DB
	
	// Apply filters
	if reportType, ok := params["report_type"].(string); ok {
		query = query.Where("report_type = ?", reportType)
	}
	
	if creatorID, ok := params["creator_id"].(uint64); ok {
		query = query.Where("creator_id = ?", creatorID)
	}
	
	if isPublic, ok := params["is_public"].(bool); ok {
		query = query.Where("is_public = ?", isPublic)
	}
	
	if search, ok := params["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
	
	// Execute query
	err := query.Find(&templates).Error
	return templates, err
}

// GetReportTemplateByID gets a report template by ID
func (s *ReportService) GetReportTemplateByID(id uint64) (*models.ReportTemplate, error) {
	var template models.ReportTemplate
	if err := s.DB.First(&template, id).Error; err != nil {
		return nil, err
	}
	return &template, nil
}

// UpdateReportTemplate updates a report template
func (s *ReportService) UpdateReportTemplate(template *models.ReportTemplate) error {
	return s.DB.Save(template).Error
}

// DeleteReportTemplate deletes a report template
func (s *ReportService) DeleteReportTemplate(id uint64) error {
	return s.DB.Delete(&models.ReportTemplate{}, id).Error
}

// IncrementTemplateUsageCount increments the usage count for a template
func (s *ReportService) IncrementTemplateUsageCount(id uint64) error {
	return s.DB.Model(&models.ReportTemplate{}).Where("id = ?", id).
		UpdateColumn("usage_count", gorm.Expr("usage_count + ?", 1)).Error
}

// CreateProjectReport creates a new project report
func (s *ReportService) CreateProjectReport(report *models.ProjectReport) error {
	// If a template is used, increment its usage count
	if report.TemplateID != nil {
		if err := s.IncrementTemplateUsageCount(*report.TemplateID); err != nil {
			return err
		}
	}
	
	return s.DB.Create(report).Error
}

// GetProjectReports gets project reports with optional filtering
func (s *ReportService) GetProjectReports(params map[string]interface{}) ([]models.ProjectReport, error) {
	var reports []models.ProjectReport
	
	query := s.DB
	
	// Apply filters
	if projectID, ok := params["project_id"].(uint64); ok {
		query = query.Where("project_id = ?", projectID)
	}
	
	if reportType, ok := params["report_type"].(string); ok {
		query = query.Where("report_type = ?", reportType)
	}
	
	if authorID, ok := params["author_id"].(uint64); ok {
		query = query.Where("author_id = ?", authorID)
	}
	
	if isPublic, ok := params["is_public"].(bool); ok {
		query = query.Where("is_public = ?", isPublic)
	}
	
	if search, ok := params["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
	
	// Execute query
	err := query.Order("created_at DESC").Find(&reports).Error
	return reports, err
}

// GetProjectReportByID gets a project report by ID
func (s *ReportService) GetProjectReportByID(id uint64) (*models.ProjectReport, error) {
	var report models.ProjectReport
	if err := s.DB.First(&report, id).Error; err != nil {
		return nil, err
	}
	return &report, nil
}

// UpdateProjectReport updates a project report
func (s *ReportService) UpdateProjectReport(report *models.ProjectReport) error {
	return s.DB.Save(report).Error
}

// DeleteProjectReport deletes a project report
func (s *ReportService) DeleteProjectReport(id uint64) error {
	return s.DB.Delete(&models.ProjectReport{}, id).Error
}

// PublishProjectReport marks a project report as published
func (s *ReportService) PublishProjectReport(id uint64) error {
	now := time.Now()
	return s.DB.Model(&models.ProjectReport{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"published_at": now,
			"is_public":    true,
		}).Error
}

// IncrementReportViewCount increments the view count for a report
func (s *ReportService) IncrementReportViewCount(id uint64) error {
	return s.DB.Model(&models.ProjectReport{}).Where("id = ?", id).
		UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error
}

// CreateReportFeedback creates new feedback for a report
func (s *ReportService) CreateReportFeedback(feedback *models.ReportFeedback) error {
	return s.DB.Create(feedback).Error
}

// GetReportFeedback gets feedback for a report
func (s *ReportService) GetReportFeedback(reportID uint64) ([]models.ReportFeedback, error) {
	var feedback []models.ReportFeedback
	err := s.DB.Where("report_id = ?", reportID).
		Order("created_at DESC").
		Find(&feedback).Error
	return feedback, err
}

// GetAverageRating gets the average rating for a report
func (s *ReportService) GetAverageRating(reportID uint64) (float64, error) {
	var result struct {
		Average float64
	}
	
	err := s.DB.Model(&models.ReportFeedback{}).
		Select("COALESCE(AVG(rating), 0) as average").
		Where("report_id = ?", reportID).
		Scan(&result).Error
	
	return result.Average, err
}

// GetReportsByBookSection gets reports associated with a book section
func (s *ReportService) GetReportsByBookSection(bookID, chapterID, sectionID uint64) ([]models.ProjectReport, error) {
	var reports []models.ProjectReport
	
	// Parse BookSectionIDs JSON array for each report and check if it contains the target section
	// This is a simplified approach - in production you'd use a proper JSON query
	// or a many-to-many mapping table for better performance
	err := s.DB.Where("book_section_ids LIKE ?", 
		"%"+string(bookID)+"-"+string(chapterID)+"-"+string(sectionID)+"%").
		Order("created_at DESC").
		Find(&reports).Error
	
	return reports, err
}

// AddReportToBookSection associates a report with a book section
func (s *ReportService) AddReportToBookSection(reportID, bookID, chapterID, sectionID uint64) error {
	// Get the report
	var report models.ProjectReport
	if err := s.DB.First(&report, reportID).Error; err != nil {
		return err
	}
	
	// Create section identifier
	sectionIdentifier := string(bookID) + "-" + string(chapterID) + "-" + string(sectionID)
	
	// Parse existing book section IDs or create new array
	var sectionIDs []string
	if report.BookSectionIDs != "" {
		if err := json.Unmarshal([]byte(report.BookSectionIDs), &sectionIDs); err != nil {
			return err
		}
	}
	
	// Check if section already exists
	for _, id := range sectionIDs {
		if id == sectionIdentifier {
			return nil // Already associated
		}
	}
	
	// Add new section ID
	sectionIDs = append(sectionIDs, sectionIdentifier)
	
	// Serialize back to JSON
	sectionIDsJSON, err := json.Marshal(sectionIDs)
	if err != nil {
		return err
	}
	
	// Update report
	return s.DB.Model(&models.ProjectReport{}).Where("id = ?", reportID).
		Update("book_section_ids", string(sectionIDsJSON)).Error
}