import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  FormHelperText,
  Autocomplete,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Save as SaveIcon,
  Publish as PublishIcon,
  Archive as ArchiveIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Preview as PreviewIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { AppDispatch, RootState } from '../store';
import {
  fetchMetrics,
  fetchUserMetrics,
  fetchCategories,
  fetchReportById,
  createReport,
  updateReport,
  publishReport,
  archiveReport,
  deleteReport,
} from '../store/slices/impactSlice';
import { ImpactMetric, ImpactReport, ImpactCategory } from '../services/impactService';
import { ResponsiveContainer, LineChart, Line, BarChart, Bar, PieChart, Pie, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, Cell } from 'recharts';

// Metric Selection Component
const MetricSelectionStep: React.FC<{
  metrics: ImpactMetric[];
  selectedMetrics: number[];
  onMetricSelect: (metricIds: number[]) => void;
  categories: ImpactCategory[];
  loading: boolean;
}> = ({ metrics, selectedMetrics, onMetricSelect, categories, loading }) => {
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const filteredMetrics = metrics.filter((metric) => {
    const matchesCategory = filter === 'all' || metric.category === filter;
    const matchesSearch = metric.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         metric.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });
  
  const handleMetricToggle = (metricId: number) => {
    if (selectedMetrics.includes(metricId)) {
      onMetricSelect(selectedMetrics.filter(id => id !== metricId));
    } else {
      onMetricSelect([...selectedMetrics, metricId]);
    }
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Select Metrics to Include in Report
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <TextField
            fullWidth
            label="Search metrics"
            variant="outlined"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Category</InputLabel>
            <Select
              value={filter}
              onChange={(e) => setFilter(e.target.value as string)}
              label="Category"
            >
              <MenuItem value="all">All Categories</MenuItem>
              {categories.map((category) => (
                <MenuItem key={category.id} value={category.id.toString()}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredMetrics.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No metrics found matching your criteria. Try adjusting your filters.
        </Alert>
      ) : (
        <Grid container spacing={2}>
          {filteredMetrics.map((metric) => {
            const isSelected = selectedMetrics.includes(metric.id);
            const category = categories.find(c => c.id.toString() === metric.category);
            
            return (
              <Grid item key={metric.id} xs={12} sm={6} md={4}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    border: isSelected ? 2 : 0,
                    borderColor: 'primary.main',
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" component="div" gutterBottom>
                      {metric.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {metric.description}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Chip 
                        label={`${metric.currentValue} / ${metric.targetValue} ${metric.unit}`}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                      {category && (
                        <Chip 
                          label={category.name}
                          size="small"
                          sx={{ bgcolor: category.color, color: 'white' }}
                        />
                      )}
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      color={isSelected ? 'error' : 'primary'}
                      onClick={() => handleMetricToggle(metric.id)}
                      startIcon={isSelected ? <RemoveIcon /> : <AddIcon />}
                    >
                      {isSelected ? 'Remove' : 'Add to Report'}
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}
      
      <Box sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
        <Typography variant="subtitle1" sx={{ mr: 2 }}>
          Selected Metrics: {selectedMetrics.length}
        </Typography>
        {selectedMetrics.length > 0 && (
          <Button
            variant="outlined"
            color="error"
            size="small"
            onClick={() => onMetricSelect([])}
          >
            Clear All
          </Button>
        )}
      </Box>
      
      <Box sx={{ mt: 2 }}>
        {selectedMetrics.map((metricId) => {
          const metric = metrics.find(m => m.id === metricId);
          if (!metric) return null;
          
          return (
            <Chip
              key={metricId}
              label={metric.name}
              onDelete={() => handleMetricToggle(metricId)}
              sx={{ mr: 1, mb: 1 }}
            />
          );
        })}
      </Box>
    </Box>
  );
};

// Report Details Component
const ReportDetailsStep: React.FC<{
  reportData: Partial<ImpactReport>;
  setReportData: (data: Partial<ImpactReport>) => void;
}> = ({ reportData, setReportData }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReportData({ ...reportData, [name]: value });
  };
  
  const handleDateChange = (field: string, date: Date | null) => {
    if (date) {
      setReportData({ ...reportData, [field]: date.toISOString() });
    }
  };
  
  const handleTagsChange = (event: React.SyntheticEvent, newValue: string[]) => {
    setReportData({ ...reportData, tags: newValue });
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Details
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            required
            fullWidth
            label="Report Title"
            name="title"
            value={reportData.title || ''}
            onChange={handleChange}
            variant="outlined"
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Description"
            name="description"
            value={reportData.description || ''}
            onChange={handleChange}
            variant="outlined"
            multiline
            rows={4}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Start Date"
              value={reportData.startDate ? new Date(reportData.startDate) : null}
              onChange={(date) => handleDateChange('startDate', date)}
              slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
            />
          </LocalizationProvider>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="End Date"
              value={reportData.endDate ? new Date(reportData.endDate) : null}
              onChange={(date) => handleDateChange('endDate', date)}
              slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
            />
          </LocalizationProvider>
        </Grid>
        
        <Grid item xs={12}>
          <Autocomplete
            multiple
            freeSolo
            options={[]}
            value={reportData.tags || []}
            onChange={handleTagsChange}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Tags"
                placeholder="Add tags"
                helperText="Press Enter to add tags"
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Switch
                checked={reportData.isPublic || false}
                onChange={(e) => setReportData({ ...reportData, isPublic: e.target.checked })}
                color="primary"
              />
            }
            label="Make this report public"
          />
          <FormHelperText>
            Public reports can be viewed by anyone with the link. Private reports are only visible to you.
          </FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

// Report Preview Component
const ReportPreviewStep: React.FC<{
  reportData: Partial<ImpactReport>;
  metrics: ImpactMetric[];
  selectedMetrics: number[];
  categories: ImpactCategory[];
  onPublish: () => void;
  loading: boolean;
}> = ({ reportData, metrics, selectedMetrics, categories, onPublish, loading }) => {
  const selectedMetricsData = metrics.filter(metric => selectedMetrics.includes(metric.id));
  
  // Sample data for charts
  const generateTimeSeriesData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => {
      const dataPoint: any = { month };
      selectedMetricsData.forEach(metric => {
        dataPoint[metric.name] = Math.floor(Math.random() * metric.targetValue);
      });
      return dataPoint;
    });
  };
  
  const timeSeriesData = generateTimeSeriesData();
  
  const categoryData = categories.map(category => {
    const metricsInCategory = selectedMetricsData.filter(m => m.category === category.id.toString());
    return {
      name: category.name,
      value: metricsInCategory.length,
      color: category.color,
    };
  }).filter(item => item.value > 0);
  
  const progressData = selectedMetricsData.map(metric => {
    const progress = (metric.currentValue / metric.targetValue) * 100;
    return {
      name: metric.name,
      value: Math.min(100, progress),
    };
  });
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];
  
  return (
    <Box>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">{reportData.title}</Typography>
          <Chip
            label={reportData.isPublic ? 'Public' : 'Private'}
            color={reportData.isPublic ? 'success' : 'default'}
          />
        </Box>
        
        <Typography variant="body1" paragraph>
          {reportData.description}
        </Typography>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {reportData.tags && reportData.tags.map((tag, index) => (
            <Chip key={index} label={tag} size="small" />
          ))}
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Period: {reportData.startDate && new Date(reportData.startDate).toLocaleDateString()} - 
            {reportData.endDate && new Date(reportData.endDate).toLocaleDateString()}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Metrics: {selectedMetrics.length}
          </Typography>
        </Box>
      </Paper>
      
      <Typography variant="h6" gutterBottom>
        Metrics Overview
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Progress by Metric
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={progressData} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 100]} />
                  <YAxis dataKey="name" type="category" width={150} />
                  <RechartsTooltip formatter={(value) => `${value}%`} />
                  <Bar dataKey="value" fill="#8884d8">
                    {progressData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Metrics by Category
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom>
        Metrics Details
      </Typography>
      
      {selectedMetricsData.map((metric) => {
        const category = categories.find(c => c.id.toString() === metric.category);
        const progress = (metric.currentValue / metric.targetValue) * 100;
        
        return (
          <Paper key={metric.id} sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">{metric.name}</Typography>
              {category && (
                <Chip 
                  label={category.name}
                  size="small"
                  sx={{ bgcolor: category.color, color: 'white' }}
                />
              )}
            </Box>
            
            <Typography variant="body2" color="text.secondary" paragraph>
              {metric.description}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={Math.min(100, progress)}
                  sx={{ height: 10, borderRadius: 5 }}
                />
              </Box>
              <Box sx={{ minWidth: 35 }}>
                <Typography variant="body2" color="text.secondary">
                  {Math.round(progress)}%
                </Typography>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Current: {metric.currentValue} {metric.unit}
              </Typography>
              <Typography variant="body2">
                Target: {metric.targetValue} {metric.unit}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="caption" color="text.secondary">
                Start: {new Date(metric.startDate).toLocaleDateString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                End: {new Date(metric.endDate).toLocaleDateString()}
              </Typography>
            </Box>
          </Paper>
        );
      })}
      
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
        <Box>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            sx={{ mr: 1 }}
          >
            Export as PDF
          </Button>
          <Button
            variant="outlined"
            startIcon={<ShareIcon />}
          >
            Share Report
          </Button>
        </Box>
        
        <Button
          variant="contained"
          color="primary"
          startIcon={<PublishIcon />}
          onClick={onPublish}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Publish Report'}
        </Button>
      </Box>
    </Box>
  );
};

// Main Report Interface Component
const ImpactReportingInterface: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { items: metrics, loading: metricsLoading } = useSelector(
    (state: RootState) => state.impact.metrics
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.impact.categories
  );
  const { data: currentReport, loading: reportLoading, error: reportError } = useSelector(
    (state: RootState) => state.impact.currentReport
  );
  
  const [activeStep, setActiveStep] = useState(0);
  const [reportData, setReportData] = useState<Partial<ImpactReport>>({
    title: '',
    description: '',
    metrics: [],
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    isPublic: false,
    status: 'draft',
    tags: [],
  });
  const [selectedMetrics, setSelectedMetrics] = useState<number[]>([]);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    dispatch(fetchMetrics());
    dispatch(fetchCategories());
    
    if (id) {
      dispatch(fetchReportById(parseInt(id)));
    }
  }, [dispatch, id]);
  
  useEffect(() => {
    if (currentReport) {
      setReportData(currentReport);
      setSelectedMetrics(currentReport.metrics || []);
    }
  }, [currentReport]);
  
  const handleNext = async () => {
    if (activeStep === 0) {
      if (selectedMetrics.length === 0) {
        setError('Please select at least one metric for your report.');
        return;
      }
      setError(null);
      setReportData({ ...reportData, metrics: selectedMetrics });
    }
    
    if (activeStep === 1) {
      if (!reportData.title) {
        setError('Please provide a title for your report.');
        return;
      }
      setError(null);
      
      try {
        setSaving(true);
        
        const reportToSave = {
          ...reportData,
          metrics: selectedMetrics,
          createdBy: user?.id,
        };
        
        if (id) {
          await dispatch(updateReport({ id: parseInt(id), report: reportToSave })).unwrap();
        } else {
          const savedReport = await dispatch(createReport(reportToSave)).unwrap();
          navigate(`/impact/reports/edit/${savedReport.id}`, { replace: true });
        }
      } catch (err: any) {
        setError(err.message || 'Failed to save report');
        setSaving(false);
        return;
      } finally {
        setSaving(false);
      }
    }
    
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };
  
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  
  const handlePublish = async () => {
    if (!id) return;
    
    try {
      setSaving(true);
      setError(null);
      
      await dispatch(publishReport(parseInt(id))).unwrap();
      
      // Navigate to reports list after publishing
      navigate('/impact/reports');
    } catch (err: any) {
      setError(err.message || 'Failed to publish report');
    } finally {
      setSaving(false);
    }
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to create or edit reports. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  const steps = ['Select Metrics', 'Report Details', 'Preview & Publish'];
  
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <MetricSelectionStep
            metrics={metrics}
            selectedMetrics={selectedMetrics}
            onMetricSelect={setSelectedMetrics}
            categories={categories}
            loading={metricsLoading || categoriesLoading}
          />
        );
      case 1:
        return (
          <ReportDetailsStep
            reportData={reportData}
            setReportData={setReportData}
          />
        );
      case 2:
        return (
          <ReportPreviewStep
            reportData={reportData}
            metrics={metrics}
            selectedMetrics={selectedMetrics}
            categories={categories}
            onPublish={handlePublish}
            loading={saving}
          />
        );
      default:
        return 'Unknown step';
    }
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {id ? 'Edit Impact Report' : 'Create New Impact Report'}
        </Typography>
        
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {reportLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : reportError ? (
          <Alert severity="error" sx={{ mb: 3 }}>
            {reportError}
          </Alert>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mb: 3 }}>{getStepContent(activeStep)}</Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                variant="outlined"
                startIcon={<ArrowBackIcon />}
              >
                Back
              </Button>
              <Box>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/impact/reports')}
                  sx={{ mr: 1 }}
                >
                  Cancel
                </Button>
                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => navigate('/impact/reports')}
                    startIcon={<PreviewIcon />}
                  >
                    Finish
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    disabled={
                      saving ||
                      (activeStep === 0 && selectedMetrics.length === 0) ||
                      (activeStep === 1 && !reportData.title)
                    }
                    endIcon={<ArrowForwardIcon />}
                  >
                    {saving ? <CircularProgress size={24} /> : 'Next'}
                  </Button>
                )}
              </Box>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

// Define the LinearProgress component that was missing
const LinearProgress: React.FC<{
  variant: 'determinate';
  value: number;
  sx?: any;
}> = ({ variant, value, sx }) => {
  return (
    <Box
      sx={{
        height: sx?.height || 4,
        width: '100%',
        backgroundColor: 'grey.300',
        borderRadius: sx?.borderRadius || 0,
        ...sx,
      }}
    >
      <Box
        sx={{
          height: '100%',
          width: `${value}%`,
          backgroundColor: 'primary.main',
          borderRadius: sx?.borderRadius || 0,
          transition: 'width 0.4s ease-in-out',
        }}
      />
    </Box>
  );
};

export default ImpactReportingInterface;
