import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  Button,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  People as PeopleIcon,
  EmojiEvents as LeaderboardIcon,
  CardGiftcard as GiftIcon,
  Chat as ChatIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import {
  fetchStreamById,
  fetchStreamGifts,
  fetchStreamRankings,
  startStream,
  endStream,
  resetStreamState,
  updateViewerCount,
  addStreamGift,
  updateStreamRankings
} from '../features/livestream/livestreamSlice';
import useWebSocket from '../hooks/useWebSocket';
import livestreamService from '../api/livestreamService';
import VideoPlayer from '../components/livestream/VideoPlayer';
import GiftPanel from '../components/livestream/GiftPanel';
import ChatPanel from '../components/livestream/ChatPanel';
import LeaderboardPanel from '../components/livestream/LeaderboardPanel';
import StreamInfoPanel from '../components/livestream/StreamInfoPanel';
import GiftAnimationManager from '../components/livestream/GiftAnimationManager';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`stream-tabpanel-${index}`}
      aria-labelledby={`stream-tab-${index}`}
      {...other}
      style={{ height: '100%' }}
    >
      {value === index && (
        <Box sx={{ p: 2, height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const StreamDetailPage: React.FC = () => {
  const { streamId } = useParams<{ streamId: string }>();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { user } = useSelector((state: RootState) => state.auth);
  const {
    data: stream,
    gifts,
    rankings,
    loading,
    error
  } = useSelector((state: RootState) => state.livestream.currentStream);

  const [tabValue, setTabValue] = useState(0);
  const [messages, setMessages] = useState<any[]>([]);

  // WebSocket connection
  const wsUrl = user ? livestreamService.getWebSocketUrl(user.id, Number(streamId)) : '';
  const { isConnected, sendMessage } = useWebSocket(wsUrl, {
    onMessage: (message) => {
      if (message.type === 'gift') {
        dispatch(addStreamGift(message.content));
      } else if (message.type === 'viewer_count') {
        dispatch(updateViewerCount(message.content.count));
      } else if (message.type === 'rankings') {
        dispatch(updateStreamRankings(message.content.rankings));
      } else if (message.type === 'chat') {
        setMessages(prev => [...prev, message]);
      }
    }
  });

  useEffect(() => {
    if (streamId) {
      dispatch(fetchStreamById(Number(streamId)) as any);
      dispatch(fetchStreamGifts({ streamId: Number(streamId), page: 1, limit: 20 }) as any);
      dispatch(fetchStreamRankings({ streamId: Number(streamId), period: 'daily', limit: 10 }) as any);
    }

    return () => {
      dispatch(resetStreamState());
    };
  }, [dispatch, streamId]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleStartStream = () => {
    if (streamId) {
      dispatch(startStream(Number(streamId)) as any);
    }
  };

  const handleEndStream = () => {
    if (streamId) {
      dispatch(endStream(Number(streamId)) as any);
    }
  };

  const handleSendMessage = (message: string) => {
    if (isConnected && user) {
      sendMessage({
        type: 'chat',
        content: message,
        from: user.id,
        streamId: Number(streamId)
      });

      // Add message to local state immediately for better UX
      setMessages(prev => [
        ...prev,
        {
          type: 'chat',
          content: message,
          from: user.id,
          streamId: Number(streamId),
          time: new Date().toISOString()
        }
      ]);
    }
  };

  if (loading && !stream) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/livestream')}
          sx={{ mt: 2 }}
        >
          Back to Streams
        </Button>
      </Container>
    );
  }

  if (!stream) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h6">
          Stream not found
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/livestream')}
          sx={{ mt: 2 }}
        >
          Back to Streams
        </Button>
      </Container>
    );
  }

  const isCreator = user && user.id === stream.creatorId;
  const isLive = stream.status === 'live';

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Stream Details
      </Typography>

      <LivestreamNavigation />

      <Grid container spacing={3}>
        {/* Left column - Video and stream info */}
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ mb: 3, overflow: 'hidden' }}>
            <Box sx={{ position: 'relative', paddingTop: '56.25%' }}>
              {isLive ? (
                <>
                  <VideoPlayer url={stream.playbackUrl} />
                  <GiftAnimationManager gifts={gifts} />
                </>
              ) : (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    backgroundImage: stream.thumbnailUrl ? `url(${stream.thumbnailUrl})` : 'none',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                >
                  <Typography variant="h5" color="white" gutterBottom>
                    {stream.status === 'scheduled' ? 'Stream Scheduled' : 'Stream Ended'}
                  </Typography>
                  {stream.status === 'scheduled' && (
                    <Typography variant="body1" color="white">
                      Scheduled for {new Date(stream.scheduledStart).toLocaleString()}
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </Paper>

          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="h5" gutterBottom>
                  {stream.title}
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Box display="flex" alignItems="center">
                    <PeopleIcon fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="body2">
                      {stream.viewerCount} viewers
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {isLive ? 'Started' : stream.status === 'scheduled' ? 'Scheduled' : 'Ended'} {' '}
                    {new Date(isLive ? stream.actualStart! : stream.status === 'scheduled' ? stream.scheduledStart : stream.endTime!).toLocaleString()}
                  </Typography>
                </Box>
              </Box>

              {isCreator && (
                <Box>
                  {stream.status === 'scheduled' && (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayIcon />}
                      onClick={handleStartStream}
                    >
                      Start Stream
                    </Button>
                  )}

                  {stream.status === 'live' && (
                    <Button
                      variant="contained"
                      color="error"
                      startIcon={<StopIcon />}
                      onClick={handleEndStream}
                    >
                      End Stream
                    </Button>
                  )}
                </Box>
              )}
            </Box>

            <Divider sx={{ my: 2 }} />

            <StreamInfoPanel stream={stream} />
          </Paper>
        </Grid>

        {/* Right column - Chat, gifts, leaderboard */}
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
              >
                <Tab
                  icon={<ChatIcon />}
                  label="Chat"
                  id="stream-tab-0"
                  aria-controls="stream-tabpanel-0"
                />
                <Tab
                  icon={<GiftIcon />}
                  label="Gifts"
                  id="stream-tab-1"
                  aria-controls="stream-tabpanel-1"
                />
                <Tab
                  icon={<LeaderboardIcon />}
                  label="Top Gifters"
                  id="stream-tab-2"
                  aria-controls="stream-tabpanel-2"
                />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <ChatPanel
                messages={messages}
                onSendMessage={handleSendMessage}
                isConnected={isConnected}
                isLive={isLive}
              />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <GiftPanel
                gifts={gifts}
                streamId={Number(streamId)}
                recipientId={stream.creatorId}
                isLive={isLive}
              />
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <LeaderboardPanel
                rankings={rankings}
                streamId={Number(streamId)}
              />
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default StreamDetailPage;
