import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../features/auth/authSlice';
import bookReducer from '../features/books/booksSlice';
import forumReducer from '../features/forum/forumSlice';
import livestreamReducer from '../features/livestream/livestreamSlice';
import featuresReducer from '../features/features/featuresSlice';
import badgesReducer from '../features/badges/badgesSlice';
import marketplaceReducer from '../features/marketplace/marketplaceSlice';
import walletReducer from '../features/wallet/walletSlice';
import escrowReducer from '../features/escrow/escrowSlice';
import affiliateReducer from '../features/affiliate/affiliateSlice';
import progressReducer from '../features/progress/progressSlice';
import tipsReducer from '../features/tips/tipsSlice';
import personalizationReducer from '../features/personalization/personalizationSlice';
import searchReducer from '../features/search/searchSlice';
import coursesReducer from './slices/coursesSlice';
import tutorialsReducer from './slices/tutorialsSlice';
import quizzesReducer from './slices/quizzesSlice';
import impactReducer from './slices/impactSlice';
import rewardsReducer from './slices/rewardsSlice';
import skillsReducer from './slices/skillsSlice';
import groupsReducer from './slices/groupsSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    books: bookReducer,
    forum: forumReducer,
    livestream: livestreamReducer,
    features: featuresReducer,
    badges: badgesReducer,
    marketplace: marketplaceReducer,
    wallet: walletReducer,
    escrow: escrowReducer,
    affiliate: affiliateReducer,
    progress: progressReducer,
    tips: tipsReducer,
    personalization: personalizationReducer,
    search: searchReducer,
    courses: coursesReducer,
    tutorials: tutorialsReducer,
    quizzes: quizzesReducer,
    impact: impactReducer,
    rewards: rewardsReducer,
    skills: skillsReducer,
    groups: groupsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
