package service

import (
        "github.com/yerenwgventures/GreatNigeriaLibrary/internal/resource/models"
        "gorm.io/gorm"
)

// ResourceService handles business logic for resources
type ResourceService struct {
        DB *gorm.DB
}

// NewResourceService creates a new ResourceService
func NewResourceService(db *gorm.DB) *ResourceService {
        return &ResourceService{
                DB: db,
        }
}

// CreateResourceCategory creates a new resource category
func (s *ResourceService) CreateResourceCategory(category *models.ResourceCategory) error {
        return s.DB.Create(category).Error
}

// GetResourceCategories gets all resource categories
func (s *ResourceService) GetResourceCategories() ([]models.ResourceCategory, error) {
        var categories []models.ResourceCategory
        err := s.DB.Find(&categories).Error
        return categories, err
}

// GetResourceCategoryByID gets a resource category by ID
func (s *ResourceService) GetResourceCategoryByID(id uint64) (*models.ResourceCategory, error) {
        var category models.ResourceCategory
        if err := s.DB.First(&category, id).Error; err != nil {
                return nil, err
        }
        return &category, nil
}

// UpdateResourceCategory updates a resource category
func (s *ResourceService) UpdateResourceCategory(category *models.ResourceCategory) error {
        return s.DB.Save(category).Error
}

// DeleteResourceCategory deletes a resource category
func (s *ResourceService) DeleteResourceCategory(id uint64) error {
        return s.DB.Delete(&models.ResourceCategory{}, id).Error
}

// CreateResource creates a new resource
func (s *ResourceService) CreateResource(resource *models.Resource) error {
        return s.DB.Create(resource).Error
}

// GetResources gets resources with optional filtering
func (s *ResourceService) GetResources(params map[string]interface{}) ([]models.Resource, error) {
        var resources []models.Resource
        
        query := s.DB
        
        // Apply filters
        if categoryID, ok := params["category_id"].(uint64); ok {
                query = query.Where("category_id = ?", categoryID)
        }
        
        if resourceType, ok := params["resource_type"].(string); ok {
                query = query.Where("resource_type = ?", resourceType)
        }
        
        if authorID, ok := params["author_id"].(uint64); ok {
                query = query.Where("author_id = ?", authorID)
        }
        
        if isFeatured, ok := params["is_featured"].(bool); ok {
                query = query.Where("is_featured = ?", isFeatured)
        }
        
        if isPremium, ok := params["is_premium"].(bool); ok {
                query = query.Where("is_premium = ?", isPremium)
        }
        
        if search, ok := params["search"].(string); ok && search != "" {
                query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
        }
        
        // Execute query
        err := query.Preload("Tags").Find(&resources).Error
        return resources, err
}

// GetResourceByID gets a resource by ID
func (s *ResourceService) GetResourceByID(id uint64) (*models.Resource, error) {
        var resource models.Resource
        if err := s.DB.Preload("Tags").First(&resource, id).Error; err != nil {
                return nil, err
        }
        return &resource, nil
}

// UpdateResource updates a resource
func (s *ResourceService) UpdateResource(resource *models.Resource) error {
        return s.DB.Save(resource).Error
}

// DeleteResource deletes a resource
func (s *ResourceService) DeleteResource(id uint64) error {
        return s.DB.Delete(&models.Resource{}, id).Error
}

// IncrementResourceViewCount increments the view count for a resource
func (s *ResourceService) IncrementResourceViewCount(id uint64) error {
        return s.DB.Model(&models.Resource{}).Where("id = ?", id).
                UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error
}

// IncrementResourceDownloadCount increments the download count for a resource
func (s *ResourceService) IncrementResourceDownloadCount(id uint64) error {
        return s.DB.Model(&models.Resource{}).Where("id = ?", id).
                UpdateColumn("download_count", gorm.Expr("download_count + ?", 1)).Error
}

// GetResourcesByBookSectionID gets resources associated with a book section
func (s *ResourceService) GetResourcesByBookSectionID(bookID, chapterID, sectionID uint64) ([]models.Resource, error) {
        var resources []models.Resource
        
        // Query for resources through the mapping table
        err := s.DB.Joins("JOIN resource_book_section_mappings rbsm ON rbsm.resource_id = resources.id").
                Where("rbsm.book_id = ? AND rbsm.chapter_id = ? AND rbsm.section_id = ?", 
                        bookID, chapterID, sectionID).
                Preload("Tags").
                Find(&resources).Error
        
        return resources, err
}

// AddResourceToBookSection associates a resource with a book section
func (s *ResourceService) AddResourceToBookSection(resourceID, bookID, chapterID, sectionID uint64) error {
        mapping := models.ResourceBookSectionMapping{
                ResourceID: resourceID,
                BookID:     bookID,
                ChapterID:  chapterID,
                SectionID:  sectionID,
        }
        
        return s.DB.Create(&mapping).Error
}

// GetResourceTags gets all resource tags
func (s *ResourceService) GetResourceTags() ([]models.ResourceTag, error) {
        var tags []models.ResourceTag
        err := s.DB.Find(&tags).Error
        return tags, err
}

// CreateResourceTag creates a new resource tag
func (s *ResourceService) CreateResourceTag(tag *models.ResourceTag) error {
        return s.DB.Create(tag).Error
}

// GetResourcesByTags gets resources by tag names
func (s *ResourceService) GetResourcesByTags(tagNames []string) ([]models.Resource, error) {
        var resources []models.Resource
        
        err := s.DB.Joins("JOIN resource_tag_mappings rtm ON rtm.resource_id = resources.id").
                Joins("JOIN resource_tags rt ON rt.id = rtm.resource_tag_id").
                Where("rt.name IN ?", tagNames).
                Group("resources.id").
                Having("COUNT(DISTINCT rt.name) = ?", len(tagNames)).
                Preload("Tags").
                Find(&resources).Error
        
        return resources, err
}

// AddTagToResource adds a tag to a resource
func (s *ResourceService) AddTagToResource(resourceID uint64, tagName string) error {
        // Begin transaction
        tx := s.DB.Begin()
        if tx.Error != nil {
                return tx.Error
        }
        
        // Rollback transaction on error
        defer func() {
                if r := recover(); r != nil {
                        tx.Rollback()
                }
        }()
        
        // Find or create tag
        var tag models.ResourceTag
        err := tx.Where("name = ?", tagName).FirstOrCreate(&tag, models.ResourceTag{
                Name: tagName,
        }).Error
        
        if err != nil {
                tx.Rollback()
                return err
        }
        
        // Add association between resource and tag
        err = tx.Exec("INSERT INTO resource_tag_mappings (resource_id, resource_tag_id) VALUES (?, ?) ON CONFLICT DO NOTHING", 
                resourceID, tag.ID).Error
        
        if err != nil {
                tx.Rollback()
                return err
        }
        
        // Commit transaction
        return tx.Commit().Error
}

// RemoveTagFromResource removes a tag from a resource
func (s *ResourceService) RemoveTagFromResource(resourceID uint64, tagName string) error {
        var tag models.ResourceTag
        
        // Find tag by name
        if err := s.DB.Where("name = ?", tagName).First(&tag).Error; err != nil {
                return err
        }
        
        // Remove association
        return s.DB.Exec("DELETE FROM resource_tag_mappings WHERE resource_id = ? AND resource_tag_id = ?", 
                resourceID, tag.ID).Error
}