import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  CircularProgress,
  Alert,
  Divider,
  Rating,
  IconButton,
  Menu,
  MenuItem as MuiMenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchTutorials, fetchCategories, deleteTutorial } from '../store/slices/tutorialsSlice';
import { Tutorial } from '../services/tutorialsService';

const TutorialsListPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { items: tutorials, total, loading, error } = useSelector(
    (state: RootState) => state.tutorials.tutorials
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.tutorials.categories
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(9);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    categoryId: '',
    difficulty: '',
    sortBy: 'newest',
  });
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTutorial, setSelectedTutorial] = useState<Tutorial | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  useEffect(() => {
    dispatch(fetchCategories());
    
    const fetchData = () => {
      const filterParams: any = {};
      
      if (filters.categoryId) filterParams.categoryId = filters.categoryId;
      if (filters.difficulty) filterParams.difficulty = filters.difficulty;
      if (searchQuery) filterParams.search = searchQuery;
      
      dispatch(fetchTutorials({ page, pageSize, filters: filterParams }));
    };
    
    fetchData();
  }, [dispatch, page, pageSize, filters, searchQuery]);
  
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };
  
  const handleFilterChange = (event: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    const { name, value } = event.target;
    if (name) {
      setFilters({
        ...filters,
        [name]: value,
      });
      setPage(1); // Reset to first page when filters change
    }
  };
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  
  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setPage(1); // Reset to first page when search changes
  };
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, tutorial: Tutorial) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedTutorial(tutorial);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };
  
  const handleDeleteConfirm = async () => {
    if (selectedTutorial) {
      try {
        await dispatch(deleteTutorial(selectedTutorial.id)).unwrap();
        setDeleteDialogOpen(false);
        setSelectedTutorial(null);
      } catch (error) {
        console.error('Failed to delete tutorial:', error);
      }
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };
  
  const handleCreateTutorial = () => {
    navigate('/tutorials/create');
  };
  
  const totalPages = Math.ceil(total / pageSize);
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Tutorials
        </Typography>
        {user && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateTutorial}
          >
            Create Tutorial
          </Button>
        )}
      </Box>
      
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <form onSubmit={handleSearchSubmit}>
              <TextField
                fullWidth
                label="Search tutorials"
                variant="outlined"
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </form>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Category</InputLabel>
                  <Select
                    name="categoryId"
                    value={filters.categoryId}
                    onChange={handleFilterChange}
                    label="Category"
                  >
                    <MenuItem value="">All Categories</MenuItem>
                    {categoriesLoading ? (
                      <MenuItem disabled>Loading categories...</MenuItem>
                    ) : (
                      categories.map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Difficulty</InputLabel>
                  <Select
                    name="difficulty"
                    value={filters.difficulty}
                    onChange={handleFilterChange}
                    label="Difficulty"
                  >
                    <MenuItem value="">All Levels</MenuItem>
                    <MenuItem value="beginner">Beginner</MenuItem>
                    <MenuItem value="intermediate">Intermediate</MenuItem>
                    <MenuItem value="advanced">Advanced</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      
      <Divider sx={{ mb: 4 }} />
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      ) : tutorials.length === 0 ? (
        <Alert severity="info" sx={{ mb: 4 }}>
          No tutorials found. Try adjusting your filters.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {tutorials.map((tutorial) => (
              <Grid item key={tutorial.id} xs={12} sm={6} md={4}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardMedia
                    component="img"
                    height="140"
                    image={tutorial.thumbnailURL || '/images/tutorial-placeholder.jpg'}
                    alt={tutorial.title}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography gutterBottom variant="h5" component="h2">
                      {tutorial.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {tutorial.description.length > 120
                        ? `${tutorial.description.substring(0, 120)}...`
                        : tutorial.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        {tutorial.authorName}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        {tutorial.estimatedTime} min
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Rating value={tutorial.rating} precision={0.5} readOnly size="small" />
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        ({tutorial.rating.toFixed(1)})
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      <Chip
                        label={tutorial.difficulty}
                        size="small"
                        color={
                          tutorial.difficulty === 'beginner'
                            ? 'success'
                            : tutorial.difficulty === 'intermediate'
                            ? 'primary'
                            : 'secondary'
                        }
                      />
                      {tutorial.tags.slice(0, 2).map((tag) => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions sx={{ justifyContent: 'space-between' }}>
                    <Button
                      size="small"
                      component={Link}
                      to={`/tutorials/${tutorial.id}`}
                    >
                      View Tutorial
                    </Button>
                    {user && user.id === tutorial.authorId && (
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, tutorial)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        </>
      )}
      
      {/* Tutorial actions menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MuiMenuItem
          onClick={() => {
            navigate(`/tutorials/${selectedTutorial?.id}`);
            handleMenuClose();
          }}
        >
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View
        </MuiMenuItem>
        <MuiMenuItem
          onClick={() => {
            navigate(`/tutorials/edit/${selectedTutorial?.id}`);
            handleMenuClose();
          }}
        >
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MuiMenuItem>
        <MuiMenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MuiMenuItem>
      </Menu>
      
      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Tutorial</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the tutorial "{selectedTutorial?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TutorialsListPage;
