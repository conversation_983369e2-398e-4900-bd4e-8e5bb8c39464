import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import groupsService, {
  Group,
  GroupMember,
  LocalEvent,
  EventAttendee,
  SharedResource,
  GroupDiscussion,
  GroupComment,
  GroupAction,
  GroupInvitation,
  GroupJoinRequest,
} from '../../services/groupsService';

interface GroupsState {
  groups: {
    items: Group[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  userGroups: {
    items: Group[];
    loading: boolean;
    error: string | null;
  };
  nearbyGroups: {
    items: Group[];
    loading: boolean;
    error: string | null;
  };
  currentGroup: {
    data: Group | null;
    loading: boolean;
    error: string | null;
  };
  members: {
    items: GroupMember[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentMember: {
    data: GroupMember | null;
    loading: boolean;
    error: string | null;
  };
  events: {
    items: LocalEvent[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  upcomingEvents: {
    items: LocalEvent[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentEvent: {
    data: LocalEvent | null;
    loading: boolean;
    error: string | null;
  };
  attendees: {
    items: EventAttendee[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentAttendee: {
    data: EventAttendee | null;
    loading: boolean;
    error: string | null;
  };
  resources: {
    items: SharedResource[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentResource: {
    data: SharedResource | null;
    loading: boolean;
    error: string | null;
  };
  discussions: {
    items: GroupDiscussion[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentDiscussion: {
    data: GroupDiscussion | null;
    loading: boolean;
    error: string | null;
  };
  comments: {
    items: GroupComment[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentComment: {
    data: GroupComment | null;
    loading: boolean;
    error: string | null;
  };
  actions: {
    items: GroupAction[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  userActions: {
    items: GroupAction[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentAction: {
    data: GroupAction | null;
    loading: boolean;
    error: string | null;
  };
  invitations: {
    items: GroupInvitation[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  userInvitations: {
    items: GroupInvitation[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentInvitation: {
    data: GroupInvitation | null;
    loading: boolean;
    error: string | null;
  };
  joinRequests: {
    items: GroupJoinRequest[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  userJoinRequests: {
    items: GroupJoinRequest[];
    loading: boolean;
    error: string | null;
    total: number;
  };
  currentJoinRequest: {
    data: GroupJoinRequest | null;
    loading: boolean;
    error: string | null;
  };
}

const initialState: GroupsState = {
  groups: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  userGroups: {
    items: [],
    loading: false,
    error: null,
  },
  nearbyGroups: {
    items: [],
    loading: false,
    error: null,
  },
  currentGroup: {
    data: null,
    loading: false,
    error: null,
  },
  members: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentMember: {
    data: null,
    loading: false,
    error: null,
  },
  events: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  upcomingEvents: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentEvent: {
    data: null,
    loading: false,
    error: null,
  },
  attendees: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentAttendee: {
    data: null,
    loading: false,
    error: null,
  },
  resources: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentResource: {
    data: null,
    loading: false,
    error: null,
  },
  discussions: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentDiscussion: {
    data: null,
    loading: false,
    error: null,
  },
  comments: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentComment: {
    data: null,
    loading: false,
    error: null,
  },
  actions: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  userActions: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentAction: {
    data: null,
    loading: false,
    error: null,
  },
  invitations: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  userInvitations: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentInvitation: {
    data: null,
    loading: false,
    error: null,
  },
  joinRequests: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  userJoinRequests: {
    items: [],
    loading: false,
    error: null,
    total: 0,
  },
  currentJoinRequest: {
    data: null,
    loading: false,
    error: null,
  },
};

// Group thunks
export const fetchGroups = createAsyncThunk(
  'groups/fetchGroups',
  async (params: { page?: number; pageSize?: number } = {}, { rejectWithValue }) => {
    try {
      return await groupsService.getGroups(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch groups');
    }
  }
);

export const fetchGroupById = createAsyncThunk(
  'groups/fetchGroupById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await groupsService.getGroupById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group');
    }
  }
);

export const createGroup = createAsyncThunk(
  'groups/createGroup',
  async (group: Partial<Group>, { rejectWithValue }) => {
    try {
      return await groupsService.createGroup(group);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create group');
    }
  }
);

export const updateGroup = createAsyncThunk(
  'groups/updateGroup',
  async ({ id, group }: { id: number; group: Partial<Group> }, { rejectWithValue }) => {
    try {
      return await groupsService.updateGroup(id, group);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update group');
    }
  }
);

export const deleteGroup = createAsyncThunk(
  'groups/deleteGroup',
  async (id: number, { rejectWithValue }) => {
    try {
      await groupsService.deleteGroup(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete group');
    }
  }
);

export const fetchNearbyGroups = createAsyncThunk(
  'groups/fetchNearbyGroups',
  async (
    { latitude, longitude, radius }: { latitude: number; longitude: number; radius?: number },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getNearbyGroups(latitude, longitude, radius);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch nearby groups');
    }
  }
);

export const searchGroups = createAsyncThunk(
  'groups/searchGroups',
  async (
    params: {
      q?: string;
      type?: string;
      tags?: string;
      page?: number;
      pageSize?: number;
    },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.searchGroups(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to search groups');
    }
  }
);

export const fetchUserGroups = createAsyncThunk(
  'groups/fetchUserGroups',
  async (
    { userId, includeInvited }: { userId: number; includeInvited?: boolean },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getUserGroups(userId, includeInvited);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch user groups');
    }
  }
);

// Member thunks
export const fetchGroupMembers = createAsyncThunk(
  'groups/fetchGroupMembers',
  async (
    { groupId, params }: { groupId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupMembers(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group members');
    }
  }
);

export const fetchGroupMember = createAsyncThunk(
  'groups/fetchGroupMember',
  async ({ groupId, userId }: { groupId: number; userId: number }, { rejectWithValue }) => {
    try {
      return await groupsService.getGroupMember(groupId, userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group member');
    }
  }
);

export const addGroupMember = createAsyncThunk(
  'groups/addGroupMember',
  async (
    {
      groupId,
      data,
    }: { groupId: number; data: { userId: number; role: string; status: string } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.addMember(groupId, data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to add group member');
    }
  }
);

export const updateGroupMember = createAsyncThunk(
  'groups/updateGroupMember',
  async (
    {
      groupId,
      userId,
      data,
    }: { groupId: number; userId: number; data: { role: string; status: string } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.updateMember(groupId, userId, data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update group member');
    }
  }
);

export const removeGroupMember = createAsyncThunk(
  'groups/removeGroupMember',
  async ({ groupId, userId }: { groupId: number; userId: number }, { rejectWithValue }) => {
    try {
      await groupsService.removeMember(groupId, userId);
      return { groupId, userId };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to remove group member');
    }
  }
);

// Event thunks
export const fetchGroupEvents = createAsyncThunk(
  'groups/fetchGroupEvents',
  async (
    { groupId, params }: { groupId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupEvents(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group events');
    }
  }
);

export const fetchEventById = createAsyncThunk(
  'groups/fetchEventById',
  async (eventId: number, { rejectWithValue }) => {
    try {
      return await groupsService.getEventById(eventId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch event');
    }
  }
);

export const createEvent = createAsyncThunk(
  'groups/createEvent',
  async (
    { groupId, event }: { groupId: number; event: Partial<LocalEvent> },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.createEvent(groupId, event);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create event');
    }
  }
);

export const updateEvent = createAsyncThunk(
  'groups/updateEvent',
  async (
    { eventId, event }: { eventId: number; event: Partial<LocalEvent> },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.updateEvent(eventId, event);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update event');
    }
  }
);

export const deleteEvent = createAsyncThunk(
  'groups/deleteEvent',
  async (eventId: number, { rejectWithValue }) => {
    try {
      await groupsService.deleteEvent(eventId);
      return eventId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete event');
    }
  }
);

export const fetchUserUpcomingEvents = createAsyncThunk(
  'groups/fetchUserUpcomingEvents',
  async (
    { userId, params }: { userId: number; params?: { page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getUserUpcomingEvents(userId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch upcoming events');
    }
  }
);

// Attendee thunks
export const fetchEventAttendees = createAsyncThunk(
  'groups/fetchEventAttendees',
  async (
    { eventId, params }: { eventId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getEventAttendees(eventId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch event attendees');
    }
  }
);

export const fetchEventAttendee = createAsyncThunk(
  'groups/fetchEventAttendee',
  async ({ eventId, userId }: { eventId: number; userId: number }, { rejectWithValue }) => {
    try {
      return await groupsService.getEventAttendee(eventId, userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch event attendee');
    }
  }
);

export const addEventAttendee = createAsyncThunk(
  'groups/addEventAttendee',
  async (
    { eventId, data }: { eventId: number; data: { status: string } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.addAttendee(eventId, data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to add event attendee');
    }
  }
);

export const updateEventAttendee = createAsyncThunk(
  'groups/updateEventAttendee',
  async (
    {
      eventId,
      userId,
      data,
    }: { eventId: number; userId: number; data: { status: string } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.updateAttendee(eventId, userId, data);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update event attendee');
    }
  }
);

export const removeEventAttendee = createAsyncThunk(
  'groups/removeEventAttendee',
  async ({ eventId, userId }: { eventId: number; userId: number }, { rejectWithValue }) => {
    try {
      await groupsService.removeAttendee(eventId, userId);
      return { eventId, userId };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to remove event attendee');
    }
  }
);

export const checkInEventAttendee = createAsyncThunk(
  'groups/checkInEventAttendee',
  async ({ eventId, userId }: { eventId: number; userId: number }, { rejectWithValue }) => {
    try {
      return await groupsService.checkInAttendee(eventId, userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to check in event attendee');
    }
  }
);

// Resource thunks
export const fetchGroupResources = createAsyncThunk(
  'groups/fetchGroupResources',
  async (
    { groupId, params }: { groupId: number; params?: { type?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupResources(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group resources');
    }
  }
);

// Discussion thunks
export const fetchGroupDiscussions = createAsyncThunk(
  'groups/fetchGroupDiscussions',
  async (
    { groupId, params }: { groupId: number; params?: { page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupDiscussions(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group discussions');
    }
  }
);

// Action thunks
export const fetchGroupActions = createAsyncThunk(
  'groups/fetchGroupActions',
  async (
    { groupId, params }: { groupId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupActions(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group actions');
    }
  }
);

// Invitation thunks
export const fetchGroupInvitations = createAsyncThunk(
  'groups/fetchGroupInvitations',
  async (
    { groupId, params }: { groupId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupInvitations(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group invitations');
    }
  }
);

// Join request thunks
export const fetchGroupJoinRequests = createAsyncThunk(
  'groups/fetchGroupJoinRequests',
  async (
    { groupId, params }: { groupId: number; params?: { status?: string; page?: number; pageSize?: number } },
    { rejectWithValue }
  ) => {
    try {
      return await groupsService.getGroupJoinRequests(groupId, params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch group join requests');
    }
  }
);

const groupsSlice = createSlice({
  name: 'groups',
  initialState,
  reducers: {
    resetCurrentGroup: (state) => {
      state.currentGroup.data = null;
      state.currentGroup.error = null;
    },
    resetCurrentMember: (state) => {
      state.currentMember.data = null;
      state.currentMember.error = null;
    },
    resetCurrentEvent: (state) => {
      state.currentEvent.data = null;
      state.currentEvent.error = null;
    },
    resetCurrentAttendee: (state) => {
      state.currentAttendee.data = null;
      state.currentAttendee.error = null;
    },
    resetCurrentResource: (state) => {
      state.currentResource.data = null;
      state.currentResource.error = null;
    },
    resetCurrentDiscussion: (state) => {
      state.currentDiscussion.data = null;
      state.currentDiscussion.error = null;
    },
    resetCurrentComment: (state) => {
      state.currentComment.data = null;
      state.currentComment.error = null;
    },
    resetCurrentAction: (state) => {
      state.currentAction.data = null;
      state.currentAction.error = null;
    },
    resetCurrentInvitation: (state) => {
      state.currentInvitation.data = null;
      state.currentInvitation.error = null;
    },
    resetCurrentJoinRequest: (state) => {
      state.currentJoinRequest.data = null;
      state.currentJoinRequest.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch groups
    builder.addCase(fetchGroups.pending, (state) => {
      state.groups.loading = true;
      state.groups.error = null;
    });
    builder.addCase(fetchGroups.fulfilled, (state, action) => {
      state.groups.loading = false;
      state.groups.items = action.payload.data;
      state.groups.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroups.rejected, (state, action) => {
      state.groups.loading = false;
      state.groups.error = action.payload as string;
    });

    // Fetch group by ID
    builder.addCase(fetchGroupById.pending, (state) => {
      state.currentGroup.loading = true;
      state.currentGroup.error = null;
    });
    builder.addCase(fetchGroupById.fulfilled, (state, action) => {
      state.currentGroup.loading = false;
      state.currentGroup.data = action.payload;
    });
    builder.addCase(fetchGroupById.rejected, (state, action) => {
      state.currentGroup.loading = false;
      state.currentGroup.error = action.payload as string;
    });

    // Create group
    builder.addCase(createGroup.fulfilled, (state, action) => {
      state.groups.items.push(action.payload);
      state.currentGroup.data = action.payload;
    });

    // Update group
    builder.addCase(updateGroup.fulfilled, (state, action) => {
      const updatedGroup = action.payload;
      const index = state.groups.items.findIndex((group) => group.id === updatedGroup.id);
      if (index !== -1) {
        state.groups.items[index] = updatedGroup;
      }
      state.currentGroup.data = updatedGroup;
    });

    // Delete group
    builder.addCase(deleteGroup.fulfilled, (state, action) => {
      const groupId = action.payload;
      state.groups.items = state.groups.items.filter((group) => group.id !== groupId);
      if (state.currentGroup.data?.id === groupId) {
        state.currentGroup.data = null;
      }
    });

    // Fetch nearby groups
    builder.addCase(fetchNearbyGroups.pending, (state) => {
      state.nearbyGroups.loading = true;
      state.nearbyGroups.error = null;
    });
    builder.addCase(fetchNearbyGroups.fulfilled, (state, action) => {
      state.nearbyGroups.loading = false;
      state.nearbyGroups.items = action.payload;
    });
    builder.addCase(fetchNearbyGroups.rejected, (state, action) => {
      state.nearbyGroups.loading = false;
      state.nearbyGroups.error = action.payload as string;
    });

    // Search groups
    builder.addCase(searchGroups.fulfilled, (state, action) => {
      state.groups.loading = false;
      state.groups.items = action.payload.data;
      state.groups.total = action.payload.meta.total;
    });

    // Fetch user groups
    builder.addCase(fetchUserGroups.pending, (state) => {
      state.userGroups.loading = true;
      state.userGroups.error = null;
    });
    builder.addCase(fetchUserGroups.fulfilled, (state, action) => {
      state.userGroups.loading = false;
      state.userGroups.items = action.payload;
    });
    builder.addCase(fetchUserGroups.rejected, (state, action) => {
      state.userGroups.loading = false;
      state.userGroups.error = action.payload as string;
    });

    // Fetch group members
    builder.addCase(fetchGroupMembers.pending, (state) => {
      state.members.loading = true;
      state.members.error = null;
    });
    builder.addCase(fetchGroupMembers.fulfilled, (state, action) => {
      state.members.loading = false;
      state.members.items = action.payload.data;
      state.members.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupMembers.rejected, (state, action) => {
      state.members.loading = false;
      state.members.error = action.payload as string;
    });

    // Fetch group member
    builder.addCase(fetchGroupMember.pending, (state) => {
      state.currentMember.loading = true;
      state.currentMember.error = null;
    });
    builder.addCase(fetchGroupMember.fulfilled, (state, action) => {
      state.currentMember.loading = false;
      state.currentMember.data = action.payload;
    });
    builder.addCase(fetchGroupMember.rejected, (state, action) => {
      state.currentMember.loading = false;
      state.currentMember.error = action.payload as string;
    });

    // Add group member
    builder.addCase(addGroupMember.fulfilled, (state, action) => {
      state.members.items.push(action.payload);
    });

    // Update group member
    builder.addCase(updateGroupMember.fulfilled, (state, action) => {
      const updatedMember = action.payload;
      const index = state.members.items.findIndex(
        (member) => member.groupId === updatedMember.groupId && member.userId === updatedMember.userId
      );
      if (index !== -1) {
        state.members.items[index] = updatedMember;
      }
      state.currentMember.data = updatedMember;
    });

    // Remove group member
    builder.addCase(removeGroupMember.fulfilled, (state, action) => {
      const { groupId, userId } = action.payload;
      state.members.items = state.members.items.filter(
        (member) => !(member.groupId === groupId && member.userId === userId)
      );
      if (
        state.currentMember.data?.groupId === groupId &&
        state.currentMember.data?.userId === userId
      ) {
        state.currentMember.data = null;
      }
    });

    // Fetch group events
    builder.addCase(fetchGroupEvents.pending, (state) => {
      state.events.loading = true;
      state.events.error = null;
    });
    builder.addCase(fetchGroupEvents.fulfilled, (state, action) => {
      state.events.loading = false;
      state.events.items = action.payload.data;
      state.events.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupEvents.rejected, (state, action) => {
      state.events.loading = false;
      state.events.error = action.payload as string;
    });

    // Fetch event by ID
    builder.addCase(fetchEventById.pending, (state) => {
      state.currentEvent.loading = true;
      state.currentEvent.error = null;
    });
    builder.addCase(fetchEventById.fulfilled, (state, action) => {
      state.currentEvent.loading = false;
      state.currentEvent.data = action.payload;
    });
    builder.addCase(fetchEventById.rejected, (state, action) => {
      state.currentEvent.loading = false;
      state.currentEvent.error = action.payload as string;
    });

    // Create event
    builder.addCase(createEvent.fulfilled, (state, action) => {
      state.events.items.push(action.payload);
      state.currentEvent.data = action.payload;
    });

    // Update event
    builder.addCase(updateEvent.fulfilled, (state, action) => {
      const updatedEvent = action.payload;
      const index = state.events.items.findIndex((event) => event.id === updatedEvent.id);
      if (index !== -1) {
        state.events.items[index] = updatedEvent;
      }
      state.currentEvent.data = updatedEvent;
    });

    // Delete event
    builder.addCase(deleteEvent.fulfilled, (state, action) => {
      const eventId = action.payload;
      state.events.items = state.events.items.filter((event) => event.id !== eventId);
      if (state.currentEvent.data?.id === eventId) {
        state.currentEvent.data = null;
      }
    });

    // Fetch user upcoming events
    builder.addCase(fetchUserUpcomingEvents.pending, (state) => {
      state.upcomingEvents.loading = true;
      state.upcomingEvents.error = null;
    });
    builder.addCase(fetchUserUpcomingEvents.fulfilled, (state, action) => {
      state.upcomingEvents.loading = false;
      state.upcomingEvents.items = action.payload.data;
      state.upcomingEvents.total = action.payload.meta.total;
    });
    builder.addCase(fetchUserUpcomingEvents.rejected, (state, action) => {
      state.upcomingEvents.loading = false;
      state.upcomingEvents.error = action.payload as string;
    });

    // Fetch event attendees
    builder.addCase(fetchEventAttendees.pending, (state) => {
      state.attendees.loading = true;
      state.attendees.error = null;
    });
    builder.addCase(fetchEventAttendees.fulfilled, (state, action) => {
      state.attendees.loading = false;
      state.attendees.items = action.payload.data;
      state.attendees.total = action.payload.meta.total;
    });
    builder.addCase(fetchEventAttendees.rejected, (state, action) => {
      state.attendees.loading = false;
      state.attendees.error = action.payload as string;
    });

    // Fetch event attendee
    builder.addCase(fetchEventAttendee.pending, (state) => {
      state.currentAttendee.loading = true;
      state.currentAttendee.error = null;
    });
    builder.addCase(fetchEventAttendee.fulfilled, (state, action) => {
      state.currentAttendee.loading = false;
      state.currentAttendee.data = action.payload;
    });
    builder.addCase(fetchEventAttendee.rejected, (state, action) => {
      state.currentAttendee.loading = false;
      state.currentAttendee.error = action.payload as string;
    });

    // Add event attendee
    builder.addCase(addEventAttendee.fulfilled, (state, action) => {
      state.attendees.items.push(action.payload);
      state.currentAttendee.data = action.payload;
    });

    // Update event attendee
    builder.addCase(updateEventAttendee.fulfilled, (state, action) => {
      const updatedAttendee = action.payload;
      const index = state.attendees.items.findIndex(
        (attendee) =>
          attendee.eventId === updatedAttendee.eventId && attendee.userId === updatedAttendee.userId
      );
      if (index !== -1) {
        state.attendees.items[index] = updatedAttendee;
      }
      state.currentAttendee.data = updatedAttendee;
    });

    // Remove event attendee
    builder.addCase(removeEventAttendee.fulfilled, (state, action) => {
      const { eventId, userId } = action.payload;
      state.attendees.items = state.attendees.items.filter(
        (attendee) => !(attendee.eventId === eventId && attendee.userId === userId)
      );
      if (
        state.currentAttendee.data?.eventId === eventId &&
        state.currentAttendee.data?.userId === userId
      ) {
        state.currentAttendee.data = null;
      }
    });

    // Check in event attendee
    builder.addCase(checkInEventAttendee.fulfilled, (state, action) => {
      const updatedAttendee = action.payload;
      const index = state.attendees.items.findIndex(
        (attendee) =>
          attendee.eventId === updatedAttendee.eventId && attendee.userId === updatedAttendee.userId
      );
      if (index !== -1) {
        state.attendees.items[index] = updatedAttendee;
      }
      state.currentAttendee.data = updatedAttendee;
    });

    // Fetch group resources
    builder.addCase(fetchGroupResources.pending, (state) => {
      state.resources.loading = true;
      state.resources.error = null;
    });
    builder.addCase(fetchGroupResources.fulfilled, (state, action) => {
      state.resources.loading = false;
      state.resources.items = action.payload.data;
      state.resources.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupResources.rejected, (state, action) => {
      state.resources.loading = false;
      state.resources.error = action.payload as string;
    });

    // Fetch group discussions
    builder.addCase(fetchGroupDiscussions.pending, (state) => {
      state.discussions.loading = true;
      state.discussions.error = null;
    });
    builder.addCase(fetchGroupDiscussions.fulfilled, (state, action) => {
      state.discussions.loading = false;
      state.discussions.items = action.payload.data;
      state.discussions.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupDiscussions.rejected, (state, action) => {
      state.discussions.loading = false;
      state.discussions.error = action.payload as string;
    });

    // Fetch group actions
    builder.addCase(fetchGroupActions.pending, (state) => {
      state.actions.loading = true;
      state.actions.error = null;
    });
    builder.addCase(fetchGroupActions.fulfilled, (state, action) => {
      state.actions.loading = false;
      state.actions.items = action.payload.data;
      state.actions.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupActions.rejected, (state, action) => {
      state.actions.loading = false;
      state.actions.error = action.payload as string;
    });

    // Fetch group invitations
    builder.addCase(fetchGroupInvitations.pending, (state) => {
      state.invitations.loading = true;
      state.invitations.error = null;
    });
    builder.addCase(fetchGroupInvitations.fulfilled, (state, action) => {
      state.invitations.loading = false;
      state.invitations.items = action.payload.data;
      state.invitations.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupInvitations.rejected, (state, action) => {
      state.invitations.loading = false;
      state.invitations.error = action.payload as string;
    });

    // Fetch group join requests
    builder.addCase(fetchGroupJoinRequests.pending, (state) => {
      state.joinRequests.loading = true;
      state.joinRequests.error = null;
    });
    builder.addCase(fetchGroupJoinRequests.fulfilled, (state, action) => {
      state.joinRequests.loading = false;
      state.joinRequests.items = action.payload.data;
      state.joinRequests.total = action.payload.meta.total;
    });
    builder.addCase(fetchGroupJoinRequests.rejected, (state, action) => {
      state.joinRequests.loading = false;
      state.joinRequests.error = action.payload as string;
    });
  },
});

export const {
  resetCurrentGroup,
  resetCurrentMember,
  resetCurrentEvent,
  resetCurrentAttendee,
  resetCurrentResource,
  resetCurrentDiscussion,
  resetCurrentComment,
  resetCurrentAction,
  resetCurrentInvitation,
  resetCurrentJoinRequest,
} = groupsSlice.actions;

export default groupsSlice.reducer;
