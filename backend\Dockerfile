# Multi-stage build for Go services
FROM golang:1.21-alpine AS builder

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build all services
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o auth-service ./cmd/auth-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o content-service ./cmd/content-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o discussion-service ./cmd/discussion-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o payment-service ./cmd/payment-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o points-service ./cmd/points-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o livestream-service ./cmd/livestream-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o personalization-service ./cmd/personalization-service/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o progress-service ./cmd/progress-service/main.go

# Final stage - Auth Service
FROM alpine:latest AS auth-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/auth-service .
EXPOSE 8081
CMD ["./auth-service"]

# Final stage - Content Service  
FROM alpine:latest AS content-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/content-service .
EXPOSE 8082
CMD ["./content-service"]

# Final stage - Discussion Service
FROM alpine:latest AS discussion-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/discussion-service .
EXPOSE 8083
CMD ["./discussion-service"]

# Final stage - Payment Service
FROM alpine:latest AS payment-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/payment-service .
EXPOSE 8084
CMD ["./payment-service"]

# Final stage - Points Service
FROM alpine:latest AS points-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/points-service .
EXPOSE 8085
CMD ["./points-service"]

# Final stage - Livestream Service
FROM alpine:latest AS livestream-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/livestream-service .
EXPOSE 8086
CMD ["./livestream-service"]

# Final stage - Personalization Service
FROM alpine:latest AS personalization-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/personalization-service .
EXPOSE 8087
CMD ["./personalization-service"]

# Final stage - Progress Service
FROM alpine:latest AS progress-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/progress-service .
EXPOSE 8088
CMD ["./progress-service"]
