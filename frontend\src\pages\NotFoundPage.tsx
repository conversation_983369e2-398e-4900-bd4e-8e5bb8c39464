import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 1rem;
  max-width: 600px;
  margin: 0 auto;
`;

const ErrorCode = styled.h1`
  font-size: 8rem;
  margin: 0;
  color: #16213e;
  line-height: 1;
`;

const Title = styled.h2`
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const Description = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
`;

const HomeButton = styled(Link)`
  display: inline-block;
  background-color: #16213e;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  text-decoration: none;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
    text-decoration: none;
  }
`;

const NotFoundPage: React.FC = () => {
  return (
    <Container>
      <ErrorCode>404</ErrorCode>
      <Title>Page Not Found</Title>
      <Description>
        The page you are looking for might have been removed, had its name changed, or is temporarily
        unavailable.
      </Description>
      <HomeButton to="/">Return to Home Page</HomeButton>
    </Container>
  );
};

export default NotFoundPage;
