package repository

import (
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/tips/models"
	"gorm.io/gorm"
)

// TipsRepository defines the interface for tips data access
type TipsRepository interface {
	// Tip methods
	GetTipByID(id uint) (*models.Tip, error)
	GetAllTips() ([]models.Tip, error)
	GetActiveTips() ([]models.Tip, error)
	GetTipsByCategory(category models.TipCategory) ([]models.Tip, error)
	GetTipsByTrigger(trigger models.TipTrigger) ([]models.Tip, error)
	CreateTip(tip *models.Tip) error
	UpdateTip(tip *models.Tip) error
	DeleteTip(id uint) error
	
	// TipRule methods
	GetTipRulesByTipID(tipID uint) ([]models.TipRule, error)
	GetTipRulesByContext(contextType, contextValue string) ([]models.TipRule, error)
	CreateTipRule(rule *models.TipRule) error
	UpdateTipRule(rule *models.TipRule) error
	DeleteTipRule(id uint) error
	
	// UserTip methods
	GetUserTipsByUserID(userID uint) ([]models.UserTip, error)
	GetUserTipByUserAndTipID(userID, tipID uint) (*models.UserTip, error)
	CreateUserTip(userTip *models.UserTip) error
	UpdateUserTip(userTip *models.UserTip) error
	
	// TipFeedback methods
	GetTipFeedbackByTipID(tipID uint) ([]models.TipFeedback, error)
	CreateTipFeedback(feedback *models.TipFeedback) error
	
	// Statistics methods
	GetTipStatistics(tipID uint) (*models.TipStatistics, error)
	GetAllTipStatistics() ([]models.TipStatistics, error)
	
	// Context-aware tip retrieval
	GetContextualTips(request *models.TipRequest) ([]models.Tip, error)
}

// GormTipsRepository implements TipsRepository using GORM
type GormTipsRepository struct {
	db *gorm.DB
}

// NewTipsRepository creates a new tips repository
func NewTipsRepository(db *gorm.DB) TipsRepository {
	return &GormTipsRepository{db: db}
}

// GetTipByID retrieves a tip by its ID
func (r *GormTipsRepository) GetTipByID(id uint) (*models.Tip, error) {
	var tip models.Tip
	result := r.db.First(&tip, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &tip, nil
}

// GetAllTips retrieves all tips
func (r *GormTipsRepository) GetAllTips() ([]models.Tip, error) {
	var tips []models.Tip
	result := r.db.Find(&tips)
	if result.Error != nil {
		return nil, result.Error
	}
	return tips, nil
}

// GetActiveTips retrieves all active tips
func (r *GormTipsRepository) GetActiveTips() ([]models.Tip, error) {
	var tips []models.Tip
	result := r.db.Where("active = ?", true).Find(&tips)
	if result.Error != nil {
		return nil, result.Error
	}
	return tips, nil
}

// GetTipsByCategory retrieves tips by category
func (r *GormTipsRepository) GetTipsByCategory(category models.TipCategory) ([]models.Tip, error) {
	var tips []models.Tip
	result := r.db.Where("category = ? AND active = ?", category, true).Find(&tips)
	if result.Error != nil {
		return nil, result.Error
	}
	return tips, nil
}

// GetTipsByTrigger retrieves tips by trigger
func (r *GormTipsRepository) GetTipsByTrigger(trigger models.TipTrigger) ([]models.Tip, error) {
	var tips []models.Tip
	result := r.db.Where("trigger = ? AND active = ?", trigger, true).Find(&tips)
	if result.Error != nil {
		return nil, result.Error
	}
	return tips, nil
}

// CreateTip creates a new tip
func (r *GormTipsRepository) CreateTip(tip *models.Tip) error {
	return r.db.Create(tip).Error
}

// UpdateTip updates an existing tip
func (r *GormTipsRepository) UpdateTip(tip *models.Tip) error {
	return r.db.Save(tip).Error
}

// DeleteTip deletes a tip by its ID
func (r *GormTipsRepository) DeleteTip(id uint) error {
	return r.db.Delete(&models.Tip{}, id).Error
}

// GetTipRulesByTipID retrieves tip rules by tip ID
func (r *GormTipsRepository) GetTipRulesByTipID(tipID uint) ([]models.TipRule, error) {
	var rules []models.TipRule
	result := r.db.Where("tip_id = ?", tipID).Find(&rules)
	if result.Error != nil {
		return nil, result.Error
	}
	return rules, nil
}

// GetTipRulesByContext retrieves tip rules by context
func (r *GormTipsRepository) GetTipRulesByContext(contextType, contextValue string) ([]models.TipRule, error) {
	var rules []models.TipRule
	result := r.db.Where("context_type = ? AND context_value = ?", contextType, contextValue).Find(&rules)
	if result.Error != nil {
		return nil, result.Error
	}
	return rules, nil
}

// CreateTipRule creates a new tip rule
func (r *GormTipsRepository) CreateTipRule(rule *models.TipRule) error {
	return r.db.Create(rule).Error
}

// UpdateTipRule updates an existing tip rule
func (r *GormTipsRepository) UpdateTipRule(rule *models.TipRule) error {
	return r.db.Save(rule).Error
}

// DeleteTipRule deletes a tip rule by its ID
func (r *GormTipsRepository) DeleteTipRule(id uint) error {
	return r.db.Delete(&models.TipRule{}, id).Error
}

// GetUserTipsByUserID retrieves user tips by user ID
func (r *GormTipsRepository) GetUserTipsByUserID(userID uint) ([]models.UserTip, error) {
	var userTips []models.UserTip
	result := r.db.Where("user_id = ?", userID).Find(&userTips)
	if result.Error != nil {
		return nil, result.Error
	}
	return userTips, nil
}

// GetUserTipByUserAndTipID retrieves a user tip by user ID and tip ID
func (r *GormTipsRepository) GetUserTipByUserAndTipID(userID, tipID uint) (*models.UserTip, error) {
	var userTip models.UserTip
	result := r.db.Where("user_id = ? AND tip_id = ?", userID, tipID).First(&userTip)
	if result.Error != nil {
		return nil, result.Error
	}
	return &userTip, nil
}

// CreateUserTip creates a new user tip
func (r *GormTipsRepository) CreateUserTip(userTip *models.UserTip) error {
	return r.db.Create(userTip).Error
}

// UpdateUserTip updates an existing user tip
func (r *GormTipsRepository) UpdateUserTip(userTip *models.UserTip) error {
	return r.db.Save(userTip).Error
}

// GetTipFeedbackByTipID retrieves tip feedback by tip ID
func (r *GormTipsRepository) GetTipFeedbackByTipID(tipID uint) ([]models.TipFeedback, error) {
	var feedback []models.TipFeedback
	result := r.db.Where("tip_id = ?", tipID).Find(&feedback)
	if result.Error != nil {
		return nil, result.Error
	}
	return feedback, nil
}

// CreateTipFeedback creates new tip feedback
func (r *GormTipsRepository) CreateTipFeedback(feedback *models.TipFeedback) error {
	return r.db.Create(feedback).Error
}

// GetTipStatistics retrieves statistics for a tip
func (r *GormTipsRepository) GetTipStatistics(tipID uint) (*models.TipStatistics, error) {
	var stats models.TipStatistics
	stats.TipID = tipID
	
	// Get view count
	if err := r.db.Model(&models.UserTip{}).Where("tip_id = ?", tipID).Count(&stats.ViewCount).Error; err != nil {
		return nil, err
	}
	
	// Get dismiss count
	if err := r.db.Model(&models.UserTip{}).Where("tip_id = ? AND dismissed = ?", tipID, true).Count(&stats.DismissCount).Error; err != nil {
		return nil, err
	}
	
	// Get click count
	if err := r.db.Model(&models.UserTip{}).Where("tip_id = ? AND clicked = ?", tipID, true).Count(&stats.ClickCount).Error; err != nil {
		return nil, err
	}
	
	// Get helpful count
	if err := r.db.Model(&models.TipFeedback{}).Where("tip_id = ? AND helpful = ?", tipID, true).Count(&stats.HelpfulCount).Error; err != nil {
		return nil, err
	}
	
	// Calculate effectiveness rate
	if stats.ViewCount > 0 {
		stats.EffectivenessRate = float64(stats.ClickCount) / float64(stats.ViewCount) * 100
	}
	
	return &stats, nil
}

// GetAllTipStatistics retrieves statistics for all tips
func (r *GormTipsRepository) GetAllTipStatistics() ([]models.TipStatistics, error) {
	var tips []models.Tip
	if err := r.db.Find(&tips).Error; err != nil {
		return nil, err
	}
	
	var allStats []models.TipStatistics
	for _, tip := range tips {
		stats, err := r.GetTipStatistics(tip.ID)
		if err != nil {
			return nil, err
		}
		allStats = append(allStats, *stats)
	}
	
	return allStats, nil
}

// GetContextualTips retrieves tips based on context
func (r *GormTipsRepository) GetContextualTips(request *models.TipRequest) ([]models.Tip, error) {
	// Get rules that match the context
	var rules []models.TipRule
	if err := r.db.Where("context_type = ? AND context_value = ?", request.ContextType, request.ContextID).Find(&rules).Error; err != nil {
		return nil, err
	}
	
	// If no specific rules found, try to find general rules for the page
	if len(rules) == 0 && request.PageURL != "" {
		if err := r.db.Where("context_type = 'page' AND context_value = ?", request.PageURL).Find(&rules).Error; err != nil {
			return nil, err
		}
	}
	
	// If still no rules, get default tips
	if len(rules) == 0 {
		if err := r.db.Where("context_type = 'default'").Find(&rules).Error; err != nil {
			return nil, err
		}
	}
	
	// Get tips based on rules
	var tipIDs []uint
	for _, rule := range rules {
		tipIDs = append(tipIDs, rule.TipID)
	}
	
	// If no tips found, return empty slice
	if len(tipIDs) == 0 {
		return []models.Tip{}, nil
	}
	
	// Get active tips
	var tips []models.Tip
	if err := r.db.Where("id IN ? AND active = ?", tipIDs, true).Find(&tips).Error; err != nil {
		return nil, err
	}
	
	// If user is authenticated, filter out tips they've already seen
	if request.UserID > 0 {
		var filteredTips []models.Tip
		for _, tip := range tips {
			var userTip models.UserTip
			result := r.db.Where("user_id = ? AND tip_id = ?", request.UserID, tip.ID).First(&userTip)
			if result.Error != nil || (!userTip.Dismissed && !userTip.Clicked) {
				filteredTips = append(filteredTips, tip)
			}
		}
		tips = filteredTips
	}
	
	return tips, nil
}
