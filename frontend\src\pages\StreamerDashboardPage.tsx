import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Button,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody
} from '@mui/material';
import {
  Videocam as VideoIcon,
  VideoCall as LiveIcon,
  Schedule as ScheduleIcon,
  History as HistoryIcon,
  BarChart as AnalyticsIcon,
  MonetizationOn as RevenueIcon,
  CardGiftcard as GiftIcon,
  Add as AddIcon,
  Person as PersonIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import StreamAnalytics from '../components/livestream/StreamAnalytics';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';
import CreateStreamModal from '../components/livestream/CreateStreamModal';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-controls={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Sample data for demonstration
const SAMPLE_STREAMS = [
  {
    id: 1,
    title: 'Introduction to Nigerian Literature',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    status: 'live',
    viewerCount: 245,
    startTime: new Date(Date.now() - 3600000).toISOString(),
    totalGiftsValue: 15000
  },
  {
    id: 2,
    title: 'Upcoming Q&A Session on Chinua Achebe',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    status: 'scheduled',
    scheduledStart: new Date(Date.now() + 86400000).toISOString(),
    totalGiftsValue: 0
  },
  {
    id: 3,
    title: 'Poetry Reading Session',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    status: 'ended',
    viewerCount: 0,
    startTime: new Date(Date.now() - 172800000).toISOString(),
    endTime: new Date(Date.now() - 169200000).toISOString(),
    totalGiftsValue: 8500
  }
];

const SAMPLE_REVENUE_SUMMARY = {
  daily: {
    totalGifts: 25,
    totalCoins: 2500,
    totalNairaValue: 25000,
    netRevenue: 17500
  },
  weekly: {
    totalGifts: 120,
    totalCoins: 12000,
    totalNairaValue: 120000,
    netRevenue: 84000
  },
  monthly: {
    totalGifts: 500,
    totalCoins: 50000,
    totalNairaValue: 500000,
    netRevenue: 350000
  },
  unpaidRevenue: 250000,
  recentWithdrawals: [
    {
      id: 1,
      amount: 100000,
      status: 'completed',
      createdAt: new Date(Date.now() - 604800000).toISOString()
    },
    {
      id: 2,
      amount: 150000,
      status: 'pending',
      createdAt: new Date(Date.now() - 86400000).toISOString()
    }
  ]
};

const SAMPLE_TOP_GIFTS = [
  {
    id: 1,
    giftName: 'Diamond',
    senderId: 123,
    coinsAmount: 500,
    nairaValue: 5000,
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    isAnonymous: false
  },
  {
    id: 2,
    giftName: 'Crown',
    senderId: 456,
    coinsAmount: 1000,
    nairaValue: 10000,
    createdAt: new Date(Date.now() - 7200000).toISOString(),
    isAnonymous: true
  },
  {
    id: 3,
    giftName: 'Rocket',
    senderId: 789,
    coinsAmount: 5000,
    nairaValue: 50000,
    createdAt: new Date(Date.now() - 10800000).toISOString(),
    isAnonymous: false
  }
];

const StreamerDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  const [tabValue, setTabValue] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [streams, setStreams] = useState(SAMPLE_STREAMS);
  const [revenueSummary, setRevenueSummary] = useState(SAMPLE_REVENUE_SUMMARY);
  const [topGifts, setTopGifts] = useState(SAMPLE_TOP_GIFTS);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedStream, setSelectedStream] = useState<any | null>(null);

  useEffect(() => {
    if (!user) {
      navigate('/login');
    }

    // Simulate loading data
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [user, navigate]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCreateStream = () => {
    setIsCreateModalOpen(true);
  };

  const handleStreamClick = (stream: any) => {
    if (stream.status === 'live' || stream.status === 'ended') {
      navigate(`/livestream/${stream.id}`);
    } else {
      setSelectedStream(stream);
    }
  };

  const handleStartStream = (streamId: number) => {
    // In a real app, this would call an API to start the stream
    setStreams(streams.map(stream =>
      stream.id === streamId
        ? { ...stream, status: 'live', startTime: new Date().toISOString() }
        : stream
    ));

    // Navigate to the stream
    navigate(`/livestream/${streamId}`);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Streamer Dashboard
      </Typography>

      <LivestreamNavigation />

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Streams
              </Typography>
              <Typography variant="h4">
                {streams.length}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Typography variant="body2" color="text.secondary">
                  {streams.filter(s => s.status === 'live').length} live now
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Viewers
              </Typography>
              <Typography variant="h4">
                {streams.reduce((sum, stream) => sum + (stream.viewerCount || 0), 0)}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Typography variant="body2" color="text.secondary">
                  Across all streams
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Revenue
              </Typography>
              <Typography variant="h4">
                ₦{streams.reduce((sum, stream) => sum + (stream.totalGiftsValue || 0), 0).toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Typography variant="body2" color="text.secondary">
                  From all streams
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Available for Withdrawal
              </Typography>
              <Typography variant="h4">
                ₦{revenueSummary.unpaidRevenue.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => navigate('/creator/revenue')}
                >
                  Withdraw
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h2">
            Your Streams
          </Typography>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateStream}
          >
            Create Stream
          </Button>
        </Box>

        <Grid container spacing={2}>
          {streams.map((stream) => (
            <Grid item xs={12} sm={6} md={4} key={stream.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <CardActionArea
                  onClick={() => handleStreamClick(stream)}
                  sx={{ flexGrow: 1 }}
                >
                  <Box sx={{ position: 'relative' }}>
                    <Box
                      component="img"
                      src={stream.thumbnailUrl}
                      alt={stream.title}
                      sx={{ width: '100%', height: 180, objectFit: 'cover' }}
                    />

                    <Chip
                      label={stream.status === 'live' ? 'LIVE' : stream.status === 'scheduled' ? 'SCHEDULED' : 'ENDED'}
                      color={stream.status === 'live' ? 'error' : stream.status === 'scheduled' ? 'primary' : 'default'}
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 10,
                        left: 10,
                        fontWeight: 'bold'
                      }}
                    />

                    {stream.status === 'live' && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 10,
                          right: 10,
                          bgcolor: 'rgba(0, 0, 0, 0.6)',
                          color: 'white',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <VideoIcon fontSize="small" sx={{ mr: 0.5 }} />
                        <Typography variant="caption">
                          {stream.viewerCount}
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  <CardContent>
                    <Typography variant="h6" component="div" noWrap>
                      {stream.title}
                    </Typography>

                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                      <Typography variant="caption" color="text.secondary">
                        {stream.status === 'live'
                          ? `Started ${new Date(stream.startTime).toLocaleString()}`
                          : stream.status === 'scheduled'
                            ? `Scheduled for ${new Date(stream.scheduledStart).toLocaleString()}`
                            : `Ended ${new Date(stream.endTime).toLocaleString()}`
                        }
                      </Typography>

                      {stream.totalGiftsValue > 0 && (
                        <Chip
                          label={`₦${stream.totalGiftsValue.toLocaleString()}`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </CardContent>
                </CardActionArea>

                {stream.status === 'scheduled' && (
                  <Box sx={{ p: 1, pt: 0 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      fullWidth
                      startIcon={<LiveIcon />}
                      onClick={() => handleStartStream(stream.id)}
                    >
                      Start Stream
                    </Button>
                  </Box>
                )}
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Paper elevation={2}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<AnalyticsIcon />} label="Analytics" id="dashboard-tab-0" aria-controls="dashboard-tabpanel-0" />
          <Tab icon={<RevenueIcon />} label="Revenue" id="dashboard-tab-1" aria-controls="dashboard-tabpanel-1" />
          <Tab icon={<GiftIcon />} label="Top Gifts" id="dashboard-tab-2" aria-controls="dashboard-tabpanel-2" />
        </Tabs>

        {/* Analytics Tab */}
        <TabPanel value={tabValue} index={0}>
          {selectedStream ? (
            <StreamAnalytics streamId={selectedStream.id} creatorId={user?.id || 0} />
          ) : (
            <Box textAlign="center" py={4}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Select a stream to view analytics
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Click on any of your streams above to view detailed analytics
              </Typography>
            </Box>
          )}
        </TabPanel>

        {/* Revenue Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                Revenue Summary
              </Typography>

              <Grid container spacing={2}>
                {['daily', 'weekly', 'monthly'].map((period) => (
                  <Grid item xs={12} sm={4} key={period}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          {period.charAt(0).toUpperCase() + period.slice(1)} Revenue
                        </Typography>
                        <Typography variant="h5" color="primary">
                          ₦{(revenueSummary as any)[period].netRevenue.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          From {(revenueSummary as any)[period].totalGifts} gifts
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              <Box mt={4}>
                <Typography variant="h6" gutterBottom>
                  Revenue by Stream
                </Typography>

                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Stream</TableCell>
                        <TableCell align="right">Gifts</TableCell>
                        <TableCell align="right">Revenue</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {streams.map((stream) => (
                        <TableRow key={stream.id}>
                          <TableCell>{stream.title}</TableCell>
                          <TableCell align="right">
                            {Math.floor(stream.totalGiftsValue / 100)} gifts
                          </TableCell>
                          <TableCell align="right">
                            ₦{stream.totalGiftsValue.toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Withdrawal History
              </Typography>

              <List>
                {revenueSummary.recentWithdrawals.map((withdrawal) => (
                  <React.Fragment key={withdrawal.id}>
                    <ListItem>
                      <ListItemText
                        primary={`₦${withdrawal.amount.toLocaleString()}`}
                        secondary={new Date(withdrawal.createdAt).toLocaleDateString()}
                      />
                      <Chip
                        label={withdrawal.status.toUpperCase()}
                        color={withdrawal.status === 'completed' ? 'success' : 'warning'}
                        size="small"
                      />
                    </ListItem>
                    <Divider component="li" />
                  </React.Fragment>
                ))}
              </List>

              <Box mt={2}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={() => navigate('/creator/revenue')}
                >
                  Manage Revenue
                </Button>
              </Box>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Top Gifts Tab */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Top Gifts Received
          </Typography>

          <List>
            {topGifts.map((gift) => (
              <React.Fragment key={gift.id}>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar>
                      {gift.isAnonymous ? <VisibilityOffIcon /> : <PersonIcon />}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="body1">
                        {gift.isAnonymous ? 'Anonymous' : `User #${gift.senderId}`} sent a {gift.giftName}
                      </Typography>
                    }
                    secondary={
                      <>
                        <Typography component="span" variant="body2" color="text.primary">
                          {gift.coinsAmount} coins (₦{gift.nairaValue.toLocaleString()})
                        </Typography>
                        <Typography component="div" variant="caption" color="text.secondary">
                          {new Date(gift.createdAt).toLocaleString()}
                        </Typography>
                      </>
                    }
                  />
                  <Chip
                    label={`₦${gift.nairaValue.toLocaleString()}`}
                    color="primary"
                    variant="outlined"
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
              </React.Fragment>
            ))}
          </List>

          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              variant="outlined"
              onClick={() => navigate('/creator/revenue')}
            >
              View All Gifts
            </Button>
          </Box>
        </TabPanel>
      </Paper>

      <CreateStreamModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />
    </Container>
  );
};

export default StreamerDashboardPage;
