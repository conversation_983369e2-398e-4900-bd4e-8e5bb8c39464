package service

import (
	"context"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// GifterRankingService defines the interface for gifter ranking business logic
type GifterRankingService interface {
	// Ranking queries
	GetStreamRankings(ctx context.Context, streamID uint, period string, limit int) ([]repository.GifterRanking, error)
	GetGlobalRankings(ctx context.Context, period string, limit int) ([]repository.GifterRanking, error)
	GetUserRanking(ctx context.Context, userID uint, streamID *uint, period string) (*repository.GifterRanking, error)
	
	// Ranking operations
	UpdateStreamRankings(ctx context.Context, streamID uint) error
	UpdateGlobalRankings(ctx context.Context) error
	CalculateBadgeLevels(ctx context.Context) error
}

// GifterRankingServiceImpl implements the GifterRankingService interface
type GifterRankingServiceImpl struct {
	repo   repository.GifterRankingRepository
	logger *logger.Logger
}

// NewGifterRankingService creates a new instance of the gifter ranking service
func NewGifterRankingService(repo repository.GifterRankingRepository, logger *logger.Logger) GifterRankingService {
	return &GifterRankingServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// GetStreamRankings retrieves rankings for a stream
func (s *GifterRankingServiceImpl) GetStreamRankings(ctx context.Context, streamID uint, period string, limit int) ([]repository.GifterRanking, error) {
	// Validate period
	if !isValidPeriod(period) {
		period = "daily" // Default to daily
	}
	
	rankings, err := s.repo.GetStreamRankings(ctx, streamID, period, limit)
	if err != nil {
		return nil, err
	}
	
	s.logger.Infof("Retrieved %d %s rankings for stream %d", len(rankings), period, streamID)
	
	return rankings, nil
}

// GetGlobalRankings retrieves global rankings
func (s *GifterRankingServiceImpl) GetGlobalRankings(ctx context.Context, period string, limit int) ([]repository.GifterRanking, error) {
	// Validate period
	if !isValidPeriod(period) {
		period = "daily" // Default to daily
	}
	
	rankings, err := s.repo.GetGlobalRankings(ctx, period, limit)
	if err != nil {
		return nil, err
	}
	
	s.logger.Infof("Retrieved %d global %s rankings", len(rankings), period)
	
	return rankings, nil
}

// GetUserRanking retrieves a user's ranking
func (s *GifterRankingServiceImpl) GetUserRanking(ctx context.Context, userID uint, streamID *uint, period string) (*repository.GifterRanking, error) {
	// Validate period
	if !isValidPeriod(period) {
		period = "daily" // Default to daily
	}
	
	ranking, err := s.repo.GetUserRanking(ctx, userID, streamID, period)
	if err != nil {
		return nil, err
	}
	
	if streamID != nil {
		s.logger.Infof("Retrieved %s ranking for user %d in stream %d: rank %d", period, userID, *streamID, ranking.Rank)
	} else {
		s.logger.Infof("Retrieved global %s ranking for user %d: rank %d", period, userID, ranking.Rank)
	}
	
	return ranking, nil
}

// UpdateStreamRankings updates rankings for a stream
func (s *GifterRankingServiceImpl) UpdateStreamRankings(ctx context.Context, streamID uint) error {
	if err := s.repo.UpdateStreamRankings(ctx, streamID); err != nil {
		s.logger.Errorf("Failed to update rankings for stream %d: %v", streamID, err)
		return err
	}
	
	s.logger.Infof("Updated rankings for stream %d", streamID)
	
	return nil
}

// UpdateGlobalRankings updates global rankings
func (s *GifterRankingServiceImpl) UpdateGlobalRankings(ctx context.Context) error {
	if err := s.repo.UpdateGlobalRankings(ctx); err != nil {
		s.logger.Errorf("Failed to update global rankings: %v", err)
		return err
	}
	
	s.logger.Info("Updated global rankings")
	
	return nil
}

// CalculateBadgeLevels calculates badge levels for all rankings
func (s *GifterRankingServiceImpl) CalculateBadgeLevels(ctx context.Context) error {
	if err := s.repo.CalculateBadgeLevels(ctx); err != nil {
		s.logger.Errorf("Failed to calculate badge levels: %v", err)
		return err
	}
	
	s.logger.Info("Calculated badge levels for all rankings")
	
	return nil
}

// isValidPeriod checks if a period is valid
func isValidPeriod(period string) bool {
	validPeriods := map[string]bool{
		"daily":    true,
		"weekly":   true,
		"monthly":  true,
		"all_time": true,
	}
	
	return validPeriods[period]
}
