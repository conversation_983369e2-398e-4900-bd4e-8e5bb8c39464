import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { ResourceService } from '../../api';
import { ResourcesState } from '../../types';

// Initial state
const initialState: ResourcesState = {
  categories: [],
  resources: [],
  currentResource: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchCategories = createAsyncThunk(
  'resources/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await ResourceService.getCategories();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

export const fetchResourcesByCategory = createAsyncThunk(
  'resources/fetchResourcesByCategory',
  async (categoryId: string, { rejectWithValue }) => {
    try {
      return await ResourceService.getResourcesByCategory(categoryId);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch resources');
    }
  }
);

export const fetchResourceById = createAsyncThunk(
  'resources/fetchResourceById',
  async (resourceId: string, { rejectWithValue }) => {
    try {
      return await ResourceService.getResourceById(resourceId);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch resource');
    }
  }
);

export const trackDownload = createAsyncThunk(
  'resources/trackDownload',
  async (resourceId: string, { rejectWithValue }) => {
    try {
      await ResourceService.trackDownload(resourceId);
      return resourceId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to track download');
    }
  }
);

export const searchResources = createAsyncThunk(
  'resources/searchResources',
  async (query: string, { rejectWithValue }) => {
    try {
      return await ResourceService.searchResources(query);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search resources');
    }
  }
);

// Slice
const resourcesSlice = createSlice({
  name: 'resources',
  initialState,
  reducers: {
    clearCurrentResource: (state) => {
      state.currentResource = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch resources by category
      .addCase(fetchResourcesByCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResourcesByCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resources = action.payload;
      })
      .addCase(fetchResourcesByCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch resource by ID
      .addCase(fetchResourceById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResourceById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentResource = action.payload;
      })
      .addCase(fetchResourceById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Track download
      .addCase(trackDownload.fulfilled, (state, action) => {
        // Increment download count for the resource
        if (state.currentResource && state.currentResource.id === action.payload) {
          state.currentResource.downloads_count += 1;
        }
        
        const resourceIndex = state.resources.findIndex((r) => r.id === action.payload);
        if (resourceIndex !== -1) {
          state.resources[resourceIndex].downloads_count += 1;
        }
      })
      // Search resources
      .addCase(searchResources.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchResources.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resources = action.payload;
      })
      .addCase(searchResources.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearCurrentResource, clearError } = resourcesSlice.actions;
export default resourcesSlice.reducer;
