package repository

import (
	"encoding/json"
	"errors"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/models"
	"gorm.io/gorm"
	"time"
)

// PersonalizationRepository defines the interface for personalization data access
type PersonalizationRepository interface {
	// Learning Style methods
	GetLearningStyleByUserID(userID uint) (*models.LearningStyle, error)
	SaveLearningStyle(style *models.LearningStyle) error
	
	// Learning Preference methods
	GetLearningPreferenceByUserID(userID uint) (*models.LearningPreference, error)
	SaveLearningPreference(pref *models.LearningPreference) error
	
	// Personalized Path methods
	GetPersonalizedPathsByUserID(userID uint) ([]models.PersonalizedPath, error)
	GetPersonalizedPathByID(pathID uint) (*models.PersonalizedPath, error)
	CreatePersonalizedPath(path *models.PersonalizedPath) error
	UpdatePersonalizedPath(path *models.PersonalizedPath) error
	DeletePersonalizedPath(pathID uint) error
	
	// Path Item methods
	GetPathItemsByPathID(pathID uint) ([]models.PathItem, error)
	AddPathItem(item *models.PathItem) error
	UpdatePathItem(item *models.PathItem) error
	DeletePathItem(itemID uint) error
	MarkPathItemComplete(itemID uint, completed bool) error
	
	// Assessment Question methods
	GetAllAssessmentQuestions() ([]models.AssessmentQuestion, error)
	GetActiveAssessmentQuestions() ([]models.AssessmentQuestion, error)
	SaveAssessmentQuestion(question *models.AssessmentQuestion) error
	
	// Assessment Response methods
	SaveAssessmentResponses(responses []models.AssessmentResponse) error
	GetAssessmentResponsesByUserID(userID uint) ([]models.AssessmentResponse, error)
	
	// Content Recommendation methods
	GetRecommendationsForUser(userID uint, contentType string, limit int) ([]models.ContentRecommendation, error)
	SaveContentRecommendation(rec *models.ContentRecommendation) error
	UpdateRecommendationStatus(recID uint, viewed, saved, rejected bool) error
	
	// Difficulty Level methods
	GetAllDifficultyLevels() ([]models.DifficultyLevel, error)
	GetDifficultyLevelByName(name string) (*models.DifficultyLevel, error)
	
	// User Performance methods
	GetUserPerformance(userID uint) (*models.UserPerformance, error)
	UpdateUserPerformance(perf *models.UserPerformance) error
	
	// Learning Path Template methods
	GetAllLearningPathTemplates() ([]models.LearningPathTemplate, error)
	GetActiveLearningPathTemplates() ([]models.LearningPathTemplate, error)
	GetLearningPathTemplateByID(templateID uint) (*models.LearningPathTemplate, error)
	SaveLearningPathTemplate(template *models.LearningPathTemplate) error
	
	// Template Item methods
	GetTemplateItemsByTemplateID(templateID uint) ([]models.TemplateItem, error)
	SaveTemplateItem(item *models.TemplateItem) error
}

// GormPersonalizationRepository implements PersonalizationRepository using GORM
type GormPersonalizationRepository struct {
	db *gorm.DB
}

// NewPersonalizationRepository creates a new personalization repository
func NewPersonalizationRepository(db *gorm.DB) PersonalizationRepository {
	return &GormPersonalizationRepository{db: db}
}

// GetLearningStyleByUserID retrieves a user's learning style
func (r *GormPersonalizationRepository) GetLearningStyleByUserID(userID uint) (*models.LearningStyle, error) {
	var style models.LearningStyle
	result := r.db.Where("user_id = ?", userID).First(&style)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // User doesn't have a learning style yet
		}
		return nil, result.Error
	}
	return &style, nil
}

// SaveLearningStyle saves a user's learning style
func (r *GormPersonalizationRepository) SaveLearningStyle(style *models.LearningStyle) error {
	// Check if the user already has a learning style
	existingStyle, err := r.GetLearningStyleByUserID(style.UserID)
	if err != nil {
		return err
	}
	
	if existingStyle != nil {
		// Update existing style
		style.ID = existingStyle.ID
		return r.db.Save(style).Error
	}
	
	// Create new style
	return r.db.Create(style).Error
}

// GetLearningPreferenceByUserID retrieves a user's learning preferences
func (r *GormPersonalizationRepository) GetLearningPreferenceByUserID(userID uint) (*models.LearningPreference, error) {
	var pref models.LearningPreference
	result := r.db.Where("user_id = ?", userID).First(&pref)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // User doesn't have preferences yet
		}
		return nil, result.Error
	}
	
	// Deserialize JSON fields
	if pref.PreferredTopicsDB != "" {
		if err := json.Unmarshal([]byte(pref.PreferredTopicsDB), &pref.PreferredTopics); err != nil {
			return nil, err
		}
	}
	
	if pref.AvoidTopicsDB != "" {
		if err := json.Unmarshal([]byte(pref.AvoidTopicsDB), &pref.AvoidTopics); err != nil {
			return nil, err
		}
	}
	
	if pref.PreferredFormatsDB != "" {
		if err := json.Unmarshal([]byte(pref.PreferredFormatsDB), &pref.PreferredFormats); err != nil {
			return nil, err
		}
	}
	
	if pref.LearningGoalsDB != "" {
		if err := json.Unmarshal([]byte(pref.LearningGoalsDB), &pref.LearningGoals); err != nil {
			return nil, err
		}
	}
	
	return &pref, nil
}

// SaveLearningPreference saves a user's learning preferences
func (r *GormPersonalizationRepository) SaveLearningPreference(pref *models.LearningPreference) error {
	// Serialize JSON fields
	if pref.PreferredTopics != nil {
		data, err := json.Marshal(pref.PreferredTopics)
		if err != nil {
			return err
		}
		pref.PreferredTopicsDB = string(data)
	}
	
	if pref.AvoidTopics != nil {
		data, err := json.Marshal(pref.AvoidTopics)
		if err != nil {
			return err
		}
		pref.AvoidTopicsDB = string(data)
	}
	
	if pref.PreferredFormats != nil {
		data, err := json.Marshal(pref.PreferredFormats)
		if err != nil {
			return err
		}
		pref.PreferredFormatsDB = string(data)
	}
	
	if pref.LearningGoals != nil {
		data, err := json.Marshal(pref.LearningGoals)
		if err != nil {
			return err
		}
		pref.LearningGoalsDB = string(data)
	}
	
	// Check if the user already has preferences
	existingPref, err := r.GetLearningPreferenceByUserID(pref.UserID)
	if err != nil {
		return err
	}
	
	if existingPref != nil {
		// Update existing preferences
		pref.ID = existingPref.ID
		return r.db.Save(pref).Error
	}
	
	// Create new preferences
	return r.db.Create(pref).Error
}

// GetPersonalizedPathsByUserID retrieves all personalized paths for a user
func (r *GormPersonalizationRepository) GetPersonalizedPathsByUserID(userID uint) ([]models.PersonalizedPath, error) {
	var paths []models.PersonalizedPath
	result := r.db.Where("user_id = ?", userID).Find(&paths)
	if result.Error != nil {
		return nil, result.Error
	}
	return paths, nil
}

// GetPersonalizedPathByID retrieves a personalized path by ID
func (r *GormPersonalizationRepository) GetPersonalizedPathByID(pathID uint) (*models.PersonalizedPath, error) {
	var path models.PersonalizedPath
	result := r.db.First(&path, pathID)
	if result.Error != nil {
		return nil, result.Error
	}
	return &path, nil
}

// CreatePersonalizedPath creates a new personalized path
func (r *GormPersonalizationRepository) CreatePersonalizedPath(path *models.PersonalizedPath) error {
	return r.db.Create(path).Error
}

// UpdatePersonalizedPath updates an existing personalized path
func (r *GormPersonalizationRepository) UpdatePersonalizedPath(path *models.PersonalizedPath) error {
	return r.db.Save(path).Error
}

// DeletePersonalizedPath deletes a personalized path
func (r *GormPersonalizationRepository) DeletePersonalizedPath(pathID uint) error {
	// First delete all path items
	if err := r.db.Where("path_id = ?", pathID).Delete(&models.PathItem{}).Error; err != nil {
		return err
	}
	
	// Then delete the path
	return r.db.Delete(&models.PersonalizedPath{}, pathID).Error
}

// GetPathItemsByPathID retrieves all items in a personalized path
func (r *GormPersonalizationRepository) GetPathItemsByPathID(pathID uint) ([]models.PathItem, error) {
	var items []models.PathItem
	result := r.db.Where("path_id = ?", pathID).Order("order").Find(&items)
	if result.Error != nil {
		return nil, result.Error
	}
	return items, nil
}

// AddPathItem adds an item to a personalized path
func (r *GormPersonalizationRepository) AddPathItem(item *models.PathItem) error {
	return r.db.Create(item).Error
}

// UpdatePathItem updates an existing path item
func (r *GormPersonalizationRepository) UpdatePathItem(item *models.PathItem) error {
	return r.db.Save(item).Error
}

// DeletePathItem deletes a path item
func (r *GormPersonalizationRepository) DeletePathItem(itemID uint) error {
	return r.db.Delete(&models.PathItem{}, itemID).Error
}

// MarkPathItemComplete marks a path item as complete or incomplete
func (r *GormPersonalizationRepository) MarkPathItemComplete(itemID uint, completed bool) error {
	var item models.PathItem
	if err := r.db.First(&item, itemID).Error; err != nil {
		return err
	}
	
	item.IsCompleted = completed
	if completed {
		now := time.Now()
		item.CompletedAt = &now
	} else {
		item.CompletedAt = nil
	}
	
	return r.db.Save(&item).Error
}

// GetAllAssessmentQuestions retrieves all assessment questions
func (r *GormPersonalizationRepository) GetAllAssessmentQuestions() ([]models.AssessmentQuestion, error) {
	var questions []models.AssessmentQuestion
	result := r.db.Find(&questions)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize options
	for i := range questions {
		if questions[i].OptionsDB != "" {
			if err := json.Unmarshal([]byte(questions[i].OptionsDB), &questions[i].Options); err != nil {
				return nil, err
			}
		}
	}
	
	return questions, nil
}

// GetActiveAssessmentQuestions retrieves all active assessment questions
func (r *GormPersonalizationRepository) GetActiveAssessmentQuestions() ([]models.AssessmentQuestion, error) {
	var questions []models.AssessmentQuestion
	result := r.db.Where("is_active = ?", true).Find(&questions)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize options
	for i := range questions {
		if questions[i].OptionsDB != "" {
			if err := json.Unmarshal([]byte(questions[i].OptionsDB), &questions[i].Options); err != nil {
				return nil, err
			}
		}
	}
	
	return questions, nil
}

// SaveAssessmentQuestion saves an assessment question
func (r *GormPersonalizationRepository) SaveAssessmentQuestion(question *models.AssessmentQuestion) error {
	// Serialize options
	if question.Options != nil {
		data, err := json.Marshal(question.Options)
		if err != nil {
			return err
		}
		question.OptionsDB = string(data)
	}
	
	if question.ID == 0 {
		return r.db.Create(question).Error
	}
	return r.db.Save(question).Error
}

// SaveAssessmentResponses saves a batch of assessment responses
func (r *GormPersonalizationRepository) SaveAssessmentResponses(responses []models.AssessmentResponse) error {
	return r.db.Create(&responses).Error
}

// GetAssessmentResponsesByUserID retrieves all assessment responses for a user
func (r *GormPersonalizationRepository) GetAssessmentResponsesByUserID(userID uint) ([]models.AssessmentResponse, error) {
	var responses []models.AssessmentResponse
	result := r.db.Where("user_id = ?", userID).Find(&responses)
	if result.Error != nil {
		return nil, result.Error
	}
	return responses, nil
}

// GetRecommendationsForUser retrieves content recommendations for a user
func (r *GormPersonalizationRepository) GetRecommendationsForUser(userID uint, contentType string, limit int) ([]models.ContentRecommendation, error) {
	query := r.db.Where("user_id = ? AND is_rejected = ?", userID, false)
	
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	var recommendations []models.ContentRecommendation
	result := query.Order("recommendation_score DESC").Find(&recommendations)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize reason codes
	for i := range recommendations {
		if recommendations[i].ReasonCodesDB != "" {
			if err := json.Unmarshal([]byte(recommendations[i].ReasonCodesDB), &recommendations[i].ReasonCodes); err != nil {
				return nil, err
			}
		}
	}
	
	return recommendations, nil
}

// SaveContentRecommendation saves a content recommendation
func (r *GormPersonalizationRepository) SaveContentRecommendation(rec *models.ContentRecommendation) error {
	// Serialize reason codes
	if rec.ReasonCodes != nil {
		data, err := json.Marshal(rec.ReasonCodes)
		if err != nil {
			return err
		}
		rec.ReasonCodesDB = string(data)
	}
	
	return r.db.Create(rec).Error
}

// UpdateRecommendationStatus updates the status of a recommendation
func (r *GormPersonalizationRepository) UpdateRecommendationStatus(recID uint, viewed, saved, rejected bool) error {
	var rec models.ContentRecommendation
	if err := r.db.First(&rec, recID).Error; err != nil {
		return err
	}
	
	rec.IsViewed = viewed
	rec.IsSaved = saved
	rec.IsRejected = rejected
	
	return r.db.Save(&rec).Error
}

// GetAllDifficultyLevels retrieves all difficulty levels
func (r *GormPersonalizationRepository) GetAllDifficultyLevels() ([]models.DifficultyLevel, error) {
	var levels []models.DifficultyLevel
	result := r.db.Find(&levels)
	if result.Error != nil {
		return nil, result.Error
	}
	return levels, nil
}

// GetDifficultyLevelByName retrieves a difficulty level by name
func (r *GormPersonalizationRepository) GetDifficultyLevelByName(name string) (*models.DifficultyLevel, error) {
	var level models.DifficultyLevel
	result := r.db.Where("name = ?", name).First(&level)
	if result.Error != nil {
		return nil, result.Error
	}
	return &level, nil
}

// GetUserPerformance retrieves a user's performance metrics
func (r *GormPersonalizationRepository) GetUserPerformance(userID uint) (*models.UserPerformance, error) {
	var perf models.UserPerformance
	result := r.db.Where("user_id = ?", userID).First(&perf)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // User doesn't have performance metrics yet
		}
		return nil, result.Error
	}
	
	// Deserialize topic scores
	if perf.TopicScoresDB != "" {
		if err := json.Unmarshal([]byte(perf.TopicScoresDB), &perf.TopicScores); err != nil {
			return nil, err
		}
	}
	
	return &perf, nil
}

// UpdateUserPerformance updates a user's performance metrics
func (r *GormPersonalizationRepository) UpdateUserPerformance(perf *models.UserPerformance) error {
	// Serialize topic scores
	if perf.TopicScores != nil {
		data, err := json.Marshal(perf.TopicScores)
		if err != nil {
			return err
		}
		perf.TopicScoresDB = string(data)
	}
	
	// Check if the user already has performance metrics
	existingPerf, err := r.GetUserPerformance(perf.UserID)
	if err != nil {
		return err
	}
	
	if existingPerf != nil {
		// Update existing metrics
		perf.ID = existingPerf.ID
		return r.db.Save(perf).Error
	}
	
	// Create new metrics
	return r.db.Create(perf).Error
}

// GetAllLearningPathTemplates retrieves all learning path templates
func (r *GormPersonalizationRepository) GetAllLearningPathTemplates() ([]models.LearningPathTemplate, error) {
	var templates []models.LearningPathTemplate
	result := r.db.Find(&templates)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize JSON fields
	for i := range templates {
		if templates[i].TargetStylesDB != "" {
			if err := json.Unmarshal([]byte(templates[i].TargetStylesDB), &templates[i].TargetStyles); err != nil {
				return nil, err
			}
		}
		
		if templates[i].TopicsDB != "" {
			if err := json.Unmarshal([]byte(templates[i].TopicsDB), &templates[i].Topics); err != nil {
				return nil, err
			}
		}
	}
	
	return templates, nil
}

// GetActiveLearningPathTemplates retrieves all active learning path templates
func (r *GormPersonalizationRepository) GetActiveLearningPathTemplates() ([]models.LearningPathTemplate, error) {
	var templates []models.LearningPathTemplate
	result := r.db.Where("is_active = ?", true).Find(&templates)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize JSON fields
	for i := range templates {
		if templates[i].TargetStylesDB != "" {
			if err := json.Unmarshal([]byte(templates[i].TargetStylesDB), &templates[i].TargetStyles); err != nil {
				return nil, err
			}
		}
		
		if templates[i].TopicsDB != "" {
			if err := json.Unmarshal([]byte(templates[i].TopicsDB), &templates[i].Topics); err != nil {
				return nil, err
			}
		}
	}
	
	return templates, nil
}

// GetLearningPathTemplateByID retrieves a learning path template by ID
func (r *GormPersonalizationRepository) GetLearningPathTemplateByID(templateID uint) (*models.LearningPathTemplate, error) {
	var template models.LearningPathTemplate
	result := r.db.First(&template, templateID)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize JSON fields
	if template.TargetStylesDB != "" {
		if err := json.Unmarshal([]byte(template.TargetStylesDB), &template.TargetStyles); err != nil {
			return nil, err
		}
	}
	
	if template.TopicsDB != "" {
		if err := json.Unmarshal([]byte(template.TopicsDB), &template.Topics); err != nil {
			return nil, err
		}
	}
	
	return &template, nil
}

// SaveLearningPathTemplate saves a learning path template
func (r *GormPersonalizationRepository) SaveLearningPathTemplate(template *models.LearningPathTemplate) error {
	// Serialize JSON fields
	if template.TargetStyles != nil {
		data, err := json.Marshal(template.TargetStyles)
		if err != nil {
			return err
		}
		template.TargetStylesDB = string(data)
	}
	
	if template.Topics != nil {
		data, err := json.Marshal(template.Topics)
		if err != nil {
			return err
		}
		template.TopicsDB = string(data)
	}
	
	if template.ID == 0 {
		return r.db.Create(template).Error
	}
	return r.db.Save(template).Error
}

// GetTemplateItemsByTemplateID retrieves all items in a learning path template
func (r *GormPersonalizationRepository) GetTemplateItemsByTemplateID(templateID uint) ([]models.TemplateItem, error) {
	var items []models.TemplateItem
	result := r.db.Where("template_id = ?", templateID).Order("order").Find(&items)
	if result.Error != nil {
		return nil, result.Error
	}
	
	// Deserialize style weights
	for i := range items {
		if items[i].StyleWeightsDB != "" {
			if err := json.Unmarshal([]byte(items[i].StyleWeightsDB), &items[i].StyleWeights); err != nil {
				return nil, err
			}
		}
	}
	
	return items, nil
}

// SaveTemplateItem saves a template item
func (r *GormPersonalizationRepository) SaveTemplateItem(item *models.TemplateItem) error {
	// Serialize style weights
	if item.StyleWeights != nil {
		data, err := json.Marshal(item.StyleWeights)
		if err != nil {
			return err
		}
		item.StyleWeightsDB = string(data)
	}
	
	if item.ID == 0 {
		return r.db.Create(item).Error
	}
	return r.db.Save(item).Error
}
