services:
  # PostgreSQL Database for Foundation
  foundation-db:
    image: postgres:13-alpine
    container_name: great-nigeria-foundation-db
    environment:
      POSTGRES_DB: great_nigeria_foundation
      POSTGRES_USER: foundation_user
      POSTGRES_PASSWORD: foundation_pass
    ports:
      - "5433:5432"  # Different port to avoid conflict with existing DB
    volumes:
      - foundation_db_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - foundation-network
    restart: unless-stopped

  # Redis for Foundation (Sessions, Cache)
  foundation-redis:
    image: redis:6-alpine
    container_name: great-nigeria-foundation-redis
    ports:
      - "6380:6379"  # Different port to avoid conflict with existing Redis
    volumes:
      - foundation_redis_data:/data
    networks:
      - foundation-network
    restart: unless-stopped

  # Foundation Application
  foundation-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: great-nigeria-foundation-app
    environment:
      # Database Configuration
      DB_HOST: foundation-db
      DB_PORT: 5432
      DB_USERNAME: foundation_user
      DB_PASSWORD: foundation_pass
      DB_DATABASE: great_nigeria_foundation
      DB_SSL_MODE: disable
      
      # Redis Configuration
      REDIS_HOST: foundation-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ""
      REDIS_DATABASE: 0
      
      # Server Configuration
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      ENVIRONMENT: development
      
      # Auth Configuration
      JWT_SECRET: foundation-jwt-secret-key-change-in-production
      ACCESS_TOKEN_EXPIRATION: 15m
      REFRESH_TOKEN_EXPIRATION: 168h
      
      # Logging
      LOG_LEVEL: info
      LOG_FORMAT: json
      LOG_OUTPUT: stdout
    ports:
      - "8080:8080"
    depends_on:
      - foundation-db
      - foundation-redis
    networks:
      - foundation-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  foundation_db_data:
    driver: local
  foundation_redis_data:
    driver: local

networks:
  foundation-network:
    driver: bridge
