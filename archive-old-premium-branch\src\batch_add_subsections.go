package main

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	_ "github.com/lib/pq"
)

// Database connection details
var (
	dbUser     = os.Getenv("PGUSER")
	dbPassword = os.Getenv("PGPASSWORD")
	dbName     = os.Getenv("PGDATABASE")
	dbHost     = os.Getenv("PGHOST")
	dbPort     = os.Getenv("PGPORT")
)

// Database connection string
func getDBConnectionString() string {
	// First try to use the DATABASE_URL environment variable
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL != "" {
		// Parse the DATABASE_URL to check if it already has sslmode parameter
		if strings.Contains(dbURL, "sslmode=") {
			// Already has sslmode, use as is
			return dbURL
		}
		// If DATABASE_URL doesn't have sslmode, add it
		return dbURL + " sslmode=disable"
	}
	
	// Fallback to individual connection parameters
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)
}

// Creates generic subsection titles
func generateSubsectionTitles(count int) []string {
	baseTitles := []string{
		"Introduction and Context",
		"Key Concepts and Principles",
		"Historical Background and Evolution",
		"Primary Analysis and Framework",
		"Critical Insights and Observations",
		"Evidence and Case Studies",
		"Comparative Analysis and Patterns",
		"Challenges and Limitations",
		"Synthesis and Interconnections",
		"Conclusions and Future Directions",
	}
	
	// If we need fewer than the base titles, return a subset
	if count <= len(baseTitles) {
		return baseTitles[:count]
	}
	
	// If we need more, add numbered variations
	result := make([]string, count)
	for i := 0; i < count; i++ {
		if i < len(baseTitles) {
			result[i] = baseTitles[i]
		} else {
			baseIndex := i % len(baseTitles)
			iteration := (i / len(baseTitles)) + 1
			result[i] = fmt.Sprintf("%s (Part %d)", baseTitles[baseIndex], iteration)
		}
	}
	return result
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run batch_add_subsections.go <chapter_number>")
		os.Exit(1)
	}
	
	chapterNum, err := strconv.Atoi(os.Args[1])
	if err != nil {
		fmt.Printf("Invalid chapter number: %s\n", os.Args[1])
		os.Exit(1)
	}
	
	if chapterNum < 1 || chapterNum > 13 {
		fmt.Println("Chapter number must be between 1 and 13")
		os.Exit(1)
	}

	fmt.Println("==================================")
	fmt.Printf("ADDING SUBSECTIONS FOR CHAPTER %d\n", chapterNum)
	fmt.Println("==================================")
	fmt.Println("This program will:")
	fmt.Printf("1. Add 5-10 subsections to each section in Chapter %d\n", chapterNum)
	fmt.Println("==================================")
	
	// Open database connection
	db, err := sql.Open("postgres", getDBConnectionString())
	if err != nil {
		fmt.Printf("Error opening database connection: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	// Test the connection
	err = db.Ping()
	if err != nil {
		fmt.Printf("Error testing the database connection: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("Successfully connected to the database")

	// Get chapter ID
	var chapterID int
	err = db.QueryRow("SELECT id FROM book_chapters WHERE book_id = 3 AND number = $1", chapterNum).Scan(&chapterID)
	if err != nil {
		fmt.Printf("Error getting chapter ID: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Found Chapter %d with ID: %d\n", chapterNum, chapterID)
	
	// Get all sections for this chapter
	rows, err := db.Query("SELECT id, number FROM book_sections WHERE chapter_id = $1 ORDER BY number", chapterID)
	if err != nil {
		fmt.Printf("Error getting sections: %v\n", err)
		os.Exit(1)
	}
	defer rows.Close()
	
	totalSectionsProcessed := 0
	totalSubsectionsAdded := 0
	
	// Process each section
	for rows.Next() {
		var sectionID, sectionNum int
		if err := rows.Scan(&sectionID, &sectionNum); err != nil {
			fmt.Printf("Error scanning section row: %v\n", err)
			continue
		}
		
		totalSectionsProcessed++
		fmt.Printf("\nProcessing Section %d.%d (ID: %d)\n", chapterNum, sectionNum, sectionID)
		
		// Determine number of subsections (randomly 5-10)
		subsectionCount := 5 + (sectionNum % 6) // This gives a range from 5 to 10
		
		// Generate titles
		titles := generateSubsectionTitles(subsectionCount)
		
		// Add subsections
		subsectionsAdded := 0
		for i, title := range titles {
			// Check if subsection already exists
			var exists bool
			err := db.QueryRow("SELECT EXISTS(SELECT 1 FROM book_subsections WHERE section_id = $1 AND number = $2)",
				sectionID, i+1).Scan(&exists)
			if err != nil {
				fmt.Printf("Error checking if subsection exists: %v\n", err)
				continue
			}
			
			if exists {
				fmt.Printf("Subsection %d.%d.%d already exists\n", chapterNum, sectionNum, i+1)
				continue
			}
			
			// Create the subsection
			_, err = db.Exec(`
				INSERT INTO book_subsections 
				(book_id, section_id, number, title, content, published, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
				3, sectionID, i+1, title, "", true, time.Now(), time.Now(),
			)
			if err != nil {
				fmt.Printf("Error creating subsection %d.%d.%d: %v\n", chapterNum, sectionNum, i+1, err)
				continue
			}
			
			subsectionsAdded++
			totalSubsectionsAdded++
			fmt.Printf("Created subsection %d.%d.%d: %s\n", chapterNum, sectionNum, i+1, title)
		}
		
		fmt.Printf("Added %d subsections to Section %d.%d\n", subsectionsAdded, chapterNum, sectionNum)
	}
	
	if err := rows.Err(); err != nil {
		fmt.Printf("Error iterating section rows: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n==================================")
	fmt.Println("BATCH SUBSECTION CREATION COMPLETED")
	fmt.Println("==================================")
	fmt.Printf("Processed %d sections in Chapter %d\n", totalSectionsProcessed, chapterNum)
	fmt.Printf("Added a total of %d subsections\n", totalSubsectionsAdded)
}