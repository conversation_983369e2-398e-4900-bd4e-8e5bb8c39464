package api

import (
	"net/http"
	"strconv"
	"time"
	
	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/service"
)

// PaymentHandlers handles payment-related API requests
type PaymentHandlers struct {
	paymentService service.PaymentService
}

// NewPaymentHandlers creates new payment handlers
func NewPaymentHandlers(paymentService service.PaymentService) *PaymentHandlers {
	return &PaymentHandlers{
		paymentService: paymentService,
	}
}

// RegisterRoutes registers the payment routes
func (h *PaymentHandlers) RegisterRoutes(router *gin.RouterGroup) {
	payments := router.Group("/payments")
	{
		payments.POST("/intent", h.createPaymentIntent)
		payments.POST("/process", h.processPayment)
		payments.GET("/transactions", h.getTransactions)
		payments.GET("/transactions/:id", h.getTransaction)
		
		plans := payments.Group("/plans")
		{
			plans.GET("", h.getSubscriptionPlans)
			plans.GET("/:id", h.getSubscriptionPlan)
			plans.POST("", h.createSubscriptionPlan)
			plans.PUT("/:id", h.updateSubscriptionPlan)
		}
		
		subscriptions := payments.Group("/subscriptions")
		{
			subscriptions.POST("", h.createSubscription)
			subscriptions.GET("", h.getUserSubscriptions)
			subscriptions.GET("/:id", h.getSubscription)
			subscriptions.POST("/:id/cancel", h.cancelSubscription)
		}
		
		methods := payments.Group("/methods")
		{
			methods.POST("", h.createPaymentMethod)
			methods.GET("", h.getPaymentMethods)
			methods.GET("/:id", h.getPaymentMethod)
			methods.PUT("/:id", h.updatePaymentMethod)
			methods.DELETE("/:id", h.deletePaymentMethod)
		}
		
		refunds := payments.Group("/refunds")
		{
			refunds.POST("", h.createRefund)
			refunds.GET("/:id", h.getRefund)
			refunds.GET("/payment/:paymentId", h.getRefundsByPayment)
		}
		
		disputes := payments.Group("/disputes")
		{
			disputes.POST("", h.createDispute)
			disputes.GET("/:id", h.getDispute)
			disputes.PUT("/:id/status", h.updateDisputeStatus)
		}
		
		promotions := payments.Group("/promotions")
		{
			promotions.POST("", h.createPromotion)
			promotions.GET("/:id", h.getPromotion)
			promotions.POST("/apply", h.applyPromotion)
		}
		
		virtualAccounts := payments.Group("/virtual-accounts")
		{
			virtualAccounts.POST("", h.createVirtualAccount)
			virtualAccounts.GET("", h.getUserVirtualAccounts)
			virtualAccounts.GET("/:id", h.getVirtualAccount)
		}
		
		gifts := payments.Group("/gifts")
		{
			gifts.POST("", h.createGift)
			gifts.GET("/:id", h.getGift)
			gifts.GET("/sent", h.getUserSentGifts)
			gifts.GET("/received", h.getUserReceivedGifts)
			gifts.POST("/:id/payment", h.processGiftPayment)
		}
		
		payments.POST("/webhook/:provider", h.handleWebhook)
		payments.GET("/analytics", h.getPaymentAnalytics)
	}
}

// createPaymentIntent creates a payment intent
func (h *PaymentHandlers) createPaymentIntent(c *gin.Context) {
	var req struct {
		Amount      int                    `json:"amount" binding:"required"`
		Currency    models.Currency        `json:"currency" binding:"required"`
		Description string                 `json:"description"`
		SuccessURL  string                 `json:"success_url"`
		CancelURL   string                 `json:"cancel_url"`
		MetaData    map[string]interface{} `json:"meta_data"`
		Provider    models.PaymentProvider `json:"provider" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Create payment intent
	intent, paymentURL, err := h.paymentService.CreatePaymentIntent(
		userID.(uint),
		req.Amount,
		req.Currency,
		req.Description,
		req.SuccessURL,
		req.CancelURL,
		req.MetaData,
		req.Provider,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"intent_id":          intent.ID,
		"client_secret":      intent.ClientSecret,
		"payment_url":        paymentURL,
		"provider_reference": intent.ProviderReference,
		"expires_at":         intent.ExpiresAt,
	})
}

// processPayment processes a payment callback
func (h *PaymentHandlers) processPayment(c *gin.Context) {
	var req struct {
		Reference string                 `json:"reference" binding:"required"`
		Provider  models.PaymentProvider `json:"provider" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	payment, err := h.paymentService.ProcessPaymentResult(req.Reference, req.Provider)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, payment)
}

// getTransactions gets a user's payment transactions
func (h *PaymentHandlers) getTransactions(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	payments, err := h.paymentService.GetUserPayments(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, payments)
}

// getTransaction gets a specific payment transaction
func (h *PaymentHandlers) getTransaction(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid payment ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	payment, err := h.paymentService.GetPayment(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the payment or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if payment.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, payment)
}

// getSubscriptionPlans gets all subscription plans
func (h *PaymentHandlers) getSubscriptionPlans(c *gin.Context) {
	includeInactive := false
	if c.Query("include_inactive") == "true" {
		// Only allow admins to see inactive plans
		if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
			includeInactive = true
		}
	}
	
	plans, err := h.paymentService.GetSubscriptionPlans(includeInactive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, plans)
}

// getSubscriptionPlan gets a specific subscription plan
func (h *PaymentHandlers) getSubscriptionPlan(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}
	
	plan, err := h.paymentService.GetSubscriptionPlanByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	if plan == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "plan not found"})
		return
	}
	
	c.JSON(http.StatusOK, plan)
}

// createSubscriptionPlan creates a subscription plan
func (h *PaymentHandlers) createSubscriptionPlan(c *gin.Context) {
	// Only allow admins to create plans
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	var req struct {
		Name            string                 `json:"name" binding:"required"`
		Description     string                 `json:"description"`
		Amount          int                    `json:"amount" binding:"required"`
		Interval        models.PlanInterval    `json:"interval" binding:"required"`
		Currency        models.Currency        `json:"currency" binding:"required"`
		TrialPeriodDays int                    `json:"trial_period_days"`
		Features        map[string]interface{} `json:"features"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	plan, err := h.paymentService.CreateSubscriptionPlan(
		req.Name,
		req.Description,
		req.Amount,
		req.Interval,
		req.Currency,
		req.TrialPeriodDays,
		req.Features,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, plan)
}

// updateSubscriptionPlan updates a subscription plan
func (h *PaymentHandlers) updateSubscriptionPlan(c *gin.Context) {
	// Only allow admins to update plans
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}
	
	var req struct {
		Name            string                 `json:"name" binding:"required"`
		Description     string                 `json:"description"`
		Amount          int                    `json:"amount" binding:"required"`
		Interval        models.PlanInterval    `json:"interval" binding:"required"`
		Currency        models.Currency        `json:"currency" binding:"required"`
		TrialPeriodDays int                    `json:"trial_period_days"`
		Features        map[string]interface{} `json:"features"`
		IsActive        bool                   `json:"is_active"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	plan, err := h.paymentService.UpdateSubscriptionPlan(
		uint(id),
		req.Name,
		req.Description,
		req.Amount,
		req.Interval,
		req.Currency,
		req.TrialPeriodDays,
		req.Features,
		req.IsActive,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, plan)
}

// createSubscription creates a subscription
func (h *PaymentHandlers) createSubscription(c *gin.Context) {
	var req struct {
		PlanID          uint                   `json:"plan_id" binding:"required"`
		PaymentMethodID uint                   `json:"payment_method_id" binding:"required"`
		MetaData        map[string]interface{} `json:"meta_data"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	subscription, err := h.paymentService.CreateSubscription(
		userID.(uint),
		req.PlanID,
		req.PaymentMethodID,
		req.MetaData,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, subscription)
}

// getUserSubscriptions gets a user's subscriptions
func (h *PaymentHandlers) getUserSubscriptions(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	active := true
	if c.Query("active") == "false" {
		active = false
	}
	
	subscriptions, err := h.paymentService.GetUserSubscriptions(userID.(uint), active)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, subscriptions)
}

// getSubscription gets a specific subscription
func (h *PaymentHandlers) getSubscription(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid subscription ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	subscription, err := h.paymentService.GetSubscription(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the subscription or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if subscription.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, subscription)
}

// cancelSubscription cancels a subscription
func (h *PaymentHandlers) cancelSubscription(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid subscription ID"})
		return
	}
	
	var req struct {
		CancelAtPeriodEnd bool `json:"cancel_at_period_end"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Get the subscription to check ownership
	subscription, err := h.paymentService.GetSubscription(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the subscription or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if subscription.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	updatedSubscription, err := h.paymentService.CancelSubscription(uint(id), req.CancelAtPeriodEnd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, updatedSubscription)
}

// createPaymentMethod creates a payment method
func (h *PaymentHandlers) createPaymentMethod(c *gin.Context) {
	var req struct {
		Type           string            `json:"type" binding:"required"`
		BillingDetails map[string]string `json:"billing_details"`
		CardDetails    map[string]string `json:"card_details"`
		BankDetails    map[string]string `json:"bank_details"`
		IsDefault      bool              `json:"is_default"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	method, err := h.paymentService.CreatePaymentMethod(
		userID.(uint),
		req.Type,
		req.BillingDetails,
		req.CardDetails,
		req.BankDetails,
		req.IsDefault,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, method)
}

// getPaymentMethods gets a user's payment methods
func (h *PaymentHandlers) getPaymentMethods(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	methods, err := h.paymentService.GetUserPaymentMethods(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, methods)
}

// getPaymentMethod gets a specific payment method
func (h *PaymentHandlers) getPaymentMethod(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid payment method ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	method, err := h.paymentService.GetPaymentMethod(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the payment method
	if method.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, method)
}

// updatePaymentMethod updates a payment method
func (h *PaymentHandlers) updatePaymentMethod(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid payment method ID"})
		return
	}
	
	var req struct {
		IsDefault bool `json:"is_default"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Get the payment method to check ownership
	method, err := h.paymentService.GetPaymentMethod(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the payment method
	if method.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	updatedMethod, err := h.paymentService.UpdatePaymentMethod(uint(id), req.IsDefault)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, updatedMethod)
}

// deletePaymentMethod deletes a payment method
func (h *PaymentHandlers) deletePaymentMethod(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid payment method ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Get the payment method to check ownership
	method, err := h.paymentService.GetPaymentMethod(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the payment method
	if method.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	err = h.paymentService.DeletePaymentMethod(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "payment method deleted"})
}

// createRefund creates a refund
func (h *PaymentHandlers) createRefund(c *gin.Context) {
	// Only allow admins to create refunds
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	var req struct {
		PaymentID uint   `json:"payment_id" binding:"required"`
		Amount    int    `json:"amount" binding:"required"`
		Reason    string `json:"reason" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	refund, err := h.paymentService.CreateRefund(
		req.PaymentID,
		req.Amount,
		req.Reason,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, refund)
}

// getRefund gets a specific refund
func (h *PaymentHandlers) getRefund(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid refund ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	refund, err := h.paymentService.GetRefund(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the refund or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if refund.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, refund)
}

// getRefundsByPayment gets refunds for a payment
func (h *PaymentHandlers) getRefundsByPayment(c *gin.Context) {
	paymentID, err := strconv.ParseUint(c.Param("paymentId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid payment ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Get the payment to check ownership
	payment, err := h.paymentService.GetPayment(uint(paymentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the payment or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if payment.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	refunds, err := h.paymentService.GetRefundsByPayment(uint(paymentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, refunds)
}

// createDispute creates a dispute
func (h *PaymentHandlers) createDispute(c *gin.Context) {
	var req struct {
		PaymentID uint                   `json:"payment_id" binding:"required"`
		Amount    int                    `json:"amount" binding:"required"`
		Reason    string                 `json:"reason" binding:"required"`
		Evidence  map[string]interface{} `json:"evidence"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	dispute, err := h.paymentService.CreateDispute(
		req.PaymentID,
		req.Amount,
		req.Reason,
		req.Evidence,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, dispute)
}

// getDispute gets a specific dispute
func (h *PaymentHandlers) getDispute(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid dispute ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	dispute, err := h.paymentService.GetDispute(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the dispute or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if dispute.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, dispute)
}

// updateDisputeStatus updates a dispute status
func (h *PaymentHandlers) updateDisputeStatus(c *gin.Context) {
	// Only allow admins to update dispute status
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid dispute ID"})
		return
	}
	
	var req struct {
		Status          string `json:"status" binding:"required"`
		ResolutionNotes string `json:"resolution_notes"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	dispute, err := h.paymentService.UpdateDisputeStatus(
		uint(id),
		req.Status,
		req.ResolutionNotes,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, dispute)
}

// createPromotion creates a promotion
func (h *PaymentHandlers) createPromotion(c *gin.Context) {
	// Only allow admins to create promotions
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	var req struct {
		Code             string    `json:"code" binding:"required"`
		Description      string    `json:"description"`
		DiscountType     string    `json:"discount_type" binding:"required"`
		DiscountAmount   int       `json:"discount_amount" binding:"required"`
		MinimumAmount    int       `json:"minimum_amount"`
		MaxUsageCount    int       `json:"max_usage_count"`
		MaxUsagePerUser  int       `json:"max_usage_per_user"`
		StartDate        time.Time `json:"start_date" binding:"required"`
		EndDate          time.Time `json:"end_date" binding:"required"`
		ProductIDs       []uint    `json:"product_ids"`
		PlanIDs          []uint    `json:"plan_ids"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	promotion, err := h.paymentService.CreatePromotion(
		req.Code,
		req.Description,
		req.DiscountType,
		req.DiscountAmount,
		req.MinimumAmount,
		req.MaxUsageCount,
		req.MaxUsagePerUser,
		req.StartDate,
		req.EndDate,
		req.ProductIDs,
		req.PlanIDs,
		userID.(uint),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, promotion)
}

// getPromotion gets a specific promotion
func (h *PaymentHandlers) getPromotion(c *gin.Context) {
	// Either find by ID or code
	var promotion *models.Promotion
	var err error
	
	if c.Query("code") != "" {
		promotion, err = h.paymentService.GetPromotionByCode(c.Query("code"))
	} else {
		id, err := strconv.ParseUint(c.Param("id"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid promotion ID"})
			return
		}
		
		promotion, err = h.paymentService.GetPromotion(uint(id))
	}
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	if promotion == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "promotion not found"})
		return
	}
	
	// Only allow admins to see inactive or expired promotions
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	now := time.Now()
	if (!promotion.IsActive || now.Before(promotion.StartDate) || now.After(promotion.EndDate)) && !isAdmin {
		c.JSON(http.StatusNotFound, gin.H{"error": "promotion not found"})
		return
	}
	
	c.JSON(http.StatusOK, promotion)
}

// applyPromotion applies a promotion code
func (h *PaymentHandlers) applyPromotion(c *gin.Context) {
	var req struct {
		Code      string          `json:"code" binding:"required"`
		Amount    int             `json:"amount" binding:"required"`
		Currency  models.Currency `json:"currency" binding:"required"`
		ProductID uint            `json:"product_id"`
		PlanID    uint            `json:"plan_id"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	discountedAmount, err := h.paymentService.ApplyPromotion(
		req.Code,
		userID.(uint),
		req.Amount,
		req.Currency,
		req.ProductID,
		req.PlanID,
	)
	
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"original_amount":  req.Amount,
		"discounted_amount": discountedAmount,
		"discount_amount":  req.Amount - discountedAmount,
	})
}

// createVirtualAccount creates a virtual account
func (h *PaymentHandlers) createVirtualAccount(c *gin.Context) {
	var req struct {
		Provider models.PaymentProvider `json:"provider" binding:"required"`
		Details  map[string]string      `json:"details" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	account, err := h.paymentService.CreateVirtualAccount(
		userID.(uint),
		req.Provider,
		req.Details,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, account)
}

// getUserVirtualAccounts gets a user's virtual accounts
func (h *PaymentHandlers) getUserVirtualAccounts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	accounts, err := h.paymentService.GetUserVirtualAccounts(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, accounts)
}

// getVirtualAccount gets a specific virtual account
func (h *PaymentHandlers) getVirtualAccount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid virtual account ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	account, err := h.paymentService.GetVirtualAccount(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user owns the virtual account or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if account.UserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, account)
}

// createGift creates a gift
func (h *PaymentHandlers) createGift(c *gin.Context) {
	var req struct {
		RecipientUserID uint   `json:"recipient_user_id" binding:"required"`
		ContentID       uint   `json:"content_id" binding:"required"`
		ContentType     string `json:"content_type" binding:"required"`
		GiftType        string `json:"gift_type" binding:"required"`
		Amount          int    `json:"amount" binding:"required"`
		Message         string `json:"message"`
		IsAnonymous     bool   `json:"is_anonymous"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	gift, err := h.paymentService.CreateGift(
		userID.(uint),
		req.RecipientUserID,
		req.ContentID,
		req.ContentType,
		req.GiftType,
		req.Amount,
		req.Message,
		req.IsAnonymous,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gift)
}

// getGift gets a specific gift
func (h *PaymentHandlers) getGift(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid gift ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	gift, err := h.paymentService.GetGift(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Verify the user is the sender or recipient or is an admin
	isAdmin := false
	if userRole, ok := c.Get("userRole"); ok && userRole.(string) == "admin" {
		isAdmin = true
	}
	
	if gift.SenderUserID != userID.(uint) && gift.RecipientUserID != userID.(uint) && !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	c.JSON(http.StatusOK, gift)
}

// getUserSentGifts gets gifts sent by a user
func (h *PaymentHandlers) getUserSentGifts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	gifts, err := h.paymentService.GetUserSentGifts(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gifts)
}

// getUserReceivedGifts gets gifts received by a user
func (h *PaymentHandlers) getUserReceivedGifts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	gifts, err := h.paymentService.GetUserReceivedGifts(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gifts)
}

// processGiftPayment processes payment for a gift
func (h *PaymentHandlers) processGiftPayment(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid gift ID"})
		return
	}
	
	var req struct {
		PaymentMethodID uint `json:"payment_method_id" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}
	
	// Get the gift to check ownership
	gift, err := h.paymentService.GetGift(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	if gift.SenderUserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	payment, err := h.paymentService.ProcessGiftPayment(uint(id), req.PaymentMethodID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, payment)
}

// handleWebhook handles webhooks from payment providers
func (h *PaymentHandlers) handleWebhook(c *gin.Context) {
	provider := models.PaymentProvider(c.Param("provider"))
	
	// Validate provider
	switch provider {
	case models.ProviderPaystack, models.ProviderFlutterwave, models.ProviderSquad:
		// Valid provider
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid provider"})
		return
	}
	
	// Parse request body
	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Extract event information
	var eventType, eventID string
	
	switch provider {
	case models.ProviderPaystack:
		if event, ok := payload["event"].(string); ok {
			eventType = event
		}
		if id, ok := payload["id"].(float64); ok {
			eventID = fmt.Sprintf("%d", int(id))
		}
		
	case models.ProviderFlutterwave:
		if event, ok := payload["event"].(string); ok {
			eventType = event
		}
		if id, ok := payload["id"].(string); ok {
			eventID = id
		}
		
	case models.ProviderSquad:
		if event, ok := payload["event"].(string); ok {
			eventType = event
		}
		if id, ok := payload["id"].(string); ok {
			eventID = id
		} else if id, ok := payload["transaction_reference"].(string); ok {
			eventID = id
		}
	}
	
	// Process webhook
	err := h.paymentService.ProcessWebhook(provider, eventType, eventID, payload)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "webhook processed successfully"})
}

// getPaymentAnalytics gets payment analytics
func (h *PaymentHandlers) getPaymentAnalytics(c *gin.Context) {
	// Only allow admins to access analytics
	userRole, ok := c.Get("userRole")
	if !ok || userRole.(string) != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "forbidden"})
		return
	}
	
	// Date range parameters
	startDateStr := c.DefaultQuery("start_date", "")
	endDateStr := c.DefaultQuery("end_date", "")
	
	var startDate, endDate time.Time
	var err error
	
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start date format, use YYYY-MM-DD"})
			return
		}
	} else {
		// Default to 30 days ago
		startDate = time.Now().AddDate(0, 0, -30)
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end date format, use YYYY-MM-DD"})
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}
	
	analytics, err := h.paymentService.GetPaymentAnalytics(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, analytics)
}