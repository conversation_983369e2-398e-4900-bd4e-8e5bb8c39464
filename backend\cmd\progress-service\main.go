package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	_ "github.com/go-sql-driver/mysql"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/handlers"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/service"
)

func main() {
	// Set up database connection
	dbUser := os.Getenv("DB_USER")
	dbPass := os.Getenv("DB_PASS")
	dbHost := os.Getenv("DB_HOST")
	dbName := os.Getenv("DB_NAME")
	
	if dbUser == "" {
		dbUser = "root"
	}
	if dbHost == "" {
		dbHost = "localhost:3306"
	}
	if dbName == "" {
		dbName = "greatnigeria"
	}
	
	dsn := dbUser + ":" + dbPass + "@tcp(" + dbHost + ")/" + dbName + "?parseTime=true"
	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()
	
	// Set up repository, service, and handler
	progressRepo := repository.NewSQLProgressRepository(db)
	progressService := service.NewProgressService(progressRepo)
	progressHandler := handlers.NewProgressHandler(progressService)
	
	// Set up Gin router
	router := gin.Default()
	
	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	
	// Set up API routes
	api := router.Group("/api")
	progressHandler.RegisterRoutes(api)
	
	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8085"
	}
	
	log.Printf("Starting progress service on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
