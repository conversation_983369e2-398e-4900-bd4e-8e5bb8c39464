package repository

import (
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/points/models"
	"gorm.io/gorm"
)

// PointsRepository defines the interface for points operations
type PointsRepository interface {
	// Transactions
	CreateTransaction(transaction *models.PointsTransaction) error
	GetTransactionByID(id uint) (*models.PointsTransaction, error)
	GetTransactionsByUser(userID uint, limit, offset int) ([]models.PointsTransaction, error)
	GetTransactionsByUserAndType(userID uint, sourceType models.PointSourceType, limit, offset int) ([]models.PointsTransaction, error)
	GetTotalTransactions(userID uint) (int64, error)
	GetExpiringTransactions(before time.Time) ([]models.PointsTransaction, error)
	
	// Balance
	GetUserPointsBalance(userID uint) (*models.UserPointsBalance, error)
	CreateUserPointsBalance(balance *models.UserPointsBalance) error
	UpdateUserPointsBalance(balance *models.UserPointsBalance) error
	
	// Membership
	GetUserMembership(userID uint) (*models.UserMembership, error)
	CreateUserMembership(membership *models.UserMembership) error
	UpdateUserMembership(membership *models.UserMembership) error
	GetUsersByTier(tier models.MembershipTier) ([]models.UserMembership, error)
	GetUsersForTierEvaluation(pointsThreshold int) ([]models.UserPointsBalance, error)
	
	// Expiration rules
	GetExpirationRules() ([]models.PointsExpirationRule, error)
	GetExpirationRuleBySourceType(sourceType models.PointSourceType) (*models.PointsExpirationRule, error)
	CreateExpirationRule(rule *models.PointsExpirationRule) error
	UpdateExpirationRule(rule *models.PointsExpirationRule) error
	DeleteExpirationRule(id uint) error
	
	// Achievements
	GetAchievements(active bool) ([]models.Achievement, error)
	GetAchievementByID(id uint) (*models.Achievement, error)
	CreateAchievement(achievement *models.Achievement) error
	UpdateAchievement(achievement *models.Achievement) error
	DeleteAchievement(id uint) error
	
	// User achievements
	GetUserAchievements(userID uint) ([]models.UserAchievement, error)
	GetUserAchievementByID(id uint) (*models.UserAchievement, error)
	CreateUserAchievement(userAchievement *models.UserAchievement) error
	UpdateUserAchievement(userAchievement *models.UserAchievement) error
	GetUnnotifiedAchievements() ([]models.UserAchievement, error)
	
	// Points events
	GetActivePointsEvents() ([]models.PointsEvent, error)
	GetPointsEventByID(id uint) (*models.PointsEvent, error)
	CreatePointsEvent(event *models.PointsEvent) error
	UpdatePointsEvent(event *models.PointsEvent) error
	DeletePointsEvent(id uint) error
	
	// Points transfers
	CreatePointsTransfer(transfer *models.PointsTransferRecord) error
	GetPointsTransferByID(id uint) (*models.PointsTransferRecord, error)
	GetPointsTransfersByUser(userID uint, isReceiver bool, limit, offset int) ([]models.PointsTransferRecord, error)
	UpdatePointsTransfer(transfer *models.PointsTransferRecord) error
	
	// Redemption items
	GetRedemptionItems(active bool) ([]models.PointsRedemptionItem, error)
	GetRedemptionItemByID(id uint) (*models.PointsRedemptionItem, error)
	CreateRedemptionItem(item *models.PointsRedemptionItem) error
	UpdateRedemptionItem(item *models.PointsRedemptionItem) error
	
	// Redemption records
	CreateRedemptionRecord(record *models.PointsRedemptionRecord) error
	GetRedemptionRecordByID(id uint) (*models.PointsRedemptionRecord, error)
	GetRedemptionRecordsByUser(userID uint, limit, offset int) ([]models.PointsRedemptionRecord, error)
	UpdateRedemptionRecord(record *models.PointsRedemptionRecord) error
	
	// Challenges
	GetActiveChallenges() ([]models.PointsChallenge, error)
	GetChallengeByID(id uint) (*models.PointsChallenge, error)
	CreateChallenge(challenge *models.PointsChallenge) error
	UpdateChallenge(challenge *models.PointsChallenge) error
	DeleteChallenge(id uint) error
	
	// User challenges
	GetUserChallenges(userID uint) ([]models.UserChallenge, error)
	GetUserChallengeByID(userID, challengeID uint) (*models.UserChallenge, error)
	CreateUserChallenge(userChallenge *models.UserChallenge) error
	UpdateUserChallenge(userChallenge *models.UserChallenge) error
	
	// Leaderboards
	GetGlobalLeaderboard(limit, offset int) ([]models.UserPointsBalance, error)
	GetCategoryLeaderboard(category string, limit, offset int) ([]struct {
		UserID uint
		Points int
	}, error)
	GetTimeRangeLeaderboard(startDate, endDate time.Time, limit, offset int) ([]struct {
		UserID uint
		Points int
	}, error)
}

// GormPointsRepository implements PointsRepository using GORM
type GormPointsRepository struct {
	db *gorm.DB
}

// NewGormPointsRepository creates a new points repository
func NewGormPointsRepository(db *gorm.DB) *GormPointsRepository {
	return &GormPointsRepository{db: db}
}

// CreateTransaction creates a new points transaction
func (r *GormPointsRepository) CreateTransaction(transaction *models.PointsTransaction) error {
	return r.db.Create(transaction).Error
}

// GetTransactionByID retrieves a transaction by ID
func (r *GormPointsRepository) GetTransactionByID(id uint) (*models.PointsTransaction, error) {
	var transaction models.PointsTransaction
	if err := r.db.First(&transaction, id).Error; err != nil {
		return nil, err
	}
	return &transaction, nil
}

// GetTransactionsByUser retrieves transactions for a user with pagination
func (r *GormPointsRepository) GetTransactionsByUser(userID uint, limit, offset int) ([]models.PointsTransaction, error) {
	var transactions []models.PointsTransaction
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&transactions).Error; err != nil {
		return nil, err
	}
	
	return transactions, nil
}

// GetTransactionsByUserAndType retrieves transactions for a user of a specific type
func (r *GormPointsRepository) GetTransactionsByUserAndType(userID uint, sourceType models.PointSourceType, limit, offset int) ([]models.PointsTransaction, error) {
	var transactions []models.PointsTransaction
	query := r.db.Where("user_id = ? AND source_type = ?", userID, sourceType).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&transactions).Error; err != nil {
		return nil, err
	}
	
	return transactions, nil
}

// GetTotalTransactions retrieves the total count of transactions for a user
func (r *GormPointsRepository) GetTotalTransactions(userID uint) (int64, error) {
	var count int64
	if err := r.db.Model(&models.PointsTransaction{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetExpiringTransactions retrieves transactions that are expiring before a given date
func (r *GormPointsRepository) GetExpiringTransactions(before time.Time) ([]models.PointsTransaction, error) {
	var transactions []models.PointsTransaction
	if err := r.db.Where("expires_at IS NOT NULL AND expires_at <= ? AND (expired_amount IS NULL OR expired_amount = 0)", before).Find(&transactions).Error; err != nil {
		return nil, err
	}
	return transactions, nil
}

// GetUserPointsBalance retrieves the points balance for a user
func (r *GormPointsRepository) GetUserPointsBalance(userID uint) (*models.UserPointsBalance, error) {
	var balance models.UserPointsBalance
	if err := r.db.Where("user_id = ?", userID).First(&balance).Error; err != nil {
		// If not found, return nil without error to indicate no balance record exists
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &balance, nil
}

// CreateUserPointsBalance creates a new points balance for a user
func (r *GormPointsRepository) CreateUserPointsBalance(balance *models.UserPointsBalance) error {
	return r.db.Create(balance).Error
}

// UpdateUserPointsBalance updates the points balance for a user
func (r *GormPointsRepository) UpdateUserPointsBalance(balance *models.UserPointsBalance) error {
	return r.db.Save(balance).Error
}

// GetUserMembership retrieves the membership for a user
func (r *GormPointsRepository) GetUserMembership(userID uint) (*models.UserMembership, error) {
	var membership models.UserMembership
	if err := r.db.Where("user_id = ?", userID).First(&membership).Error; err != nil {
		// If not found, return nil without error to indicate no membership record exists
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &membership, nil
}

// CreateUserMembership creates a new membership for a user
func (r *GormPointsRepository) CreateUserMembership(membership *models.UserMembership) error {
	return r.db.Create(membership).Error
}

// UpdateUserMembership updates the membership for a user
func (r *GormPointsRepository) UpdateUserMembership(membership *models.UserMembership) error {
	return r.db.Save(membership).Error
}

// GetUsersByTier retrieves users in a specific membership tier
func (r *GormPointsRepository) GetUsersByTier(tier models.MembershipTier) ([]models.UserMembership, error) {
	var memberships []models.UserMembership
	if err := r.db.Where("current_tier = ?", tier).Find(&memberships).Error; err != nil {
		return nil, err
	}
	return memberships, nil
}

// GetUsersForTierEvaluation retrieves users that might need tier evaluation
func (r *GormPointsRepository) GetUsersForTierEvaluation(pointsThreshold int) ([]models.UserPointsBalance, error) {
	var balances []models.UserPointsBalance
	if err := r.db.Where("available_points >= ?", pointsThreshold).Find(&balances).Error; err != nil {
		return nil, err
	}
	return balances, nil
}

// GetExpirationRules retrieves all expiration rules
func (r *GormPointsRepository) GetExpirationRules() ([]models.PointsExpirationRule, error) {
	var rules []models.PointsExpirationRule
	if err := r.db.Find(&rules).Error; err != nil {
		return nil, err
	}
	return rules, nil
}

// GetExpirationRuleBySourceType retrieves the expiration rule for a specific source type
func (r *GormPointsRepository) GetExpirationRuleBySourceType(sourceType models.PointSourceType) (*models.PointsExpirationRule, error) {
	var rule models.PointsExpirationRule
	if err := r.db.Where("source_type = ? AND is_active = ?", sourceType, true).First(&rule).Error; err != nil {
		// If not found, return nil without error to indicate no rule exists
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

// CreateExpirationRule creates a new expiration rule
func (r *GormPointsRepository) CreateExpirationRule(rule *models.PointsExpirationRule) error {
	return r.db.Create(rule).Error
}

// UpdateExpirationRule updates an expiration rule
func (r *GormPointsRepository) UpdateExpirationRule(rule *models.PointsExpirationRule) error {
	return r.db.Save(rule).Error
}

// DeleteExpirationRule deletes an expiration rule
func (r *GormPointsRepository) DeleteExpirationRule(id uint) error {
	return r.db.Delete(&models.PointsExpirationRule{}, id).Error
}

// GetAchievements retrieves all achievements
func (r *GormPointsRepository) GetAchievements(active bool) ([]models.Achievement, error) {
	var achievements []models.Achievement
	query := r.db
	
	if active {
		query = query.Where("is_active = ?", true)
	}
	
	if err := query.Order("display_order ASC").Find(&achievements).Error; err != nil {
		return nil, err
	}
	
	return achievements, nil
}

// GetAchievementByID retrieves an achievement by ID
func (r *GormPointsRepository) GetAchievementByID(id uint) (*models.Achievement, error) {
	var achievement models.Achievement
	if err := r.db.First(&achievement, id).Error; err != nil {
		return nil, err
	}
	return &achievement, nil
}

// CreateAchievement creates a new achievement
func (r *GormPointsRepository) CreateAchievement(achievement *models.Achievement) error {
	return r.db.Create(achievement).Error
}

// UpdateAchievement updates an achievement
func (r *GormPointsRepository) UpdateAchievement(achievement *models.Achievement) error {
	return r.db.Save(achievement).Error
}

// DeleteAchievement deletes an achievement
func (r *GormPointsRepository) DeleteAchievement(id uint) error {
	return r.db.Delete(&models.Achievement{}, id).Error
}

// GetUserAchievements retrieves all achievements for a user
func (r *GormPointsRepository) GetUserAchievements(userID uint) ([]models.UserAchievement, error) {
	var userAchievements []models.UserAchievement
	if err := r.db.Where("user_id = ?", userID).
		Preload("Achievement").
		Order("awarded_at DESC").
		Find(&userAchievements).Error; err != nil {
		return nil, err
	}
	return userAchievements, nil
}

// GetUserAchievementByID retrieves a user achievement by ID
func (r *GormPointsRepository) GetUserAchievementByID(id uint) (*models.UserAchievement, error) {
	var userAchievement models.UserAchievement
	if err := r.db.Preload("Achievement").First(&userAchievement, id).Error; err != nil {
		return nil, err
	}
	return &userAchievement, nil
}

// CreateUserAchievement creates a new user achievement
func (r *GormPointsRepository) CreateUserAchievement(userAchievement *models.UserAchievement) error {
	return r.db.Create(userAchievement).Error
}

// UpdateUserAchievement updates a user achievement
func (r *GormPointsRepository) UpdateUserAchievement(userAchievement *models.UserAchievement) error {
	return r.db.Save(userAchievement).Error
}

// GetUnnotifiedAchievements retrieves user achievements that have not been notified
func (r *GormPointsRepository) GetUnnotifiedAchievements() ([]models.UserAchievement, error) {
	var userAchievements []models.UserAchievement
	if err := r.db.Where("notification_sent = ?", false).
		Preload("Achievement").
		Find(&userAchievements).Error; err != nil {
		return nil, err
	}
	return userAchievements, nil
}

// GetActivePointsEvents retrieves active points events
func (r *GormPointsRepository) GetActivePointsEvents() ([]models.PointsEvent, error) {
	var events []models.PointsEvent
	now := time.Now()
	if err := r.db.Where("is_active = ? AND start_date <= ? AND end_date >= ?", true, now, now).Find(&events).Error; err != nil {
		return nil, err
	}
	return events, nil
}

// GetPointsEventByID retrieves a points event by ID
func (r *GormPointsRepository) GetPointsEventByID(id uint) (*models.PointsEvent, error) {
	var event models.PointsEvent
	if err := r.db.First(&event, id).Error; err != nil {
		return nil, err
	}
	return &event, nil
}

// CreatePointsEvent creates a new points event
func (r *GormPointsRepository) CreatePointsEvent(event *models.PointsEvent) error {
	return r.db.Create(event).Error
}

// UpdatePointsEvent updates a points event
func (r *GormPointsRepository) UpdatePointsEvent(event *models.PointsEvent) error {
	return r.db.Save(event).Error
}

// DeletePointsEvent deletes a points event
func (r *GormPointsRepository) DeletePointsEvent(id uint) error {
	return r.db.Delete(&models.PointsEvent{}, id).Error
}

// CreatePointsTransfer creates a new points transfer
func (r *GormPointsRepository) CreatePointsTransfer(transfer *models.PointsTransferRecord) error {
	return r.db.Create(transfer).Error
}

// GetPointsTransferByID retrieves a points transfer by ID
func (r *GormPointsRepository) GetPointsTransferByID(id uint) (*models.PointsTransferRecord, error) {
	var transfer models.PointsTransferRecord
	if err := r.db.First(&transfer, id).Error; err != nil {
		return nil, err
	}
	return &transfer, nil
}

// GetPointsTransfersByUser retrieves points transfers for a user
func (r *GormPointsRepository) GetPointsTransfersByUser(userID uint, isReceiver bool, limit, offset int) ([]models.PointsTransferRecord, error) {
	var transfers []models.PointsTransferRecord
	query := r.db
	
	if isReceiver {
		query = query.Where("to_user_id = ?", userID)
	} else {
		query = query.Where("from_user_id = ?", userID)
	}
	
	query = query.Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&transfers).Error; err != nil {
		return nil, err
	}
	
	return transfers, nil
}

// UpdatePointsTransfer updates a points transfer
func (r *GormPointsRepository) UpdatePointsTransfer(transfer *models.PointsTransferRecord) error {
	return r.db.Save(transfer).Error
}

// GetRedemptionItems retrieves all redemption items
func (r *GormPointsRepository) GetRedemptionItems(active bool) ([]models.PointsRedemptionItem, error) {
	var items []models.PointsRedemptionItem
	query := r.db
	
	if active {
		query = query.Where("is_active = ?", true)
	}
	
	if err := query.Order("points_cost ASC").Find(&items).Error; err != nil {
		return nil, err
	}
	
	return items, nil
}

// GetRedemptionItemByID retrieves a redemption item by ID
func (r *GormPointsRepository) GetRedemptionItemByID(id uint) (*models.PointsRedemptionItem, error) {
	var item models.PointsRedemptionItem
	if err := r.db.First(&item, id).Error; err != nil {
		return nil, err
	}
	return &item, nil
}

// CreateRedemptionItem creates a new redemption item
func (r *GormPointsRepository) CreateRedemptionItem(item *models.PointsRedemptionItem) error {
	return r.db.Create(item).Error
}

// UpdateRedemptionItem updates a redemption item
func (r *GormPointsRepository) UpdateRedemptionItem(item *models.PointsRedemptionItem) error {
	return r.db.Save(item).Error
}

// CreateRedemptionRecord creates a new redemption record
func (r *GormPointsRepository) CreateRedemptionRecord(record *models.PointsRedemptionRecord) error {
	return r.db.Create(record).Error
}

// GetRedemptionRecordByID retrieves a redemption record by ID
func (r *GormPointsRepository) GetRedemptionRecordByID(id uint) (*models.PointsRedemptionRecord, error) {
	var record models.PointsRedemptionRecord
	if err := r.db.First(&record, id).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

// GetRedemptionRecordsByUser retrieves redemption records for a user
func (r *GormPointsRepository) GetRedemptionRecordsByUser(userID uint, limit, offset int) ([]models.PointsRedemptionRecord, error) {
	var records []models.PointsRedemptionRecord
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&records).Error; err != nil {
		return nil, err
	}
	
	return records, nil
}

// UpdateRedemptionRecord updates a redemption record
func (r *GormPointsRepository) UpdateRedemptionRecord(record *models.PointsRedemptionRecord) error {
	return r.db.Save(record).Error
}

// GetActiveChallenges retrieves active challenges
func (r *GormPointsRepository) GetActiveChallenges() ([]models.PointsChallenge, error) {
	var challenges []models.PointsChallenge
	now := time.Now()
	if err := r.db.Where("is_active = ? AND start_date <= ? AND end_date >= ?", true, now, now).Find(&challenges).Error; err != nil {
		return nil, err
	}
	return challenges, nil
}

// GetChallengeByID retrieves a challenge by ID
func (r *GormPointsRepository) GetChallengeByID(id uint) (*models.PointsChallenge, error) {
	var challenge models.PointsChallenge
	if err := r.db.First(&challenge, id).Error; err != nil {
		return nil, err
	}
	return &challenge, nil
}

// CreateChallenge creates a new challenge
func (r *GormPointsRepository) CreateChallenge(challenge *models.PointsChallenge) error {
	return r.db.Create(challenge).Error
}

// UpdateChallenge updates a challenge
func (r *GormPointsRepository) UpdateChallenge(challenge *models.PointsChallenge) error {
	return r.db.Save(challenge).Error
}

// DeleteChallenge deletes a challenge
func (r *GormPointsRepository) DeleteChallenge(id uint) error {
	return r.db.Delete(&models.PointsChallenge{}, id).Error
}

// GetUserChallenges retrieves user challenges
func (r *GormPointsRepository) GetUserChallenges(userID uint) ([]models.UserChallenge, error) {
	var userChallenges []models.UserChallenge
	if err := r.db.Where("user_id = ?", userID).Find(&userChallenges).Error; err != nil {
		return nil, err
	}
	return userChallenges, nil
}

// GetUserChallengeByID retrieves a user challenge by ID
func (r *GormPointsRepository) GetUserChallengeByID(userID, challengeID uint) (*models.UserChallenge, error) {
	var userChallenge models.UserChallenge
	if err := r.db.Where("user_id = ? AND challenge_id = ?", userID, challengeID).First(&userChallenge).Error; err != nil {
		// If not found, return nil without error to indicate no user challenge record exists
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &userChallenge, nil
}

// CreateUserChallenge creates a new user challenge
func (r *GormPointsRepository) CreateUserChallenge(userChallenge *models.UserChallenge) error {
	return r.db.Create(userChallenge).Error
}

// UpdateUserChallenge updates a user challenge
func (r *GormPointsRepository) UpdateUserChallenge(userChallenge *models.UserChallenge) error {
	return r.db.Save(userChallenge).Error
}

// GetGlobalLeaderboard retrieves the global leaderboard
func (r *GormPointsRepository) GetGlobalLeaderboard(limit, offset int) ([]models.UserPointsBalance, error) {
	var leaderboard []models.UserPointsBalance
	query := r.db.Order("total_points DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&leaderboard).Error; err != nil {
		return nil, err
	}
	
	return leaderboard, nil
}

// GetCategoryLeaderboard retrieves a category-specific leaderboard
func (r *GormPointsRepository) GetCategoryLeaderboard(category string, limit, offset int) ([]struct {
	UserID uint
	Points int
}, error) {
	var results []struct {
		UserID uint
		Points int
	}
	
	// For a category leaderboard, we need to sum points from transactions of a specific type
	// This is just an example and may need to be adjusted based on how categories are defined
	query := r.db.Model(&models.PointsTransaction{}).
		Select("user_id, SUM(amount) as points").
		Where("reference_type = ?", category).
		Group("user_id").
		Order("points DESC")
		
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}
	
	return results, nil
}

// GetTimeRangeLeaderboard retrieves a time-range leaderboard
func (r *GormPointsRepository) GetTimeRangeLeaderboard(startDate, endDate time.Time, limit, offset int) ([]struct {
	UserID uint
	Points int
}, error) {
	var results []struct {
		UserID uint
		Points int
	}
	
	query := r.db.Model(&models.PointsTransaction{}).
		Select("user_id, SUM(amount) as points").
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Group("user_id").
		Order("points DESC")
		
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}
	
	return results, nil
}