package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/resource/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/resource/service"
)

// ResourceHandlers contains all the handlers for resource-related endpoints
type ResourceHandlers struct {
	ResourceService *service.ResourceService
}

// NewResourceHandlers creates a new ResourceHandlers
func NewResourceHandlers(resourceService *service.ResourceService) *ResourceHandlers {
	return &ResourceHandlers{
		ResourceService: resourceService,
	}
}

// GetResourceCategories handles GET /api/resources/categories
func (h *ResourceHandlers) GetResourceCategories(c *gin.Context) {
	categories, err := h.ResourceService.GetResourceCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// GetResourceCategoryByID handles GET /api/resources/categories/:id
func (h *ResourceHandlers) GetResourceCategoryByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	category, err := h.ResourceService.GetResourceCategoryByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// CreateResourceCategory handles POST /api/resources/categories
func (h *ResourceHandlers) CreateResourceCategory(c *gin.Context) {
	var category models.ResourceCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ResourceService.CreateResourceCategory(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateResourceCategory handles PUT /api/resources/categories/:id
func (h *ResourceHandlers) UpdateResourceCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	var category models.ResourceCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category.ID = id

	if err := h.ResourceService.UpdateResourceCategory(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteResourceCategory handles DELETE /api/resources/categories/:id
func (h *ResourceHandlers) DeleteResourceCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	if err := h.ResourceService.DeleteResourceCategory(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
}

// GetResources handles GET /api/resources
func (h *ResourceHandlers) GetResources(c *gin.Context) {
	// Parse filter parameters
	params := make(map[string]interface{})

	if categoryIDStr := c.Query("category_id"); categoryIDStr != "" {
		categoryID, err := strconv.ParseUint(categoryIDStr, 10, 64)
		if err == nil {
			params["category_id"] = categoryID
		}
	}

	if resourceType := c.Query("resource_type"); resourceType != "" {
		params["resource_type"] = resourceType
	}

	if authorIDStr := c.Query("author_id"); authorIDStr != "" {
		authorID, err := strconv.ParseUint(authorIDStr, 10, 64)
		if err == nil {
			params["author_id"] = authorID
		}
	}

	if isFeaturedStr := c.Query("is_featured"); isFeaturedStr != "" {
		isFeatured := isFeaturedStr == "true"
		params["is_featured"] = isFeatured
	}

	if isPremiumStr := c.Query("is_premium"); isPremiumStr != "" {
		isPremium := isPremiumStr == "true"
		params["is_premium"] = isPremium
	}

	if search := c.Query("search"); search != "" {
		params["search"] = search
	}

	resources, err := h.ResourceService.GetResources(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resources)
}

// GetResourceByID handles GET /api/resources/:id
func (h *ResourceHandlers) GetResourceByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	resource, err := h.ResourceService.GetResourceByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
		return
	}

	// Increment view count
	go h.ResourceService.IncrementResourceViewCount(id)

	c.JSON(http.StatusOK, resource)
}

// CreateResource handles POST /api/resources
func (h *ResourceHandlers) CreateResource(c *gin.Context) {
	var resource models.Resource
	if err := c.ShouldBindJSON(&resource); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ResourceService.CreateResource(&resource); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resource)
}

// UpdateResource handles PUT /api/resources/:id
func (h *ResourceHandlers) UpdateResource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	var resource models.Resource
	if err := c.ShouldBindJSON(&resource); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resource.ID = id

	if err := h.ResourceService.UpdateResource(&resource); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resource)
}

// DeleteResource handles DELETE /api/resources/:id
func (h *ResourceHandlers) DeleteResource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	if err := h.ResourceService.DeleteResource(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Resource deleted successfully"})
}

// DownloadResource handles GET /api/resources/:id/download
func (h *ResourceHandlers) DownloadResource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	resource, err := h.ResourceService.GetResourceByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
		return
	}

	// Increment download count
	go h.ResourceService.IncrementResourceDownloadCount(id)

	// Redirect to the file URL or serve the file
	if resource.FileURL != "" {
		c.Redirect(http.StatusFound, resource.FileURL)
	} else {
		c.JSON(http.StatusOK, gin.H{"content": resource.Content})
	}
}

// GetResourcesByBookSection handles GET /api/books/:bookID/chapters/:chapterID/sections/:sectionID/resources
func (h *ResourceHandlers) GetResourcesByBookSection(c *gin.Context) {
	bookID, err := strconv.ParseUint(c.Param("bookID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID format"})
		return
	}

	chapterID, err := strconv.ParseUint(c.Param("chapterID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chapter ID format"})
		return
	}

	sectionID, err := strconv.ParseUint(c.Param("sectionID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid section ID format"})
		return
	}

	resources, err := h.ResourceService.GetResourcesByBookSectionID(bookID, chapterID, sectionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resources)
}

// AddResourceToBookSection handles POST /api/resources/:id/booksections
func (h *ResourceHandlers) AddResourceToBookSection(c *gin.Context) {
	resourceID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID format"})
		return
	}

	var request struct {
		BookID    uint64 `json:"book_id" binding:"required"`
		ChapterID uint64 `json:"chapter_id" binding:"required"`
		SectionID uint64 `json:"section_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ResourceService.AddResourceToBookSection(resourceID, request.BookID, request.ChapterID, request.SectionID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Resource added to book section successfully"})
}

// GetResourceTags handles GET /api/resources/tags
func (h *ResourceHandlers) GetResourceTags(c *gin.Context) {
	tags, err := h.ResourceService.GetResourceTags()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, tags)
}

// AddTagToResource handles POST /api/resources/:id/tags
func (h *ResourceHandlers) AddTagToResource(c *gin.Context) {
	resourceID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID format"})
		return
	}

	var request struct {
		TagName string `json:"tag_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ResourceService.AddTagToResource(resourceID, request.TagName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Tag added to resource successfully"})
}

// RemoveTagFromResource handles DELETE /api/resources/:id/tags/:tagName
func (h *ResourceHandlers) RemoveTagFromResource(c *gin.Context) {
	resourceID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID format"})
		return
	}

	tagName := c.Param("tagName")

	if err := h.ResourceService.RemoveTagFromResource(resourceID, tagName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Tag removed from resource successfully"})
}

// GetResourcesByTags handles GET /api/resources/bytags
func (h *ResourceHandlers) GetResourcesByTags(c *gin.Context) {
	tagNames := c.QueryArray("tag")
	if len(tagNames) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one tag is required"})
		return
	}

	resources, err := h.ResourceService.GetResourcesByTags(tagNames)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resources)
}