package main

import (
	"fmt"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/handlers"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/repository"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/personalization/service"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetOutput(os.Stdout)
	logger.SetLevel(logrus.InfoLevel)

	// Connect to database
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		os.Getenv("DB_HOST"),
		os.Getenv("DB_USER"),
		os.Getenv("DB_PASSWORD"),
		os.<PERSON>env("DB_NAME"),
		os.Getenv("DB_PORT"),
	)
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to database")
	}
	
	// Auto migrate models
	err = db.AutoMigrate(
		&models.LearningStyle{},
		&models.LearningPreference{},
		&models.PersonalizedPath{},
		&models.PathItem{},
		&models.AssessmentQuestion{},
		&models.AssessmentResponse{},
		&models.ContentRecommendation{},
		&models.DifficultyLevel{},
		&models.UserPerformance{},
		&models.LearningPathTemplate{},
		&models.TemplateItem{},
	)
	if err != nil {
		logger.WithError(err).Fatal("Failed to migrate database")
	}
	
	// Initialize repository
	personalizationRepo := repository.NewPersonalizationRepository(db)
	
	// Initialize service
	personalizationService := service.NewPersonalizationService(personalizationRepo, logger)
	
	// Initialize handler
	personalizationHandler := handlers.NewPersonalizationHandler(personalizationService, logger)
	
	// Initialize router
	router := gin.Default()
	
	// Register routes
	api := router.Group("/api")
	personalizationHandler.RegisterRoutes(api)
	
	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8085" // Default port for personalization service
	}
	
	logger.Info("Starting personalization service on port " + port)
	if err := router.Run(":" + port); err != nil {
		logger.WithError(err).Fatal("Failed to start server")
	}
}
