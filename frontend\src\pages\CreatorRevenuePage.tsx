import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  Button,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material';
import {
  MonetizationOn as RevenueIcon,
  AccountBalance as BankIcon,
  BarChart as ChartIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { RootState } from '../store';
import {
  fetchRevenueSummary,
  fetchCreatorRevenue
} from '../features/livestream/livestreamSlice';
import WithdrawalModal from '../components/livestream/WithdrawalModal';
import RevenueChart from '../components/livestream/RevenueChart';
import LivestreamNavigation from '../components/livestream/LivestreamNavigation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`revenue-tabpanel-${index}`}
      aria-labelledby={`revenue-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CreatorRevenuePage: React.FC = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    data: revenueData,
    summary,
    loading,
    error
  } = useSelector((state: RootState) => state.livestream.revenue);

  const [tabValue, setTabValue] = useState(0);
  const [isWithdrawalModalOpen, setIsWithdrawalModalOpen] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchRevenueSummary(user.id) as any);
      dispatch(fetchCreatorRevenue({ userId: user.id, page: 1, limit: 20 }) as any);
    }
  }, [dispatch, user]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleWithdrawClick = () => {
    setIsWithdrawalModalOpen(true);
  };

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h5" align="center" sx={{ my: 4 }}>
          Please log in to access your creator revenue dashboard
        </Typography>
      </Container>
    );
  }

  if (loading && !summary) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  const unpaidRevenue = summary?.unpaidRevenue || 0;
  const recentWithdrawals = summary?.recentWithdrawals || [];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Creator Revenue Dashboard
      </Typography>

      <LivestreamNavigation />

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      <Grid container spacing={3}>
        {/* Revenue Summary */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box display="flex" alignItems="center">
                  <RevenueIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6" color="text.secondary">
                      Available for Withdrawal
                    </Typography>
                    <Typography variant="h3" fontWeight="bold">
                      ₦{unpaidRevenue.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box display="flex" justifyContent="flex-end">
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    startIcon={<BankIcon />}
                    onClick={handleWithdrawClick}
                    disabled={unpaidRevenue <= 0}
                  >
                    Withdraw Funds
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Revenue Cards */}
        <Grid item xs={12}>
          <Grid container spacing={3}>
            {['daily', 'weekly', 'monthly'].map((period) => (
              <Grid item xs={12} md={4} key={period}>
                <Card elevation={2}>
                  <CardContent>
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      {period.charAt(0).toUpperCase() + period.slice(1)} Revenue
                    </Typography>
                    <Typography variant="h4" color="primary" fontWeight="bold">
                      ₦{(summary?.[period]?.netRevenue || 0).toLocaleString()}
                    </Typography>
                    <Box mt={1}>
                      <Typography variant="body2" color="text.secondary">
                        From {summary?.[period]?.totalGifts || 0} gifts
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {summary?.[period]?.totalCoins || 0} coins (₦{(summary?.[period]?.totalNairaValue || 0).toLocaleString()})
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Tabs */}
        <Grid item xs={12}>
          <Paper elevation={2}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="revenue tabs"
              >
                <Tab
                  icon={<ChartIcon />}
                  label="Revenue Analytics"
                  id="revenue-tab-0"
                  aria-controls="revenue-tabpanel-0"
                />
                <Tab
                  icon={<HistoryIcon />}
                  label="Revenue History"
                  id="revenue-tab-1"
                  aria-controls="revenue-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Revenue Analytics Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Revenue Analytics
              </Typography>

              <Box sx={{ height: 400, mt: 3 }}>
                <RevenueChart data={revenueData} />
              </Box>
            </TabPanel>

            {/* Revenue History Tab */}
            <TabPanel value={tabValue} index={1}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Typography variant="h6" gutterBottom>
                    Revenue History
                  </Typography>

                  {loading ? (
                    <Box display="flex" justifyContent="center" my={4}>
                      <CircularProgress />
                    </Box>
                  ) : revenueData.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Period</TableCell>
                            <TableCell>Gifts</TableCell>
                            <TableCell>Coins</TableCell>
                            <TableCell>Revenue (₦)</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {revenueData.map((revenue) => (
                            <TableRow key={revenue.id}>
                              <TableCell>
                                {revenue.period.charAt(0).toUpperCase() + revenue.period.slice(1)}
                                <Typography variant="caption" display="block" color="text.secondary">
                                  {new Date(revenue.periodStart).toLocaleDateString()} - {new Date(revenue.periodEnd).toLocaleDateString()}
                                </Typography>
                              </TableCell>
                              <TableCell>{revenue.totalGifts}</TableCell>
                              <TableCell>{revenue.totalCoins}</TableCell>
                              <TableCell>₦{revenue.netRevenue.toLocaleString()}</TableCell>
                              <TableCell>
                                <Chip
                                  label={revenue.isPaid ? 'Paid' : 'Pending'}
                                  color={revenue.isPaid ? 'success' : 'warning'}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body1" color="text.secondary" align="center" sx={{ my: 4 }}>
                      No revenue history found
                    </Typography>
                  )}
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="h6" gutterBottom>
                    Recent Withdrawals
                  </Typography>

                  {recentWithdrawals.length > 0 ? (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Date</TableCell>
                            <TableCell>Amount</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {recentWithdrawals.map((withdrawal) => (
                            <TableRow key={withdrawal.id}>
                              <TableCell>
                                {new Date(withdrawal.createdAt).toLocaleDateString()}
                              </TableCell>
                              <TableCell>₦{withdrawal.amount.toLocaleString()}</TableCell>
                              <TableCell>
                                <Chip
                                  label={withdrawal.status}
                                  color={
                                    withdrawal.status === 'completed' ? 'success' :
                                    withdrawal.status === 'pending' ? 'warning' :
                                    withdrawal.status === 'rejected' ? 'error' :
                                    'default'
                                  }
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body1" color="text.secondary" align="center" sx={{ my: 4 }}>
                      No recent withdrawals
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      <WithdrawalModal
        open={isWithdrawalModalOpen}
        onClose={() => setIsWithdrawalModalOpen(false)}
        availableAmount={unpaidRevenue}
      />
    </Container>
  );
};

export default CreatorRevenuePage;
