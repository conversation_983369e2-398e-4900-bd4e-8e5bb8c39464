import apiClient from '../api/client';

// Types
export interface Tutorial {
  id: number;
  title: string;
  slug: string;
  description: string;
  authorId: number;
  authorName: string;
  thumbnailURL: string;
  categoryId: number;
  categoryName: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  prerequisites: string;
  tags: string[];
  isPublished: boolean;
  viewCount: number;
  completionCount: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

export interface TutorialStep {
  id: number;
  tutorialId: number;
  title: string;
  content: string;
  order: number;
  type: 'text' | 'video' | 'image' | 'code' | 'quiz' | 'interactive';
  mediaURL?: string;
  codeSnippet?: string;
  language?: string;
  quizData?: {
    questions: {
      id: number;
      question: string;
      options: string[];
      correctAnswer: string;
    }[];
  };
  interactiveData?: {
    type: 'codeEditor' | 'dragDrop' | 'fillBlank';
    config: any;
  };
  createdAt: string;
  updatedAt: string;
}

export interface TutorialCategory {
  id: number;
  name: string;
  description: string;
  iconURL?: string;
  tutorialCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface TutorialProgress {
  userId: number;
  tutorialId: number;
  completedSteps: number[];
  currentStepIndex: number;
  isCompleted: boolean;
  completionDate?: string;
  lastAccessedAt: string;
}

// API Service
export const tutorialsService = {
  // Tutorial endpoints
  getTutorials: async (page = 1, pageSize = 10, filters = {}) => {
    const response = await apiClient.get('/tutorials', {
      params: {
        page,
        pageSize,
        ...filters,
      },
    });
    return response.data;
  },

  getUserTutorials: async (userId: number) => {
    const response = await apiClient.get(`/tutorials/user/${userId}`);
    return response.data;
  },

  getTutorialById: async (id: number) => {
    const response = await apiClient.get(`/tutorials/${id}`);
    return response.data;
  },

  getTutorialBySlug: async (slug: string) => {
    const response = await apiClient.get(`/tutorials/slug/${slug}`);
    return response.data;
  },

  createTutorial: async (tutorial: Partial<Tutorial>) => {
    const response = await apiClient.post('/tutorials', tutorial);
    return response.data;
  },

  updateTutorial: async (id: number, tutorial: Partial<Tutorial>) => {
    const response = await apiClient.put(`/tutorials/${id}`, tutorial);
    return response.data;
  },

  deleteTutorial: async (id: number) => {
    await apiClient.delete(`/tutorials/${id}`);
  },

  // Step endpoints
  getTutorialSteps: async (tutorialId: number) => {
    const response = await apiClient.get(`/tutorials/${tutorialId}/steps`);
    return response.data;
  },

  getStepById: async (id: number) => {
    const response = await apiClient.get(`/tutorials/steps/${id}`);
    return response.data;
  },

  createStep: async (tutorialId: number, step: Partial<TutorialStep>) => {
    const response = await apiClient.post(`/tutorials/${tutorialId}/steps`, step);
    return response.data;
  },

  updateStep: async (id: number, step: Partial<TutorialStep>) => {
    const response = await apiClient.put(`/tutorials/steps/${id}`, step);
    return response.data;
  },

  deleteStep: async (id: number) => {
    await apiClient.delete(`/tutorials/steps/${id}`);
  },

  // Category endpoints
  getCategories: async () => {
    const response = await apiClient.get('/tutorials/categories');
    return response.data;
  },

  getCategoryById: async (id: number) => {
    const response = await apiClient.get(`/tutorials/categories/${id}`);
    return response.data;
  },

  createCategory: async (category: Partial<TutorialCategory>) => {
    const response = await apiClient.post('/tutorials/categories', category);
    return response.data;
  },

  updateCategory: async (id: number, category: Partial<TutorialCategory>) => {
    const response = await apiClient.put(`/tutorials/categories/${id}`, category);
    return response.data;
  },

  deleteCategory: async (id: number) => {
    await apiClient.delete(`/tutorials/categories/${id}`);
  },

  // Progress endpoints
  getTutorialProgress: async (userId: number, tutorialId: number) => {
    const response = await apiClient.get(`/tutorials/progress/user/${userId}/tutorial/${tutorialId}`);
    return response.data;
  },

  markStepCompleted: async (userId: number, stepId: number) => {
    const response = await apiClient.post('/tutorials/progress/complete-step', {
      userId,
      stepId,
    });
    return response.data;
  },

  resetProgress: async (userId: number, tutorialId: number) => {
    const response = await apiClient.delete(`/tutorials/progress/user/${userId}/tutorial/${tutorialId}`);
    return response.data;
  },

  // Rating endpoints
  rateTutorial: async (userId: number, tutorialId: number, rating: number, comment?: string) => {
    const response = await apiClient.post('/tutorials/ratings', {
      userId,
      tutorialId,
      rating,
      comment,
    });
    return response.data;
  },

  getTutorialRatings: async (tutorialId: number) => {
    const response = await apiClient.get(`/tutorials/${tutorialId}/ratings`);
    return response.data;
  },
};

export default tutorialsService;
