import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { RootState } from '../store';
import { fetchTopicById, createReply, voteReply, deleteReply } from '../features/forum/forumSlice';

const Container = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const Breadcrumbs = styled.div`
  margin-bottom: 1.5rem;
  color: #666;
  
  a {
    color: #16213e;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  span {
    margin: 0 0.5rem;
  }
`;

const TopicHeader = styled.div`
  background-color: white;
  border-radius: 8px 8px 0 0;
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TopicTitle = styled.h1`
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: #16213e;
`;

const TopicMeta = styled.div`
  color: #666;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
`;

const TopicCategory = styled(Link)`
  display: inline-block;
  background-color: #f0f0f0;
  color: #333;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.8rem;
  
  &:hover {
    background-color: #e0e0e0;
  }
`;

const PostsContainer = styled.div`
  margin-bottom: 2rem;
`;

const Post = styled.div`
  background-color: white;
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 1.5rem;
  
  &:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const PostSidebar = styled.div`
  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
  }
`;

const UserAvatar = styled.img`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 0.5rem;
  
  @media (max-width: 768px) {
    width: 50px;
    height: 50px;
    margin-bottom: 0;
  }
`;

const UserName = styled(Link)`
  display: block;
  font-weight: bold;
  color: #16213e;
  text-decoration: none;
  margin-bottom: 0.25rem;
  
  &:hover {
    text-decoration: underline;
  }
`;

const UserRole = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.5rem;
`;

const PostDate = styled.div`
  font-size: 0.8rem;
  color: #666;
`;

const PostContent = styled.div``;

const PostBody = styled.div`
  margin-bottom: 1.5rem;
  line-height: 1.6;
  
  p {
    margin-bottom: 1rem;
  }
  
  a {
    color: #16213e;
  }
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1rem 0;
  }
  
  blockquote {
    border-left: 3px solid #16213e;
    padding-left: 1rem;
    color: #666;
    margin: 1rem 0;
  }
`;

const PostActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
`;

const VoteButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const VoteButton = styled.button`
  background-color: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  
  &:hover {
    color: #16213e;
  }
`;

const VoteCount = styled.span`
  font-weight: bold;
  color: #16213e;
`;

const PostActionButtons = styled.div`
  display: flex;
  gap: 1rem;
`;

const ActionButton = styled.button`
  background-color: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 0.9rem;
  
  &:hover {
    color: #16213e;
    text-decoration: underline;
  }
`;

const DeleteButton = styled(ActionButton)`
  &:hover {
    color: #e94560;
  }
`;

const ReplyForm = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const ReplyTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 150px;
  resize: vertical;
  margin-bottom: 1rem;
  
  &:focus {
    outline: none;
    border-color: #16213e;
    box-shadow: 0 0 0 2px rgba(22, 33, 62, 0.2);
  }
`;

const SubmitButton = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LoginPrompt = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 2rem;
  
  a {
    color: #16213e;
    font-weight: bold;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  &:after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #16213e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  padding: 1rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
`;

const ForumTopicPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { currentTopic, isLoading, error } = useSelector((state: RootState) => state.forum);
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  useEffect(() => {
    if (id) {
      dispatch(fetchTopicById(id));
    }
  }, [dispatch, id]);
  
  const handleReplySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!id || !replyContent.trim()) return;
    
    setIsSubmitting(true);
    
    dispatch(
      createReply({
        topicId: id,
        newReply: { content: replyContent },
      })
    )
      .unwrap()
      .then(() => {
        setReplyContent('');
        setIsSubmitting(false);
      })
      .catch(() => {
        setIsSubmitting(false);
      });
  };
  
  const handleVote = (replyId: string, vote: 'up' | 'down') => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: `/community/topic/${id}` } });
      return;
    }
    
    dispatch(voteReply({ replyId, vote }));
  };
  
  const handleDeleteReply = (replyId: string) => {
    if (!id) return;
    
    if (window.confirm('Are you sure you want to delete this reply?')) {
      dispatch(deleteReply({ topicId: id, replyId }));
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  // Check if user can delete a reply
  const canDeleteReply = (authorId: string) => {
    if (!user) return false;
    return user.id === authorId;
  };
  
  if (isLoading && !currentTopic) {
    return <LoadingSpinner />;
  }
  
  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }
  
  if (!currentTopic) {
    return <ErrorMessage>Topic not found</ErrorMessage>;
  }
  
  return (
    <Container>
      <Breadcrumbs>
        <Link to="/community">Forum</Link>
        {currentTopic.category && (
          <>
            <span>›</span>
            <Link to={`/community?category=${currentTopic.category.id}`}>
              {currentTopic.category.name}
            </Link>
          </>
        )}
        <span>›</span>
        <span>{currentTopic.title}</span>
      </Breadcrumbs>
      
      <TopicHeader>
        <TopicTitle>{currentTopic.title}</TopicTitle>
        <TopicMeta>
          {currentTopic.category && (
            <TopicCategory to={`/community?category=${currentTopic.category.id}`}>
              {currentTopic.category.name}
            </TopicCategory>
          )}
          <span>
            Started by{' '}
            <Link to={`/profile/${currentTopic.author.id}`}>{currentTopic.author.name}</Link> on{' '}
            {formatDate(currentTopic.created_at)}
          </span>
        </TopicMeta>
      </TopicHeader>
      
      <PostsContainer>
        {/* Original post */}
        <Post>
          <PostSidebar>
            <UserAvatar
              src={currentTopic.author.avatar || 'https://via.placeholder.com/80'}
              alt={currentTopic.author.name}
            />
            <div>
              <UserName to={`/profile/${currentTopic.author.id}`}>
                {currentTopic.author.name}
              </UserName>
              <UserRole>Topic Author</UserRole>
              <PostDate>{formatDate(currentTopic.created_at)}</PostDate>
            </div>
          </PostSidebar>
          
          <PostContent>
            <PostBody dangerouslySetInnerHTML={{ __html: currentTopic.content || '' }} />
          </PostContent>
        </Post>
        
        {/* Replies */}
        {currentTopic.replies && currentTopic.replies.length > 0 ? (
          currentTopic.replies.map((reply) => (
            <Post key={reply.id}>
              <PostSidebar>
                <UserAvatar
                  src={reply.author.avatar || 'https://via.placeholder.com/80'}
                  alt={reply.author.name}
                />
                <div>
                  <UserName to={`/profile/${reply.author.id}`}>{reply.author.name}</UserName>
                  <PostDate>{formatDate(reply.created_at)}</PostDate>
                </div>
              </PostSidebar>
              
              <PostContent>
                <PostBody dangerouslySetInnerHTML={{ __html: reply.content }} />
                
                <PostActions>
                  <VoteButtons>
                    <VoteButton onClick={() => handleVote(reply.id, 'up')}>
                      👍
                    </VoteButton>
                    <VoteCount>{reply.votes}</VoteCount>
                    <VoteButton onClick={() => handleVote(reply.id, 'down')}>
                      👎
                    </VoteButton>
                  </VoteButtons>
                  
                  <PostActionButtons>
                    <ActionButton onClick={() => setReplyContent(`@${reply.author.name} `)}>
                      Quote
                    </ActionButton>
                    {canDeleteReply(reply.author.id) && (
                      <DeleteButton onClick={() => handleDeleteReply(reply.id)}>
                        Delete
                      </DeleteButton>
                    )}
                  </PostActionButtons>
                </PostActions>
              </PostContent>
            </Post>
          ))
        ) : (
          <Post>
            <div style={{ gridColumn: '1 / -1', textAlign: 'center', color: '#666' }}>
              No replies yet. Be the first to reply!
            </div>
          </Post>
        )}
      </PostsContainer>
      
      {isAuthenticated ? (
        <ReplyForm>
          <ReplyTitle>Post a Reply</ReplyTitle>
          <form onSubmit={handleReplySubmit}>
            <TextArea
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              placeholder="Write your reply here..."
              required
            />
            <SubmitButton type="submit" disabled={isSubmitting || !replyContent.trim()}>
              {isSubmitting ? 'Posting...' : 'Post Reply'}
            </SubmitButton>
          </form>
        </ReplyForm>
      ) : (
        <LoginPrompt>
          <p>
            Please <Link to="/login">login</Link> or <Link to="/register">register</Link> to post a
            reply.
          </p>
        </LoginPrompt>
      )}
    </Container>
  );
};

export default ForumTopicPage;
