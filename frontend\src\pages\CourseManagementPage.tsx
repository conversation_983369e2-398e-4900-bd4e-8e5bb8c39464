import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchCourses, deleteCourse } from '../store/slices/coursesSlice';
import { Course } from '../services/coursesService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`course-tabpanel-${index}`}
      aria-labelledby={`course-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `course-tab-${index}`,
    'aria-controls': `course-tabpanel-${index}`,
  };
}

const CourseManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { items: courses, total, loading, error } = useSelector((state: RootState) => state.courses.courses);
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);
  
  useEffect(() => {
    if (user) {
      const filters: any = {
        instructorId: user.id,
      };
      
      if (tabValue === 0) {
        // All courses
      } else if (tabValue === 1) {
        // Published courses
        filters.status = 'published';
      } else if (tabValue === 2) {
        // Draft courses
        filters.status = 'draft';
      } else if (tabValue === 3) {
        // Upcoming courses
        filters.status = 'upcoming';
      }
      
      if (searchQuery) {
        filters.search = searchQuery;
      }
      
      dispatch(fetchCourses({ filters }));
    }
  }, [dispatch, user, tabValue, searchQuery]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, courseId: number) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedCourseId(courseId);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedCourseId(null);
  };
  
  const handleDeleteClick = (course: Course) => {
    setCourseToDelete(course);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };
  
  const handleDeleteConfirm = async () => {
    if (courseToDelete) {
      try {
        await dispatch(deleteCourse(courseToDelete.id)).unwrap();
        setDeleteDialogOpen(false);
        setCourseToDelete(null);
      } catch (error) {
        console.error('Failed to delete course:', error);
      }
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCourseToDelete(null);
  };
  
  const handleEditCourse = (courseId: number) => {
    navigate(`/instructor/courses/edit/${courseId}`);
    handleMenuClose();
  };
  
  const handleViewCourse = (courseId: number) => {
    navigate(`/courses/${courseId}`);
    handleMenuClose();
  };
  
  const handleCreateCourse = () => {
    navigate('/instructor/courses/create');
  };
  
  const handleManageContent = (courseId: number) => {
    navigate(`/instructor/courses/${courseId}/content`);
    handleMenuClose();
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to manage courses. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Course Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateCourse}
        >
          Create Course
        </Button>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="course tabs">
            <Tab label="All Courses" {...a11yProps(0)} />
            <Tab label="Published" {...a11yProps(1)} />
            <Tab label="Drafts" {...a11yProps(2)} />
            <Tab label="Upcoming" {...a11yProps(3)} />
          </Tabs>
        </Box>
        
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            placeholder="Search courses..."
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        ) : courses.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              No courses found.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateCourse}
              sx={{ mt: 2 }}
            >
              Create Your First Course
            </Button>
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Students</TableCell>
                  <TableCell>Rating</TableCell>
                  <TableCell>Price</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {courses.map((course) => (
                  <TableRow key={course.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {course.thumbnailURL ? (
                          <Box
                            component="img"
                            src={course.thumbnailURL}
                            alt={course.title}
                            sx={{
                              width: 60,
                              height: 40,
                              objectFit: 'cover',
                              borderRadius: 1,
                              mr: 2,
                            }}
                          />
                        ) : (
                          <Box
                            sx={{
                              width: 60,
                              height: 40,
                              bgcolor: 'grey.300',
                              borderRadius: 1,
                              mr: 2,
                            }}
                          />
                        )}
                        <Typography variant="body1">{course.title}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={course.status}
                        color={
                          course.status === 'published'
                            ? 'success'
                            : course.status === 'draft'
                            ? 'default'
                            : 'primary'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{course.enrollmentCount}</TableCell>
                    <TableCell>{course.rating.toFixed(1)}</TableCell>
                    <TableCell>
                      {course.isFree ? (
                        <Chip label="Free" size="small" color="success" />
                      ) : (
                        `₦${course.price.toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(course.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        aria-label="more"
                        onClick={(e) => handleMenuOpen(e, course.id)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>
      
      {/* Course actions menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedCourseId && handleViewCourse(selectedCourseId)}>
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View Course
        </MenuItem>
        <MenuItem onClick={() => selectedCourseId && handleEditCourse(selectedCourseId)}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Course
        </MenuItem>
        <MenuItem onClick={() => selectedCourseId && handleManageContent(selectedCourseId)}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Manage Content
        </MenuItem>
        <MenuItem
          onClick={() => {
            const course = courses.find((c) => c.id === selectedCourseId);
            if (course) {
              handleDeleteClick(course);
            }
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete Course
        </MenuItem>
      </Menu>
      
      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the course "{courseToDelete?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CourseManagementPage;
