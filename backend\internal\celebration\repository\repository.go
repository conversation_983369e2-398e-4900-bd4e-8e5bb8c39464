package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/celebration/models"
	"github.com/jmoiron/sqlx"
)

// CelebrationRepository handles database operations for the Celebrate Nigeria feature
type CelebrationRepository struct {
	db *sqlx.DB
}

// NewCelebrationRepository creates a new repository
func NewCelebrationRepository(db *sqlx.DB) *CelebrationRepository {
	return &CelebrationRepository{
		db: db,
	}
}

// FindCategoryBySlug retrieves a category by its slug
func (r *CelebrationRepository) FindCategoryBySlug(ctx context.Context, slug string) (*models.Category, error) {
	var category models.Category
	err := r.db.GetContext(ctx, &category, `
		SELECT * FROM categories WHERE slug = $1
	`, slug)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &category, nil
}

// FindCategoryByID retrieves a category by its ID
func (r *CelebrationRepository) FindCategoryByID(ctx context.Context, id int64) (*models.Category, error) {
	var category models.Category
	err := r.db.GetContext(ctx, &category, `
		SELECT * FROM categories WHERE id = $1
	`, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &category, nil
}

// ListCategories retrieves categories, optionally filtered by parent ID
func (r *CelebrationRepository) ListCategories(ctx context.Context, parentID *int64) ([]models.Category, error) {
	var categories []models.Category
	var err error

	if parentID == nil {
		err = r.db.SelectContext(ctx, &categories, `
			SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order, name
		`)
	} else {
		err = r.db.SelectContext(ctx, &categories, `
			SELECT * FROM categories WHERE parent_id = $1 ORDER BY sort_order, name
		`, *parentID)
	}

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// CreateCategory creates a new category
func (r *CelebrationRepository) CreateCategory(ctx context.Context, category *models.Category) error {
	query := `
		INSERT INTO categories (
			name, description, slug, parent_id, image_url, icon_svg, sort_order, visible, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		category.Name,
		category.Description,
		category.Slug,
		category.ParentID,
		category.ImageURL,
		category.IconSVG,
		category.SortOrder,
		category.Visible,
		category.CreatedAt,
		category.UpdatedAt,
	).Scan(&category.ID)

	return err
}

// UpdateCategory updates an existing category
func (r *CelebrationRepository) UpdateCategory(ctx context.Context, category *models.Category) error {
	query := `
		UPDATE categories SET
			name = $1,
			description = $2,
			slug = $3,
			parent_id = $4,
			image_url = $5,
			icon_svg = $6,
			sort_order = $7,
			visible = $8,
			updated_at = $9
		WHERE id = $10
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		category.Name,
		category.Description,
		category.Slug,
		category.ParentID,
		category.ImageURL,
		category.IconSVG,
		category.SortOrder,
		category.Visible,
		category.UpdatedAt,
		category.ID,
	)

	return err
}

// DeleteCategory deletes a category
func (r *CelebrationRepository) DeleteCategory(ctx context.Context, id int64) error {
	// First check if the category has any entries
	var count int
	err := r.db.GetContext(ctx, &count, `
		SELECT COUNT(*) FROM entry_categories WHERE category_id = $1
	`, id)
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("cannot delete category: it has %d entries", count)
	}

	// Then check if it has subcategories
	err = r.db.GetContext(ctx, &count, `
		SELECT COUNT(*) FROM categories WHERE parent_id = $1
	`, id)
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("cannot delete category: it has %d subcategories", count)
	}

	// If no entries or subcategories, delete the category
	_, err = r.db.ExecContext(ctx, `
		DELETE FROM categories WHERE id = $1
	`, id)

	return err
}

// FindEntryBySlug retrieves an entry by its slug
func (r *CelebrationRepository) FindEntryBySlug(ctx context.Context, slug string) (*models.CelebrationEntry, error) {
	var entry models.CelebrationEntry
	err := r.db.GetContext(ctx, &entry, `
		SELECT * FROM celebration_entries WHERE slug = $1
	`, slug)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	// Get categories
	err = r.db.SelectContext(ctx, &entry.Categories, `
		SELECT c.* FROM categories c
		JOIN entry_categories ec ON c.id = ec.category_id
		WHERE ec.celebration_entry_id = $1
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	// Get media
	err = r.db.SelectContext(ctx, &entry.Media, `
		SELECT * FROM entry_media
		WHERE celebration_entry_id = $1
		ORDER BY sort_order, id
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	// Get facts
	err = r.db.SelectContext(ctx, &entry.Facts, `
		SELECT * FROM entry_facts
		WHERE celebration_entry_id = $1
		ORDER BY sort_order, id
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	return &entry, nil
}

// FindEntryByID retrieves an entry by its ID
func (r *CelebrationRepository) FindEntryByID(ctx context.Context, id int64) (*models.CelebrationEntry, error) {
	var entry models.CelebrationEntry
	err := r.db.GetContext(ctx, &entry, `
		SELECT * FROM celebration_entries WHERE id = $1
	`, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	// Get categories
	err = r.db.SelectContext(ctx, &entry.Categories, `
		SELECT c.* FROM categories c
		JOIN entry_categories ec ON c.id = ec.category_id
		WHERE ec.celebration_entry_id = $1
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	// Get media
	err = r.db.SelectContext(ctx, &entry.Media, `
		SELECT * FROM entry_media
		WHERE celebration_entry_id = $1
		ORDER BY sort_order, id
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	// Get facts
	err = r.db.SelectContext(ctx, &entry.Facts, `
		SELECT * FROM entry_facts
		WHERE celebration_entry_id = $1
		ORDER BY sort_order, id
	`, entry.ID)
	if err != nil {
		return nil, err
	}

	return &entry, nil
}

// FindPersonBySlug retrieves a person entry by its slug
func (r *CelebrationRepository) FindPersonBySlug(ctx context.Context, slug string) (*models.PersonEntry, error) {
	// First get the base entry
	baseEntry, err := r.FindEntryBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	if baseEntry == nil {
		return nil, nil
	}

	// Then get the person-specific data
	var person models.PersonEntry
	err = r.db.GetContext(ctx, &person, `
		SELECT * FROM person_entries WHERE celebration_entry_id = $1
	`, baseEntry.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	// Set the base entry fields
	person.CelebrationEntry = *baseEntry

	return &person, nil
}

// FindPlaceBySlug retrieves a place entry by its slug
func (r *CelebrationRepository) FindPlaceBySlug(ctx context.Context, slug string) (*models.PlaceEntry, error) {
	// First get the base entry
	baseEntry, err := r.FindEntryBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	if baseEntry == nil {
		return nil, nil
	}

	// Then get the place-specific data
	var place models.PlaceEntry
	err = r.db.GetContext(ctx, &place, `
		SELECT * FROM place_entries WHERE celebration_entry_id = $1
	`, baseEntry.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	// Set the base entry fields
	place.CelebrationEntry = *baseEntry

	return &place, nil
}

// FindEventBySlug retrieves an event entry by its slug
func (r *CelebrationRepository) FindEventBySlug(ctx context.Context, slug string) (*models.EventEntry, error) {
	// First get the base entry
	baseEntry, err := r.FindEntryBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	if baseEntry == nil {
		return nil, nil
	}

	// Then get the event-specific data
	var event models.EventEntry
	err = r.db.GetContext(ctx, &event, `
		SELECT * FROM event_entries WHERE celebration_entry_id = $1
	`, baseEntry.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	// Set the base entry fields
	event.CelebrationEntry = *baseEntry

	return &event, nil
}

// ListEntries retrieves a paginated list of entries
func (r *CelebrationRepository) ListEntries(ctx context.Context, entryType string, categoryID *int64, limit, offset int) ([]models.CelebrationEntry, int64, error) {
	var entries []models.CelebrationEntry
	var query string
	var countQuery string
	var args []interface{}
	var count int64

	if categoryID != nil {
		// Get entries by category
		if entryType != "" {
			query = `
				SELECT DISTINCT ce.* FROM celebration_entries ce
				JOIN entry_categories ec ON ce.id = ec.celebration_entry_id
				WHERE ec.category_id = $1 AND ce.entry_type = $2 AND ce.status = 'approved'
				ORDER BY ce.featured_rank DESC, ce.title
				LIMIT $3 OFFSET $4
			`
			countQuery = `
				SELECT COUNT(DISTINCT ce.id) FROM celebration_entries ce
				JOIN entry_categories ec ON ce.id = ec.celebration_entry_id
				WHERE ec.category_id = $1 AND ce.entry_type = $2 AND ce.status = 'approved'
			`
			args = []interface{}{*categoryID, entryType, limit, offset}
			err := r.db.GetContext(ctx, &count, countQuery, *categoryID, entryType)
			if err != nil {
				return nil, 0, err
			}
		} else {
			query = `
				SELECT DISTINCT ce.* FROM celebration_entries ce
				JOIN entry_categories ec ON ce.id = ec.celebration_entry_id
				WHERE ec.category_id = $1 AND ce.status = 'approved'
				ORDER BY ce.featured_rank DESC, ce.title
				LIMIT $2 OFFSET $3
			`
			countQuery = `
				SELECT COUNT(DISTINCT ce.id) FROM celebration_entries ce
				JOIN entry_categories ec ON ce.id = ec.celebration_entry_id
				WHERE ec.category_id = $1 AND ce.status = 'approved'
			`
			args = []interface{}{*categoryID, limit, offset}
			err := r.db.GetContext(ctx, &count, countQuery, *categoryID)
			if err != nil {
				return nil, 0, err
			}
		}
	} else {
		// Get all entries
		if entryType != "" {
			query = `
				SELECT * FROM celebration_entries
				WHERE entry_type = $1 AND status = 'approved'
				ORDER BY featured_rank DESC, title
				LIMIT $2 OFFSET $3
			`
			countQuery = `
				SELECT COUNT(*) FROM celebration_entries
				WHERE entry_type = $1 AND status = 'approved'
			`
			args = []interface{}{entryType, limit, offset}
			err := r.db.GetContext(ctx, &count, countQuery, entryType)
			if err != nil {
				return nil, 0, err
			}
		} else {
			query = `
				SELECT * FROM celebration_entries
				WHERE status = 'approved'
				ORDER BY featured_rank DESC, title
				LIMIT $1 OFFSET $2
			`
			countQuery = `
				SELECT COUNT(*) FROM celebration_entries
				WHERE status = 'approved'
			`
			args = []interface{}{limit, offset}
			err := r.db.GetContext(ctx, &count, countQuery)
			if err != nil {
				return nil, 0, err
			}
		}
	}

	err := r.db.SelectContext(ctx, &entries, query, args...)
	if err != nil {
		return nil, 0, err
	}

	// Get categories for each entry
	for i := range entries {
		err = r.db.SelectContext(ctx, &entries[i].Categories, `
			SELECT c.* FROM categories c
			JOIN entry_categories ec ON c.id = ec.category_id
			WHERE ec.celebration_entry_id = $1
		`, entries[i].ID)
		if err != nil {
			return nil, 0, err
		}
	}

	return entries, count, nil
}

// ListFeaturedEntries retrieves featured entries
func (r *CelebrationRepository) ListFeaturedEntries(ctx context.Context, entryType string, limit int) ([]models.CelebrationEntry, error) {
	var entries []models.CelebrationEntry
	var query string
	var args []interface{}

	if entryType != "" {
		query = `
			SELECT * FROM celebration_entries
			WHERE entry_type = $1 AND status = 'approved' AND featured_rank > 0
			ORDER BY featured_rank DESC
			LIMIT $2
		`
		args = []interface{}{entryType, limit}
	} else {
		query = `
			SELECT * FROM celebration_entries
			WHERE status = 'approved' AND featured_rank > 0
			ORDER BY featured_rank DESC
			LIMIT $1
		`
		args = []interface{}{limit}
	}

	err := r.db.SelectContext(ctx, &entries, query, args...)
	if err != nil {
		return nil, err
	}

	// Get categories for each entry
	for i := range entries {
		err = r.db.SelectContext(ctx, &entries[i].Categories, `
			SELECT c.* FROM categories c
			JOIN entry_categories ec ON c.id = ec.category_id
			WHERE ec.celebration_entry_id = $1
		`, entries[i].ID)
		if err != nil {
			return nil, err
		}
	}

	return entries, nil
}

// ListEntryComments retrieves comments for an entry
func (r *CelebrationRepository) ListEntryComments(ctx context.Context, entryID int64, limit, offset int) ([]models.EntryComment, int64, error) {
	var comments []models.EntryComment
	var count int64

	// Get total count
	err := r.db.GetContext(ctx, &count, `
		SELECT COUNT(*) FROM entry_comments
		WHERE celebration_entry_id = $1 AND status = 'approved'
	`, entryID)
	if err != nil {
		return nil, 0, err
	}

	// Get comments
	err = r.db.SelectContext(ctx, &comments, `
		SELECT * FROM entry_comments
		WHERE celebration_entry_id = $1 AND status = 'approved'
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`, entryID, limit, offset)
	if err != nil {
		return nil, 0, err
	}

	return comments, count, nil
}

// CreatePersonEntry creates a new person entry
func (r *CelebrationRepository) CreatePersonEntry(ctx context.Context, entry *models.PersonEntry) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Insert base entry
	baseQuery := `
		INSERT INTO celebration_entries (
			entry_type, slug, title, short_desc, full_desc, primary_image_url,
			location, featured_rank, status, submitted_by, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		baseQuery,
		entry.EntryType,
		entry.Slug,
		entry.Title,
		entry.ShortDesc,
		entry.FullDesc,
		entry.PrimaryImageURL,
		entry.Location,
		entry.FeaturedRank,
		entry.Status,
		entry.SubmittedBy,
		entry.CreatedAt,
		entry.UpdatedAt,
	).Scan(&entry.ID)
	if err != nil {
		return err
	}

	// Insert person entry
	personQuery := `
		INSERT INTO person_entries (
			celebration_entry_id, birth_date, death_date, profession,
			achievements, contributions, education, related_links
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		personQuery,
		entry.ID,
		entry.BirthDate,
		entry.DeathDate,
		entry.Profession,
		entry.Achievements,
		entry.Contributions,
		entry.Education,
		entry.RelatedLinks,
	).Scan(&entry.PersonID)
	if err != nil {
		return err
	}

	// Add categories
	for _, category := range entry.Categories {
		_, err = tx.ExecContext(ctx, `
			INSERT INTO entry_categories (celebration_entry_id, category_id)
			VALUES ($1, $2)
		`, entry.ID, category.ID)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// CreatePlaceEntry creates a new place entry
func (r *CelebrationRepository) CreatePlaceEntry(ctx context.Context, entry *models.PlaceEntry) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Insert base entry
	baseQuery := `
		INSERT INTO celebration_entries (
			entry_type, slug, title, short_desc, full_desc, primary_image_url,
			location, featured_rank, status, submitted_by, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		baseQuery,
		entry.EntryType,
		entry.Slug,
		entry.Title,
		entry.ShortDesc,
		entry.FullDesc,
		entry.PrimaryImageURL,
		entry.Location,
		entry.FeaturedRank,
		entry.Status,
		entry.SubmittedBy,
		entry.CreatedAt,
		entry.UpdatedAt,
	).Scan(&entry.ID)
	if err != nil {
		return err
	}

	// Insert place entry
	placeQuery := `
		INSERT INTO place_entries (
			celebration_entry_id, place_type, latitude, longitude,
			address, visiting_hours, visiting_fees, accessibility, history
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		placeQuery,
		entry.ID,
		entry.PlaceType,
		entry.Latitude,
		entry.Longitude,
		entry.Address,
		entry.VisitingHours,
		entry.VisitingFees,
		entry.Accessibility,
		entry.History,
	).Scan(&entry.PlaceID)
	if err != nil {
		return err
	}

	// Add categories
	for _, category := range entry.Categories {
		_, err = tx.ExecContext(ctx, `
			INSERT INTO entry_categories (celebration_entry_id, category_id)
			VALUES ($1, $2)
		`, entry.ID, category.ID)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// CreateEventEntry creates a new event entry
func (r *CelebrationRepository) CreateEventEntry(ctx context.Context, entry *models.EventEntry) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Insert base entry
	baseQuery := `
		INSERT INTO celebration_entries (
			entry_type, slug, title, short_desc, full_desc, primary_image_url,
			location, featured_rank, status, submitted_by, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		baseQuery,
		entry.EntryType,
		entry.Slug,
		entry.Title,
		entry.ShortDesc,
		entry.FullDesc,
		entry.PrimaryImageURL,
		entry.Location,
		entry.FeaturedRank,
		entry.Status,
		entry.SubmittedBy,
		entry.CreatedAt,
		entry.UpdatedAt,
	).Scan(&entry.ID)
	if err != nil {
		return err
	}

	// Insert event entry
	eventQuery := `
		INSERT INTO event_entries (
			celebration_entry_id, event_type, start_date, end_date,
			is_recurring, recurrence_pattern, organizer, contact_info, event_history
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9
		) RETURNING id
	`

	err = tx.QueryRowxContext(
		ctx,
		eventQuery,
		entry.ID,
		entry.EventType,
		entry.StartDate,
		entry.EndDate,
		entry.IsRecurring,
		entry.RecurrencePattern,
		entry.Organizer,
		entry.ContactInfo,
		entry.EventHistory,
	).Scan(&entry.EventID)
	if err != nil {
		return err
	}

	// Add categories
	for _, category := range entry.Categories {
		_, err = tx.ExecContext(ctx, `
			INSERT INTO entry_categories (celebration_entry_id, category_id)
			VALUES ($1, $2)
		`, entry.ID, category.ID)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// CreateEntryComment adds a comment to an entry
func (r *CelebrationRepository) CreateEntryComment(ctx context.Context, comment *models.EntryComment) error {
	query := `
		INSERT INTO entry_comments (
			celebration_entry_id, user_id, content, status, parent_id, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		comment.CelebrationEntryID,
		comment.UserID,
		comment.Content,
		comment.Status,
		comment.ParentID,
		comment.CreatedAt,
		comment.UpdatedAt,
	).Scan(&comment.ID)

	return err
}

// CreateEntryMedia adds media to an entry
func (r *CelebrationRepository) CreateEntryMedia(ctx context.Context, media *models.EntryMedia) error {
	query := `
		INSERT INTO entry_media (
			celebration_entry_id, media_type, url, caption, sort_order, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		media.CelebrationEntryID,
		media.MediaType,
		media.URL,
		media.Caption,
		media.SortOrder,
		media.CreatedAt,
		media.UpdatedAt,
	).Scan(&media.ID)

	return err
}

// CreateEntryFact adds a fact to an entry
func (r *CelebrationRepository) CreateEntryFact(ctx context.Context, fact *models.EntryFact) error {
	query := `
		INSERT INTO entry_facts (
			celebration_entry_id, label, value, sort_order, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		fact.CelebrationEntryID,
		fact.Label,
		fact.Value,
		fact.SortOrder,
		fact.CreatedAt,
		fact.UpdatedAt,
	).Scan(&fact.ID)

	return err
}

// SearchEntriesOptions contains options for the SearchEntries function
type SearchEntriesOptions struct {
	Query        string
	EntryType    string
	CategoryID   int64
	CategorySlug string
	SortBy       string
	Limit        int
	Offset       int
}

// SearchEntries performs a search on entries with advanced filtering
func (r *CelebrationRepository) SearchEntries(ctx context.Context, options SearchEntriesOptions) ([]models.CelebrationEntry, int64, error) {
	var entries []models.CelebrationEntry
	var count int64
	var whereConditions []string
	var orderBy string
	var args []interface{}
	var argIndex int = 1

	// Base conditions
	whereConditions = append(whereConditions, "status = 'approved'")

	// Add entry type filter if provided
	if options.EntryType != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("entry_type = $%d", argIndex))
		args = append(args, options.EntryType)
		argIndex++
	}

	// Add search query if provided
	if options.Query != "" {
		// Clean and format the query for to_tsquery
		formattedQuery := formatSearchQuery(options.Query)
		whereConditions = append(whereConditions, fmt.Sprintf("search_vector @@ to_tsquery('english', $%d)", argIndex))
		args = append(args, formattedQuery)
		argIndex++
	}

	// Add category filter if provided
	var categoryJoin string
	if options.CategoryID > 0 || options.CategorySlug != "" {
		categoryJoin = `
			JOIN entry_categories ec ON ce.id = ec.celebration_entry_id
			JOIN categories c ON ec.category_id = c.id
		`

		if options.CategoryID > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf("c.id = $%d", argIndex))
			args = append(args, options.CategoryID)
			argIndex++
		} else if options.CategorySlug != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("c.slug = $%d", argIndex))
			args = append(args, options.CategorySlug)
			argIndex++
		}
	}

	// Determine sort order
	switch options.SortBy {
	case "newest":
		orderBy = "ce.created_at DESC"
	case "oldest":
		orderBy = "ce.created_at ASC"
	case "az":
		orderBy = "ce.title ASC"
	case "za":
		orderBy = "ce.title DESC"
	case "popular":
		orderBy = "ce.like_count DESC"
	default:
		// Default to relevance sorting
		if options.Query != "" {
			formattedQuery := formatSearchQuery(options.Query)
			orderBy = fmt.Sprintf("ce.featured_rank DESC, ts_rank(ce.search_vector, to_tsquery('english', '%s')) DESC", formattedQuery)
		} else {
			orderBy = "ce.featured_rank DESC, ce.created_at DESC"
		}
	}

	// Build the WHERE clause
	whereClause := strings.Join(whereConditions, " AND ")

	// Build the query
	sqlQuery := fmt.Sprintf(`
		SELECT ce.* FROM celebration_entries ce
		%s
		WHERE %s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, categoryJoin, whereClause, orderBy, argIndex, argIndex+1)

	// Add limit and offset to args
	args = append(args, options.Limit, options.Offset)

	// Build the count query
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT ce.id) FROM celebration_entries ce
		%s
		WHERE %s
	`, categoryJoin, whereClause)

	// Execute the count query
	err := r.db.GetContext(ctx, &count, countQuery, args[:len(args)-2]...)
	if err != nil {
		return nil, 0, err
	}

	// Execute the main query
	err = r.db.SelectContext(ctx, &entries, sqlQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	// Get categories for each entry
	for i := range entries {
		err = r.db.SelectContext(ctx, &entries[i].Categories, `
			SELECT c.* FROM categories c
			JOIN entry_categories ec ON c.id = ec.category_id
			WHERE ec.celebration_entry_id = $1
		`, entries[i].ID)
		if err != nil {
			return nil, 0, err
		}
	}

	return entries, count, nil
}

// formatSearchQuery formats a user query for PostgreSQL full-text search
func formatSearchQuery(query string) string {
	// Trim whitespace
	query = strings.TrimSpace(query)

	// Split into words
	words := strings.Fields(query)

	// Format each word for tsquery
	for i, word := range words {
		// Add :* for prefix matching
		words[i] = word + ":*"
	}

	// Join with & for AND logic
	return strings.Join(words, " & ")
}

// CreateSubmission creates a new entry submission
func (r *CelebrationRepository) CreateSubmission(ctx context.Context, submission *models.EntrySubmission) error {
	query := `
		INSERT INTO entry_submissions (
			user_id, entry_type, target_entry_id, title, content, status, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		submission.UserID,
		submission.EntryType,
		submission.TargetEntryID,
		submission.Title,
		submission.Content,
		submission.Status,
		submission.CreatedAt,
		submission.UpdatedAt,
	).Scan(&submission.ID)

	return err
}

// ListPendingSubmissions retrieves pending submissions
func (r *CelebrationRepository) ListPendingSubmissions(ctx context.Context, entryType string, limit, offset int) ([]models.EntrySubmission, int64, error) {
	var submissions []models.EntrySubmission
	var count int64
	var sqlQuery string
	var countQuery string
	var args []interface{}

	if entryType != "" {
		sqlQuery = `
			SELECT * FROM entry_submissions
			WHERE status = 'pending' AND entry_type = $1
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		countQuery = `
			SELECT COUNT(*) FROM entry_submissions
			WHERE status = 'pending' AND entry_type = $1
		`
		args = []interface{}{entryType, limit, offset}
		err := r.db.GetContext(ctx, &count, countQuery, entryType)
		if err != nil {
			return nil, 0, err
		}
	} else {
		sqlQuery = `
			SELECT * FROM entry_submissions
			WHERE status = 'pending'
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		countQuery = `
			SELECT COUNT(*) FROM entry_submissions
			WHERE status = 'pending'
		`
		args = []interface{}{limit, offset}
		err := r.db.GetContext(ctx, &count, countQuery)
		if err != nil {
			return nil, 0, err
		}
	}

	err := r.db.SelectContext(ctx, &submissions, sqlQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return submissions, count, nil
}

// UpdateSubmission updates a submission
func (r *CelebrationRepository) UpdateSubmission(ctx context.Context, submission *models.EntrySubmission) error {
	query := `
		UPDATE entry_submissions SET
			status = $1,
			admin_notes = $2,
			reviewed_by = $3,
			reviewed_at = $4,
			updated_at = $5
		WHERE id = $6
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		submission.Status,
		submission.AdminNotes,
		submission.ReviewedBy,
		submission.ReviewedAt,
		submission.UpdatedAt,
		submission.ID,
	)

	return err
}

// VoteForSubmission adds a vote to a submission
func (r *CelebrationRepository) VoteForSubmission(ctx context.Context, submissionID, userID int64, voteType string) error {
	// Check if user already voted
	var count int
	err := r.db.GetContext(ctx, &count, `
		SELECT COUNT(*) FROM entry_votes
		WHERE celebration_entry_id = $1 AND user_id = $2
	`, submissionID, userID)
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("user already voted for this submission")
	}

	// Add vote
	_, err = r.db.ExecContext(ctx, `
		INSERT INTO entry_votes (celebration_entry_id, user_id, vote_type, created_at)
		VALUES ($1, $2, $3, NOW())
	`, submissionID, userID, voteType)
	if err != nil {
		return err
	}

	// Update vote count
	var voteCount int
	if voteType == "support" {
		err = r.db.GetContext(ctx, &voteCount, `
			SELECT COUNT(*) FROM entry_votes
			WHERE celebration_entry_id = $1 AND vote_type = 'support'
		`, submissionID)
	} else {
		err = r.db.GetContext(ctx, &voteCount, `
			SELECT COUNT(*) FROM entry_votes
			WHERE celebration_entry_id = $1 AND vote_type = 'oppose'
		`, submissionID)
	}
	if err != nil {
		return err
	}

	// Update submission vote count
	_, err = r.db.ExecContext(ctx, `
		UPDATE entry_submissions
		SET vote_count = $1, updated_at = NOW()
		WHERE id = $2
	`, voteCount, submissionID)

	return err
}

// VoteForEntry adds or updates a vote for an entry
func (r *CelebrationRepository) VoteForEntry(ctx context.Context, entryID, userID int64, voteType string) error {
	// Check if user already voted
	var existingVote struct {
		ID       int64
		VoteType string
	}

	err := r.db.GetContext(ctx, &existingVote, `
		SELECT id, vote_type FROM entry_votes
		WHERE celebration_entry_id = $1 AND user_id = $2
	`, entryID, userID)

	if err != nil && err != sql.ErrNoRows {
		return err
	}

	// If user already voted with the same vote type, return error
	if err == nil && existingVote.VoteType == voteType {
		return fmt.Errorf("user already voted for this entry with the same vote type")
	}

	// If user already voted with a different vote type, update the vote
	if err == nil {
		_, err = r.db.ExecContext(ctx, `
			UPDATE entry_votes
			SET vote_type = $1, created_at = NOW()
			WHERE id = $2
		`, voteType, existingVote.ID)

		if err != nil {
			return err
		}

		// Update entry like count
		return r.updateEntryLikeCount(ctx, entryID)
	}

	// Add new vote
	_, err = r.db.ExecContext(ctx, `
		INSERT INTO entry_votes (celebration_entry_id, user_id, vote_type, created_at)
		VALUES ($1, $2, $3, NOW())
	`, entryID, userID, voteType)

	if err != nil {
		return err
	}

	// Update entry like count
	return r.updateEntryLikeCount(ctx, entryID)
}

// GetEntryVoteCounts gets the upvote and downvote counts for an entry
func (r *CelebrationRepository) GetEntryVoteCounts(ctx context.Context, entryID int64) (upvotes int, downvotes int, err error) {
	query := `
		SELECT
			COUNT(CASE WHEN vote_type = 'upvote' THEN 1 END) as upvotes,
			COUNT(CASE WHEN vote_type = 'downvote' THEN 1 END) as downvotes
		FROM entry_votes
		WHERE celebration_entry_id = $1
	`

	err = r.db.QueryRowContext(ctx, query, entryID).Scan(&upvotes, &downvotes)
	return
}

// GetUserVoteForEntry gets a user's vote for an entry
func (r *CelebrationRepository) GetUserVoteForEntry(ctx context.Context, entryID, userID int64) (string, error) {
	var voteType string

	err := r.db.GetContext(ctx, &voteType, `
		SELECT vote_type FROM entry_votes
		WHERE celebration_entry_id = $1 AND user_id = $2
	`, entryID, userID)

	if err == sql.ErrNoRows {
		return "", nil // No vote found
	}

	return voteType, err
}

// DeleteVoteForEntry removes a user's vote for an entry
func (r *CelebrationRepository) DeleteVoteForEntry(ctx context.Context, entryID, userID int64) error {
	_, err := r.db.ExecContext(ctx, `
		DELETE FROM entry_votes
		WHERE celebration_entry_id = $1 AND user_id = $2
	`, entryID, userID)

	if err != nil {
		return err
	}

	// Update entry like count
	return r.updateEntryLikeCount(ctx, entryID)
}

// updateEntryLikeCount updates the like_count field in the celebration_entries table
func (r *CelebrationRepository) updateEntryLikeCount(ctx context.Context, entryID int64) error {
	// Calculate the net vote count (upvotes - downvotes)
	var upvotes, downvotes int
	query := `
		SELECT
			COUNT(CASE WHEN vote_type = 'upvote' THEN 1 END) as upvotes,
			COUNT(CASE WHEN vote_type = 'downvote' THEN 1 END) as downvotes
		FROM entry_votes
		WHERE celebration_entry_id = $1
	`

	err := r.db.QueryRowContext(ctx, query, entryID).Scan(&upvotes, &downvotes)
	if err != nil {
		return err
	}

	// Update the like_count field
	_, err = r.db.ExecContext(ctx, `
		UPDATE celebration_entries
		SET like_count = $1, updated_at = NOW()
		WHERE id = $2
	`, upvotes-downvotes, entryID)

	return err
}
