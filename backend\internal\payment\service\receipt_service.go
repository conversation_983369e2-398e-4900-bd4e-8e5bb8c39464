package service

import (
	"context"
	"io"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/models"
)

// ReceiptService defines the interface for receipt business logic
type ReceiptService interface {
	// GenerateReceiptForPayment generates a receipt for a payment
	GenerateReceiptForPayment(ctx context.Context, paymentID uint) (*models.Receipt, error)
	
	// GenerateReceiptForSubscription generates a receipt for a subscription payment
	GenerateReceiptForSubscription(ctx context.Context, subscriptionID uint) (*models.Receipt, error)
	
	// GenerateReceiptForRefund generates a receipt for a refund
	GenerateReceiptForRefund(ctx context.Context, refundID uint) (*models.Receipt, error)
	
	// GetReceiptByID retrieves a receipt by its ID
	GetReceiptByID(ctx context.Context, id uint) (*models.Receipt, error)
	
	// GetReceiptByNumber retrieves a receipt by its receipt number
	GetReceiptByNumber(ctx context.Context, receiptNumber string) (*models.Receipt, error)
	
	// GetReceiptsByUserID retrieves all receipts for a user
	GetReceiptsByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.Receipt, int64, error)
	
	// GetReceiptPDF retrieves the PDF for a receipt
	GetReceiptPDF(ctx context.Context, id uint) (io.ReadCloser, string, error)
	
	// EmailReceiptToUser sends a receipt to a user by email
	EmailReceiptToUser(ctx context.Context, id uint, emailAddress string) error
	
	// DeleteReceipt deletes a receipt by its ID
	DeleteReceipt(ctx context.Context, id uint) error
	
	// CreateReceiptTemplate creates a new receipt template
	CreateReceiptTemplate(ctx context.Context, name, templateContent, headerImagePath, footerText, customCSS string, isDefault bool, createdBy uint) (*models.ReceiptTemplate, error)
	
	// GetReceiptTemplateByID retrieves a receipt template by its ID
	GetReceiptTemplateByID(ctx context.Context, id uint) (*models.ReceiptTemplate, error)
	
	// GetDefaultReceiptTemplate retrieves the default receipt template
	GetDefaultReceiptTemplate(ctx context.Context) (*models.ReceiptTemplate, error)
	
	// UpdateReceiptTemplate updates a receipt template
	UpdateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error
	
	// DeleteReceiptTemplate deletes a receipt template by its ID
	DeleteReceiptTemplate(ctx context.Context, id uint) error
	
	// CreateReceiptCustomization creates a new receipt customization
	CreateReceiptCustomization(ctx context.Context, receiptID uint, customization *models.ReceiptCustomization) error
	
	// GetReceiptCustomizationByReceiptID retrieves a receipt customization by its receipt ID
	GetReceiptCustomizationByReceiptID(ctx context.Context, receiptID uint) (*models.ReceiptCustomization, error)
	
	// UpdateReceiptCustomization updates a receipt customization
	UpdateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error
	
	// DeleteReceiptCustomization deletes a receipt customization by its ID
	DeleteReceiptCustomization(ctx context.Context, id uint) error
}