import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  CircularProgress,
  Alert,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ThumbUp as EndorseIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  VerifiedUser as VerifiedIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchUserSkills,
  fetchSkills,
  fetchSkillCategories,
  addUserSkill,
  updateUserSkill,
  deleteUserSkill,
  fetchSkillEndorsements,
  endorseSkill,
} from '../store/slices/skillsSlice';
import { Skill, UserSkill, SkillEndorsement } from '../services/skillsService';

// Helper components
const SkillCard: React.FC<{
  userSkill: UserSkill;
  onEdit: () => void;
  onDelete: () => void;
  onEndorse: () => void;
  isCurrentUser: boolean;
  endorsements: SkillEndorsement[];
}> = ({ userSkill, onEdit, onDelete, onEndorse, isCurrentUser, endorsements }) => {
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return '#4caf50'; // green
      case 'intermediate':
        return '#2196f3'; // blue
      case 'advanced':
        return '#ff9800'; // orange
      case 'expert':
        return '#f44336'; // red
      default:
        return '#9e9e9e'; // grey
    }
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography variant="h6" component="div">
            {userSkill.skill.name}
            {userSkill.isVerified && (
              <Tooltip title="Verified Skill">
                <VerifiedIcon color="primary" fontSize="small" sx={{ ml: 1 }} />
              </Tooltip>
            )}
          </Typography>
          <Chip
            label={userSkill.level.charAt(0).toUpperCase() + userSkill.level.slice(1)}
            size="small"
            sx={{ bgcolor: getLevelColor(userSkill.level), color: 'white' }}
          />
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
          {userSkill.skill.description}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {userSkill.yearsOfExperience} {userSkill.yearsOfExperience === 1 ? 'year' : 'years'} of experience
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <EndorseIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {userSkill.endorsements} {userSkill.endorsements === 1 ? 'endorsement' : 'endorsements'}
          </Typography>
        </Box>
        
        {userSkill.skill.tags && userSkill.skill.tags.length > 0 && (
          <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {userSkill.skill.tags.map((tag, index) => (
              <Chip key={index} label={tag} size="small" variant="outlined" />
            ))}
          </Box>
        )}
      </CardContent>
      <CardActions>
        {isCurrentUser ? (
          <>
            <Button size="small" startIcon={<EditIcon />} onClick={onEdit}>
              Edit
            </Button>
            <Button size="small" color="error" startIcon={<DeleteIcon />} onClick={onDelete}>
              Remove
            </Button>
          </>
        ) : (
          <Button size="small" startIcon={<EndorseIcon />} onClick={onEndorse}>
            Endorse
          </Button>
        )}
        {endorsements.length > 0 && (
          <Button size="small" sx={{ ml: 'auto' }}>
            View Endorsements ({endorsements.length})
          </Button>
        )}
      </CardActions>
    </Card>
  );
};

const AddSkillDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onSave: (userSkill: Partial<UserSkill>) => void;
  skills: Skill[];
  categories: any[];
  loading: boolean;
}> = ({ open, onClose, onSave, skills, categories, loading }) => {
  const [selectedSkill, setSelectedSkill] = useState<number | ''>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('beginner');
  const [yearsOfExperience, setYearsOfExperience] = useState<number>(0);
  const [filteredSkills, setFilteredSkills] = useState<Skill[]>(skills);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    let filtered = skills;
    
    if (selectedCategory) {
      filtered = filtered.filter(skill => skill.category === selectedCategory);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        skill => 
          skill.name.toLowerCase().includes(query) || 
          skill.description.toLowerCase().includes(query) ||
          (skill.tags && skill.tags.some(tag => tag.toLowerCase().includes(query)))
      );
    }
    
    setFilteredSkills(filtered);
  }, [skills, selectedCategory, searchQuery]);

  const handleSave = () => {
    if (selectedSkill === '') return;
    
    const userSkill: Partial<UserSkill> = {
      skillId: selectedSkill as number,
      level: selectedLevel as 'beginner' | 'intermediate' | 'advanced' | 'expert',
      yearsOfExperience,
      isVerified: false,
      endorsements: 0,
    };
    
    onSave(userSkill);
    
    // Reset form
    setSelectedSkill('');
    setSelectedLevel('beginner');
    setYearsOfExperience(0);
    setSelectedCategory('');
    setSearchQuery('');
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Add Skill</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Search Skills"
              variant="outlined"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as string)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Skill</InputLabel>
              <Select
                value={selectedSkill}
                onChange={(e) => setSelectedSkill(e.target.value as number)}
                label="Skill"
              >
                <MenuItem value="">Select a Skill</MenuItem>
                {filteredSkills.map((skill) => (
                  <MenuItem key={skill.id} value={skill.id}>
                    {skill.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Proficiency Level</InputLabel>
              <Select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                label="Proficiency Level"
              >
                <MenuItem value="beginner">Beginner</MenuItem>
                <MenuItem value="intermediate">Intermediate</MenuItem>
                <MenuItem value="advanced">Advanced</MenuItem>
                <MenuItem value="expert">Expert</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Years of Experience"
              type="number"
              value={yearsOfExperience}
              onChange={(e) => setYearsOfExperience(parseInt(e.target.value) || 0)}
              InputProps={{ inputProps: { min: 0 } }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          color="primary"
          disabled={selectedSkill === '' || loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Add Skill'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Main component
const SkillsProfile: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { items: userSkills, loading: userSkillsLoading, error: userSkillsError } = useSelector(
    (state: RootState) => state.skills.userSkills
  );
  const { items: skills, loading: skillsLoading } = useSelector(
    (state: RootState) => state.skills.skills
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.skills.categories
  );
  const { items: endorsements, loading: endorsementsLoading } = useSelector(
    (state: RootState) => state.skills.endorsements
  );
  
  const [addSkillDialogOpen, setAddSkillDialogOpen] = useState(false);
  const [editSkillDialogOpen, setEditSkillDialogOpen] = useState(false);
  const [selectedUserSkill, setSelectedUserSkill] = useState<UserSkill | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [endorseDialogOpen, setEndorseDialogOpen] = useState(false);
  const [endorseComment, setEndorseComment] = useState('');
  
  const targetUserId = userId ? parseInt(userId) : user?.id;
  const isCurrentUser = !userId || (user && user.id === parseInt(userId));
  
  useEffect(() => {
    if (targetUserId) {
      dispatch(fetchUserSkills(targetUserId));
      dispatch(fetchSkills());
      dispatch(fetchSkillCategories());
    }
  }, [dispatch, targetUserId]);
  
  const handleAddSkill = async (userSkill: Partial<UserSkill>) => {
    if (!user) return;
    
    try {
      await dispatch(addUserSkill({ userId: user.id, userSkill })).unwrap();
      setAddSkillDialogOpen(false);
    } catch (error) {
      console.error('Failed to add skill:', error);
    }
  };
  
  const handleEditSkill = (userSkill: UserSkill) => {
    setSelectedUserSkill(userSkill);
    setEditSkillDialogOpen(true);
  };
  
  const handleDeleteSkill = (userSkill: UserSkill) => {
    setSelectedUserSkill(userSkill);
    setDeleteDialogOpen(true);
  };
  
  const handleConfirmDelete = async () => {
    if (!user || !selectedUserSkill) return;
    
    try {
      await dispatch(deleteUserSkill({ userId: user.id, skillId: selectedUserSkill.skillId })).unwrap();
      setDeleteDialogOpen(false);
      setSelectedUserSkill(null);
    } catch (error) {
      console.error('Failed to delete skill:', error);
    }
  };
  
  const handleEndorseSkill = (userSkill: UserSkill) => {
    setSelectedUserSkill(userSkill);
    setEndorseDialogOpen(true);
  };
  
  const handleConfirmEndorse = async () => {
    if (!user || !selectedUserSkill) return;
    
    try {
      await dispatch(
        endorseSkill({
          userSkillId: selectedUserSkill.id,
          endorserId: user.id,
          comment: endorseComment,
        })
      ).unwrap();
      setEndorseDialogOpen(false);
      setSelectedUserSkill(null);
      setEndorseComment('');
    } catch (error) {
      console.error('Failed to endorse skill:', error);
    }
  };
  
  const loading = userSkillsLoading || skillsLoading || categoriesLoading || endorsementsLoading;
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to view skills profiles. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          {isCurrentUser ? 'My Skills Profile' : 'User Skills Profile'}
        </Typography>
        {isCurrentUser && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => setAddSkillDialogOpen(true)}
          >
            Add Skill
          </Button>
        )}
      </Box>
      
      {userSkillsError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {userSkillsError}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : userSkills.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SchoolIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No Skills Added Yet
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            {isCurrentUser
              ? 'Add your skills to showcase your expertise and find matching opportunities.'
              : 'This user has not added any skills to their profile yet.'}
          </Typography>
          {isCurrentUser && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => setAddSkillDialogOpen(true)}
            >
              Add Your First Skill
            </Button>
          )}
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {userSkills.map((userSkill) => (
            <Grid item key={userSkill.id} xs={12} sm={6} md={4}>
              <SkillCard
                userSkill={userSkill}
                onEdit={() => handleEditSkill(userSkill)}
                onDelete={() => handleDeleteSkill(userSkill)}
                onEndorse={() => handleEndorseSkill(userSkill)}
                isCurrentUser={isCurrentUser}
                endorsements={endorsements.filter((e) => e.userSkillId === userSkill.id)}
              />
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Add Skill Dialog */}
      <AddSkillDialog
        open={addSkillDialogOpen}
        onClose={() => setAddSkillDialogOpen(false)}
        onSave={handleAddSkill}
        skills={skills}
        categories={categories}
        loading={loading}
      />
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Remove Skill</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to remove {selectedUserSkill?.skill.name} from your skills profile?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error">
            Remove
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Endorse Dialog */}
      <Dialog open={endorseDialogOpen} onClose={() => setEndorseDialogOpen(false)}>
        <DialogTitle>Endorse Skill</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            You are endorsing {selectedUserSkill?.skill.name} for this user. Would you like to add a comment?
          </DialogContentText>
          <TextField
            fullWidth
            label="Comment (Optional)"
            multiline
            rows={3}
            value={endorseComment}
            onChange={(e) => setEndorseComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEndorseDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmEndorse} color="primary" variant="contained">
            Endorse
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SkillsProfile;
