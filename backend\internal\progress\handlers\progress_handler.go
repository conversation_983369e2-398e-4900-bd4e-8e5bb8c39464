package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/progress/service"
)

// ProgressHandler handles HTTP requests related to progress tracking
type Progress<PERSON>andler struct {
	service service.ProgressService
}

// NewProgressHandler creates a new ProgressHandler
func NewProgressHandler(service service.ProgressService) *ProgressHandler {
	return &ProgressHandler{service: service}
}

// GetUserProgress handles GET /api/progress/user
func (h *ProgressHandler) GetUserProgress(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user progress
	progress, err := h.service.GetUserProgress(uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user progress"})
		return
	}

	c.JSON(http.StatusOK, progress)
}

// GetMilestones handles GET /api/progress/milestones
func (h *ProgressHandler) GetMilestones(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get milestones
	milestones, err := h.service.GetMilestones(uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get milestones"})
		return
	}

	c.JSON(http.StatusOK, milestones)
}

// GetAchievements handles GET /api/progress/achievements
func (h *ProgressHandler) GetAchievements(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get achievements
	achievements, err := h.service.GetAchievements(uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get achievements"})
		return
	}

	c.JSON(http.StatusOK, achievements)
}

// GetHistoricalData handles GET /api/progress/history
func (h *ProgressHandler) GetHistoricalData(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get historical data
	historicalData, err := h.service.GetHistoricalData(uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get historical data"})
		return
	}

	c.JSON(http.StatusOK, historicalData)
}

// GetSkillsData handles GET /api/progress/skills
func (h *ProgressHandler) GetSkillsData(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get skills data
	skillsData, err := h.service.GetSkillsData(uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get skills data"})
		return
	}

	c.JSON(http.StatusOK, skillsData)
}

// UpdateMilestoneProgress handles PUT /api/progress/milestones/:id
func (h *ProgressHandler) UpdateMilestoneProgress(c *gin.Context) {
	// Get milestone ID from URL
	milestoneID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid milestone ID"})
		return
	}

	// Parse request body
	var request struct {
		Progress int `json:"progress" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Update milestone progress
	if err := h.service.UpdateMilestoneProgress(milestoneID, request.Progress); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update milestone progress"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Milestone progress updated"})
}

// UpdateAchievementProgress handles PUT /api/progress/achievements/:id
func (h *ProgressHandler) UpdateAchievementProgress(c *gin.Context) {
	// Get achievement ID from URL
	achievementID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid achievement ID"})
		return
	}

	// Parse request body
	var request struct {
		Progress int `json:"progress" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Update achievement progress
	if err := h.service.UpdateAchievementProgress(achievementID, request.Progress); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update achievement progress"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Achievement progress updated"})
}

// LogActivity handles POST /api/progress/activities
func (h *ProgressHandler) LogActivity(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert userID to int
	uid, ok := userID.(int)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse request body
	var request struct {
		Type     string `json:"type" binding:"required"`
		Name     string `json:"name" binding:"required"`
		Progress int    `json:"progress" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Log activity
	if err := h.service.LogActivity(uid, request.Type, request.Name, request.Progress); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to log activity"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Activity logged"})
}

// RegisterRoutes registers the progress routes
func (h *ProgressHandler) RegisterRoutes(router *gin.RouterGroup) {
	progressGroup := router.Group("/progress")
	{
		progressGroup.GET("/user", h.GetUserProgress)
		progressGroup.GET("/milestones", h.GetMilestones)
		progressGroup.GET("/achievements", h.GetAchievements)
		progressGroup.GET("/history", h.GetHistoricalData)
		progressGroup.GET("/skills", h.GetSkillsData)
		progressGroup.PUT("/milestones/:id", h.UpdateMilestoneProgress)
		progressGroup.PUT("/achievements/:id", h.UpdateAchievementProgress)
		progressGroup.POST("/activities", h.LogActivity)
	}
}
