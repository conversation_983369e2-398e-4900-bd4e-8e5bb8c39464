package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// LiveStreamHandler handles HTTP requests for live stream operations
type LiveStreamHandler struct {
	service service.LiveStreamService
	logger  *logger.Logger
}

// NewLiveStreamHandler creates a new instance of the live stream handler
func NewLiveStreamHandler(service service.LiveStreamService, logger *logger.Logger) *LiveStreamHandler {
	return &LiveStreamHandler{
		service: service,
		logger:  logger,
	}
}

// GetActiveStreams retrieves all active (live) streams
func (h *LiveStreamHandler) GetActiveStreams(c *gin.Context) {
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.Default<PERSON>uer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get active streams
	streams, total, err := h.service.GetActiveStreams(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get active streams: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active streams"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"streams": streams,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetStreamDetails retrieves details of a specific stream
func (h *LiveStreamHandler) GetStreamDetails(c *gin.Context) {
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Get stream details
	stream, err := h.service.GetStreamByID(c.Request.Context(), uint(streamID))
	if err != nil {
		h.logger.Errorf("Failed to get stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get stream details"})
		return
	}
	
	c.JSON(http.StatusOK, stream)
}

// CreateStream creates a new live stream
func (h *LiveStreamHandler) CreateStream(c *gin.Context) {
	// Get user ID from authenticated user
	creatorID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Parse request body
	var request struct {
		Title          string    `json:"title" binding:"required"`
		Description    string    `json:"description"`
		ThumbnailURL   string    `json:"thumbnailUrl"`
		ScheduledStart time.Time `json:"scheduledStart" binding:"required"`
		IsPrivate      bool      `json:"isPrivate"`
		Categories     string    `json:"categories"`
		Tags           string    `json:"tags"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Create stream
	stream, err := h.service.CreateStream(
		c.Request.Context(),
		creatorID.(uint),
		request.Title,
		request.Description,
		request.ThumbnailURL,
		request.ScheduledStart,
		request.IsPrivate,
		request.Categories,
		request.Tags,
	)
	
	if err != nil {
		h.logger.Errorf("Failed to create stream for user %d: %v", creatorID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, stream)
}

// UpdateStream updates a live stream
func (h *LiveStreamHandler) UpdateStream(c *gin.Context) {
	// Get user ID from authenticated user
	creatorID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Get stream to verify ownership
	stream, err := h.service.GetStreamByID(c.Request.Context(), uint(streamID))
	if err != nil {
		h.logger.Errorf("Failed to get stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get stream"})
		return
	}
	
	// Verify ownership
	if stream.CreatorID != creatorID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You are not the creator of this stream"})
		return
	}
	
	// Parse request body
	var request struct {
		Title          string    `json:"title" binding:"required"`
		Description    string    `json:"description"`
		ThumbnailURL   string    `json:"thumbnailUrl"`
		ScheduledStart time.Time `json:"scheduledStart" binding:"required"`
		IsPrivate      bool      `json:"isPrivate"`
		Categories     string    `json:"categories"`
		Tags           string    `json:"tags"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Update stream
	updatedStream, err := h.service.UpdateStream(
		c.Request.Context(),
		uint(streamID),
		request.Title,
		request.Description,
		request.ThumbnailURL,
		request.ScheduledStart,
		request.IsPrivate,
		request.Categories,
		request.Tags,
	)
	
	if err != nil {
		h.logger.Errorf("Failed to update stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, updatedStream)
}

// StartStream marks a stream as live
func (h *LiveStreamHandler) StartStream(c *gin.Context) {
	// Get user ID from authenticated user
	creatorID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Start stream
	stream, err := h.service.StartStream(c.Request.Context(), uint(streamID), creatorID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to start stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, stream)
}

// EndStream marks a stream as ended
func (h *LiveStreamHandler) EndStream(c *gin.Context) {
	// Get user ID from authenticated user
	creatorID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// End stream
	stream, err := h.service.EndStream(c.Request.Context(), uint(streamID), creatorID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to end stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, stream)
}

// GetViewers retrieves viewers of a stream
func (h *LiveStreamHandler) GetViewers(c *gin.Context) {
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get viewers
	viewers, total, err := h.service.GetStreamViewers(c.Request.Context(), uint(streamID), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get viewers for stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get viewers"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"viewers": viewers,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}
