import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepButton,
  Grid,
  Divider,
  Chip,
  Avatar,
  Rating,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  LinearProgress,
  Drawer,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Edit as EditIcon,
  Share as ShareIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  Code as CodeIcon,
  VideoLibrary as VideoIcon,
  Quiz as QuizIcon,
  TouchApp as InteractiveIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchTutorialById,
  fetchTutorialBySlug,
  fetchTutorialSteps,
  markStepCompleted,
  fetchTutorialProgress,
  resetProgress,
  setCurrentStepIndex,
} from '../store/slices/tutorialsSlice';
import { TutorialStep } from '../services/tutorialsService';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const TutorialViewPage: React.FC = () => {
  const { id, slug } = useParams<{ id?: string; slug?: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { data: tutorial, loading: tutorialLoading, error: tutorialError } = useSelector(
    (state: RootState) => state.tutorials.currentTutorial
  );
  const { items: steps, loading: stepsLoading, error: stepsError } = useSelector(
    (state: RootState) => state.tutorials.tutorialSteps
  );
  const { currentStepIndex, completedSteps, isCompleted } = useSelector(
    (state: RootState) => state.tutorials.progress
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [activeStep, setActiveStep] = useState(0);
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [ratingDialogOpen, setRatingDialogOpen] = useState(false);
  const [rating, setRating] = useState<number | null>(null);
  const [comment, setComment] = useState('');
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [quizDialogOpen, setQuizDialogOpen] = useState(false);
  const [currentQuiz, setCurrentQuiz] = useState<any>(null);
  const [quizAnswers, setQuizAnswers] = useState<Record<number, string>>({});
  
  useEffect(() => {
    const fetchTutorial = async () => {
      try {
        if (id) {
          await dispatch(fetchTutorialById(parseInt(id))).unwrap();
        } else if (slug) {
          await dispatch(fetchTutorialBySlug(slug)).unwrap();
        }
      } catch (error) {
        console.error('Failed to fetch tutorial:', error);
      }
    };
    
    fetchTutorial();
  }, [dispatch, id, slug]);
  
  useEffect(() => {
    if (tutorial?.id) {
      dispatch(fetchTutorialSteps(tutorial.id));
      
      if (user) {
        dispatch(fetchTutorialProgress({ userId: user.id, tutorialId: tutorial.id }));
      }
    }
  }, [dispatch, tutorial, user]);
  
  useEffect(() => {
    setActiveStep(currentStepIndex);
  }, [currentStepIndex]);
  
  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };
  
  const handleStepClick = (index: number) => {
    setActiveStep(index);
    dispatch(setCurrentStepIndex(index));
    
    if (isMobile) {
      setDrawerOpen(false);
    }
  };
  
  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      const nextStep = activeStep + 1;
      setActiveStep(nextStep);
      dispatch(setCurrentStepIndex(nextStep));
    }
  };
  
  const handleBack = () => {
    if (activeStep > 0) {
      const prevStep = activeStep - 1;
      setActiveStep(prevStep);
      dispatch(setCurrentStepIndex(prevStep));
    }
  };
  
  const handleCompleteStep = async () => {
    if (!user || !tutorial || !steps[activeStep]) return;
    
    try {
      await dispatch(
        markStepCompleted({ userId: user.id, stepId: steps[activeStep].id })
      ).unwrap();
      
      // If this is the last step, show rating dialog
      if (activeStep === steps.length - 1) {
        setRatingDialogOpen(true);
      } else {
        handleNext();
      }
    } catch (error) {
      console.error('Failed to mark step as completed:', error);
    }
  };
  
  const handleRatingSubmit = async () => {
    if (!rating || !tutorial || !user) return;
    
    try {
      // Call API to submit rating
      // await tutorialsService.rateTutorial(user.id, tutorial.id, rating, comment);
      
      setRatingDialogOpen(false);
      // Show success message or navigate to completion page
    } catch (error) {
      console.error('Failed to submit rating:', error);
    }
  };
  
  const handleShareTutorial = () => {
    setShareDialogOpen(true);
  };
  
  const handleCopyLink = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url);
    // Show success message
    setShareDialogOpen(false);
  };
  
  const handleResetProgress = async () => {
    if (!user || !tutorial) return;
    
    try {
      await dispatch(resetProgress()).unwrap();
      setActiveStep(0);
    } catch (error) {
      console.error('Failed to reset progress:', error);
    }
  };
  
  const handleStartQuiz = (quiz: any) => {
    setCurrentQuiz(quiz);
    setQuizAnswers({});
    setQuizDialogOpen(true);
  };
  
  const handleQuizAnswerChange = (questionId: number, answer: string) => {
    setQuizAnswers({
      ...quizAnswers,
      [questionId]: answer,
    });
  };
  
  const handleSubmitQuiz = async () => {
    // Process quiz answers and submit
    setQuizDialogOpen(false);
    handleCompleteStep();
  };
  
  const getStepIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <TextIcon />;
      case 'image':
        return <ImageIcon />;
      case 'video':
        return <VideoIcon />;
      case 'code':
        return <CodeIcon />;
      case 'quiz':
        return <QuizIcon />;
      case 'interactive':
        return <InteractiveIcon />;
      default:
        return <TextIcon />;
    }
  };
  
  const renderStepContent = (step: TutorialStep) => {
    switch (step.type) {
      case 'text':
        return <ReactMarkdown>{step.content}</ReactMarkdown>;
      case 'image':
        return (
          <Box sx={{ textAlign: 'center', my: 2 }}>
            <img
              src={step.mediaURL}
              alt={step.title}
              style={{ maxWidth: '100%', maxHeight: '400px' }}
            />
            <Typography variant="caption" display="block">
              {step.content}
            </Typography>
          </Box>
        );
      case 'video':
        return (
          <Box sx={{ my: 2 }}>
            <Box sx={{ position: 'relative', paddingTop: '56.25%', mb: 2 }}>
              <iframe
                src={step.mediaURL}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none',
                }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title={step.title}
              />
            </Box>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      case 'code':
        return (
          <Box sx={{ my: 2 }}>
            <SyntaxHighlighter language={step.language || 'javascript'} style={materialDark}>
              {step.codeSnippet || ''}
            </SyntaxHighlighter>
            <ReactMarkdown>{step.content}</ReactMarkdown>
          </Box>
        );
      case 'quiz':
        return (
          <Box sx={{ my: 2 }}>
            <ReactMarkdown>{step.content}</ReactMarkdown>
            <Button
              variant="contained"
              color="primary"
              onClick={() => handleStartQuiz(step.quizData)}
              sx={{ mt: 2 }}
            >
              Start Quiz
            </Button>
          </Box>
        );
      case 'interactive':
        return (
          <Box sx={{ my: 2 }}>
            <ReactMarkdown>{step.content}</ReactMarkdown>
            <Alert severity="info" sx={{ mt: 2 }}>
              Interactive content is available in this step.
            </Alert>
          </Box>
        );
      default:
        return <ReactMarkdown>{step.content}</ReactMarkdown>;
    }
  };
  
  if (tutorialLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (tutorialError || !tutorial) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {tutorialError || 'Tutorial not found. Please try again later.'}
        </Alert>
      </Container>
    );
  }
  
  const sortedSteps = [...steps].sort((a, b) => a.order - b.order);
  const currentStep = sortedSteps[activeStep];
  const progress = steps.length > 0 ? (completedSteps.length / steps.length) * 100 : 0;
  
  const drawer = (
    <Box sx={{ width: 280, overflow: 'auto' }}>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" noWrap>
          Tutorial Steps
        </Typography>
        {isMobile && (
          <IconButton onClick={handleDrawerToggle}>
            <ChevronLeftIcon />
          </IconButton>
        )}
      </Box>
      
      <Divider />
      
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Your Progress
        </Typography>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{ mb: 1 }}
        />
        <Typography variant="body2" color="text.secondary">
          {completedSteps.length}/{steps.length} steps completed
        </Typography>
      </Box>
      
      <Divider />
      
      <List>
        {sortedSteps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.id);
          const isActive = index === activeStep;
          
          return (
            <ListItem
              button
              key={step.id}
              onClick={() => handleStepClick(index)}
              selected={isActive}
              sx={{
                bgcolor: isActive ? 'primary.light' : 'transparent',
                color: isActive ? 'primary.contrastText' : 'inherit',
              }}
            >
              <ListItemIcon>
                {isCompleted ? (
                  <CheckCircleIcon color="success" />
                ) : (
                  <RadioButtonUncheckedIcon />
                )}
              </ListItemIcon>
              <ListItemText
                primary={`${index + 1}. ${step.title}`}
                primaryTypographyProps={{
                  variant: 'body2',
                  color: isActive ? 'inherit' : isCompleted ? 'text.primary' : 'text.secondary',
                }}
              />
              <ListItemIcon>
                {getStepIcon(step.type)}
              </ListItemIcon>
            </ListItem>
          );
        })}
      </List>
    </Box>
  );
  
  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Sidebar */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            '& .MuiDrawer-paper': { width: 280 },
          }}
        >
          {drawer}
        </Drawer>
      ) : (
        <Drawer
          variant="persistent"
          open={drawerOpen}
          sx={{
            width: drawerOpen ? 280 : 0,
            flexShrink: 0,
            '& .MuiDrawer-paper': { width: 280 },
          }}
        >
          {drawer}
        </Drawer>
      )}
      
      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          overflow: 'auto',
          transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          marginLeft: isMobile ? 0 : drawerOpen ? '280px' : 0,
        }}
      >
        {/* Tutorial header */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h4" component="h1" gutterBottom>
                {tutorial.title}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ mr: 1, width: 24, height: 24 }}>
                    <PersonIcon fontSize="small" />
                  </Avatar>
                  <Typography variant="body2">
                    {tutorial.authorName}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                  <Typography variant="body2">
                    {tutorial.estimatedTime} min
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                  <Typography variant="body2">
                    {tutorial.difficulty}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Rating value={tutorial.rating} precision={0.5} readOnly size="small" />
                  <Typography variant="body2" sx={{ ml: 0.5 }}>
                    ({tutorial.rating.toFixed(1)})
                  </Typography>
                </Box>
              </Box>
            </Box>
            
            <Box>
              {user && tutorial.authorId === user.id && (
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => navigate(`/tutorials/edit/${tutorial.id}`)}
                  sx={{ mr: 1 }}
                >
                  Edit
                </Button>
              )}
              
              <Button
                variant="outlined"
                startIcon={<ShareIcon />}
                onClick={handleShareTutorial}
              >
                Share
              </Button>
            </Box>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="body1" paragraph>
            {tutorial.description}
          </Typography>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {tutorial.tags.map((tag) => (
              <Chip key={tag} label={tag} variant="outlined" size="small" />
            ))}
          </Box>
        </Paper>
        
        {/* Step content */}
        {stepsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : stepsError ? (
          <Alert severity="error" sx={{ mx: 3, mb: 3 }}>
            {stepsError}
          </Alert>
        ) : steps.length === 0 ? (
          <Alert severity="info" sx={{ mx: 3, mb: 3 }}>
            This tutorial doesn't have any steps yet.
          </Alert>
        ) : (
          <Box sx={{ px: 3, pb: 3 }}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                Step {activeStep + 1}: {currentStep?.title}
              </Typography>
              
              <Divider sx={{ mb: 3 }} />
              
              {currentStep && renderStepContent(currentStep)}
            </Paper>
            
            {/* Navigation buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={activeStep === 0}
                startIcon={<ChevronLeftIcon />}
              >
                Previous
              </Button>
              
              <Box>
                {user && (
                  <Button
                    variant="outlined"
                    onClick={handleResetProgress}
                    sx={{ mr: 1 }}
                  >
                    Reset Progress
                  </Button>
                )}
                
                {activeStep < steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleCompleteStep}
                    endIcon={<ChevronRightIcon />}
                    disabled={!user}
                  >
                    {completedSteps.includes(currentStep?.id) ? 'Next' : 'Complete & Continue'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="success"
                    onClick={handleCompleteStep}
                    disabled={!user}
                  >
                    {completedSteps.includes(currentStep?.id) ? 'Completed' : 'Complete Tutorial'}
                  </Button>
                )}
              </Box>
            </Box>
          </Box>
        )}
      </Box>
      
      {/* Rating Dialog */}
      <Dialog open={ratingDialogOpen} onClose={() => setRatingDialogOpen(false)}>
        <DialogTitle>Rate this Tutorial</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Congratulations on completing the tutorial! Please rate your experience.
          </DialogContentText>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 2 }}>
            <Rating
              value={rating}
              onChange={(event, newValue) => {
                setRating(newValue);
              }}
              size="large"
            />
          </Box>
          
          <TextField
            autoFocus
            margin="dense"
            label="Comments (optional)"
            fullWidth
            multiline
            rows={4}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRatingDialogOpen(false)}>Skip</Button>
          <Button onClick={handleRatingSubmit} variant="contained" disabled={!rating}>
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)}>
        <DialogTitle>Share Tutorial</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Share this tutorial with others:
          </DialogContentText>
          
          <TextField
            margin="dense"
            fullWidth
            value={window.location.href}
            InputProps={{
              readOnly: true,
            }}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Button
              variant="contained"
              onClick={handleCopyLink}
            >
              Copy Link
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Quiz Dialog */}
      <Dialog open={quizDialogOpen} onClose={() => setQuizDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Quiz: {currentStep?.title}</DialogTitle>
        <DialogContent>
          {currentQuiz && currentQuiz.questions ? (
            <Box>
              {currentQuiz.questions.map((question: any, index: number) => (
                <Box key={question.id} sx={{ mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    {index + 1}. {question.question}
                  </Typography>
                  
                  <Grid container spacing={2}>
                    {question.options.map((option: string) => (
                      <Grid item xs={12} key={option}>
                        <Button
                          variant={quizAnswers[question.id] === option ? 'contained' : 'outlined'}
                          fullWidth
                          onClick={() => handleQuizAnswerChange(question.id, option)}
                          sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                        >
                          {option}
                        </Button>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              ))}
            </Box>
          ) : (
            <Typography>No quiz questions available.</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQuizDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSubmitQuiz}
            variant="contained"
            disabled={
              !currentQuiz ||
              !currentQuiz.questions ||
              Object.keys(quizAnswers).length !== currentQuiz.questions.length
            }
          >
            Submit Answers
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TutorialViewPage;
