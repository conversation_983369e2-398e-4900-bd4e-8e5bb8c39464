#!/usr/bin/env python3

import http.server
import socketserver
import os
import socket
import time
from datetime import datetime, timedelta

# Configuration
PORT = 3000
BACKUP_DIR = "./backups"
TIMEOUT = 30  # minutes

# Get the file details
def get_backup_files():
    files = []
    for filename in os.listdir(BACKUP_DIR):
        if filename.startswith("combined_backups_"):
            filepath = os.path.join(BACKUP_DIR, filename)
            size = os.path.getsize(filepath)
            human_size = f"{size / (1024*1024):.1f} MB"
            files.append({
                "name": filename,
                "path": filepath,
                "size": size,
                "human_size": human_size
            })
    return sorted(files, key=lambda x: x["name"], reverse=True)

# Create custom handler
class BackupServerHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == "/":
            # Get backup files
            backup_files = get_backup_files()
            if not backup_files:
                self.send_response(404)
                self.send_header("Content-type", "text/html")
                self.end_headers()
                self.wfile.write(b"No backup files found")
                return
            
            # Send HTML page
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            # Create HTML content
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Great Nigeria Project Backups</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                    h1 {{ color: #006400; }}
                    .container {{ max-width: 800px; margin: 0 auto; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                    th {{ background-color: #f2f2f2; }}
                    .download-btn {{ 
                        background-color: #006400; 
                        color: white; 
                        padding: 8px 16px; 
                        text-decoration: none; 
                        border-radius: 4px; 
                    }}
                    .timer {{ margin-top: 20px; padding: 10px; background-color: #f8f8f8; border-radius: 4px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Great Nigeria Project Backups</h1>
                    <p>These backup files contain a complete snapshot of the project's database and code.</p>
                    
                    <table>
                        <tr>
                            <th>Filename</th>
                            <th>Size</th>
                            <th>Action</th>
                        </tr>
            """
            
            # Add one row for each backup file
            for file in backup_files:
                html += f"""
                        <tr>
                            <td>{file['name']}</td>
                            <td>{file['human_size']}</td>
                            <td><a href="/{file['name']}" class="download-btn">Download</a></td>
                        </tr>
                """
            
            # Add expiration message
            expiry_time = datetime.now() + timedelta(minutes=TIMEOUT)
            html += f"""
                    </table>
                    
                    <div class="timer">
                        <p><strong>Note:</strong> This backup server will automatically shut down after {TIMEOUT} minutes.</p>
                        <p>Server will stop at: {expiry_time.strftime('%H:%M:%S')}</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
            return
        
        # Handle file download request
        if self.path.startswith("/combined_backups_"):
            filename = self.path[1:]  # Remove leading slash
            filepath = os.path.join(BACKUP_DIR, filename)
            
            if os.path.exists(filepath):
                self.send_response(200)
                self.send_header("Content-type", "application/octet-stream")
                self.send_header("Content-Disposition", f"attachment; filename={filename}")
                self.send_header("Content-Length", str(os.path.getsize(filepath)))
                self.end_headers()
                
                with open(filepath, "rb") as file:
                    self.wfile.write(file.read())
                return
        
        # Default handler for other paths
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

def get_ip_address():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    # Connect to a public DNS server to get our IP
    try:
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

# Main function
def main():
    # Change directory to serve from
    os.chdir(BACKUP_DIR)
    
    # Create and start the server
    handler = BackupServerHandler
    with socketserver.TCPServer(("0.0.0.0", PORT), handler) as httpd:
        ip_address = get_ip_address()
        print(f"Backup server started at:")
        print(f"http://{ip_address}:{PORT}")
        print(f"Server will run for {TIMEOUT} minutes")
        
        # Run the server indefinitely (timeout managed externally)
        print("Press Ctrl+C to stop the server")
        httpd.serve_forever()

if __name__ == "__main__":
    main()