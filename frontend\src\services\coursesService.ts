import api from '../api/client';

// Course types
export interface Course {
  id: number;
  title: string;
  slug: string;
  description: string;
  instructorID: number;
  instructorName: string;
  thumbnailURL: string;
  price: number;
  isFree: boolean;
  isPublic: boolean;
  isFeatured: boolean;
  status: string;
  duration: number;
  level: string;
  prerequisites: string;
  objectives: string;
  tags: string[];
  rating: number;
  enrollmentCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Module {
  id: number;
  courseID: number;
  title: string;
  description: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Lesson {
  id: number;
  moduleID: number;
  title: string;
  description: string;
  content: string;
  contentType: string;
  videoURL?: string;
  audioURL?: string;
  documentURL?: string;
  duration: number;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Quiz {
  id: number;
  lessonID: number;
  title: string;
  description: string;
  passingScore: number;
  timeLimit: number;
  createdAt: string;
  updatedAt: string;
}

export interface Question {
  id: number;
  quizID: number;
  question: string;
  questionType: string;
  options: string[];
  correctAnswer: string;
  points: number;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Assignment {
  id: number;
  lessonID: number;
  title: string;
  description: string;
  instructions: string;
  dueDate: string;
  maxScore: number;
  createdAt: string;
  updatedAt: string;
}

export interface Enrollment {
  id: number;
  userID: number;
  courseID: number;
  enrollmentDate: string;
  isCompleted: boolean;
  completionDate?: string;
  progress: number;
  createdAt: string;
  updatedAt: string;
}

export interface Progress {
  id: number;
  userID: number;
  lessonID: number;
  completed: boolean;
  completionDate?: string;
  lastAccessedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuizAttempt {
  id: number;
  userID: number;
  quizID: number;
  score: number;
  passed: boolean;
  startedAt: string;
  completedAt?: string;
  answers: {
    questionID: number;
    answer: string;
    isCorrect: boolean;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface AssignmentSubmission {
  id: number;
  userID: number;
  assignmentID: number;
  content: string;
  fileURL?: string;
  score?: number;
  feedback?: string;
  submittedAt: string;
  gradedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Review {
  id: number;
  userID: number;
  courseID: number;
  rating: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
}

export interface Certificate {
  id: number;
  userID: number;
  courseID: number;
  certificateURL: string;
  issuedAt: string;
  createdAt: string;
  updatedAt: string;
}

// API functions
const coursesService = {
  // Course methods
  getCourses: async (page = 1, pageSize = 10, filters = {}) => {
    const response = await api.get('/courses', { params: { page, pageSize, ...filters } });
    return response.data;
  },
  
  getCourseByID: async (id: number) => {
    const response = await api.get(`/courses/${id}`);
    return response.data;
  },
  
  getCourseBySlug: async (slug: string) => {
    const response = await api.get(`/courses/slug/${slug}`);
    return response.data;
  },
  
  createCourse: async (course: Partial<Course>) => {
    const response = await api.post('/courses', course);
    return response.data;
  },
  
  updateCourse: async (id: number, course: Partial<Course>) => {
    const response = await api.put(`/courses/${id}`, course);
    return response.data;
  },
  
  deleteCourse: async (id: number) => {
    const response = await api.delete(`/courses/${id}`);
    return response.data;
  },
  
  // Module methods
  getModulesByCourseID: async (courseID: number) => {
    const response = await api.get(`/courses/${courseID}/modules`);
    return response.data;
  },
  
  getModuleByID: async (id: number) => {
    const response = await api.get(`/courses/modules/${id}`);
    return response.data;
  },
  
  createModule: async (courseID: number, module: Partial<Module>) => {
    const response = await api.post(`/courses/${courseID}/modules`, module);
    return response.data;
  },
  
  updateModule: async (id: number, module: Partial<Module>) => {
    const response = await api.put(`/courses/modules/${id}`, module);
    return response.data;
  },
  
  deleteModule: async (id: number) => {
    const response = await api.delete(`/courses/modules/${id}`);
    return response.data;
  },
  
  // Lesson methods
  getLessonsByModuleID: async (moduleID: number) => {
    const response = await api.get(`/courses/modules/${moduleID}/lessons`);
    return response.data;
  },
  
  getLessonByID: async (id: number) => {
    const response = await api.get(`/courses/lessons/${id}`);
    return response.data;
  },
  
  createLesson: async (moduleID: number, lesson: Partial<Lesson>) => {
    const response = await api.post(`/courses/modules/${moduleID}/lessons`, lesson);
    return response.data;
  },
  
  updateLesson: async (id: number, lesson: Partial<Lesson>) => {
    const response = await api.put(`/courses/lessons/${id}`, lesson);
    return response.data;
  },
  
  deleteLesson: async (id: number) => {
    const response = await api.delete(`/courses/lessons/${id}`);
    return response.data;
  },
  
  // Quiz methods
  getQuizByLessonID: async (lessonID: number) => {
    const response = await api.get(`/courses/lessons/${lessonID}/quiz`);
    return response.data;
  },
  
  getQuizByID: async (id: number) => {
    const response = await api.get(`/courses/quizzes/${id}`);
    return response.data;
  },
  
  createQuiz: async (lessonID: number, quiz: Partial<Quiz>) => {
    const response = await api.post(`/courses/lessons/${lessonID}/quiz`, quiz);
    return response.data;
  },
  
  updateQuiz: async (id: number, quiz: Partial<Quiz>) => {
    const response = await api.put(`/courses/quizzes/${id}`, quiz);
    return response.data;
  },
  
  deleteQuiz: async (id: number) => {
    const response = await api.delete(`/courses/quizzes/${id}`);
    return response.data;
  },
  
  // Question methods
  getQuestionsByQuizID: async (quizID: number) => {
    const response = await api.get(`/courses/quizzes/${quizID}/questions`);
    return response.data;
  },
  
  getQuestionByID: async (id: number) => {
    const response = await api.get(`/courses/questions/${id}`);
    return response.data;
  },
  
  createQuestion: async (quizID: number, question: Partial<Question>) => {
    const response = await api.post(`/courses/quizzes/${quizID}/questions`, question);
    return response.data;
  },
  
  updateQuestion: async (id: number, question: Partial<Question>) => {
    const response = await api.put(`/courses/questions/${id}`, question);
    return response.data;
  },
  
  deleteQuestion: async (id: number) => {
    const response = await api.delete(`/courses/questions/${id}`);
    return response.data;
  },
  
  // Assignment methods
  getAssignmentByLessonID: async (lessonID: number) => {
    const response = await api.get(`/courses/lessons/${lessonID}/assignment`);
    return response.data;
  },
  
  getAssignmentByID: async (id: number) => {
    const response = await api.get(`/courses/assignments/${id}`);
    return response.data;
  },
  
  createAssignment: async (lessonID: number, assignment: Partial<Assignment>) => {
    const response = await api.post(`/courses/lessons/${lessonID}/assignment`, assignment);
    return response.data;
  },
  
  updateAssignment: async (id: number, assignment: Partial<Assignment>) => {
    const response = await api.put(`/courses/assignments/${id}`, assignment);
    return response.data;
  },
  
  deleteAssignment: async (id: number) => {
    const response = await api.delete(`/courses/assignments/${id}`);
    return response.data;
  },
  
  // Enrollment methods
  getEnrollmentsByUserID: async (userID: number) => {
    const response = await api.get(`/courses/enrollments/user/${userID}`);
    return response.data;
  },
  
  getEnrollmentsByCourseID: async (courseID: number) => {
    const response = await api.get(`/courses/enrollments/course/${courseID}`);
    return response.data;
  },
  
  getEnrollment: async (userID: number, courseID: number) => {
    const response = await api.get(`/courses/enrollments/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  createEnrollment: async (enrollment: Partial<Enrollment>) => {
    const response = await api.post('/courses/enrollments', enrollment);
    return response.data;
  },
  
  updateEnrollment: async (id: number, enrollment: Partial<Enrollment>) => {
    const response = await api.put(`/courses/enrollments/${id}`, enrollment);
    return response.data;
  },
  
  deleteEnrollment: async (id: number) => {
    const response = await api.delete(`/courses/enrollments/${id}`);
    return response.data;
  },
  
  // Progress methods
  getProgressByUserAndCourse: async (userID: number, courseID: number) => {
    const response = await api.get(`/courses/progress/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  getProgressByUserAndLesson: async (userID: number, lessonID: number) => {
    const response = await api.get(`/courses/progress/user/${userID}/lesson/${lessonID}`);
    return response.data;
  },
  
  createProgress: async (progress: Partial<Progress>) => {
    const response = await api.post('/courses/progress', progress);
    return response.data;
  },
  
  updateProgress: async (id: number, progress: Partial<Progress>) => {
    const response = await api.put(`/courses/progress/${id}`, progress);
    return response.data;
  },
  
  // Quiz attempt methods
  getQuizAttemptsByUserAndQuiz: async (userID: number, quizID: number) => {
    const response = await api.get(`/courses/quiz-attempts/user/${userID}/quiz/${quizID}`);
    return response.data;
  },
  
  getQuizAttemptByID: async (id: number) => {
    const response = await api.get(`/courses/quiz-attempts/${id}`);
    return response.data;
  },
  
  createQuizAttempt: async (attempt: Partial<QuizAttempt>) => {
    const response = await api.post('/courses/quiz-attempts', attempt);
    return response.data;
  },
  
  updateQuizAttempt: async (id: number, attempt: Partial<QuizAttempt>) => {
    const response = await api.put(`/courses/quiz-attempts/${id}`, attempt);
    return response.data;
  },
  
  // Assignment submission methods
  getSubmissionsByUserAndAssignment: async (userID: number, assignmentID: number) => {
    const response = await api.get(`/courses/submissions/user/${userID}/assignment/${assignmentID}`);
    return response.data;
  },
  
  getSubmissionByID: async (id: number) => {
    const response = await api.get(`/courses/submissions/${id}`);
    return response.data;
  },
  
  createSubmission: async (submission: Partial<AssignmentSubmission>) => {
    const response = await api.post('/courses/submissions', submission);
    return response.data;
  },
  
  updateSubmission: async (id: number, submission: Partial<AssignmentSubmission>) => {
    const response = await api.put(`/courses/submissions/${id}`, submission);
    return response.data;
  },
  
  // Review methods
  getReviewsByCourseID: async (courseID: number) => {
    const response = await api.get(`/courses/reviews/course/${courseID}`);
    return response.data;
  },
  
  getReviewByUserAndCourse: async (userID: number, courseID: number) => {
    const response = await api.get(`/courses/reviews/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  createReview: async (review: Partial<Review>) => {
    const response = await api.post('/courses/reviews', review);
    return response.data;
  },
  
  updateReview: async (id: number, review: Partial<Review>) => {
    const response = await api.put(`/courses/reviews/${id}`, review);
    return response.data;
  },
  
  deleteReview: async (id: number) => {
    const response = await api.delete(`/courses/reviews/${id}`);
    return response.data;
  },
  
  // Certificate methods
  getCertificatesByUserID: async (userID: number) => {
    const response = await api.get(`/courses/certificates/user/${userID}`);
    return response.data;
  },
  
  getCertificateByUserAndCourse: async (userID: number, courseID: number) => {
    const response = await api.get(`/courses/certificates/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  createCertificate: async (certificate: Partial<Certificate>) => {
    const response = await api.post('/courses/certificates', certificate);
    return response.data;
  },
  
  // Course completion methods
  checkCourseCompletion: async (userID: number, courseID: number) => {
    const response = await api.get(`/courses/completion/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  markCourseAsCompleted: async (userID: number, courseID: number) => {
    const response = await api.post(`/courses/completion/user/${userID}/course/${courseID}`);
    return response.data;
  },
  
  generateCourseCompletionCertificate: async (userID: number, courseID: number) => {
    const response = await api.post(`/courses/certificates/generate/user/${userID}/course/${courseID}`);
    return response.data;
  },
};

export default coursesService;
