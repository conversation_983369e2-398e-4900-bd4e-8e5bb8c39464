import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import impactService, {
  ImpactMetric,
  ImpactDataPoint,
  ImpactReport,
  ImpactCategory,
  ImpactDashboardConfig,
} from '../../services/impactService';

interface ImpactState {
  metrics: {
    items: ImpactMetric[];
    loading: boolean;
    error: string | null;
  };
  userMetrics: {
    items: ImpactMetric[];
    loading: boolean;
    error: string | null;
  };
  currentMetric: {
    data: ImpactMetric | null;
    dataPoints: ImpactDataPoint[];
    loading: boolean;
    error: string | null;
  };
  reports: {
    items: ImpactReport[];
    loading: boolean;
    error: string | null;
  };
  userReports: {
    items: ImpactReport[];
    loading: boolean;
    error: string | null;
  };
  currentReport: {
    data: ImpactReport | null;
    loading: boolean;
    error: string | null;
  };
  categories: {
    items: ImpactCategory[];
    loading: boolean;
    error: string | null;
  };
  dashboardConfig: {
    data: ImpactDashboardConfig | null;
    loading: boolean;
    error: string | null;
  };
  analytics: {
    metricAnalytics: any;
    categoryAnalytics: any;
    userAnalytics: any;
    loading: boolean;
    error: string | null;
  };
}

const initialState: ImpactState = {
  metrics: {
    items: [],
    loading: false,
    error: null,
  },
  userMetrics: {
    items: [],
    loading: false,
    error: null,
  },
  currentMetric: {
    data: null,
    dataPoints: [],
    loading: false,
    error: null,
  },
  reports: {
    items: [],
    loading: false,
    error: null,
  },
  userReports: {
    items: [],
    loading: false,
    error: null,
  },
  currentReport: {
    data: null,
    loading: false,
    error: null,
  },
  categories: {
    items: [],
    loading: false,
    error: null,
  },
  dashboardConfig: {
    data: null,
    loading: false,
    error: null,
  },
  analytics: {
    metricAnalytics: null,
    categoryAnalytics: null,
    userAnalytics: null,
    loading: false,
    error: null,
  },
};

// Metrics thunks
export const fetchMetrics = createAsyncThunk(
  'impact/fetchMetrics',
  async (params: { category?: string; status?: string; isPublic?: boolean } = {}, { rejectWithValue }) => {
    try {
      return await impactService.getMetrics(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch metrics');
    }
  }
);

export const fetchMetricById = createAsyncThunk(
  'impact/fetchMetricById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await impactService.getMetricById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch metric');
    }
  }
);

export const fetchUserMetrics = createAsyncThunk(
  'impact/fetchUserMetrics',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await impactService.getUserMetrics(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user metrics');
    }
  }
);

export const createMetric = createAsyncThunk(
  'impact/createMetric',
  async (metric: Partial<ImpactMetric>, { rejectWithValue }) => {
    try {
      return await impactService.createMetric(metric);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create metric');
    }
  }
);

export const updateMetric = createAsyncThunk(
  'impact/updateMetric',
  async ({ id, metric }: { id: number; metric: Partial<ImpactMetric> }, { rejectWithValue }) => {
    try {
      return await impactService.updateMetric(id, metric);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update metric');
    }
  }
);

export const deleteMetric = createAsyncThunk(
  'impact/deleteMetric',
  async (id: number, { rejectWithValue }) => {
    try {
      await impactService.deleteMetric(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete metric');
    }
  }
);

// Data points thunks
export const fetchDataPoints = createAsyncThunk(
  'impact/fetchDataPoints',
  async (metricId: number, { rejectWithValue }) => {
    try {
      return await impactService.getDataPoints(metricId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch data points');
    }
  }
);

export const createDataPoint = createAsyncThunk(
  'impact/createDataPoint',
  async (
    { metricId, dataPoint }: { metricId: number; dataPoint: Partial<ImpactDataPoint> },
    { rejectWithValue }
  ) => {
    try {
      return await impactService.createDataPoint(metricId, dataPoint);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create data point');
    }
  }
);

export const updateDataPoint = createAsyncThunk(
  'impact/updateDataPoint',
  async (
    { id, dataPoint }: { id: number; dataPoint: Partial<ImpactDataPoint> },
    { rejectWithValue }
  ) => {
    try {
      return await impactService.updateDataPoint(id, dataPoint);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update data point');
    }
  }
);

export const deleteDataPoint = createAsyncThunk(
  'impact/deleteDataPoint',
  async (id: number, { rejectWithValue }) => {
    try {
      await impactService.deleteDataPoint(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete data point');
    }
  }
);

// Reports thunks
export const fetchReports = createAsyncThunk(
  'impact/fetchReports',
  async (params: { status?: string; isPublic?: boolean } = {}, { rejectWithValue }) => {
    try {
      return await impactService.getReports(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch reports');
    }
  }
);

export const fetchReportById = createAsyncThunk(
  'impact/fetchReportById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await impactService.getReportById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch report');
    }
  }
);

export const fetchUserReports = createAsyncThunk(
  'impact/fetchUserReports',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await impactService.getUserReports(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user reports');
    }
  }
);

export const createReport = createAsyncThunk(
  'impact/createReport',
  async (report: Partial<ImpactReport>, { rejectWithValue }) => {
    try {
      return await impactService.createReport(report);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create report');
    }
  }
);

export const updateReport = createAsyncThunk(
  'impact/updateReport',
  async ({ id, report }: { id: number; report: Partial<ImpactReport> }, { rejectWithValue }) => {
    try {
      return await impactService.updateReport(id, report);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update report');
    }
  }
);

export const deleteReport = createAsyncThunk(
  'impact/deleteReport',
  async (id: number, { rejectWithValue }) => {
    try {
      await impactService.deleteReport(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete report');
    }
  }
);

export const publishReport = createAsyncThunk(
  'impact/publishReport',
  async (id: number, { rejectWithValue }) => {
    try {
      return await impactService.publishReport(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to publish report');
    }
  }
);

export const archiveReport = createAsyncThunk(
  'impact/archiveReport',
  async (id: number, { rejectWithValue }) => {
    try {
      return await impactService.archiveReport(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to archive report');
    }
  }
);

// Categories thunks
export const fetchCategories = createAsyncThunk(
  'impact/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await impactService.getCategories();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch categories');
    }
  }
);

export const createCategory = createAsyncThunk(
  'impact/createCategory',
  async (category: Partial<ImpactCategory>, { rejectWithValue }) => {
    try {
      return await impactService.createCategory(category);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create category');
    }
  }
);

export const updateCategory = createAsyncThunk(
  'impact/updateCategory',
  async (
    { id, category }: { id: number; category: Partial<ImpactCategory> },
    { rejectWithValue }
  ) => {
    try {
      return await impactService.updateCategory(id, category);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update category');
    }
  }
);

export const deleteCategory = createAsyncThunk(
  'impact/deleteCategory',
  async (id: number, { rejectWithValue }) => {
    try {
      await impactService.deleteCategory(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete category');
    }
  }
);

// Dashboard config thunks
export const fetchDashboardConfig = createAsyncThunk(
  'impact/fetchDashboardConfig',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await impactService.getDashboardConfig(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard config');
    }
  }
);

export const updateDashboardConfig = createAsyncThunk(
  'impact/updateDashboardConfig',
  async (
    { userId, config }: { userId: number; config: Partial<ImpactDashboardConfig> },
    { rejectWithValue }
  ) => {
    try {
      return await impactService.updateDashboardConfig(userId, config);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update dashboard config');
    }
  }
);

// Analytics thunks
export const fetchMetricAnalytics = createAsyncThunk(
  'impact/fetchMetricAnalytics',
  async ({ metricId, timeRange }: { metricId: number; timeRange: string }, { rejectWithValue }) => {
    try {
      return await impactService.getMetricAnalytics(metricId, timeRange);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch metric analytics');
    }
  }
);

export const fetchCategoryAnalytics = createAsyncThunk(
  'impact/fetchCategoryAnalytics',
  async (
    { categoryId, timeRange }: { categoryId: number; timeRange: string },
    { rejectWithValue }
  ) => {
    try {
      return await impactService.getCategoryAnalytics(categoryId, timeRange);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch category analytics');
    }
  }
);

export const fetchUserAnalytics = createAsyncThunk(
  'impact/fetchUserAnalytics',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await impactService.getUserAnalytics(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user analytics');
    }
  }
);

const impactSlice = createSlice({
  name: 'impact',
  initialState,
  reducers: {
    resetCurrentMetric: (state) => {
      state.currentMetric.data = null;
      state.currentMetric.dataPoints = [];
      state.currentMetric.error = null;
    },
    resetCurrentReport: (state) => {
      state.currentReport.data = null;
      state.currentReport.error = null;
    },
    resetAnalytics: (state) => {
      state.analytics.metricAnalytics = null;
      state.analytics.categoryAnalytics = null;
      state.analytics.userAnalytics = null;
      state.analytics.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch metrics
    builder.addCase(fetchMetrics.pending, (state) => {
      state.metrics.loading = true;
      state.metrics.error = null;
    });
    builder.addCase(fetchMetrics.fulfilled, (state, action) => {
      state.metrics.loading = false;
      state.metrics.items = action.payload;
    });
    builder.addCase(fetchMetrics.rejected, (state, action) => {
      state.metrics.loading = false;
      state.metrics.error = action.payload as string;
    });

    // Fetch metric by ID
    builder.addCase(fetchMetricById.pending, (state) => {
      state.currentMetric.loading = true;
      state.currentMetric.error = null;
    });
    builder.addCase(fetchMetricById.fulfilled, (state, action) => {
      state.currentMetric.loading = false;
      state.currentMetric.data = action.payload;
    });
    builder.addCase(fetchMetricById.rejected, (state, action) => {
      state.currentMetric.loading = false;
      state.currentMetric.error = action.payload as string;
    });

    // Fetch user metrics
    builder.addCase(fetchUserMetrics.pending, (state) => {
      state.userMetrics.loading = true;
      state.userMetrics.error = null;
    });
    builder.addCase(fetchUserMetrics.fulfilled, (state, action) => {
      state.userMetrics.loading = false;
      state.userMetrics.items = action.payload;
    });
    builder.addCase(fetchUserMetrics.rejected, (state, action) => {
      state.userMetrics.loading = false;
      state.userMetrics.error = action.payload as string;
    });

    // Create metric
    builder.addCase(createMetric.fulfilled, (state, action) => {
      state.metrics.items.push(action.payload);
      state.userMetrics.items.push(action.payload);
      state.currentMetric.data = action.payload;
    });

    // Update metric
    builder.addCase(updateMetric.fulfilled, (state, action) => {
      const updatedMetric = action.payload;
      
      // Update in all metrics list
      const metricIndex = state.metrics.items.findIndex((metric) => metric.id === updatedMetric.id);
      if (metricIndex !== -1) {
        state.metrics.items[metricIndex] = updatedMetric;
      }
      
      // Update in user metrics list
      const userMetricIndex = state.userMetrics.items.findIndex((metric) => metric.id === updatedMetric.id);
      if (userMetricIndex !== -1) {
        state.userMetrics.items[userMetricIndex] = updatedMetric;
      }
      
      // Update current metric if it's the same
      if (state.currentMetric.data?.id === updatedMetric.id) {
        state.currentMetric.data = updatedMetric;
      }
    });

    // Delete metric
    builder.addCase(deleteMetric.fulfilled, (state, action) => {
      const metricId = action.payload;
      state.metrics.items = state.metrics.items.filter((metric) => metric.id !== metricId);
      state.userMetrics.items = state.userMetrics.items.filter((metric) => metric.id !== metricId);
      
      if (state.currentMetric.data?.id === metricId) {
        state.currentMetric.data = null;
      }
    });

    // Fetch data points
    builder.addCase(fetchDataPoints.pending, (state) => {
      state.currentMetric.loading = true;
      state.currentMetric.error = null;
    });
    builder.addCase(fetchDataPoints.fulfilled, (state, action) => {
      state.currentMetric.loading = false;
      state.currentMetric.dataPoints = action.payload;
    });
    builder.addCase(fetchDataPoints.rejected, (state, action) => {
      state.currentMetric.loading = false;
      state.currentMetric.error = action.payload as string;
    });

    // Create data point
    builder.addCase(createDataPoint.fulfilled, (state, action) => {
      state.currentMetric.dataPoints.push(action.payload);
    });

    // Update data point
    builder.addCase(updateDataPoint.fulfilled, (state, action) => {
      const updatedDataPoint = action.payload;
      const dataPointIndex = state.currentMetric.dataPoints.findIndex(
        (dataPoint) => dataPoint.id === updatedDataPoint.id
      );
      if (dataPointIndex !== -1) {
        state.currentMetric.dataPoints[dataPointIndex] = updatedDataPoint;
      }
    });

    // Delete data point
    builder.addCase(deleteDataPoint.fulfilled, (state, action) => {
      const dataPointId = action.payload;
      state.currentMetric.dataPoints = state.currentMetric.dataPoints.filter(
        (dataPoint) => dataPoint.id !== dataPointId
      );
    });

    // Fetch reports
    builder.addCase(fetchReports.pending, (state) => {
      state.reports.loading = true;
      state.reports.error = null;
    });
    builder.addCase(fetchReports.fulfilled, (state, action) => {
      state.reports.loading = false;
      state.reports.items = action.payload;
    });
    builder.addCase(fetchReports.rejected, (state, action) => {
      state.reports.loading = false;
      state.reports.error = action.payload as string;
    });

    // Fetch report by ID
    builder.addCase(fetchReportById.pending, (state) => {
      state.currentReport.loading = true;
      state.currentReport.error = null;
    });
    builder.addCase(fetchReportById.fulfilled, (state, action) => {
      state.currentReport.loading = false;
      state.currentReport.data = action.payload;
    });
    builder.addCase(fetchReportById.rejected, (state, action) => {
      state.currentReport.loading = false;
      state.currentReport.error = action.payload as string;
    });

    // Fetch user reports
    builder.addCase(fetchUserReports.pending, (state) => {
      state.userReports.loading = true;
      state.userReports.error = null;
    });
    builder.addCase(fetchUserReports.fulfilled, (state, action) => {
      state.userReports.loading = false;
      state.userReports.items = action.payload;
    });
    builder.addCase(fetchUserReports.rejected, (state, action) => {
      state.userReports.loading = false;
      state.userReports.error = action.payload as string;
    });

    // Create report
    builder.addCase(createReport.fulfilled, (state, action) => {
      state.reports.items.push(action.payload);
      state.userReports.items.push(action.payload);
      state.currentReport.data = action.payload;
    });

    // Update report
    builder.addCase(updateReport.fulfilled, (state, action) => {
      const updatedReport = action.payload;
      
      // Update in all reports list
      const reportIndex = state.reports.items.findIndex((report) => report.id === updatedReport.id);
      if (reportIndex !== -1) {
        state.reports.items[reportIndex] = updatedReport;
      }
      
      // Update in user reports list
      const userReportIndex = state.userReports.items.findIndex((report) => report.id === updatedReport.id);
      if (userReportIndex !== -1) {
        state.userReports.items[userReportIndex] = updatedReport;
      }
      
      // Update current report if it's the same
      if (state.currentReport.data?.id === updatedReport.id) {
        state.currentReport.data = updatedReport;
      }
    });

    // Delete report
    builder.addCase(deleteReport.fulfilled, (state, action) => {
      const reportId = action.payload;
      state.reports.items = state.reports.items.filter((report) => report.id !== reportId);
      state.userReports.items = state.userReports.items.filter((report) => report.id !== reportId);
      
      if (state.currentReport.data?.id === reportId) {
        state.currentReport.data = null;
      }
    });

    // Publish report
    builder.addCase(publishReport.fulfilled, (state, action) => {
      const updatedReport = action.payload;
      
      // Update in all reports list
      const reportIndex = state.reports.items.findIndex((report) => report.id === updatedReport.id);
      if (reportIndex !== -1) {
        state.reports.items[reportIndex] = updatedReport;
      }
      
      // Update in user reports list
      const userReportIndex = state.userReports.items.findIndex((report) => report.id === updatedReport.id);
      if (userReportIndex !== -1) {
        state.userReports.items[userReportIndex] = updatedReport;
      }
      
      // Update current report if it's the same
      if (state.currentReport.data?.id === updatedReport.id) {
        state.currentReport.data = updatedReport;
      }
    });

    // Archive report
    builder.addCase(archiveReport.fulfilled, (state, action) => {
      const updatedReport = action.payload;
      
      // Update in all reports list
      const reportIndex = state.reports.items.findIndex((report) => report.id === updatedReport.id);
      if (reportIndex !== -1) {
        state.reports.items[reportIndex] = updatedReport;
      }
      
      // Update in user reports list
      const userReportIndex = state.userReports.items.findIndex((report) => report.id === updatedReport.id);
      if (userReportIndex !== -1) {
        state.userReports.items[userReportIndex] = updatedReport;
      }
      
      // Update current report if it's the same
      if (state.currentReport.data?.id === updatedReport.id) {
        state.currentReport.data = updatedReport;
      }
    });

    // Fetch categories
    builder.addCase(fetchCategories.pending, (state) => {
      state.categories.loading = true;
      state.categories.error = null;
    });
    builder.addCase(fetchCategories.fulfilled, (state, action) => {
      state.categories.loading = false;
      state.categories.items = action.payload;
    });
    builder.addCase(fetchCategories.rejected, (state, action) => {
      state.categories.loading = false;
      state.categories.error = action.payload as string;
    });

    // Create category
    builder.addCase(createCategory.fulfilled, (state, action) => {
      state.categories.items.push(action.payload);
    });

    // Update category
    builder.addCase(updateCategory.fulfilled, (state, action) => {
      const updatedCategory = action.payload;
      const categoryIndex = state.categories.items.findIndex(
        (category) => category.id === updatedCategory.id
      );
      if (categoryIndex !== -1) {
        state.categories.items[categoryIndex] = updatedCategory;
      }
    });

    // Delete category
    builder.addCase(deleteCategory.fulfilled, (state, action) => {
      const categoryId = action.payload;
      state.categories.items = state.categories.items.filter(
        (category) => category.id !== categoryId
      );
    });

    // Fetch dashboard config
    builder.addCase(fetchDashboardConfig.pending, (state) => {
      state.dashboardConfig.loading = true;
      state.dashboardConfig.error = null;
    });
    builder.addCase(fetchDashboardConfig.fulfilled, (state, action) => {
      state.dashboardConfig.loading = false;
      state.dashboardConfig.data = action.payload;
    });
    builder.addCase(fetchDashboardConfig.rejected, (state, action) => {
      state.dashboardConfig.loading = false;
      state.dashboardConfig.error = action.payload as string;
    });

    // Update dashboard config
    builder.addCase(updateDashboardConfig.fulfilled, (state, action) => {
      state.dashboardConfig.data = action.payload;
    });

    // Fetch metric analytics
    builder.addCase(fetchMetricAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchMetricAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.metricAnalytics = action.payload;
    });
    builder.addCase(fetchMetricAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });

    // Fetch category analytics
    builder.addCase(fetchCategoryAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchCategoryAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.categoryAnalytics = action.payload;
    });
    builder.addCase(fetchCategoryAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });

    // Fetch user analytics
    builder.addCase(fetchUserAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchUserAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.userAnalytics = action.payload;
    });
    builder.addCase(fetchUserAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });
  },
});

export const { resetCurrentMetric, resetCurrentReport, resetAnalytics } = impactSlice.actions;

export default impactSlice.reducer;
