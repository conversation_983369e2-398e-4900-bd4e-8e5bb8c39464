import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import quizzesService, { Quiz, Question, QuizAttempt } from '../../services/quizzesService';

interface QuizzesState {
  quizzes: {
    items: Quiz[];
    total: number;
    loading: boolean;
    error: string | null;
  };
  userCreatedQuizzes: {
    items: Quiz[];
    loading: boolean;
    error: string | null;
  };
  userAttemptedQuizzes: {
    items: Quiz[];
    loading: boolean;
    error: string | null;
  };
  currentQuiz: {
    data: Quiz | null;
    loading: boolean;
    error: string | null;
  };
  questions: {
    items: Question[];
    loading: boolean;
    error: string | null;
  };
  currentQuestion: {
    data: Question | null;
    loading: boolean;
    error: string | null;
  };
  quizAttempts: {
    items: QuizAttempt[];
    loading: boolean;
    error: string | null;
  };
  currentAttempt: {
    data: QuizAttempt | null;
    loading: boolean;
    error: string | null;
  };
  analytics: {
    quizAnalytics: any;
    questionAnalytics: any;
    userAnalytics: any;
    loading: boolean;
    error: string | null;
  };
}

const initialState: QuizzesState = {
  quizzes: {
    items: [],
    total: 0,
    loading: false,
    error: null,
  },
  userCreatedQuizzes: {
    items: [],
    loading: false,
    error: null,
  },
  userAttemptedQuizzes: {
    items: [],
    loading: false,
    error: null,
  },
  currentQuiz: {
    data: null,
    loading: false,
    error: null,
  },
  questions: {
    items: [],
    loading: false,
    error: null,
  },
  currentQuestion: {
    data: null,
    loading: false,
    error: null,
  },
  quizAttempts: {
    items: [],
    loading: false,
    error: null,
  },
  currentAttempt: {
    data: null,
    loading: false,
    error: null,
  },
  analytics: {
    quizAnalytics: null,
    questionAnalytics: null,
    userAnalytics: null,
    loading: false,
    error: null,
  },
};

// Quiz thunks
export const fetchQuizzes = createAsyncThunk(
  'quizzes/fetchQuizzes',
  async (params: { page?: number; pageSize?: number; filters?: any } = {}, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizzes(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quizzes');
    }
  }
);

export const fetchQuizById = createAsyncThunk(
  'quizzes/fetchQuizById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quiz');
    }
  }
);

export const fetchQuizzesByCategory = createAsyncThunk(
  'quizzes/fetchQuizzesByCategory',
  async (categoryId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizzesByCategory(categoryId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quizzes by category');
    }
  }
);

export const fetchQuizzesByCourse = createAsyncThunk(
  'quizzes/fetchQuizzesByCourse',
  async (courseId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizzesByCourse(courseId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quizzes by course');
    }
  }
);

export const fetchQuizzesByLesson = createAsyncThunk(
  'quizzes/fetchQuizzesByLesson',
  async (lessonId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizzesByLesson(lessonId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quizzes by lesson');
    }
  }
);

export const fetchQuizzesByTutorial = createAsyncThunk(
  'quizzes/fetchQuizzesByTutorial',
  async (tutorialId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizzesByTutorial(tutorialId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quizzes by tutorial');
    }
  }
);

export const fetchUserCreatedQuizzes = createAsyncThunk(
  'quizzes/fetchUserCreatedQuizzes',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getUserCreatedQuizzes(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user created quizzes');
    }
  }
);

export const fetchUserAttemptedQuizzes = createAsyncThunk(
  'quizzes/fetchUserAttemptedQuizzes',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getUserAttemptedQuizzes(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user attempted quizzes');
    }
  }
);

export const createQuiz = createAsyncThunk(
  'quizzes/createQuiz',
  async (quiz: Partial<Quiz>, { rejectWithValue }) => {
    try {
      return await quizzesService.createQuiz(quiz);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create quiz');
    }
  }
);

export const updateQuiz = createAsyncThunk(
  'quizzes/updateQuiz',
  async ({ id, quiz }: { id: number; quiz: Partial<Quiz> }, { rejectWithValue }) => {
    try {
      return await quizzesService.updateQuiz(id, quiz);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update quiz');
    }
  }
);

export const deleteQuiz = createAsyncThunk(
  'quizzes/deleteQuiz',
  async (id: number, { rejectWithValue }) => {
    try {
      await quizzesService.deleteQuiz(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete quiz');
    }
  }
);

export const publishQuiz = createAsyncThunk(
  'quizzes/publishQuiz',
  async (id: number, { rejectWithValue }) => {
    try {
      return await quizzesService.publishQuiz(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to publish quiz');
    }
  }
);

export const unpublishQuiz = createAsyncThunk(
  'quizzes/unpublishQuiz',
  async (id: number, { rejectWithValue }) => {
    try {
      return await quizzesService.unpublishQuiz(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to unpublish quiz');
    }
  }
);

// Question thunks
export const fetchQuestions = createAsyncThunk(
  'quizzes/fetchQuestions',
  async (quizId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuestions(quizId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch questions');
    }
  }
);

export const fetchQuestionById = createAsyncThunk(
  'quizzes/fetchQuestionById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuestionById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch question');
    }
  }
);

export const createQuestion = createAsyncThunk(
  'quizzes/createQuestion',
  async ({ quizId, question }: { quizId: number; question: Partial<Question> }, { rejectWithValue }) => {
    try {
      return await quizzesService.createQuestion(quizId, question);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create question');
    }
  }
);

export const updateQuestion = createAsyncThunk(
  'quizzes/updateQuestion',
  async ({ id, question }: { id: number; question: Partial<Question> }, { rejectWithValue }) => {
    try {
      return await quizzesService.updateQuestion(id, question);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update question');
    }
  }
);

export const deleteQuestion = createAsyncThunk(
  'quizzes/deleteQuestion',
  async (id: number, { rejectWithValue }) => {
    try {
      await quizzesService.deleteQuestion(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete question');
    }
  }
);

export const reorderQuestions = createAsyncThunk(
  'quizzes/reorderQuestions',
  async ({ quizId, questionIds }: { quizId: number; questionIds: number[] }, { rejectWithValue }) => {
    try {
      return await quizzesService.reorderQuestions(quizId, questionIds);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reorder questions');
    }
  }
);

// Quiz attempt thunks
export const startQuizAttempt = createAsyncThunk(
  'quizzes/startQuizAttempt',
  async ({ quizId, userId }: { quizId: number; userId: number }, { rejectWithValue }) => {
    try {
      return await quizzesService.startQuizAttempt(quizId, userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to start quiz attempt');
    }
  }
);

export const submitQuizAttempt = createAsyncThunk(
  'quizzes/submitQuizAttempt',
  async (
    { attemptId, answers }: { attemptId: number; answers: { questionId: number; answer: string | string[] }[] },
    { rejectWithValue }
  ) => {
    try {
      return await quizzesService.submitQuizAttempt(attemptId, answers);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit quiz attempt');
    }
  }
);

export const fetchQuizAttempt = createAsyncThunk(
  'quizzes/fetchQuizAttempt',
  async (attemptId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizAttempt(attemptId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quiz attempt');
    }
  }
);

export const fetchUserQuizAttempts = createAsyncThunk(
  'quizzes/fetchUserQuizAttempts',
  async ({ userId, quizId }: { userId: number; quizId: number }, { rejectWithValue }) => {
    try {
      return await quizzesService.getUserQuizAttempts(userId, quizId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user quiz attempts');
    }
  }
);

// Analytics thunks
export const fetchQuizAnalytics = createAsyncThunk(
  'quizzes/fetchQuizAnalytics',
  async (quizId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuizAnalytics(quizId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch quiz analytics');
    }
  }
);

export const fetchQuestionAnalytics = createAsyncThunk(
  'quizzes/fetchQuestionAnalytics',
  async (questionId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getQuestionAnalytics(questionId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch question analytics');
    }
  }
);

export const fetchUserQuizAnalytics = createAsyncThunk(
  'quizzes/fetchUserQuizAnalytics',
  async (userId: number, { rejectWithValue }) => {
    try {
      return await quizzesService.getUserQuizAnalytics(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user quiz analytics');
    }
  }
);

const quizzesSlice = createSlice({
  name: 'quizzes',
  initialState,
  reducers: {
    resetCurrentQuiz: (state) => {
      state.currentQuiz.data = null;
      state.currentQuiz.error = null;
    },
    resetCurrentQuestion: (state) => {
      state.currentQuestion.data = null;
      state.currentQuestion.error = null;
    },
    resetCurrentAttempt: (state) => {
      state.currentAttempt.data = null;
      state.currentAttempt.error = null;
    },
    resetQuestions: (state) => {
      state.questions.items = [];
      state.questions.error = null;
    },
    resetAnalytics: (state) => {
      state.analytics.quizAnalytics = null;
      state.analytics.questionAnalytics = null;
      state.analytics.userAnalytics = null;
      state.analytics.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch quizzes
    builder.addCase(fetchQuizzes.pending, (state) => {
      state.quizzes.loading = true;
      state.quizzes.error = null;
    });
    builder.addCase(fetchQuizzes.fulfilled, (state, action) => {
      state.quizzes.loading = false;
      state.quizzes.items = action.payload.items;
      state.quizzes.total = action.payload.total;
    });
    builder.addCase(fetchQuizzes.rejected, (state, action) => {
      state.quizzes.loading = false;
      state.quizzes.error = action.payload as string;
    });

    // Fetch quiz by ID
    builder.addCase(fetchQuizById.pending, (state) => {
      state.currentQuiz.loading = true;
      state.currentQuiz.error = null;
    });
    builder.addCase(fetchQuizById.fulfilled, (state, action) => {
      state.currentQuiz.loading = false;
      state.currentQuiz.data = action.payload;
    });
    builder.addCase(fetchQuizById.rejected, (state, action) => {
      state.currentQuiz.loading = false;
      state.currentQuiz.error = action.payload as string;
    });

    // Fetch user created quizzes
    builder.addCase(fetchUserCreatedQuizzes.pending, (state) => {
      state.userCreatedQuizzes.loading = true;
      state.userCreatedQuizzes.error = null;
    });
    builder.addCase(fetchUserCreatedQuizzes.fulfilled, (state, action) => {
      state.userCreatedQuizzes.loading = false;
      state.userCreatedQuizzes.items = action.payload;
    });
    builder.addCase(fetchUserCreatedQuizzes.rejected, (state, action) => {
      state.userCreatedQuizzes.loading = false;
      state.userCreatedQuizzes.error = action.payload as string;
    });

    // Fetch user attempted quizzes
    builder.addCase(fetchUserAttemptedQuizzes.pending, (state) => {
      state.userAttemptedQuizzes.loading = true;
      state.userAttemptedQuizzes.error = null;
    });
    builder.addCase(fetchUserAttemptedQuizzes.fulfilled, (state, action) => {
      state.userAttemptedQuizzes.loading = false;
      state.userAttemptedQuizzes.items = action.payload;
    });
    builder.addCase(fetchUserAttemptedQuizzes.rejected, (state, action) => {
      state.userAttemptedQuizzes.loading = false;
      state.userAttemptedQuizzes.error = action.payload as string;
    });

    // Create quiz
    builder.addCase(createQuiz.fulfilled, (state, action) => {
      state.quizzes.items.push(action.payload);
      state.userCreatedQuizzes.items.push(action.payload);
      state.currentQuiz.data = action.payload;
    });

    // Update quiz
    builder.addCase(updateQuiz.fulfilled, (state, action) => {
      const updatedQuiz = action.payload;
      
      // Update in all quizzes list
      const quizIndex = state.quizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (quizIndex !== -1) {
        state.quizzes.items[quizIndex] = updatedQuiz;
      }
      
      // Update in user created quizzes list
      const userQuizIndex = state.userCreatedQuizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (userQuizIndex !== -1) {
        state.userCreatedQuizzes.items[userQuizIndex] = updatedQuiz;
      }
      
      // Update current quiz if it's the same
      if (state.currentQuiz.data?.id === updatedQuiz.id) {
        state.currentQuiz.data = updatedQuiz;
      }
    });

    // Delete quiz
    builder.addCase(deleteQuiz.fulfilled, (state, action) => {
      const quizId = action.payload;
      state.quizzes.items = state.quizzes.items.filter((quiz) => quiz.id !== quizId);
      state.userCreatedQuizzes.items = state.userCreatedQuizzes.items.filter((quiz) => quiz.id !== quizId);
      
      if (state.currentQuiz.data?.id === quizId) {
        state.currentQuiz.data = null;
      }
    });

    // Publish quiz
    builder.addCase(publishQuiz.fulfilled, (state, action) => {
      const updatedQuiz = action.payload;
      
      // Update in all quizzes list
      const quizIndex = state.quizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (quizIndex !== -1) {
        state.quizzes.items[quizIndex] = updatedQuiz;
      }
      
      // Update in user created quizzes list
      const userQuizIndex = state.userCreatedQuizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (userQuizIndex !== -1) {
        state.userCreatedQuizzes.items[userQuizIndex] = updatedQuiz;
      }
      
      // Update current quiz if it's the same
      if (state.currentQuiz.data?.id === updatedQuiz.id) {
        state.currentQuiz.data = updatedQuiz;
      }
    });

    // Unpublish quiz
    builder.addCase(unpublishQuiz.fulfilled, (state, action) => {
      const updatedQuiz = action.payload;
      
      // Update in all quizzes list
      const quizIndex = state.quizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (quizIndex !== -1) {
        state.quizzes.items[quizIndex] = updatedQuiz;
      }
      
      // Update in user created quizzes list
      const userQuizIndex = state.userCreatedQuizzes.items.findIndex((quiz) => quiz.id === updatedQuiz.id);
      if (userQuizIndex !== -1) {
        state.userCreatedQuizzes.items[userQuizIndex] = updatedQuiz;
      }
      
      // Update current quiz if it's the same
      if (state.currentQuiz.data?.id === updatedQuiz.id) {
        state.currentQuiz.data = updatedQuiz;
      }
    });

    // Fetch questions
    builder.addCase(fetchQuestions.pending, (state) => {
      state.questions.loading = true;
      state.questions.error = null;
    });
    builder.addCase(fetchQuestions.fulfilled, (state, action) => {
      state.questions.loading = false;
      state.questions.items = action.payload;
    });
    builder.addCase(fetchQuestions.rejected, (state, action) => {
      state.questions.loading = false;
      state.questions.error = action.payload as string;
    });

    // Fetch question by ID
    builder.addCase(fetchQuestionById.pending, (state) => {
      state.currentQuestion.loading = true;
      state.currentQuestion.error = null;
    });
    builder.addCase(fetchQuestionById.fulfilled, (state, action) => {
      state.currentQuestion.loading = false;
      state.currentQuestion.data = action.payload;
    });
    builder.addCase(fetchQuestionById.rejected, (state, action) => {
      state.currentQuestion.loading = false;
      state.currentQuestion.error = action.payload as string;
    });

    // Create question
    builder.addCase(createQuestion.fulfilled, (state, action) => {
      state.questions.items.push(action.payload);
      state.currentQuestion.data = action.payload;
    });

    // Update question
    builder.addCase(updateQuestion.fulfilled, (state, action) => {
      const updatedQuestion = action.payload;
      
      // Update in questions list
      const questionIndex = state.questions.items.findIndex((question) => question.id === updatedQuestion.id);
      if (questionIndex !== -1) {
        state.questions.items[questionIndex] = updatedQuestion;
      }
      
      // Update current question if it's the same
      if (state.currentQuestion.data?.id === updatedQuestion.id) {
        state.currentQuestion.data = updatedQuestion;
      }
    });

    // Delete question
    builder.addCase(deleteQuestion.fulfilled, (state, action) => {
      const questionId = action.payload;
      state.questions.items = state.questions.items.filter((question) => question.id !== questionId);
      
      if (state.currentQuestion.data?.id === questionId) {
        state.currentQuestion.data = null;
      }
    });

    // Reorder questions
    builder.addCase(reorderQuestions.fulfilled, (state, action) => {
      state.questions.items = action.payload;
    });

    // Start quiz attempt
    builder.addCase(startQuizAttempt.pending, (state) => {
      state.currentAttempt.loading = true;
      state.currentAttempt.error = null;
    });
    builder.addCase(startQuizAttempt.fulfilled, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.data = action.payload;
      state.quizAttempts.items.push(action.payload);
    });
    builder.addCase(startQuizAttempt.rejected, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.error = action.payload as string;
    });

    // Submit quiz attempt
    builder.addCase(submitQuizAttempt.pending, (state) => {
      state.currentAttempt.loading = true;
      state.currentAttempt.error = null;
    });
    builder.addCase(submitQuizAttempt.fulfilled, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.data = action.payload;
      
      // Update in attempts list
      const attemptIndex = state.quizAttempts.items.findIndex((attempt) => attempt.id === action.payload.id);
      if (attemptIndex !== -1) {
        state.quizAttempts.items[attemptIndex] = action.payload;
      }
    });
    builder.addCase(submitQuizAttempt.rejected, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.error = action.payload as string;
    });

    // Fetch quiz attempt
    builder.addCase(fetchQuizAttempt.pending, (state) => {
      state.currentAttempt.loading = true;
      state.currentAttempt.error = null;
    });
    builder.addCase(fetchQuizAttempt.fulfilled, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.data = action.payload;
    });
    builder.addCase(fetchQuizAttempt.rejected, (state, action) => {
      state.currentAttempt.loading = false;
      state.currentAttempt.error = action.payload as string;
    });

    // Fetch user quiz attempts
    builder.addCase(fetchUserQuizAttempts.pending, (state) => {
      state.quizAttempts.loading = true;
      state.quizAttempts.error = null;
    });
    builder.addCase(fetchUserQuizAttempts.fulfilled, (state, action) => {
      state.quizAttempts.loading = false;
      state.quizAttempts.items = action.payload;
    });
    builder.addCase(fetchUserQuizAttempts.rejected, (state, action) => {
      state.quizAttempts.loading = false;
      state.quizAttempts.error = action.payload as string;
    });

    // Fetch quiz analytics
    builder.addCase(fetchQuizAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchQuizAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.quizAnalytics = action.payload;
    });
    builder.addCase(fetchQuizAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });

    // Fetch question analytics
    builder.addCase(fetchQuestionAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchQuestionAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.questionAnalytics = action.payload;
    });
    builder.addCase(fetchQuestionAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });

    // Fetch user quiz analytics
    builder.addCase(fetchUserQuizAnalytics.pending, (state) => {
      state.analytics.loading = true;
      state.analytics.error = null;
    });
    builder.addCase(fetchUserQuizAnalytics.fulfilled, (state, action) => {
      state.analytics.loading = false;
      state.analytics.userAnalytics = action.payload;
    });
    builder.addCase(fetchUserQuizAnalytics.rejected, (state, action) => {
      state.analytics.loading = false;
      state.analytics.error = action.payload as string;
    });
  },
});

export const {
  resetCurrentQuiz,
  resetCurrentQuestion,
  resetCurrentAttempt,
  resetQuestions,
  resetAnalytics,
} = quizzesSlice.actions;

export default quizzesSlice.reducer;
