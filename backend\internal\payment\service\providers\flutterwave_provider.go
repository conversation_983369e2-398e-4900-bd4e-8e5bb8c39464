package providers

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/models"
)

const (
	// FlutterwaveBaseURL is the base URL for Flutterwave API
	FlutterwaveBaseURL = "https://api.flutterwave.com/v3"
)

// FlutterwaveProvider implements the payment provider interface for Flutterwave
type FlutterwaveProvider struct {
	secretKey string
	publicKey string
	client    *http.Client
}

// NewFlutterwaveProvider creates a new Flutterwave provider
func NewFlutterwaveProvider(secretKey, publicKey string) *FlutterwaveProvider {
	return &FlutterwaveProvider{
		secretKey: secretKey,
		publicKey: publicKey,
		client: &http.Client{
			Timeout: RequestTimeout,
		},
	}
}

// FlutterwaveResponse represents a generic Flutterwave API response
type FlutterwaveResponse struct {
	Status  string          `json:"status"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// FlutterwaveErrorResponse represents a Flutterwave API error response
type FlutterwaveErrorResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// FlutterwavePaymentResponse represents a payment response
type FlutterwavePaymentResponse struct {
	ID            int    `json:"id"`
	TxRef         string `json:"tx_ref"`
	FlwRef        string `json:"flw_ref"`
	DeviceFingerprint string `json:"device_fingerprint"`
	Amount        int    `json:"amount"`
	Currency      string `json:"currency"`
	ChargedAmount int    `json:"charged_amount"`
	AppFee        float64 `json:"app_fee"`
	MerchantFee   int    `json:"merchant_fee"`
	ProcessorResponse string `json:"processor_response"`
	AuthModel    string   `json:"auth_model"`
	IP           string   `json:"ip"`
	Narration    string   `json:"narration"`
	Status       string   `json:"status"`
	PaymentType  string   `json:"payment_type"`
	CreatedAt    string   `json:"created_at"`
	AccountID    int      `json:"account_id"`
	Customer     struct {
		ID            int    `json:"id"`
		Name          string `json:"name"`
		PhoneNumber   string `json:"phone_number"`
		Email         string `json:"email"`
		CreatedAt     string `json:"created_at"`
	} `json:"customer"`
	PaymentLink  interface{} `json:"payment_link"`
}

// FlutterwaveTransactionVerifyResponse represents a transaction verification response
type FlutterwaveTransactionVerifyResponse struct {
	ID            int    `json:"id"`
	TxRef         string `json:"tx_ref"`
	FlwRef        string `json:"flw_ref"`
	Amount        int    `json:"amount"`
	Currency      string `json:"currency"`
	ChargedAmount int    `json:"charged_amount"`
	AppFee        float64 `json:"app_fee"`
	MerchantFee   int    `json:"merchant_fee"`
	Status        string `json:"status"`
	PaymentType   string `json:"payment_type"`
	CreatedAt     string `json:"created_at"`
	CustomerID    int    `json:"customer_id"`
	Customer      struct {
		ID            int    `json:"id"`
		Name          string `json:"name"`
		PhoneNumber   string `json:"phone_number"`
		Email         string `json:"email"`
		CreatedAt     string `json:"created_at"`
	} `json:"customer"`
	Meta          interface{} `json:"meta"`
}

// FlutterwavePaymentLinkResponse represents a payment link response
type FlutterwavePaymentLinkResponse struct {
	ID               int    `json:"id"`
	Link             string `json:"link"`
	Status           string `json:"status"`
	Title            string `json:"title"`
	Description      string `json:"description"`
	Amount           int    `json:"amount"`
	Currency         string `json:"currency"`
	RedirectURL      string `json:"redirect_url"`
	PaymentReference string `json:"payment_reference"`
	GenerateReference bool   `json:"generate_reference"`
	PaymentOptions   string `json:"payment_options"`
	SuccessMessage   string `json:"success_message"`
	ExpiryDate       string `json:"expiry_date"`
	CollectPhoneNumber bool  `json:"collect_phone_number"`
}

// FlutterwaveSubscriptionResponse represents a subscription response
type FlutterwaveSubscriptionResponse struct {
	ID               int    `json:"id"`
	Name             string `json:"name"`
	Amount           int    `json:"amount"`
	Interval         string `json:"interval"`
	Currency         string `json:"currency"`
	PlanCode         string `json:"plan_code"`
	Status           string `json:"status"`
	CreatedAt        string `json:"created_at"`
}

// FlutterwaveCustomerResponse represents a customer response
type FlutterwaveCustomerResponse struct {
	ID            int    `json:"id"`
	FullName      string `json:"fullname"`
	PhoneNumber   string `json:"phone_number"`
	Email         string `json:"email"`
	CreatedAt     string `json:"created_at"`
	AccountID     int    `json:"account_id"`
	Status        string `json:"status"`
	CustomerIdentifier string `json:"customer_identifier"`
}

// FlutterwaveRefundResponse represents a refund response
type FlutterwaveRefundResponse struct {
	ID              int    `json:"id"`
	TxID            int    `json:"tx_id"`
	TxRef           string `json:"tx_ref"`
	FlwRef          string `json:"flw_ref"`
	Amount          int    `json:"amount"`
	Currency        string `json:"currency"`
	Status          string `json:"status"`
	AccountID       int    `json:"account_id"`
	Meta            interface{} `json:"meta"`
	CreatedAt       string `json:"created_at"`
	Comment         string `json:"comment"`
	SettlementID    interface{} `json:"settlement_id"`
}

// FlutterwaveTransferResponse represents a transfer response
type FlutterwaveTransferResponse struct {
	ID                    int    `json:"id"`
	AccountNumber         string `json:"account_number"`
	BankCode              string `json:"bank_code"`
	FullName              string `json:"full_name"`
	CreatedAt             string `json:"created_at"`
	Currency              string `json:"currency"`
	Amount                int    `json:"amount"`
	Fee                   int    `json:"fee"`
	Status                string `json:"status"`
	Reference             string `json:"reference"`
	Meta                  interface{} `json:"meta"`
	Narration             string `json:"narration"`
	CompleteMessage       string `json:"complete_message"`
	RequiresApproval      int    `json:"requires_approval"`
	IsApproved            int    `json:"is_approved"`
}

// makeRequest makes a request to the Flutterwave API
func (f *FlutterwaveProvider) makeRequest(method, endpoint string, body interface{}) ([]byte, error) {
	var reqBody io.Reader
	
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}
	
	req, err := http.NewRequest(method, FlutterwaveBaseURL+endpoint, reqBody)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}
	
	req.Header.Set("Authorization", "Bearer "+f.secretKey)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := f.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()
	
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}
	
	if resp.StatusCode >= 400 {
		var errResp FlutterwaveErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %w", err)
		}
		return nil, errors.New(errResp.Message)
	}
	
	return respBody, nil
}

// InitializePayment initializes a payment with Flutterwave
func (f *FlutterwaveProvider) InitializePayment(amount int, currency models.Currency, txRef, customerEmail, customerName, redirectURL, paymentDescription string, metaData map[string]interface{}) (string, error) {
	requestBody := map[string]interface{}{
		"tx_ref":         txRef,
		"amount":         amount,
		"currency":       currency,
		"redirect_url":   redirectURL,
		"payment_options": "card,bank_transfer",
		"meta":           metaData,
		"customer": map[string]string{
			"email":        customerEmail,
			"name":         customerName,
		},
		"customizations": map[string]string{
			"title":       "Great Nigeria Subscription",
			"description": paymentDescription,
			"logo":        "https://assets.piedpiper.com/logo.png",
		},
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/payments", requestBody)
	if err != nil {
		return "", err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return "", fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return "", errors.New(flwResp.Message)
	}
	
	var respData struct {
		Link string `json:"link"`
	}
	
	if err := json.Unmarshal(flwResp.Data, &respData); err != nil {
		return "", fmt.Errorf("error parsing payment response: %w", err)
	}
	
	return respData.Link, nil
}

// CreatePaymentLink creates a payment link with Flutterwave
func (f *FlutterwaveProvider) CreatePaymentLink(title, description string, amount int, currency models.Currency, redirectURL string) (*FlutterwavePaymentLinkResponse, error) {
	requestBody := map[string]interface{}{
		"title":            title,
		"description":      description,
		"amount":           amount,
		"currency":         currency,
		"redirect_url":     redirectURL,
		"payment_options":  "card,bank_transfer",
		"generate_reference": true,
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/payment-links", requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var paymentLinkResp FlutterwavePaymentLinkResponse
	if err := json.Unmarshal(flwResp.Data, &paymentLinkResp); err != nil {
		return nil, fmt.Errorf("error parsing payment link response: %w", err)
	}
	
	return &paymentLinkResp, nil
}

// VerifyTransaction verifies a transaction with Flutterwave
func (f *FlutterwaveProvider) VerifyTransaction(transactionID string) (*FlutterwaveTransactionVerifyResponse, error) {
	respBody, err := f.makeRequest(http.MethodGet, "/transactions/"+transactionID+"/verify", nil)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var verifyResp FlutterwaveTransactionVerifyResponse
	if err := json.Unmarshal(flwResp.Data, &verifyResp); err != nil {
		return nil, fmt.Errorf("error parsing verification response: %w", err)
	}
	
	return &verifyResp, nil
}

// VerifyTransactionByReference verifies a transaction by reference with Flutterwave
func (f *FlutterwaveProvider) VerifyTransactionByReference(txRef string) (*FlutterwaveTransactionVerifyResponse, error) {
	respBody, err := f.makeRequest(http.MethodGet, "/transactions/verify_by_reference?tx_ref="+txRef, nil)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var verifyResp FlutterwaveTransactionVerifyResponse
	if err := json.Unmarshal(flwResp.Data, &verifyResp); err != nil {
		return nil, fmt.Errorf("error parsing verification response: %w", err)
	}
	
	return &verifyResp, nil
}

// CreateCustomer creates a customer with Flutterwave
func (f *FlutterwaveProvider) CreateCustomer(email, name, phoneNumber string) (*FlutterwaveCustomerResponse, error) {
	requestBody := map[string]interface{}{
		"email":        email,
		"fullname":     name,
		"phone_number": phoneNumber,
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/customers", requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var customerResp FlutterwaveCustomerResponse
	if err := json.Unmarshal(flwResp.Data, &customerResp); err != nil {
		return nil, fmt.Errorf("error parsing customer response: %w", err)
	}
	
	return &customerResp, nil
}

// CreatePlan creates a subscription plan with Flutterwave
func (f *FlutterwaveProvider) CreatePlan(name, description string, amount int, interval string, currency models.Currency) (*FlutterwaveSubscriptionResponse, error) {
	requestBody := map[string]interface{}{
		"name":        name,
		"description": description,
		"amount":      amount,
		"interval":    interval,
		"currency":    currency,
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/payment-plans", requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var planResp FlutterwaveSubscriptionResponse
	if err := json.Unmarshal(flwResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// GetPlan retrieves a plan from Flutterwave
func (f *FlutterwaveProvider) GetPlan(planID string) (*FlutterwaveSubscriptionResponse, error) {
	respBody, err := f.makeRequest(http.MethodGet, "/payment-plans/"+planID, nil)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var planResp FlutterwaveSubscriptionResponse
	if err := json.Unmarshal(flwResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// UpdatePlan updates a plan in Flutterwave
func (f *FlutterwaveProvider) UpdatePlan(planID, name, description string, amount int, status string) (*FlutterwaveSubscriptionResponse, error) {
	requestBody := map[string]interface{}{
		"name":        name,
		"description": description,
		"amount":      amount,
		"status":      status,
	}
	
	respBody, err := f.makeRequest(http.MethodPut, "/payment-plans/"+planID, requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var planResp FlutterwaveSubscriptionResponse
	if err := json.Unmarshal(flwResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// InitiateRefund initiates a refund with Flutterwave
func (f *FlutterwaveProvider) InitiateRefund(transactionID string, amount int, reason string) (*FlutterwaveRefundResponse, error) {
	requestBody := map[string]interface{}{
		"id":      transactionID,
		"amount":  amount,
		"comment": reason,
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/transactions/"+transactionID+"/refund", requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var refundResp FlutterwaveRefundResponse
	if err := json.Unmarshal(flwResp.Data, &refundResp); err != nil {
		return nil, fmt.Errorf("error parsing refund response: %w", err)
	}
	
	return &refundResp, nil
}

// GetRefund retrieves a refund from Flutterwave
func (f *FlutterwaveProvider) GetRefund(refundID string) (*FlutterwaveRefundResponse, error) {
	respBody, err := f.makeRequest(http.MethodGet, "/refunds/"+refundID, nil)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var refundResp FlutterwaveRefundResponse
	if err := json.Unmarshal(flwResp.Data, &refundResp); err != nil {
		return nil, fmt.Errorf("error parsing refund response: %w", err)
	}
	
	return &refundResp, nil
}

// TransferToBank initiates a transfer to a bank account with Flutterwave
func (f *FlutterwaveProvider) TransferToBank(accountBank, accountNumber, accountName, narrattion, reference string, amount int, currency models.Currency) (*FlutterwaveTransferResponse, error) {
	requestBody := map[string]interface{}{
		"account_bank":   accountBank,
		"account_number": accountNumber,
		"amount":         amount,
		"narration":      narrattion,
		"currency":       currency,
		"reference":      reference,
		"beneficiary_name": accountName,
	}
	
	respBody, err := f.makeRequest(http.MethodPost, "/transfers", requestBody)
	if err != nil {
		return nil, err
	}
	
	var flwResp FlutterwaveResponse
	if err := json.Unmarshal(respBody, &flwResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if flwResp.Status != "success" {
		return nil, errors.New(flwResp.Message)
	}
	
	var transferResp FlutterwaveTransferResponse
	if err := json.Unmarshal(flwResp.Data, &transferResp); err != nil {
		return nil, fmt.Errorf("error parsing transfer response: %w", err)
	}
	
	return &transferResp, nil
}