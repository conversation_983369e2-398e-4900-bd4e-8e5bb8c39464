package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/tips/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/tips/service"
	"github.com/sirupsen/logrus"
)

// TipsHandler handles HTTP requests for tips
type TipsHandler struct {
	service service.TipsService
	logger  *logrus.Logger
}

// NewTipsHandler creates a new tips handler
func NewTipsHandler(service service.TipsService, logger *logrus.Logger) *TipsHandler {
	return &TipsHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers the routes for the tips handler
func (h *TipsHandler) RegisterRoutes(router *gin.RouterGroup) {
	tipsGroup := router.Group("/tips")
	{
		// Public routes
		tipsGroup.GET("", h.GetAllTips)
		tipsGroup.GET("/:id", h.GetTipByID)
		tipsGroup.GET("/category/:category", h.GetTipsByCategory)
		tipsGroup.POST("/contextual", h.GetContextualTips)
		
		// Admin routes (should be protected)
		tipsGroup.POST("", h.CreateTip)
		tipsGroup.PUT("/:id", h.UpdateTip)
		tipsGroup.DELETE("/:id", h.DeleteTip)
		
		// Rule routes (should be protected)
		tipsGroup.GET("/rules/:tipID", h.GetTipRulesByTipID)
		tipsGroup.POST("/rules", h.CreateTipRule)
		tipsGroup.PUT("/rules/:id", h.UpdateTipRule)
		tipsGroup.DELETE("/rules/:id", h.DeleteTipRule)
		
		// User interaction routes
		tipsGroup.POST("/view/:id", h.RecordTipView)
		tipsGroup.POST("/dismiss/:id", h.RecordTipDismiss)
		tipsGroup.POST("/click/:id", h.RecordTipClick)
		tipsGroup.POST("/feedback", h.SubmitTipFeedback)
		
		// Statistics routes (should be protected)
		tipsGroup.GET("/statistics/:id", h.GetTipStatistics)
		tipsGroup.GET("/statistics", h.GetAllTipStatistics)
	}
}

// GetAllTips handles GET /tips
func (h *TipsHandler) GetAllTips(c *gin.Context) {
	tips, err := h.service.GetAllTips()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get all tips")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tips"})
		return
	}
	
	c.JSON(http.StatusOK, tips)
}

// GetTipByID handles GET /tips/:id
func (h *TipsHandler) GetTipByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	tip, err := h.service.GetTipByID(uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tip")
		c.JSON(http.StatusNotFound, gin.H{"error": "Tip not found"})
		return
	}
	
	c.JSON(http.StatusOK, tip)
}

// GetTipsByCategory handles GET /tips/category/:category
func (h *TipsHandler) GetTipsByCategory(c *gin.Context) {
	categoryStr := c.Param("category")
	category := models.TipCategory(categoryStr)
	
	tips, err := h.service.GetTipsByCategory(category)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tips by category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tips"})
		return
	}
	
	c.JSON(http.StatusOK, tips)
}

// CreateTip handles POST /tips
func (h *TipsHandler) CreateTip(c *gin.Context) {
	var tip models.Tip
	if err := c.ShouldBindJSON(&tip); err != nil {
		h.logger.WithError(err).Error("Invalid tip data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip data"})
		return
	}
	
	if err := h.service.CreateTip(&tip); err != nil {
		h.logger.WithError(err).Error("Failed to create tip")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create tip"})
		return
	}
	
	c.JSON(http.StatusCreated, tip)
}

// UpdateTip handles PUT /tips/:id
func (h *TipsHandler) UpdateTip(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	var tip models.Tip
	if err := c.ShouldBindJSON(&tip); err != nil {
		h.logger.WithError(err).Error("Invalid tip data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip data"})
		return
	}
	
	tip.ID = uint(id)
	if err := h.service.UpdateTip(&tip); err != nil {
		h.logger.WithError(err).Error("Failed to update tip")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update tip"})
		return
	}
	
	c.JSON(http.StatusOK, tip)
}

// DeleteTip handles DELETE /tips/:id
func (h *TipsHandler) DeleteTip(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	if err := h.service.DeleteTip(uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete tip")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete tip"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Tip deleted successfully"})
}

// GetTipRulesByTipID handles GET /tips/rules/:tipID
func (h *TipsHandler) GetTipRulesByTipID(c *gin.Context) {
	tipIDStr := c.Param("tipID")
	tipID, err := strconv.ParseUint(tipIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	rules, err := h.service.GetTipRulesByTipID(uint(tipID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tip rules")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tip rules"})
		return
	}
	
	c.JSON(http.StatusOK, rules)
}

// CreateTipRule handles POST /tips/rules
func (h *TipsHandler) CreateTipRule(c *gin.Context) {
	var rule models.TipRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		h.logger.WithError(err).Error("Invalid rule data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule data"})
		return
	}
	
	if err := h.service.CreateTipRule(&rule); err != nil {
		h.logger.WithError(err).Error("Failed to create rule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create rule"})
		return
	}
	
	c.JSON(http.StatusCreated, rule)
}

// UpdateTipRule handles PUT /tips/rules/:id
func (h *TipsHandler) UpdateTipRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid rule ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}
	
	var rule models.TipRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		h.logger.WithError(err).Error("Invalid rule data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule data"})
		return
	}
	
	rule.ID = uint(id)
	if err := h.service.UpdateTipRule(&rule); err != nil {
		h.logger.WithError(err).Error("Failed to update rule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update rule"})
		return
	}
	
	c.JSON(http.StatusOK, rule)
}

// DeleteTipRule handles DELETE /tips/rules/:id
func (h *TipsHandler) DeleteTipRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid rule ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}
	
	if err := h.service.DeleteTipRule(uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete rule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete rule"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Rule deleted successfully"})
}

// RecordTipView handles POST /tips/view/:id
func (h *TipsHandler) RecordTipView(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	if err := h.service.RecordTipView(userID.(uint), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to record tip view")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record tip view"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Tip view recorded successfully"})
}

// RecordTipDismiss handles POST /tips/dismiss/:id
func (h *TipsHandler) RecordTipDismiss(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	if err := h.service.RecordTipDismiss(userID.(uint), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to record tip dismiss")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record tip dismiss"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Tip dismiss recorded successfully"})
}

// RecordTipClick handles POST /tips/click/:id
func (h *TipsHandler) RecordTipClick(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	if err := h.service.RecordTipClick(userID.(uint), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to record tip click")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record tip click"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Tip click recorded successfully"})
}

// SubmitTipFeedback handles POST /tips/feedback
func (h *TipsHandler) SubmitTipFeedback(c *gin.Context) {
	var feedback models.TipFeedback
	if err := c.ShouldBindJSON(&feedback); err != nil {
		h.logger.WithError(err).Error("Invalid feedback data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid feedback data"})
		return
	}
	
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	feedback.UserID = userID.(uint)
	if err := h.service.SubmitTipFeedback(&feedback); err != nil {
		h.logger.WithError(err).Error("Failed to submit feedback")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to submit feedback"})
		return
	}
	
	c.JSON(http.StatusCreated, feedback)
}

// GetTipStatistics handles GET /tips/statistics/:id
func (h *TipsHandler) GetTipStatistics(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tip ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tip ID"})
		return
	}
	
	stats, err := h.service.GetTipStatistics(uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tip statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tip statistics"})
		return
	}
	
	c.JSON(http.StatusOK, stats)
}

// GetAllTipStatistics handles GET /tips/statistics
func (h *TipsHandler) GetAllTipStatistics(c *gin.Context) {
	stats, err := h.service.GetAllTipStatistics()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get all tip statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tip statistics"})
		return
	}
	
	c.JSON(http.StatusOK, stats)
}

// GetContextualTips handles POST /tips/contextual
func (h *TipsHandler) GetContextualTips(c *gin.Context) {
	var request models.TipRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid request data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}
	
	// Get user ID from context if authenticated
	userID, exists := c.Get("userID")
	if exists {
		request.UserID = userID.(uint)
	}
	
	tips, err := h.service.GetContextualTips(&request)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get contextual tips")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get contextual tips"})
		return
	}
	
	c.JSON(http.StatusOK, models.TipResponse{Tips: tips})
}
