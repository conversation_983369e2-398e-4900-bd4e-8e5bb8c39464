# TikTok-Style Live Streaming Gifting System

This document provides an overview of the TikTok-Style Live Streaming Gifting System implemented for the Great Nigeria Library platform.

## Overview

The Live Streaming Gifting System allows users to:
- Create and manage live streams
- Purchase virtual currency (coins)
- Send virtual gifts during live streams
- Track gifter rankings and leaderboards
- Monetize content through gift revenue

## System Components

### 1. Virtual Currency Economy

The virtual currency system provides a digital economy for the platform:

- **Coin Packages**: Users can purchase coin packages with different denominations and bonus structures
- **Virtual Wallet**: Each user has a wallet to store and manage their coins
- **Transaction History**: Complete record of all coin purchases, gifts sent, and gifts received
- **Volume Discounts**: Larger coin packages offer better value with bonus coins

### 2. Real-time Gifting Infrastructure

The real-time gifting system enables interactive engagement during live streams:

- **WebSocket Communication**: Real-time gift delivery and display
- **Gift Animation Rendering**: Visual representation of gifts with animations
- **Combo/Streak Visualization**: Special effects for consecutive gifts
- **High-Volume Event Handling**: Optimized for peak traffic during popular streams

### 3. Gifter Recognition and Ranking

The ranking system recognizes and rewards generous gifters:

- **Real-time Leaderboards**: Live updating leaderboards during streams
- **Timeframe-based Leaderboards**: Daily, weekly, monthly, and all-time rankings
- **Gifter Rank Badges**: Visual indicators of gifting status (bronze, silver, gold, platinum, diamond)
- **Recognition Notifications**: Special acknowledgments for top gifters

### 4. Creator Monetization Tools

The monetization system allows content creators to earn revenue:

- **Creator Analytics Dashboard**: Detailed insights into gift revenue
- **Revenue Share Calculation**: 70% of gift value goes to creators, 30% to platform
- **Payout Processing**: Secure withdrawal of earnings to Nigerian bank accounts
- **Creator Incentives**: Special promotions and bonuses for top creators

### 5. Anti-fraud and Safety Measures

The security system protects the integrity of the gifting economy:

- **Transaction Security**: Secure payment processing and coin management
- **Suspicious Pattern Detection**: Algorithms to detect unusual gifting patterns
- **Spending Limits**: Configurable limits to prevent excessive spending
- **Dispute Resolution**: Process for handling transaction disputes

## Technical Architecture

### Microservice Components

- **LiveStream Service**: Manages stream creation, viewing, and lifecycle
- **VirtualCurrency Service**: Handles coin purchases and balance management
- **Gift Service**: Processes gift transactions and animations
- **Ranking Service**: Calculates and maintains leaderboards
- **Revenue Service**: Manages creator earnings and payouts
- **WebSocket Hub**: Enables real-time communication

### Database Schema

The system uses the following database tables:

- `virtual_currencies`: User coin balances
- `virtual_currency_transactions`: Transaction history
- `coin_packages`: Available coin packages for purchase
- `live_streams`: Stream metadata and status
- `live_stream_viewers`: Stream viewership tracking
- `live_stream_gifts`: Gift transaction records
- `gifter_rankings`: Leaderboard rankings
- `creator_revenues`: Creator earnings records
- `withdrawal_requests`: Payout requests
- `fraud_detection_logs`: Security monitoring

### API Endpoints

The system exposes the following API endpoints:

#### Virtual Currency
- `GET /api/currency/balance/:userId`: Get user balance
- `POST /api/currency/purchase`: Purchase coins
- `GET /api/currency/transactions/:userId`: Get transaction history
- `GET /api/currency/packages`: Get available coin packages

#### Live Streaming
- `GET /api/streams/`: Get active streams
- `GET /api/streams/:streamId`: Get stream details
- `POST /api/streams/`: Create a stream
- `PUT /api/streams/:streamId`: Update a stream
- `DELETE /api/streams/:streamId`: End a stream
- `GET /api/streams/:streamId/viewers`: Get stream viewers

#### Gifting
- `POST /api/gifts/send`: Send a gift
- `GET /api/gifts/stream/:streamId`: Get gifts for a stream
- `GET /api/gifts/user/:userId/sent`: Get gifts sent by a user
- `GET /api/gifts/user/:userId/received`: Get gifts received by a user

#### Rankings
- `GET /api/rankings/stream/:streamId`: Get rankings for a stream
- `GET /api/rankings/global`: Get global rankings
- `GET /api/rankings/user/:userId`: Get a user's ranking

#### Revenue
- `GET /api/revenue/creator/:userId`: Get creator revenue
- `GET /api/revenue/summary/:userId`: Get revenue summary
- `POST /api/revenue/withdraw`: Request a withdrawal

#### WebSocket
- `GET /api/ws`: WebSocket connection for real-time events

## Getting Started

### Running the Service

To start the Live Streaming service:

```powershell
./scripts/run_services.ps1 -Action start -Service livestream
```

### Testing the API

You can test the API endpoints using tools like Postman or curl:

```bash
# Get active streams
curl -X GET http://localhost:5000/api/streams/

# Get coin packages
curl -X GET http://localhost:5000/api/currency/packages
```

### WebSocket Testing

To test WebSocket functionality, you can use a WebSocket client:

```javascript
const socket = new WebSocket('ws://localhost:5000/api/ws?userId=1&streamId=1');

socket.onopen = () => {
  console.log('Connected to WebSocket');
  
  // Send a message
  socket.send(JSON.stringify({
    type: 'gift',
    streamId: 1,
    content: {
      giftId: 1,
      amount: 100
    }
  }));
};

socket.onmessage = (event) => {
  console.log('Received:', event.data);
};
```

## Future Enhancements

- **Advanced Gift Effects**: More sophisticated animations and effects
- **Gift Customization**: Allow users to customize gift appearances
- **Group Gifting**: Enable collaborative gifting by multiple users
- **Gift Challenges**: Time-limited gifting challenges with special rewards
- **AI-powered Fraud Detection**: Enhanced security with machine learning
- **Multi-platform Support**: Extend to mobile apps and smart TVs
