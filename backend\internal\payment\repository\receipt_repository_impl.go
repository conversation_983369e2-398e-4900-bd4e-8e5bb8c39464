package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/payment/models"
	"gorm.io/gorm"
)

// ReceiptRepositoryImpl implements the ReceiptRepository interface
type ReceiptRepositoryImpl struct {
	db *gorm.DB
}

// NewReceiptRepository creates a new receipt repository
func NewReceiptRepository(db *gorm.DB) ReceiptRepository {
	return &ReceiptRepositoryImpl{
		db: db,
	}
}

// CreateReceipt creates a new receipt in the database
func (r *ReceiptRepositoryImpl) CreateReceipt(ctx context.Context, receipt *models.Receipt) error {
	return r.db.WithContext(ctx).Create(receipt).Error
}

// GetReceiptByID retrieves a receipt by its ID
func (r *ReceiptRepositoryImpl) GetReceiptByID(ctx context.Context, id uint) (*models.Receipt, error) {
	var receipt models.Receipt
	if err := r.db.WithContext(ctx).First(&receipt, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt: %w", err)
	}
	return &receipt, nil
}

// GetReceiptByNumber retrieves a receipt by its receipt number
func (r *ReceiptRepositoryImpl) GetReceiptByNumber(ctx context.Context, receiptNumber string) (*models.Receipt, error) {
	var receipt models.Receipt
	if err := r.db.WithContext(ctx).Where("receipt_number = ?", receiptNumber).First(&receipt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt: %w", err)
	}
	return &receipt, nil
}

// GetReceiptsByUserID retrieves all receipts for a user
func (r *ReceiptRepositoryImpl) GetReceiptsByUserID(ctx context.Context, userID uint, limit, offset int) ([]*models.Receipt, int64, error) {
	var receipts []*models.Receipt
	var count int64

	query := r.db.WithContext(ctx).Model(&models.Receipt{}).Where("user_id = ?", userID)
	
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, fmt.Errorf("error counting receipts: %w", err)
	}
	
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&receipts).Error; err != nil {
		return nil, 0, fmt.Errorf("error getting receipts: %w", err)
	}
	
	return receipts, count, nil
}

// GetReceiptByPaymentID retrieves a receipt for a payment
func (r *ReceiptRepositoryImpl) GetReceiptByPaymentID(ctx context.Context, paymentID uint) (*models.Receipt, error) {
	var receipt models.Receipt
	if err := r.db.WithContext(ctx).Where("payment_id = ?", paymentID).First(&receipt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt: %w", err)
	}
	return &receipt, nil
}

// GetReceiptBySubscriptionID retrieves a receipt for a subscription payment
func (r *ReceiptRepositoryImpl) GetReceiptBySubscriptionID(ctx context.Context, subscriptionID uint) (*models.Receipt, error) {
	var receipt models.Receipt
	if err := r.db.WithContext(ctx).Where("subscription_id = ?", subscriptionID).First(&receipt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt: %w", err)
	}
	return &receipt, nil
}

// GetReceiptByRefundID retrieves a receipt for a refund
func (r *ReceiptRepositoryImpl) GetReceiptByRefundID(ctx context.Context, refundID uint) (*models.Receipt, error) {
	var receipt models.Receipt
	if err := r.db.WithContext(ctx).Where("refund_id = ?", refundID).First(&receipt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt: %w", err)
	}
	return &receipt, nil
}

// UpdateReceiptStatus updates the status of a receipt
func (r *ReceiptRepositoryImpl) UpdateReceiptStatus(ctx context.Context, id uint, status models.ReceiptStatus) error {
	return r.db.WithContext(ctx).Model(&models.Receipt{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":      status,
			"updated_at":  time.Now(),
			"generated_at": status == models.ReceiptStatusGenerated ? time.Now() : nil,
		}).Error
}

// UpdateReceiptPDFInfo updates the PDF path and hash
func (r *ReceiptRepositoryImpl) UpdateReceiptPDFInfo(ctx context.Context, id uint, pdfPath, publicURL, contentHash string) error {
	return r.db.WithContext(ctx).Model(&models.Receipt{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"pdf_path":     pdfPath,
			"public_url":   publicURL,
			"content_hash": contentHash,
			"updated_at":   time.Now(),
		}).Error
}

// UpdateReceiptEmailInfo updates the emailed information
func (r *ReceiptRepositoryImpl) UpdateReceiptEmailInfo(ctx context.Context, id uint, emailedTo string) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.Receipt{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"emailed_to": emailedTo,
			"emailed_at": now,
			"status":     models.ReceiptStatusDelivered,
			"updated_at": now,
		}).Error
}

// DeleteReceipt deletes a receipt by its ID
func (r *ReceiptRepositoryImpl) DeleteReceipt(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Receipt{}, id).Error
}

// CreateReceiptTemplate creates a new receipt template
func (r *ReceiptRepositoryImpl) CreateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// GetReceiptTemplateByID retrieves a receipt template by its ID
func (r *ReceiptRepositoryImpl) GetReceiptTemplateByID(ctx context.Context, id uint) (*models.ReceiptTemplate, error) {
	var template models.ReceiptTemplate
	if err := r.db.WithContext(ctx).First(&template, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt template not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt template: %w", err)
	}
	return &template, nil
}

// GetDefaultReceiptTemplate retrieves the default receipt template
func (r *ReceiptRepositoryImpl) GetDefaultReceiptTemplate(ctx context.Context) (*models.ReceiptTemplate, error) {
	var template models.ReceiptTemplate
	if err := r.db.WithContext(ctx).Where("is_default = ? AND is_active = ?", true, true).First(&template).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("default receipt template not found: %w", err)
		}
		return nil, fmt.Errorf("error getting default receipt template: %w", err)
	}
	return &template, nil
}

// UpdateReceiptTemplate updates a receipt template
func (r *ReceiptRepositoryImpl) UpdateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error {
	return r.db.WithContext(ctx).Save(template).Error
}

// DeleteReceiptTemplate deletes a receipt template by its ID
func (r *ReceiptRepositoryImpl) DeleteReceiptTemplate(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ReceiptTemplate{}, id).Error
}

// CreateReceiptCustomization creates a new receipt customization
func (r *ReceiptRepositoryImpl) CreateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error {
	return r.db.WithContext(ctx).Create(customization).Error
}

// GetReceiptCustomizationByReceiptID retrieves a receipt customization by its receipt ID
func (r *ReceiptRepositoryImpl) GetReceiptCustomizationByReceiptID(ctx context.Context, receiptID uint) (*models.ReceiptCustomization, error) {
	var customization models.ReceiptCustomization
	if err := r.db.WithContext(ctx).Where("receipt_id = ?", receiptID).First(&customization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("receipt customization not found: %w", err)
		}
		return nil, fmt.Errorf("error getting receipt customization: %w", err)
	}
	return &customization, nil
}

// UpdateReceiptCustomization updates a receipt customization
func (r *ReceiptRepositoryImpl) UpdateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error {
	return r.db.WithContext(ctx).Save(customization).Error
}

// DeleteReceiptCustomization deletes a receipt customization by its ID
func (r *ReceiptRepositoryImpl) DeleteReceiptCustomization(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ReceiptCustomization{}, id).Error
}