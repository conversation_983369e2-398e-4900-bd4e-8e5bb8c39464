import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  Container,
  Paper,
  TextField,
  Typography,
  Divider,
  Grid,
  MenuItem,
  IconButton,
  Card,
  CardContent,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { AppDispatch } from '../store';
import {
  fetchPersonalizedPathWithItems,
  updatePersonalizedPath,
  addPathItem,
  updatePathItem,
  deletePathItem,
  selectCurrentPath,
  selectCurrentPathItems,
  selectPersonalizationLoading,
  selectPersonalizationError,
} from '../features/personalization/personalizationSlice';
import { PathItem } from '../api/personalizationService';

const EditPersonalizedPathPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const pathId = parseInt(id || '0');
  
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  
  const currentPath = useSelector(selectCurrentPath);
  const currentItems = useSelector(selectCurrentPathItems);
  const loading = useSelector(selectPersonalizationLoading);
  const error = useSelector(selectPersonalizationError);
  
  const [path, setPath] = useState({
    name: '',
    description: '',
  });
  
  const [items, setItems] = useState<PathItem[]>([]);
  const [newItems, setNewItems] = useState<Omit<PathItem, 'id' | 'pathId'>[]>([]);
  const [itemsToDelete, setItemsToDelete] = useState<number[]>([]);
  
  useEffect(() => {
    if (pathId) {
      dispatch(fetchPersonalizedPathWithItems(pathId));
    }
  }, [dispatch, pathId]);
  
  useEffect(() => {
    if (currentPath) {
      setPath({
        name: currentPath.name,
        description: currentPath.description,
      });
    }
  }, [currentPath]);
  
  useEffect(() => {
    if (currentItems) {
      setItems(currentItems);
    }
  }, [currentItems]);
  
  const handlePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPath({
      ...path,
      [name]: value,
    });
  };
  
  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newItemsArray = [...items];
    
    if (name === 'itemId' || name === 'order' || name === 'estimatedDuration') {
      newItemsArray[index] = {
        ...newItemsArray[index],
        [name]: parseInt(value) || 0,
      };
    } else {
      newItemsArray[index] = {
        ...newItemsArray[index],
        [name]: value,
      };
    }
    
    setItems(newItemsArray);
  };
  
  const handleNewItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newItemsArray = [...newItems];
    
    if (name === 'itemId' || name === 'order' || name === 'estimatedDuration') {
      newItemsArray[index] = {
        ...newItemsArray[index],
        [name]: parseInt(value) || 0,
      };
    } else {
      newItemsArray[index] = {
        ...newItemsArray[index],
        [name]: value,
      };
    }
    
    setNewItems(newItemsArray);
  };
  
  const handleAddNewItem = () => {
    setNewItems([
      ...newItems,
      {
        itemType: 'book',
        itemId: 0,
        title: '',
        description: '',
        order: items.length + newItems.length + 1,
        isCompleted: false,
        estimatedDuration: 30,
      },
    ]);
  };
  
  const handleRemoveItem = (index: number) => {
    const item = items[index];
    if (item.id) {
      setItemsToDelete([...itemsToDelete, item.id]);
    }
    
    const newItemsArray = [...items];
    newItemsArray.splice(index, 1);
    
    // Update order for remaining items
    newItemsArray.forEach((item, i) => {
      item.order = i + 1;
    });
    
    setItems(newItemsArray);
  };
  
  const handleRemoveNewItem = (index: number) => {
    const newItemsArray = [...newItems];
    newItemsArray.splice(index, 1);
    
    // Update order for remaining items
    const totalExistingItems = items.length;
    newItemsArray.forEach((item, i) => {
      item.order = totalExistingItems + i + 1;
    });
    
    setNewItems(newItemsArray);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!path.name) {
      alert('Please enter a path name');
      return;
    }
    
    if (items.length === 0 && newItems.length === 0) {
      alert('Please add at least one item to the path');
      return;
    }
    
    try {
      // Update path
      if (currentPath) {
        await dispatch(
          updatePersonalizedPath({
            pathId,
            path: {
              ...currentPath,
              name: path.name,
              description: path.description,
            },
          })
        ).unwrap();
      }
      
      // Update existing items
      for (const item of items) {
        await dispatch(
          updatePathItem({
            itemId: item.id!,
            item,
          })
        ).unwrap();
      }
      
      // Add new items
      for (const item of newItems) {
        await dispatch(
          addPathItem({
            pathId,
            item,
          })
        ).unwrap();
      }
      
      // Delete items
      for (const itemId of itemsToDelete) {
        await dispatch(deletePathItem(itemId)).unwrap();
      }
      
      navigate('/personalized-paths');
    } catch (err) {
      console.error('Failed to update path:', err);
    }
  };
  
  const handleCancel = () => {
    navigate('/personalized-paths');
  };
  
  if (loading && !currentPath) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }
  
  if (!currentPath && !loading) {
    return (
      <Container maxWidth="md">
        <Box py={4}>
          <Alert severity="error">Path not found</Alert>
          <Box mt={2}>
            <Button variant="contained" onClick={() => navigate('/personalized-paths')}>
              Back to Paths
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="md">
      <Box py={4}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Edit Personalized Learning Path
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  required
                  label="Path Name"
                  name="name"
                  value={path.name}
                  onChange={handlePathChange}
                  disabled={loading}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={path.description}
                  onChange={handlePathChange}
                  multiline
                  rows={3}
                  disabled={loading}
                />
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 4 }} />
            
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h5">Path Items</Typography>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={handleAddNewItem}
                disabled={loading}
              >
                Add Item
              </Button>
            </Box>
            
            {/* Existing Items */}
            {items.map((item, index) => (
              <Card key={`existing-${item.id}`} sx={{ mb: 3 }}>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Title"
                        name="title"
                        value={item.title}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        select
                        label="Item Type"
                        name="itemType"
                        value={item.itemType}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      >
                        <MenuItem value="book">Book</MenuItem>
                        <MenuItem value="video">Video</MenuItem>
                        <MenuItem value="course">Course</MenuItem>
                        <MenuItem value="tutorial">Tutorial</MenuItem>
                      </TextField>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Item ID"
                        name="itemId"
                        type="number"
                        value={item.itemId}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Estimated Duration (minutes)"
                        name="estimatedDuration"
                        type="number"
                        value={item.estimatedDuration}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={item.description}
                        onChange={(e) => handleItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        multiline
                        rows={2}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} display="flex" justifyContent="flex-end">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={loading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            ))}
            
            {/* New Items */}
            {newItems.map((item, index) => (
              <Card key={`new-${index}`} sx={{ mb: 3, bgcolor: 'action.hover' }}>
                <CardContent>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="primary">
                      New Item
                    </Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Title"
                        name="title"
                        value={item.title}
                        onChange={(e) => handleNewItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        select
                        label="Item Type"
                        name="itemType"
                        value={item.itemType}
                        onChange={(e) => handleNewItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      >
                        <MenuItem value="book">Book</MenuItem>
                        <MenuItem value="video">Video</MenuItem>
                        <MenuItem value="course">Course</MenuItem>
                        <MenuItem value="tutorial">Tutorial</MenuItem>
                      </TextField>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Item ID"
                        name="itemId"
                        type="number"
                        value={item.itemId}
                        onChange={(e) => handleNewItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Estimated Duration (minutes)"
                        name="estimatedDuration"
                        type="number"
                        value={item.estimatedDuration}
                        onChange={(e) => handleNewItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={item.description}
                        onChange={(e) => handleNewItemChange(index, e as React.ChangeEvent<HTMLInputElement>)}
                        multiline
                        rows={2}
                        disabled={loading}
                      />
                    </Grid>
                    
                    <Grid item xs={12} display="flex" justifyContent="flex-end">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveNewItem(index)}
                        disabled={loading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            ))}
            
            <Box display="flex" justifyContent="space-between" mt={4}>
              <Button variant="outlined" onClick={handleCancel} disabled={loading}>
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </Box>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default EditPersonalizedPathPage;
