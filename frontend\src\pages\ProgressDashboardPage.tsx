import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  Tabs,
  Tab,
  CircularProgress,
  LinearProgress,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  Celebration as CelebrationIcon,
  TrendingUp as TrendingUpIcon,
  History as HistoryIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Share as ShareIcon
} from '@mui/icons-material';

// Import progress actions and selectors
import {
  fetchUserProgress,
  fetchMilestones,
  fetchAchievements,
  fetchHistoricalData,
  fetchSkillsData,
  selectUserProgress,
  selectMilestones,
  selectAchievements,
  selectHistoricalData,
  selectSkillsData,
  selectLoading
} from '../features/progress/progressSlice';

// Mock data for development
const mockUserProgress = {
  overallCompletion: 68,
  pointsEarned: 1250,
  streak: 15,
  level: 7,
  recentActivities: [
    { id: 1, type: 'course', name: 'Introduction to Leadership', progress: 100, date: '2023-05-15' },
    { id: 2, type: 'book', name: 'Building a Great Nigeria', progress: 75, date: '2023-05-14' },
    { id: 3, type: 'quiz', name: 'Community Development Quiz', progress: 90, date: '2023-05-12' },
    { id: 4, type: 'project', name: 'Local Initiative Planning', progress: 40, date: '2023-05-10' }
  ]
};

const mockMilestones = [
  { id: 1, name: 'Getting Started', description: 'Complete your profile and first course', completed: true, date: '2023-04-10', icon: 'CheckCircle' },
  { id: 2, name: 'Knowledge Builder', description: 'Complete 5 courses', completed: true, date: '2023-04-25', icon: 'School' },
  { id: 3, name: 'Consistent Learner', description: 'Maintain a 10-day streak', completed: true, date: '2023-05-05', icon: 'TrendingUp' },
  { id: 4, name: 'Community Contributor', description: 'Participate in 3 forum discussions', completed: false, progress: 66, icon: 'Group' },
  { id: 5, name: 'Project Initiator', description: 'Start your first community project', completed: false, progress: 20, icon: 'Assignment' }
];

const mockAchievements = [
  { id: 1, name: 'Fast Learner', description: 'Complete a course in record time', earned: true, date: '2023-04-15', icon: 'Speed' },
  { id: 2, name: 'Knowledge Seeker', description: 'Read 3 books', earned: true, date: '2023-04-30', icon: 'MenuBook' },
  { id: 3, name: 'Perfect Score', description: 'Get 100% on a quiz', earned: true, date: '2023-05-02', icon: 'Grade' },
  { id: 4, name: 'Helpful Hand', description: 'Answer 10 questions in the forum', earned: false, progress: 70, icon: 'Help' },
  { id: 5, name: 'Thought Leader', description: 'Have a post featured by moderators', earned: false, progress: 0, icon: 'Lightbulb' }
];

const mockHistoricalData = [
  { month: 'Jan', progress: 10, activities: 5 },
  { month: 'Feb', progress: 25, activities: 12 },
  { month: 'Mar', progress: 40, activities: 18 },
  { month: 'Apr', progress: 55, activities: 25 },
  { month: 'May', progress: 68, activities: 32 }
];

const mockSkillsData = [
  { name: 'Leadership', value: 85 },
  { name: 'Community Building', value: 70 },
  { name: 'Project Management', value: 60 },
  { name: 'Communication', value: 90 },
  { name: 'Problem Solving', value: 75 }
];

// Define tab panel interface
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab Panel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`progress-tabpanel-${index}`}
      aria-labelledby={`progress-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Tab props
function a11yProps(index: number) {
  return {
    id: `progress-tab-${index}`,
    'aria-controls': `progress-tabpanel-${index}`,
  };
}

// Animation component for milestone achievement
const MilestoneAnimation = ({ milestone }) => {
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    // Trigger animation when component mounts
    setAnimate(true);

    // Reset animation after it completes
    const timer = setTimeout(() => {
      setAnimate(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Box
      sx={{
        position: 'relative',
        transition: 'all 0.5s ease-in-out',
        transform: animate ? 'scale(1.1)' : 'scale(1)',
        animation: animate ? 'pulse 2s infinite' : 'none',
        '@keyframes pulse': {
          '0%': { boxShadow: '0 0 0 0 rgba(255, 215, 0, 0.7)' },
          '70%': { boxShadow: '0 0 0 10px rgba(255, 215, 0, 0)' },
          '100%': { boxShadow: '0 0 0 0 rgba(255, 215, 0, 0)' }
        }
      }}
    >
      <Card
        sx={{
          bgcolor: animate ? 'gold' : 'background.paper',
          color: animate ? 'common.black' : 'text.primary',
          transition: 'all 0.5s ease-in-out'
        }}
      >
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="center" flexDirection="column">
            <CelebrationIcon
              sx={{
                fontSize: 48,
                color: animate ? 'common.black' : 'primary.main',
                animation: animate ? 'spin 2s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Typography variant="h6" align="center" gutterBottom>
              {milestone.name}
            </Typography>
            <Typography variant="body2" align="center">
              {milestone.description}
            </Typography>
            {animate && (
              <Typography
                variant="h5"
                align="center"
                sx={{
                  mt: 2,
                  fontWeight: 'bold',
                  animation: 'fadeIn 1s ease-in',
                  '@keyframes fadeIn': {
                    '0%': { opacity: 0 },
                    '100%': { opacity: 1 }
                  }
                }}
              >
                Achievement Unlocked!
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

const ProgressDashboardPage: React.FC = () => {
  const dispatch = useDispatch();
  const userProgress = useSelector(selectUserProgress);
  const milestones = useSelector(selectMilestones);
  const achievements = useSelector(selectAchievements);
  const historicalData = useSelector(selectHistoricalData);
  const skillsData = useSelector(selectSkillsData);
  const loading = useSelector(selectLoading);

  // Use mock data as fallback if API data is not available
  const effectiveUserProgress = userProgress || mockUserProgress;
  const effectiveMilestones = milestones.length > 0 ? milestones : mockMilestones;
  const effectiveAchievements = achievements.length > 0 ? achievements : mockAchievements;
  const effectiveHistoricalData = historicalData.length > 0 ? historicalData : mockHistoricalData;
  const effectiveSkillsData = skillsData.length > 0 ? skillsData : mockSkillsData;

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for selected milestone for animation
  const [selectedMilestone, setSelectedMilestone] = useState(null);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchUserProgress());
    dispatch(fetchMilestones());
    dispatch(fetchAchievements());
    dispatch(fetchHistoricalData());
    dispatch(fetchSkillsData());

    // Simulate a milestone being achieved
    const timer = setTimeout(() => {
      if (effectiveMilestones.length > 2) {
        setSelectedMilestone(effectiveMilestones[2]);
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [dispatch]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Your Learning Progress
      </Typography>

      {loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Overall Progress Summary */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 3,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  height: '100%'
                }}
              >
                <Typography variant="h6" gutterBottom>Overall Progress</Typography>
                <Box
                  sx={{
                    position: 'relative',
                    display: 'inline-flex',
                    my: 2
                  }}
                >
                  <CircularProgress
                    variant="determinate"
                    value={effectiveUserProgress.overallCompletion}
                    size={160}
                    thickness={5}
                    sx={{ color: 'primary.main' }}
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography
                      variant="h4"
                      component="div"
                      color="text.primary"
                    >{`${effectiveUserProgress.overallCompletion}%`}</Typography>
                  </Box>
                </Box>
                <Typography variant="body1" color="text.secondary">
                  Keep going! You're making great progress.
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" gutterBottom>Your Stats</Typography>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={6}>
                    <Box display="flex" alignItems="center">
                      <StarIcon color="primary" sx={{ mr: 1 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Points Earned</Typography>
                        <Typography variant="h6">{effectiveUserProgress.pointsEarned}</Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box display="flex" alignItems="center">
                      <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Current Streak</Typography>
                        <Typography variant="h6">{effectiveUserProgress.streak} days</Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box display="flex" alignItems="center">
                      <SchoolIcon color="primary" sx={{ mr: 1 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Current Level</Typography>
                        <Typography variant="h6">{effectiveUserProgress.level}</Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box display="flex" alignItems="center">
                      <TrophyIcon color="primary" sx={{ mr: 1 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Achievements</Typography>
                        <Typography variant="h6">
                          {effectiveAchievements.filter(a => a.earned).length}/{effectiveAchievements.length}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>Recent Activity</Typography>
                <Box sx={{ maxHeight: 150, overflow: 'auto' }}>
                  {effectiveUserProgress.recentActivities.map((activity) => (
                    <Box key={activity.id} sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                      <LinearProgress
                        variant="determinate"
                        value={activity.progress}
                        sx={{ flexGrow: 1, mr: 2 }}
                      />
                      <Typography variant="body2" sx={{ minWidth: 40 }}>
                        {activity.progress}%
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Milestone Achievement Animation */}
          {selectedMilestone && (
            <Box sx={{ mb: 4 }}>
              <MilestoneAnimation milestone={selectedMilestone} />
            </Box>
          )}

          {/* Tabs for different progress views */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="progress tabs">
              <Tab label="Milestones" {...a11yProps(0)} />
              <Tab label="Achievements" {...a11yProps(1)} />
              <Tab label="History" {...a11yProps(2)} />
              <Tab label="Skills" {...a11yProps(3)} />
            </Tabs>
          </Box>

          {/* Milestones Tab */}
          <TabPanel value={tabValue} index={0}>
            <Timeline position="alternate">
              {effectiveMilestones.map((milestone) => (
                <TimelineItem key={milestone.id}>
                  <TimelineOppositeContent color="text.secondary">
                    {milestone.completed ? milestone.date : 'In Progress'}
                  </TimelineOppositeContent>
                  <TimelineSeparator>
                    <TimelineDot
                      color={milestone.completed ? "success" : "grey"}
                      variant={milestone.completed ? "filled" : "outlined"}
                    >
                      {milestone.icon === 'CheckCircle' && <CheckCircleIcon />}
                      {milestone.icon === 'School' && <SchoolIcon />}
                      {milestone.icon === 'TrendingUp' && <TrendingUpIcon />}
                    </TimelineDot>
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Paper elevation={3} sx={{ p: 2 }}>
                      <Typography variant="h6" component="h3">
                        {milestone.name}
                      </Typography>
                      <Typography>{milestone.description}</Typography>
                      {!milestone.completed && milestone.progress && (
                        <Box sx={{ mt: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={milestone.progress}
                            sx={{ height: 8, borderRadius: 5 }}
                          />
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            {milestone.progress}% Complete
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </TabPanel>

          {/* Achievements Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              {effectiveAchievements.map((achievement) => (
                <Grid item xs={12} sm={6} md={4} key={achievement.id}>
                  <Card
                    sx={{
                      height: '100%',
                      opacity: achievement.earned ? 1 : 0.7,
                      filter: achievement.earned ? 'none' : 'grayscale(0.5)',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    <CardHeader
                      avatar={
                        <Avatar
                          sx={{
                            bgcolor: achievement.earned ? 'primary.main' : 'grey.500',
                          }}
                        >
                          <StarIcon />
                        </Avatar>
                      }
                      action={
                        <Tooltip title="Achievement details">
                          <IconButton aria-label="info">
                            <InfoIcon />
                          </IconButton>
                        </Tooltip>
                      }
                      title={achievement.name}
                      subheader={achievement.earned ? `Earned on ${achievement.date}` : 'Not yet earned'}
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        {achievement.description}
                      </Typography>

                      {!achievement.earned && achievement.progress > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <LinearProgress
                            variant="determinate"
                            value={achievement.progress}
                            sx={{ height: 8, borderRadius: 5 }}
                          />
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            {achievement.progress}% Progress
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          {/* History Tab */}
          <TabPanel value={tabValue} index={2}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>Progress Over Time</Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart
                  data={effectiveHistoricalData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="progress"
                    name="Overall Progress (%)"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="activities"
                    name="Activities Completed"
                    stroke="#82ca9d"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>

              <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Activity Breakdown</Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={effectiveHistoricalData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar
                      dataKey="activities"
                      name="Activities Completed"
                      fill="#8884d8"
                      animationDuration={1500}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </TabPanel>

          {/* Skills Tab */}
          <TabPanel value={tabValue} index={3}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>Skills Development</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={effectiveSkillsData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        animationDuration={1500}
                      >
                        {effectiveSkillsData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box>
                    {effectiveSkillsData.map((skill, index) => (
                      <Box key={skill.name} sx={{ mb: 2 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 0.5 }}>
                          <Typography variant="body1">{skill.name}</Typography>
                          <Typography variant="body2">{skill.value}%</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={skill.value}
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            bgcolor: 'grey.200',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: COLORS[index % COLORS.length],
                            }
                          }}
                        />
                      </Box>
                    ))}
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Recommended Skills to Develop</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Chip label="Public Speaking" color="primary" variant="outlined" />
                  <Chip label="Financial Management" color="primary" variant="outlined" />
                  <Chip label="Team Building" color="primary" variant="outlined" />
                  <Chip label="Strategic Planning" color="primary" variant="outlined" />
                  <Chip label="Conflict Resolution" color="primary" variant="outlined" />
                </Box>
              </Box>
            </Paper>
          </TabPanel>

          {/* Share Progress Button */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ShareIcon />}
              sx={{ borderRadius: 28, px: 3 }}
            >
              Share Your Progress
            </Button>
          </Box>
        </>
      )}
    </Container>
  );
};

export default ProgressDashboardPage;
