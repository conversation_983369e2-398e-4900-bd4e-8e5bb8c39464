package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/service"
	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/common/logger"
)

// VirtualCurrencyHandler handles HTTP requests for virtual currency operations
type VirtualCurrencyHandler struct {
	service service.VirtualCurrencyService
	logger  *logger.Logger
}

// NewVirtualCurrencyHandler creates a new instance of the virtual currency handler
func NewVirtualCurrencyHandler(service service.VirtualCurrencyService, logger *logger.Logger) *VirtualCurrencyHandler {
	return &VirtualCurrencyHandler{
		service: service,
		logger:  logger,
	}
}

// GetBalance retrieves a user's virtual currency balance
func (h *VirtualCurrencyHandler) GetBalance(c *gin.Context) {
	// Get user ID from URL parameter
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}
	
	// Get balance
	balance, err := h.service.GetUserBalance(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.Errorf("Failed to get balance for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get balance"})
		return
	}
	
	c.JSON(http.StatusOK, balance)
}

// PurchaseCoins processes a coin purchase
func (h *VirtualCurrencyHandler) PurchaseCoins(c *gin.Context) {
	// Get user ID from authenticated user
	userID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Parse request body
	var request struct {
		PackageID uint `json:"packageId" binding:"required"`
		PaymentID uint `json:"paymentId" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Process purchase
	err := h.service.PurchaseCoins(c.Request.Context(), userID.(uint), request.PackageID, request.PaymentID)
	if err != nil {
		h.logger.Errorf("Failed to purchase coins for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// Get updated balance
	balance, err := h.service.GetUserBalance(c.Request.Context(), userID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to get updated balance for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated balance"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Coins purchased successfully",
		"balance": balance,
	})
}

// GetTransactions retrieves a user's virtual currency transactions
func (h *VirtualCurrencyHandler) GetTransactions(c *gin.Context) {
	// Get user ID from URL parameter
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get transactions
	transactions, total, err := h.service.GetUserTransactions(c.Request.Context(), uint(userID), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get transactions for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get transactions"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetCoinPackages retrieves available coin packages
func (h *VirtualCurrencyHandler) GetCoinPackages(c *gin.Context) {
	// Get packages
	packages, err := h.service.GetCoinPackages(c.Request.Context())
	if err != nil {
		h.logger.Errorf("Failed to get coin packages: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get coin packages"})
		return
	}
	
	c.JSON(http.StatusOK, packages)
}
