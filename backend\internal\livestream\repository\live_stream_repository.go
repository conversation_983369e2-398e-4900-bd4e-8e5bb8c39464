package repository

import (
	"context"
	"errors"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/models"
	"gorm.io/gorm"
)

// LiveStream represents a live streaming session
type LiveStream struct {
	gorm.Model
	CreatorID       uint      `json:"creatorId" gorm:"index"`
	Title           string    `json:"title" gorm:"type:varchar(255)"`
	Description     string    `json:"description" gorm:"type:text"`
	ThumbnailURL    string    `json:"thumbnailUrl" gorm:"type:varchar(255)"`
	Status          string    `json:"status" gorm:"type:varchar(50);default:'scheduled'"` // scheduled, live, ended
	ScheduledStart  time.Time `json:"scheduledStart"`
	ActualStart     *time.Time `json:"actualStart"`
	EndTime         *time.Time `json:"endTime"`
	ViewerCount     int       `json:"viewerCount" gorm:"default:0"`
	PeakViewerCount int       `json:"peakViewerCount" gorm:"default:0"`
	TotalGiftsValue float64   `json:"totalGiftsValue" gorm:"default:0"`
	StreamKey       string    `json:"streamKey" gorm:"type:varchar(100);uniqueIndex"`
	PlaybackURL     string    `json:"playbackUrl" gorm:"type:varchar(255)"`
	IsPrivate       bool      `json:"isPrivate" gorm:"default:false"`
	AllowGifting    bool      `json:"allowGifting" gorm:"default:true"`
	Categories      string    `json:"categories" gorm:"type:varchar(255)"`
	Tags            string    `json:"tags" gorm:"type:varchar(255)"`
}

// LiveStreamViewer represents a viewer of a live stream
type LiveStreamViewer struct {
	gorm.Model
	StreamID  uint      `json:"streamId" gorm:"index:idx_stream_viewer"`
	UserID    uint      `json:"userId" gorm:"index:idx_stream_viewer"`
	JoinTime  time.Time `json:"joinTime"`
	LeaveTime *time.Time `json:"leaveTime"`
	Duration  int       `json:"duration" gorm:"default:0"` // in seconds
	IsActive  bool      `json:"isActive" gorm:"default:true"`
}

// LiveStreamRepository defines the interface for live stream data access
type LiveStreamRepository interface {
	// Stream CRUD operations
	CreateStream(ctx context.Context, stream *LiveStream) error
	GetStreamByID(ctx context.Context, id uint) (*LiveStream, error)
	UpdateStream(ctx context.Context, stream *LiveStream) error
	DeleteStream(ctx context.Context, id uint) error
	
	// Stream queries
	GetActiveStreams(ctx context.Context, page, limit int) ([]LiveStream, int, error)
	GetStreamsByCreator(ctx context.Context, creatorID uint, page, limit int) ([]LiveStream, int, error)
	GetScheduledStreams(ctx context.Context, page, limit int) ([]LiveStream, int, error)
	SearchStreams(ctx context.Context, query string, page, limit int) ([]LiveStream, int, error)
	
	// Stream status operations
	StartStream(ctx context.Context, id uint) error
	EndStream(ctx context.Context, id uint) error
	
	// Stream viewer operations
	AddViewer(ctx context.Context, streamID, userID uint) error
	RemoveViewer(ctx context.Context, streamID, userID uint) error
	GetStreamViewers(ctx context.Context, streamID uint, page, limit int) ([]LiveStreamViewer, int, error)
	GetViewerCount(ctx context.Context, streamID uint) (int, error)
	
	// Stream metrics operations
	UpdateStreamMetrics(ctx context.Context, streamID uint, viewerCount int, giftsValue float64) error
}

// LiveStreamRepositoryImpl implements the LiveStreamRepository interface
type LiveStreamRepositoryImpl struct {
	db *gorm.DB
}

// NewLiveStreamRepository creates a new instance of the live stream repository
func NewLiveStreamRepository(db *gorm.DB) LiveStreamRepository {
	return &LiveStreamRepositoryImpl{
		db: db,
	}
}

// CreateStream creates a new live stream
func (r *LiveStreamRepositoryImpl) CreateStream(ctx context.Context, stream *LiveStream) error {
	return r.db.WithContext(ctx).Create(stream).Error
}

// GetStreamByID retrieves a live stream by its ID
func (r *LiveStreamRepositoryImpl) GetStreamByID(ctx context.Context, id uint) (*LiveStream, error) {
	var stream LiveStream
	if err := r.db.WithContext(ctx).First(&stream, id).Error; err != nil {
		return nil, err
	}
	return &stream, nil
}

// UpdateStream updates a live stream
func (r *LiveStreamRepositoryImpl) UpdateStream(ctx context.Context, stream *LiveStream) error {
	return r.db.WithContext(ctx).Save(stream).Error
}

// DeleteStream deletes a live stream
func (r *LiveStreamRepositoryImpl) DeleteStream(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&LiveStream{}, id).Error
}

// GetActiveStreams retrieves all active (live) streams with pagination
func (r *LiveStreamRepositoryImpl) GetActiveStreams(ctx context.Context, page, limit int) ([]LiveStream, int, error) {
	var streams []LiveStream
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStream{}).Where("status = ?", "live").Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated streams
	if err := r.db.WithContext(ctx).
		Where("status = ?", "live").
		Order("actual_start DESC").
		Offset(offset).
		Limit(limit).
		Find(&streams).Error; err != nil {
		return nil, 0, err
	}
	
	return streams, int(count), nil
}

// GetStreamsByCreator retrieves streams by creator with pagination
func (r *LiveStreamRepositoryImpl) GetStreamsByCreator(ctx context.Context, creatorID uint, page, limit int) ([]LiveStream, int, error) {
	var streams []LiveStream
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStream{}).Where("creator_id = ?", creatorID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated streams
	if err := r.db.WithContext(ctx).
		Where("creator_id = ?", creatorID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&streams).Error; err != nil {
		return nil, 0, err
	}
	
	return streams, int(count), nil
}

// GetScheduledStreams retrieves scheduled streams with pagination
func (r *LiveStreamRepositoryImpl) GetScheduledStreams(ctx context.Context, page, limit int) ([]LiveStream, int, error) {
	var streams []LiveStream
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStream{}).Where("status = ?", "scheduled").Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated streams
	if err := r.db.WithContext(ctx).
		Where("status = ?", "scheduled").
		Order("scheduled_start ASC").
		Offset(offset).
		Limit(limit).
		Find(&streams).Error; err != nil {
		return nil, 0, err
	}
	
	return streams, int(count), nil
}

// SearchStreams searches for streams by title, description, or tags
func (r *LiveStreamRepositoryImpl) SearchStreams(ctx context.Context, query string, page, limit int) ([]LiveStream, int, error) {
	var streams []LiveStream
	var count int64
	
	// Prepare search query
	searchQuery := "%" + query + "%"
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStream{}).
		Where("title ILIKE ? OR description ILIKE ? OR categories ILIKE ? OR tags ILIKE ?", 
			searchQuery, searchQuery, searchQuery, searchQuery).
		Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated streams
	if err := r.db.WithContext(ctx).
		Where("title ILIKE ? OR description ILIKE ? OR categories ILIKE ? OR tags ILIKE ?", 
			searchQuery, searchQuery, searchQuery, searchQuery).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&streams).Error; err != nil {
		return nil, 0, err
	}
	
	return streams, int(count), nil
}

// StartStream marks a stream as live
func (r *LiveStreamRepositoryImpl) StartStream(ctx context.Context, id uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&LiveStream{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       "live",
			"actual_start": now,
		}).Error
}

// EndStream marks a stream as ended
func (r *LiveStreamRepositoryImpl) EndStream(ctx context.Context, id uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&LiveStream{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":    "ended",
			"end_time":  now,
		}).Error
}

// AddViewer adds a viewer to a stream
func (r *LiveStreamRepositoryImpl) AddViewer(ctx context.Context, streamID, userID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Check if viewer already exists
		var viewer LiveStreamViewer
		result := tx.WithContext(ctx).
			Where("stream_id = ? AND user_id = ?", streamID, userID).
			First(&viewer)
		
		now := time.Now()
		
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// Create new viewer record
				viewer = LiveStreamViewer{
					StreamID: streamID,
					UserID:   userID,
					JoinTime: now,
					IsActive: true,
				}
				if err := tx.Create(&viewer).Error; err != nil {
					return err
				}
			} else {
				return result.Error
			}
		} else {
			// Update existing viewer record
			if !viewer.IsActive {
				viewer.JoinTime = now
				viewer.IsActive = true
				viewer.LeaveTime = nil
				
				if err := tx.Save(&viewer).Error; err != nil {
					return err
				}
			}
		}
		
		// Update viewer count
		var count int64
		if err := tx.Model(&LiveStreamViewer{}).
			Where("stream_id = ? AND is_active = ?", streamID, true).
			Count(&count).Error; err != nil {
			return err
		}
		
		// Update stream viewer count
		var stream LiveStream
		if err := tx.First(&stream, streamID).Error; err != nil {
			return err
		}
		
		stream.ViewerCount = int(count)
		if stream.ViewerCount > stream.PeakViewerCount {
			stream.PeakViewerCount = stream.ViewerCount
		}
		
		return tx.Save(&stream).Error
	})
}

// RemoveViewer removes a viewer from a stream
func (r *LiveStreamRepositoryImpl) RemoveViewer(ctx context.Context, streamID, userID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Find viewer record
		var viewer LiveStreamViewer
		if err := tx.WithContext(ctx).
			Where("stream_id = ? AND user_id = ? AND is_active = ?", streamID, userID, true).
			First(&viewer).Error; err != nil {
			return err
		}
		
		// Update viewer record
		now := time.Now()
		viewer.LeaveTime = &now
		viewer.IsActive = false
		
		// Calculate duration
		duration := int(now.Sub(viewer.JoinTime).Seconds())
		viewer.Duration += duration
		
		if err := tx.Save(&viewer).Error; err != nil {
			return err
		}
		
		// Update viewer count
		var count int64
		if err := tx.Model(&LiveStreamViewer{}).
			Where("stream_id = ? AND is_active = ?", streamID, true).
			Count(&count).Error; err != nil {
			return err
		}
		
		// Update stream viewer count
		return tx.Model(&LiveStream{}).
			Where("id = ?", streamID).
			Update("viewer_count", count).Error
	})
}

// GetStreamViewers retrieves viewers of a stream with pagination
func (r *LiveStreamRepositoryImpl) GetStreamViewers(ctx context.Context, streamID uint, page, limit int) ([]LiveStreamViewer, int, error) {
	var viewers []LiveStreamViewer
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStreamViewer{}).
		Where("stream_id = ?", streamID).
		Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated viewers
	if err := r.db.WithContext(ctx).
		Where("stream_id = ?", streamID).
		Order("join_time DESC").
		Offset(offset).
		Limit(limit).
		Find(&viewers).Error; err != nil {
		return nil, 0, err
	}
	
	return viewers, int(count), nil
}

// GetViewerCount retrieves the current viewer count of a stream
func (r *LiveStreamRepositoryImpl) GetViewerCount(ctx context.Context, streamID uint) (int, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&LiveStreamViewer{}).
		Where("stream_id = ? AND is_active = ?", streamID, true).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

// UpdateStreamMetrics updates the metrics of a stream
func (r *LiveStreamRepositoryImpl) UpdateStreamMetrics(ctx context.Context, streamID uint, viewerCount int, giftsValue float64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var stream LiveStream
		if err := tx.First(&stream, streamID).Error; err != nil {
			return err
		}
		
		// Update viewer count
		stream.ViewerCount = viewerCount
		if viewerCount > stream.PeakViewerCount {
			stream.PeakViewerCount = viewerCount
		}
		
		// Update gifts value
		stream.TotalGiftsValue += giftsValue
		
		return tx.Save(&stream).Error
	})
}
