# Great Nigeria Platform - API Documentation

This directory contains comprehensive documentation for the Great Nigeria platform's API.

## Main Documentation Files

- [API_DOCUMENTATION_PART1.md](API_DOCUMENTATION_PART1.md) - Part 1 of the API documentation, covering overview, API gateway, authentication, error handling, and rate limiting
- [API_DOCUMENTATION_PART2.md](API_DOCUMENTATION_PART2.md) - Part 2 of the API documentation, covering core services (Auth, User, Content, Social)
- [API_DOCUMENTATION_PART3.md](API_DOCUMENTATION_PART3.md) - Part 3 of the API documentation, covering additional services, webhooks, versioning, and testing

## Overview

The Great Nigeria platform provides a comprehensive RESTful API that allows developers to interact with all aspects of the platform. The API is organized around microservices, each responsible for specific functionality.

### Key Features

- **RESTful Design**: Follows REST principles for intuitive resource-based URLs
- **JWT Authentication**: Secure authentication using JSON Web Tokens
- **Comprehensive Error Handling**: Consistent error responses with detailed information
- **Rate Limiting**: Tiered rate limits to ensure fair usage
- **Versioning**: URL-based versioning for API stability
- **Webhooks**: Event-based integration with external systems
- **Sandbox Environment**: Testing environment for integration development

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

## Core Services

The API is organized into several core services:

- **Auth Service**: User authentication, registration, and authorization
- **User Service**: User profiles, relationships, and settings
- **Content Service**: Book content, chapters, sections, and user progress
- **Social Service**: Social interactions, posts, and content sharing
- **Discussion Service**: Forum topics, discussions, and comments
- **Points Service**: Points-based reward system
- **Payment Service**: Payment processing, transactions, and subscriptions

## Additional Services

The platform also includes several specialized services:

- **Analytics Service**: User behavior tracking and content performance metrics
- **Chat Service**: Real-time messaging
- **Notification Service**: User notifications

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Details of the system architecture
- [Database Documentation](../database/) - Database schema and management
- [Code Analysis Documentation](../code/) - Analysis of the codebase
