# Great Nigeria Library - Architecture Documentation (Part 1)

This document consolidates information from multiple source files to provide a comprehensive guide on the architecture of the Great Nigeria Library project.

## Table of Contents

- [Overview](#overview)
- [Microservices Architecture](#microservices-architecture)
- [Key Components](#key-components)
- [Data Architecture](#data-architecture)
- [Deployment Architecture](#deployment-architecture)
- [Environment Configuration](#environment-configuration)
- [Security Architecture](#security-architecture)
- [Monitoring & Observability](#monitoring--observability)
- [Development Workflow](#development-workflow)
- [Migration Strategy](#migration-strategy)
- [Core Services](#core-services)
- [Shared Components](#shared-components)
- [Microservice Implementation](#microservice-implementation)
- [Data Storage](#data-storage)
- [API Design](#api-design)
- [Real-Time Features](#real-time-features)

## Overview

The Great Nigeria platform adopts a microservices architecture with Go as the primary backend language. This architecture provides better scalability, maintainability, and resilience. The platform is being redesigned as a modular, scalable system using Go microservices to support all the enhanced community and social features.

## Microservices Architecture

The Great Nigeria platform's architecture is structured as follows:

```
┌─────────────────────────────────────────────────────────────────┐
│                         Client Applications                      │
│    (React Web App, Mobile Web, Progressive Web App, Native)      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                            API Gateway                           │
│     (Authentication, Routing, Rate Limiting, Load Balancing)     │
└─┬──────────┬──────────┬──────────┬──────────┬──────────┬────────┘
  │          │          │          │          │          │
┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐
│  Auth  │ │ User   │ │Content │ │ Social │ │ Market │ │Payment │
│ Service│ │Service │ │Service │ │Service │ │Service │ │Service │
└────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘
  │          │          │          │          │          │
  │          │          │          │          │          │
┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐
│Analytics│ │  Chat  │ │Streaming│ │Rewards │ │ Search │ │Notif.  │
│Service │ │Service │ │Service  │ │Service │ │Service │ │Service │
└────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘
            │                                            │
┌───────────▼────────────────────────────────────────────▼──────┐
│                     Event Bus / Message Queue                  │
│               (NATS, RabbitMQ, or Google Pub/Sub)              │
└─────────────────────────────┬─────────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────────┐
│                         Data Storage                           │
│     (PostgreSQL, Redis, Object Storage, Time Series DB)        │
└─────────────────────────────────────────────────────────────────┘
```

## Key Components

### 1. API Gateway

The API Gateway serves as the entry point for all client requests, providing:
- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

```go
// Example API Gateway route configuration in Go/Gin
func setupRoutes(r *gin.Engine) {
    // Public routes
    r.GET("/api/health", HealthCheck)
    r.GET("/api/books", ForwardToContentService)
    r.GET("/api/books/:id", ForwardToContentService)
    
    // Authentication routes
    r.POST("/api/register", ForwardToAuthService)
    r.POST("/api/login", ForwardToAuthService)
    r.POST("/api/logout", ForwardToAuthService)
    
    // Protected routes - require authentication
    authorized := r.Group("/api")
    authorized.Use(AuthMiddleware())
    {
        authorized.GET("/user", ForwardToAuthService)
        authorized.GET("/user/progress", ForwardToContentService)
        authorized.POST("/discussions", ForwardToDiscussionService)
        authorized.GET("/points", ForwardToPointsService)
        authorized.POST("/payments", ForwardToPaymentService)
    }
}
```

### 2. Microservices

The application is divided into the following microservices:

#### Auth Service
- User registration and authentication
- JWT token generation and validation
- User profile management
- Session handling
- OAuth/social login integration
- Two-factor authentication
- Permission validation

**API Endpoints**:
- POST /auth/register
- POST /auth/login
- POST /auth/refresh-token
- POST /auth/password/reset
- POST /auth/logout
- GET /auth/oauth/{provider}
- POST /auth/2fa/enable
- POST /auth/2fa/validate

#### User Service
- User profile management
- User preferences and settings
- Friendship and follow relationships
- User activity tracking
- Feature toggle management
- Membership tiers
- Account management

**API Endpoints**:
- GET /users/{id}
- PATCH /users/{id}
- GET /users/{id}/profile
- GET /users/{id}/friends
- POST /users/{id}/friends/request
- GET /users/{id}/followers
- POST /users/{id}/follow
- GET /users/{id}/features
- PATCH /users/{id}/features/{featureId}

#### Content Service
- Book content management
- Chapter and section organization
- Content access control
- Progress tracking
- Bookmarks and reading history
- Media management
- Content versioning

**API Endpoints**:
- GET /books
- GET /books/{id}
- GET /books/{id}/chapters
- GET /books/{id}/chapters/{chapterId}
- GET /books/{id}/sections/{sectionId}
- POST /books/{id}/progress
- POST /books/{id}/bookmarks
- POST /books/{id}/notes

#### Discussion Service
- Forum topics and discussions
- Comments and replies
- Moderation capabilities
- Notification triggers

#### Points Service
- Points calculation and assignment
- User activity tracking
- Membership level management
- Achievement and badge system

#### Payment Service
- Payment processing (Paystack, Flutterwave, Squad)
- Subscription management
- Payment verification
- Receipt generation

#### Social Service
- Timeline and feed management
- Post creation and management
- Comments and reactions
- @mentions and #hashtags
- Content sharing
- Groups and pages
- Discover and trending

**API Endpoints**:
- GET /feed
- POST /posts
- GET /posts/{id}
- PATCH /posts/{id}
- DELETE /posts/{id}
- POST /posts/{id}/comments
- POST /posts/{id}/reactions
- GET /groups
- POST /groups
- GET /groups/{id}/posts

#### Market Service
- Marketplace listings
- Services marketplace
- Classifieds management
- Job board
- Event management
- Product/service transactions

#### Analytics Service
- User behavior tracking
- Content performance metrics
- Platform usage statistics
- Business intelligence
- Custom reports
- Real-time dashboards
- A/B testing

#### Chat Service
- Real-time messaging
- Group chat management
- Message history
- Media sharing in chats
- Read receipts
- Typing indicators
- Chat moderation

#### Streaming Service
- Live video streaming
- RTMP/WebRTC handling
- Stream recording
- Stream chat integration
- Virtual gifting in streams
- Stream discovery
- Analytics for streams

#### Rewards Service
- Points system management
- Achievements and badges
- Leaderboards
- Gifting system
- Creator monetization
- Loyalty programs
- Redemption management

#### Search Service
- Full-text search
- Faceted search
- User/content/group search
- Search suggestions
- Recommendations engine
- Discovery algorithms
- Relevance optimization

#### Notification Service
- In-app notifications
- Push notifications
- Email notifications
- SMS notifications
- Notification preferences
- Delivery tracking
- Batch processing

### 3. Common Packages

Shared functionality across services:

#### Database
- Connection management with retry logic
- GORM integration
- Migration utilities
- Transaction support

#### Middleware
- Authentication
- Logging
- Rate limiting
- CORS handling
- Request validation

#### Models
- Shared domain models
- Data transfer objects (DTOs)
- Validation schemas

#### Utils
- Logging
- Error handling
- Date/time utilities
- Security functions

## Data Architecture

### Database Design

PostgreSQL serves as the primary database with the following design principles:
- Normalized schema for relational data
- Optimized indexes for common query patterns
- JSON columns for flexible, schema-less data where appropriate
- Foreign key constraints for data integrity

### Cross-Service Communication

Services communicate with each other using:
1. **HTTP/REST**: For synchronous requests
2. **Message Queue (Optional)**: For asynchronous communication, using NATS or RabbitMQ

## Deployment Architecture

### Containerization

All services are containerized using Docker:
- Each microservice has its own Dockerfile
- Shared base image for common dependencies
- Multi-stage builds for optimized image size

### Orchestration

Kubernetes is recommended for production deployments:
- Service discovery and load balancing
- Horizontal scaling
- Self-healing capabilities
- Configuration management

### Simplified Deployment for Replit

For Replit deployment, a simplified setup is used:
- All services compiled to binaries
- Single entry point script to launch all services
- Environment-based configuration
- Logging to files for debugging

```bash
#!/bin/bash
# start.sh - Launches all Great Nigeria microservices

# Launch API Gateway
./bin/api-gateway &

# Launch services
./bin/auth-service &
./bin/content-service &
./bin/discussion-service &
./bin/points-service &
./bin/payment-service &

# Launch frontend
cd client && npm run build && npm run serve
```

## Environment Configuration

Configuration is managed through environment variables:
- Database connection string
- JWT secret
- API keys for payment providers
- Service ports and URLs
- Logging levels

Example `.env` file structure:
```
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/greatnigeria
DATABASE_MAX_CONNS=20
DATABASE_MAX_IDLE=5

# Security
JWT_SECRET=your-secret-key
JWT_EXPIRY=72h

# Services
AUTH_SERVICE_URL=http://localhost:8081
CONTENT_SERVICE_URL=http://localhost:8082
DISCUSSION_SERVICE_URL=http://localhost:8083
POINTS_SERVICE_URL=http://localhost:8084
PAYMENT_SERVICE_URL=http://localhost:8085

# Payment Providers
PAYSTACK_SECRET_KEY=sk_xxxx
PAYSTACK_PUBLIC_KEY=pk_xxxx
FLUTTERWAVE_SECRET_KEY=xxxx
FLUTTERWAVE_PUBLIC_KEY=xxxx
SQUAD_SECRET_KEY=xxxx
```

## Security Architecture

### Authentication & Authorization

- JWT-based authentication
- Role-based access control
- Secure password hashing (bcrypt)
- Two-factor authentication (optional)

### Data Security

- HTTPS for all communications
- Encryption of sensitive data
- Input validation and sanitization
- Protection against common attacks (CSRF, XSS, SQL Injection)

## Monitoring & Observability

- Structured logging (JSON format)
- Health check endpoints
- Metrics collection (Prometheus)
- Distributed tracing (optional, using OpenTelemetry)

## Development Workflow

- Git-based version control
- CI/CD pipeline
- Automated testing
- Code quality checks

## Migration Strategy

The transition from Node.js/Express to Go will follow these phases:

1. **Phase 1**: Implement core Go packages and basic service structure
2. **Phase 2**: Develop complete microservices with database integration
3. **Phase 3**: Create a proxy layer in Node.js/Express to route to Go services
4. **Phase 4**: Gradually shift traffic from Node.js to Go services
5. **Phase 5**: Complete removal of Node.js components

## Core Services

### API Gateway

**Responsibilities**:
- Unified API entry point
- Request routing to appropriate services
- Authentication validation
- Rate limiting and DDoS protection
- Request/response logging
- Cross-cutting concerns

**Technologies**:
- Go with Gin framework
- JWT validation
- Consul/etcd for service discovery
- Redis for rate limiting
- TLS termination

### Auth Service

**Responsibilities**:
- User registration and login
- JWT token generation and validation
- Password management
- OAuth/social login integration
- Two-factor authentication
- Session management
- Permission validation

### User Service

**Responsibilities**:
- User profile management
- User preferences and settings
- Friendship and follow relationships
- User activity tracking
- Feature toggle management
- Membership tiers
- Account management

### Content Service

**Responsibilities**:
- Book content management
- Chapter/section organization
- Content access control
- Progress tracking
- Bookmarks and notes
- Media management
- Content versioning

### Social Service

**Responsibilities**:
- Timeline and feed management
- Post creation and management
- Comments and reactions
- @mentions and #hashtags
- Content sharing
- Groups and pages
- Discover and trending

### Market Service

**Responsibilities**:
- Marketplace listings
- Services marketplace
- Classifieds management
- Job board
- Event management
- Product/service transactions

### Payment Service

**Responsibilities**:
- Payment processing integration
- Transaction management
- Digital wallet operations
- Subscription management
- Financial reporting
- Invoice generation
- Payout management

### Analytics Service

**Responsibilities**:
- User behavior tracking
- Content performance metrics
- Platform usage statistics
- Business intelligence
- Custom reports
- Real-time dashboards
- A/B testing

### Chat Service

**Responsibilities**:
- Real-time messaging
- Group chat management
- Message history
- Media sharing in chats
- Read receipts
- Typing indicators
- Chat moderation

### Streaming Service

**Responsibilities**:
- Live video streaming
- RTMP/WebRTC handling
- Stream recording
- Stream chat integration
- Virtual gifting in streams
- Stream discovery
- Analytics for streams

### Rewards Service

**Responsibilities**:
- Points system management
- Achievements and badges
- Leaderboards
- Gifting system
- Creator monetization
- Loyalty programs
- Redemption management

### Search Service

**Responsibilities**:
- Full-text search
- Faceted search
- User/content/group search
- Search suggestions
- Recommendations engine
- Discovery algorithms
- Relevance optimization

### Notification Service

**Responsibilities**:
- In-app notifications
- Push notifications
- Email notifications
- SMS notifications
- Notification preferences
- Delivery tracking
- Batch processing

## Shared Components

### Common Packages

**DB Package**:
- Database connection management
- Migration tools
- Transaction handling
- Query builders
- Model validation

**Auth Package**:
- JWT generation/validation
- Permission checking
- Role management
- Auth helpers

**HTTP Package**:
- Response formatting
- Error handling
- Request validation
- Middleware

**Logger Package**:
- Structured logging
- Log levels
- Log rotation
- Log shipping

**Config Package**:
- Environment configuration
- Feature flags
- App settings
- Secrets management

### Data Models

**Core Models**:
- User
- Profile
- Book
- Chapter
- Section
- Post
- Comment
- Reaction

**Social Models**:
- Friend
- Follow
- Group
- Page
- Timeline

**Marketplace Models**:
- Product
- Service
- Job
- Event
- Order
- Review

**Financial Models**:
- Transaction
- Wallet
- Subscription
- Payment
- Invoice

**Engagement Models**:
- Notification
- Message
- Stream
- Gift
- Points
- Achievement

## Microservice Implementation

### Service Template

Each microservice follows a consistent structure:

```
service-name/
├── cmd/
│   └── main.go           # Service entry point
├── internal/
│   ├── config/           # Service-specific configuration
│   ├── handlers/         # HTTP handlers
│   ├── middleware/       # Service middleware
│   ├── models/           # Service-specific models
│   ├── repository/       # Data access layer
│   └── service/          # Business logic
├── pkg/                  # Shareable packages
├── Dockerfile            # Container definition
├── go.mod                # Go module definition
└── README.md             # Service documentation
```

### Standard Patterns

**Repository Pattern**:
```go
type UserRepository interface {
    GetByID(ctx context.Context, id string) (*models.User, error)
    Create(ctx context.Context, user *models.User) error
    Update(ctx context.Context, user *models.User) error
    Delete(ctx context.Context, id string) error
    FindByUsername(ctx context.Context, username string) (*models.User, error)
}
```

**Service Pattern**:
```go
type UserService interface {
    GetUser(ctx context.Context, id string) (*models.User, error)
    CreateUser(ctx context.Context, user *models.UserCreate) (*models.User, error)
    UpdateUser(ctx context.Context, id string, updates map[string]interface{}) (*models.User, error)
    DeleteUser(ctx context.Context, id string) error
    FindUserByUsername(ctx context.Context, username string) (*models.User, error)
}
```

**Handler Pattern**:
```go
func (h *UserHandler) GetUser(c *gin.Context) {
    id := c.Param("id")
    
    user, err := h.userService.GetUser(c, id)
    if err != nil {
        h.errorResponse(c, err)
        return
    }
    
    c.JSON(http.StatusOK, user)
}
```

### Communication Patterns

**Synchronous (HTTP/gRPC)**:
- Direct service-to-service calls for immediate responses
- Health checks and service discovery
- Circuit breaking for fault tolerance

**Asynchronous (Event-based)**:
- Event publishing for state changes
- Event subscription for derived actions
- Compensating transactions for rollback

**Event Examples**:
```go
type UserCreatedEvent struct {
    ID        string    `json:"id"`
    Username  string    `json:"username"`
    Email     string    `json:"email"`
    CreatedAt time.Time `json:"created_at"`
}

type PostCreatedEvent struct {
    ID        string    `json:"id"`
    UserID    string    `json:"user_id"`
    Content   string    `json:"content"`
    CreatedAt time.Time `json:"created_at"`
}
```

## Data Storage

### Primary Datastores

**PostgreSQL**:
- Relational data with complex relationships
- Transactional data
- User and content data
- JSONB for semi-structured data

**Redis**:
- Caching layer
- Session storage
- Rate limiting
- Leaderboards
- Pub/Sub for real-time features

**Object Storage (S3-compatible)**:
- Media storage (images, videos, documents)
- Backup storage
- Static asset hosting
- Content delivery integration

**Time Series Database (InfluxDB)**:
- Metrics and telemetry
- Performance monitoring
- User activity trends
- Real-time analytics

### Database Schema (Simplified)

**Users & Authentication**:
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active',
    membership_tier INTEGER DEFAULT 1
);

CREATE TABLE profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    bio TEXT,
    avatar_url VARCHAR(255),
    cover_url VARCHAR(255),
    location VARCHAR(100),
    website VARCHAR(255),
    phone VARCHAR(20),
    points INTEGER DEFAULT 0,
    enabled_features JSONB DEFAULT '{}'::JSONB
);
```

**Content & Books**:
```sql
CREATE TABLE books (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    cover_image_url VARCHAR(255),
    publish_date TIMESTAMP WITH TIME ZONE,
    access_level INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE chapters (
    id UUID PRIMARY KEY,
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    order_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE sections (
    id UUID PRIMARY KEY,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_number INTEGER NOT NULL,
    points_reward INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Social & Engagement**:
```sql
CREATE TABLE posts (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT,
    media_urls JSONB DEFAULT '[]'::JSONB,
    location JSONB,
    feeling VARCHAR(50),
    activity VARCHAR(100),
    privacy VARCHAR(20) DEFAULT 'public',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE comments (
    id UUID PRIMARY KEY,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE reactions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    target_type VARCHAR(20) NOT NULL,
    target_id UUID NOT NULL,
    reaction_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Marketplace & Economy**:
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NGN',
    category VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    media_urls JSONB DEFAULT '[]'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE transactions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NGN',
    status VARCHAR(20) DEFAULT 'pending',
    reference VARCHAR(100),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## API Design

### RESTful API Standards

**URL Structure**:
- Resource-based paths: `/api/v1/resources`
- Nested resources: `/api/v1/resources/{id}/sub-resources`
- Query parameters for filtering: `/api/v1/resources?filter=value`
- Pagination: `/api/v1/resources?page=1&limit=20`

**HTTP Methods**:
- GET: Retrieve resources
- POST: Create resources
- PATCH: Partial update
- PUT: Complete update
- DELETE: Remove resources

**Status Codes**:
- 200: Success
- 201: Created
- 204: No Content
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 409: Conflict
- 500: Server Error

### Request/Response Format

**Standard Response Envelope**:
```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 20,
      "pages": 5
    }
  },
  "error": null
}
```

**Error Response**:
```json
{
  "success": false,
  "data": null,
  "meta": {},
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found",
    "details": {
      "resource": "user",
      "id": "123"
    }
  }
}
```

### Authentication

**JWT-Based Auth**:
- Authentication header: `Authorization: Bearer {token}`
- Token structure: Header.Payload.Signature
- Claims: `sub`, `exp`, `iat`, `roles`, `permissions`
- Refresh token mechanism

**API Key Auth (for services)**:
- API key header: `X-API-Key: {api_key}`
- Rate limiting by key
- Permission scoping

## Real-Time Features

### WebSockets

**Connection Endpoints**:
- Chat: `/ws/chat`
- Notifications: `/ws/notifications`
- Live Streams: `/ws/streams/{id}`
- Feed Updates: `/ws/feed`

**Message Format**:
```json
{
  "type": "MESSAGE_TYPE",
  "payload": { ... },
  "timestamp": "2025-04-19T14:30:00Z"
}
```

**Connection Management**:
- Authentication via token in connection request
- Heartbeat mechanism
- Reconnection strategy
- Channel subscription model

### Event Streaming

**Event Types**:
- UserEvents: registration, login, profile updates
- ContentEvents: new posts, comments, reactions
- NotificationEvents: new notifications, read status
- TransactionEvents: purchases, points awards

**Event Structure**:
```json
{
  "id": "event-uuid",
  "type": "event.type",
  "source": "service-name",
  "time": "2025-04-19T14:30:00Z",
  "subject": "resource-id",
  "data": { ... }
}
```

_Continued in Part 2..._
