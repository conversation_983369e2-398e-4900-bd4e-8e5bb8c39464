import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Dashboard as DashboardIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  Timeline as TimelineIcon,
  Map as MapIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchMetrics,
  fetchUserMetrics,
  fetchCategories,
  fetchDashboardConfig,
  fetchMetricAnalytics,
  fetchCategoryAnalytics,
  fetchUserAnalytics,
} from '../store/slices/impactSlice';
import { ImpactMetric, ImpactCategory } from '../services/impactService';
import { ResponsiveContainer, LineChart, Line, BarChart, Bar, PieChart, Pie, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, Cell } from 'recharts';

// Widget components
const MetricWidget: React.FC<{
  metric: ImpactMetric;
  onEdit: (metric: ImpactMetric) => void;
  onDelete: (metricId: number) => void;
}> = ({ metric, onEdit, onDelete }) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit(metric);
    handleMenuClose();
  };

  const handleDelete = () => {
    onDelete(metric.id);
    handleMenuClose();
  };

  const calculateProgress = () => {
    if (metric.targetValue === 0) return 0;
    return Math.min(100, (metric.currentValue / metric.targetValue) * 100);
  };

  const progress = calculateProgress();

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography variant="h6" component="div" gutterBottom>
            {metric.name}
          </Typography>
          <IconButton size="small" onClick={handleMenuOpen}>
            <MoreVertIcon />
          </IconButton>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {metric.description}
        </Typography>
        <Box sx={{ position: 'relative', display: 'inline-flex', width: '100%', justifyContent: 'center', mb: 2 }}>
          <CircularProgress
            variant="determinate"
            value={progress}
            size={80}
            thickness={4}
            sx={{
              color: progress >= 100 ? 'success.main' : 'primary.main',
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography variant="caption" component="div" color="text.secondary">
              {`${Math.round(progress)}%`}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', mb: 1 }}>
          <Typography variant="h5" component="div">
            {metric.currentValue} {metric.unit}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            of {metric.targetValue} {metric.unit} target
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Start: {new Date(metric.startDate).toLocaleDateString()}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            End: {new Date(metric.endDate).toLocaleDateString()}
          </Typography>
        </Box>
      </CardContent>
      <CardActions>
        <Button size="small" component="a" href={`/impact/metrics/${metric.id}`}>
          View Details
        </Button>
      </CardActions>

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEdit}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Card>
  );
};

const ChartWidget: React.FC<{
  title: string;
  chartType: 'line' | 'bar' | 'pie' | 'area';
  data: any[];
  dataKey: string;
  xAxisKey?: string;
  categories?: ImpactCategory[];
}> = ({ title, chartType, data, dataKey, xAxisKey = 'date', categories }) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const renderChart = () => {
    switch (chartType) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Line type="monotone" dataKey={dataKey} stroke="#8884d8" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Bar dataKey={dataKey} fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        );
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey={dataKey}
                nameKey={xAxisKey}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <RechartsTooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={250}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Area type="monotone" dataKey={dataKey} fill="#8884d8" stroke="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        );
      default:
        return null;
    }
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" gutterBottom>
          {title}
        </Typography>
        {data.length > 0 ? (
          renderChart()
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 250 }}>
            <Typography variant="body2" color="text.secondary">
              No data available
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

const SummaryWidget: React.FC<{
  title: string;
  metrics: ImpactMetric[];
}> = ({ title, metrics }) => {
  const totalMetrics = metrics.length;
  const completedMetrics = metrics.filter(m => m.currentValue >= m.targetValue).length;
  const inProgressMetrics = totalMetrics - completedMetrics;
  
  const averageProgress = metrics.length > 0
    ? metrics.reduce((sum, metric) => {
        const progress = metric.targetValue > 0 ? (metric.currentValue / metric.targetValue) * 100 : 0;
        return sum + Math.min(100, progress);
      }, 0) / metrics.length
    : 0;

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" gutterBottom>
          {title}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'primary.light' }}>
              <Typography variant="h4">{totalMetrics}</Typography>
              <Typography variant="body2">Total Metrics</Typography>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'success.light' }}>
              <Typography variant="h4">{completedMetrics}</Typography>
              <Typography variant="body2">Completed</Typography>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'warning.light' }}>
              <Typography variant="h4">{inProgressMetrics}</Typography>
              <Typography variant="body2">In Progress</Typography>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'info.light' }}>
              <Typography variant="h4">{averageProgress.toFixed(0)}%</Typography>
              <Typography variant="body2">Avg. Progress</Typography>
            </Paper>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

// Main Dashboard Component
const ImpactDashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { items: metrics, loading: metricsLoading, error: metricsError } = useSelector(
    (state: RootState) => state.impact.metrics
  );
  const { items: userMetrics, loading: userMetricsLoading } = useSelector(
    (state: RootState) => state.impact.userMetrics
  );
  const { items: categories, loading: categoriesLoading } = useSelector(
    (state: RootState) => state.impact.categories
  );
  const { data: dashboardConfig, loading: configLoading } = useSelector(
    (state: RootState) => state.impact.dashboardConfig
  );
  const { metricAnalytics, categoryAnalytics, userAnalytics, loading: analyticsLoading } = useSelector(
    (state: RootState) => state.impact.analytics
  );
  
  const [tabValue, setTabValue] = useState(0);
  const [selectedMetric, setSelectedMetric] = useState<ImpactMetric | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [metricToDelete, setMetricToDelete] = useState<number | null>(null);
  const [timeRange, setTimeRange] = useState('month');
  
  useEffect(() => {
    dispatch(fetchMetrics());
    dispatch(fetchCategories());
    
    if (user) {
      dispatch(fetchUserMetrics(user.id));
      dispatch(fetchDashboardConfig(user.id));
      dispatch(fetchUserAnalytics(user.id));
    }
  }, [dispatch, user]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleEditMetric = (metric: ImpactMetric) => {
    navigate(`/impact/metrics/${metric.id}/edit`);
  };
  
  const handleDeleteClick = (metricId: number) => {
    setMetricToDelete(metricId);
    setDeleteDialogOpen(true);
  };
  
  const handleDeleteConfirm = async () => {
    // Implementation would go here
    setDeleteDialogOpen(false);
    setMetricToDelete(null);
  };
  
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setMetricToDelete(null);
  };
  
  const handleCreateMetric = () => {
    navigate('/impact/metrics/create');
  };
  
  const handleCreateReport = () => {
    navigate('/impact/reports/create');
  };
  
  const handleRefreshData = () => {
    dispatch(fetchMetrics());
    dispatch(fetchCategories());
    
    if (user) {
      dispatch(fetchUserMetrics(user.id));
      dispatch(fetchUserAnalytics(user.id));
    }
  };
  
  // Sample data for charts
  const sampleTimeSeriesData = [
    { date: '2023-01', value: 400 },
    { date: '2023-02', value: 300 },
    { date: '2023-03', value: 500 },
    { date: '2023-04', value: 700 },
    { date: '2023-05', value: 600 },
    { date: '2023-06', value: 800 },
  ];
  
  const sampleCategoryData = [
    { name: 'Education', value: 400 },
    { name: 'Health', value: 300 },
    { name: 'Environment', value: 300 },
    { name: 'Economic', value: 200 },
  ];
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to view the impact dashboard. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  const loading = metricsLoading || userMetricsLoading || categoriesLoading || configLoading || analyticsLoading;
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Impact Dashboard
        </Typography>
        <Box>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefreshData} sx={{ mr: 1 }}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateMetric}
            sx={{ mr: 1 }}
          >
            New Metric
          </Button>
          <Button
            variant="outlined"
            startIcon={<BarChartIcon />}
            onClick={handleCreateReport}
          >
            Create Report
          </Button>
        </Box>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<DashboardIcon />} label="Dashboard" />
          <Tab icon={<BarChartIcon />} label="Metrics" />
          <Tab icon={<TimelineIcon />} label="Reports" />
          <Tab icon={<SettingsIcon />} label="Settings" />
        </Tabs>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : metricsError ? (
        <Alert severity="error" sx={{ mb: 4 }}>
          {metricsError}
        </Alert>
      ) : (
        <>
          {/* Dashboard Tab */}
          {tabValue === 0 && (
            <Box>
              <SummaryWidget title="Impact Overview" metrics={metrics} />
              
              <Typography variant="h5" sx={{ mt: 4, mb: 2 }}>
                Key Metrics
              </Typography>
              
              <Grid container spacing={3}>
                {metrics.slice(0, 3).map((metric) => (
                  <Grid item key={metric.id} xs={12} md={4}>
                    <MetricWidget
                      metric={metric}
                      onEdit={handleEditMetric}
                      onDelete={handleDeleteClick}
                    />
                  </Grid>
                ))}
              </Grid>
              
              <Typography variant="h5" sx={{ mt: 4, mb: 2 }}>
                Analytics
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <ChartWidget
                    title="Progress Over Time"
                    chartType="line"
                    data={sampleTimeSeriesData}
                    dataKey="value"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <ChartWidget
                    title="Metrics by Category"
                    chartType="pie"
                    data={sampleCategoryData}
                    dataKey="value"
                    xAxisKey="name"
                  />
                </Grid>
              </Grid>
            </Box>
          )}
          
          {/* Metrics Tab */}
          {tabValue === 1 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5">
                  All Metrics
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateMetric}
                >
                  New Metric
                </Button>
              </Box>
              
              <Grid container spacing={3}>
                {metrics.map((metric) => (
                  <Grid item key={metric.id} xs={12} sm={6} md={4}>
                    <MetricWidget
                      metric={metric}
                      onEdit={handleEditMetric}
                      onDelete={handleDeleteClick}
                    />
                  </Grid>
                ))}
              </Grid>
              
              {metrics.length === 0 && (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    No metrics found. Click "New Metric" to create your first impact metric.
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleCreateMetric}
                    sx={{ mt: 2 }}
                  >
                    Create Metric
                  </Button>
                </Paper>
              )}
            </Box>
          )}
          
          {/* Reports Tab */}
          {tabValue === 2 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5">
                  Impact Reports
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateReport}
                >
                  Create Report
                </Button>
              </Box>
              
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  No reports found. Click "Create Report" to generate your first impact report.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateReport}
                  sx={{ mt: 2 }}
                >
                  Create Report
                </Button>
              </Paper>
            </Box>
          )}
          
          {/* Settings Tab */}
          {tabValue === 3 && (
            <Box>
              <Typography variant="h5" gutterBottom>
                Dashboard Settings
              </Typography>
              
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Categories
                </Typography>
                <Grid container spacing={2}>
                  {categories.map((category) => (
                    <Grid item key={category.id} xs={12} sm={6} md={4}>
                      <Paper
                        sx={{
                          p: 2,
                          borderLeft: '4px solid',
                          borderColor: category.color,
                        }}
                      >
                        <Typography variant="subtitle1">{category.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {category.description}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                  <Grid item xs={12} sm={6} md={4}>
                    <Paper
                      sx={{
                        p: 2,
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: '1px dashed',
                        borderColor: 'divider',
                      }}
                    >
                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => navigate('/impact/categories/create')}
                      >
                        Add Category
                      </Button>
                    </Paper>
                  </Grid>
                </Grid>
              </Paper>
              
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Data Export
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={() => {}}
                      sx={{ p: 2 }}
                    >
                      Export All Metrics (CSV)
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={() => {}}
                      sx={{ p: 2 }}
                    >
                      Export All Metrics (Excel)
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={() => {}}
                      sx={{ p: 2 }}
                    >
                      Export All Metrics (PDF)
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
            </Box>
          )}
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Metric</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this metric? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ImpactDashboard;
