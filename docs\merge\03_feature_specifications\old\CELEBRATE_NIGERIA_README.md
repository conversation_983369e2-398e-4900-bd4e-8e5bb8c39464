# Celebrate Nigeria Feature

## Overview

The Celebrate Nigeria feature is a comprehensive digital repository showcasing Nigerian excellence across people, places, events, and cultural elements. It serves as an educational resource and a platform for celebrating Nigeria's rich heritage and achievements.

## Purpose

This feature aims to:

1. **Showcase Excellence**: Highlight accomplished Nigerians, iconic locations, and significant events
2. **Preserve Heritage**: Document and preserve Nigeria's diverse cultural heritage and landmarks
3. **Promote Unity**: Foster national unity by celebrating achievements across all regions
4. **Educate**: Provide accessible, engaging information about Nigerian history and culture
5. **Build Pride**: Cultivate national pride through a celebration of collective achievements

## Feature Components

### 1. Directory Structure

The Celebrate Nigeria feature organizes content into three main categories:

- **People**: Notable Nigerians who have made significant contributions in various fields
- **Places**: Important locations, landmarks, and sites throughout Nigeria
- **Events**: Significant festivals, celebrations, and historical events

Each main category has several subcategories to further organize the content.

### 2. Entry Types

Each entry in the directory includes:

- **Basic Information**: Title, short description, full description, primary image
- **Category Assignment**: Association with relevant categories
- **Key Facts**: Important facts about the entry
- **Media Gallery**: Images and other media related to the entry
- **Type-Specific Details**:
  - People: Birth/death dates, profession, achievements, contributions
  - Places: Location, visiting information, accessibility details
  - Events: Dates, recurrence pattern, organizer information

### 3. User Interactions

The feature supports various user interactions:

- **Browsing**: Navigate through categories and entries
- **Searching**: Find entries using keywords and filters
- **Commenting**: Leave comments on entries
- **Voting**: Support pending submissions
- **Submitting**: Suggest new entries for inclusion

## Technical Implementation

### Architecture

The Celebrate Nigeria feature follows a layered architecture:

1. **Database Layer**: PostgreSQL tables for storing entries and relationships
2. **Repository Layer**: Data access and persistence
3. **Service Layer**: Business logic and operations
4. **API Layer**: RESTful endpoints for frontend integration
5. **Frontend Layer**: User interface components and templates

### Key Files and Locations

- **Database Schema**: `internal/celebration/migrations/001_create_celebration_tables.sql`
- **Models**: `internal/celebration/models/models.go`
- **Repository**: `internal/celebration/repository/repository.go`
- **Service**: `internal/celebration/service/service.go`
- **Handlers**: `internal/celebration/handlers/handlers.go`
- **Frontend**: `web/static/celebrate.html` and related templates
- **Data Population**: `scripts/populate_celebrate_nigeria.go`

## Getting Started

### Prerequisites

- PostgreSQL database
- Go 1.16 or higher
- Node.js and npm (for frontend development)

### Setup

1. **Database Setup**:
   ```bash
   # Run migrations to create the necessary tables
   go run cmd/migrate/main.go up
   ```

2. **Data Population**:
   ```bash
   # Populate the database with initial entries
   go run scripts/populate_celebrate_nigeria.go
   ```

3. **Image Directories**:
   ```bash
   # Create directories for images
   ./scripts/create_celebrate_image_dirs.sh
   ```

4. **Run the Application**:
   ```bash
   # Start the server
   go run cmd/server/main.go
   ```

5. **Access the Feature**:
   Open your browser and navigate to `http://localhost:5000/celebrate.html`

## Development Guidelines

### Adding New Entries

1. Create a new entry in the appropriate data structure in `scripts/populate_celebrate_nigeria.go`
2. Run the script to add the entry to the database
3. Add any associated images to the appropriate directory in `web/static/images/celebrate/`

### Extending the Feature

1. **New Category**: Add the category to the database and update the frontend templates
2. **New Entry Type**: Create a new model, repository methods, and service functions
3. **New Interaction**: Implement the necessary API endpoints and frontend components

## Current Status

The Celebrate Nigeria feature is currently in active development. See `docs/project/CELEBRATE_NIGERIA_IMPLEMENTATION_STATUS.md` for the current status and next steps.

## Documentation

For more detailed information, refer to the following documents:

- **Implementation Plan**: `docs/project/CELEBRATE_NIGERIA_IMPLEMENTATION_PLAN.md`
- **Technical Specification**: `docs/project/CELEBRATE_NIGERIA_TECHNICAL_SPEC.md`
- **Data Plan**: `docs/project/CELEBRATE_NIGERIA_DATA_PLAN.md`
- **Testing Plan**: `docs/project/CELEBRATE_NIGERIA_TESTING_PLAN.md`
- **Implementation Status**: `docs/project/CELEBRATE_NIGERIA_IMPLEMENTATION_STATUS.md`
