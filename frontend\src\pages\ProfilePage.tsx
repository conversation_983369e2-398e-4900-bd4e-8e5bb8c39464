import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import { RootState } from '../store';
import {
  fetchUserProfile,
  fetchReadingStats,
  fetchUserBookmarks,
  fetchUserActivities,
  updateUserProfile,
  changePassword,
  uploadAvatar,
} from '../features/profile/profileSlice';
import { Link } from 'react-router-dom';

const ProfileContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (min-width: 992px) {
    flex-direction: row;
  }
`;

const Sidebar = styled.div`
  width: 100%;

  @media (min-width: 992px) {
    width: 300px;
    flex-shrink: 0;
  }
`;

const ProfileCard = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const AvatarContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const Avatar = styled.img`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
  border: 3px solid #16213e;
`;

const AvatarUpload = styled.div`
  margin-top: 0.5rem;
`;

const UploadButton = styled.label`
  display: inline-block;
  background-color: #16213e;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;

  &:hover {
    background-color: #0f3460;
  }

  input {
    display: none;
  }
`;

const UserName = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  text-align: center;
`;

const UserEmail = styled.p`
  color: #666;
  margin-bottom: 1rem;
  text-align: center;
`;

const UserStats = styled.div`
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #16213e;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const NavMenu = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const NavItem = styled.li`
  margin-bottom: 0.5rem;
`;

const NavLink = styled.button<{ active: boolean }>`
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: ${(props) => (props.active ? '#16213e' : 'transparent')};
  color: ${(props) => (props.active ? 'white' : '#333')};
  border: ${(props) => (props.active ? 'none' : '1px solid #ddd')};
  border-radius: 4px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${(props) => (props.active ? '#0f3460' : '#f0f0f0')};
  }
`;

const ContentArea = styled.div`
  flex: 1;
`;

const ContentCard = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #16213e;
    box-shadow: 0 0 0 2px rgba(22, 33, 62, 0.2);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #16213e;
    box-shadow: 0 0 0 2px rgba(22, 33, 62, 0.2);
  }
`;

const Button = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0f3460;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 4px;
`;

const SuccessMessage = styled.div`
  color: #4caf50;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
`;

const ReadingStatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
`;

const StatCardValue = styled.div`
  font-size: 2rem;
  font-weight: bold;
  color: #16213e;
  margin-bottom: 0.5rem;
`;

const StatCardLabel = styled.div`
  color: #666;
`;

const ReadingHistoryTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
`;

const ReadingHistoryChart = styled.div`
  height: 200px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
`;

const HistoryBar = styled.div<{ height: number }>`
  flex: 1;
  height: ${(props) => props.height}%;
  background-color: #16213e;
  border-radius: 4px 4px 0 0;
  position: relative;

  &:hover::after {
    content: '${(props) => props.height}%';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #16213e;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
  }
`;

const BookmarksList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const BookmarkCard = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #16213e;
`;

const BookmarkTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
`;

const BookmarkInfo = styled.div`
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
`;

const BookmarkActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const BookmarkLink = styled(Link)`
  color: #16213e;
  font-weight: bold;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const BookmarkDelete = styled.button`
  background-color: transparent;
  color: #e94560;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 0.9rem;

  &:hover {
    text-decoration: underline;
  }
`;

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ActivityItem = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ActivityIcon = styled.div<{ type: string }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${(props) => {
    switch (props.type) {
      case 'reading':
        return '#16213e';
      case 'forum':
        return '#e94560';
      case 'bookmark':
        return '#4caf50';
      case 'note':
        return '#ff9800';
      default:
        return '#16213e';
    }
  }};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityDescription = styled.div`
  margin-bottom: 0.25rem;
`;

const ActivityDate = styled.div`
  color: #666;
  font-size: 0.9rem;
`;

const ActivityLink = styled(Link)`
  color: #16213e;
  font-weight: bold;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

enum ProfileTab {
  PROFILE = 'profile',
  READING_STATS = 'reading_stats',
  BOOKMARKS = 'bookmarks',
  ACTIVITIES = 'activities',
  SETTINGS = 'settings',
}

const ProfilePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ProfileTab>(ProfileTab.PROFILE);
  const [editMode, setEditMode] = useState(false);
  const [name, setName] = useState('');
  const [bio, setBio] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');

  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const { profile, readingStats, bookmarks, activities, isLoading, error } = useSelector(
    (state: RootState) => state.profile
  );

  useEffect(() => {
    if (user) {
      dispatch(fetchUserProfile(user.id));
      dispatch(fetchReadingStats(user.id));
      dispatch(fetchUserBookmarks(user.id));
      dispatch(fetchUserActivities(user.id));
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (profile) {
      setName(profile.name);
      setBio(profile.bio || '');
    }
  }, [profile]);

  const handleEditProfile = () => {
    setEditMode(true);
  };

  const handleSaveProfile = () => {
    if (user && profile) {
      dispatch(
        updateUserProfile({
          userId: user.id,
          profileData: {
            name,
            bio,
          },
        })
      );
      setEditMode(false);
      setSuccessMessage('Profile updated successfully');
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    setName(profile?.name || '');
    setBio(profile?.bio || '');
  };

  const handleChangePassword = () => {
    setPasswordError('');
    setPasswordSuccess('');

    if (newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters');
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    if (user) {
      dispatch(
        changePassword({
          userId: user.id,
          currentPassword,
          newPassword,
        })
      )
        .unwrap()
        .then(() => {
          setPasswordSuccess('Password changed successfully');
          setCurrentPassword('');
          setNewPassword('');
          setConfirmPassword('');
        })
        .catch((error: any) => {
          setPasswordError(error);
        });
    }
  };

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && user) {
      dispatch(uploadAvatar({ userId: user.id, file }));
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'reading':
        return '📚';
      case 'forum':
        return '💬';
      case 'bookmark':
        return '🔖';
      case 'note':
        return '📝';
      default:
        return '📚';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (!user) {
    return <div>Please log in to view your profile</div>;
  }

  if (isLoading && !profile) {
    return <div>Loading profile...</div>;
  }

  return (
    <ProfileContainer>
      <Sidebar>
        <ProfileCard>
          <AvatarContainer>
            <Avatar
              src={profile?.avatar || 'https://via.placeholder.com/150'}
              alt={profile?.name || 'User'}
            />
            <AvatarUpload>
              <UploadButton>
                Change Avatar
                <input type="file" accept="image/*" onChange={handleAvatarUpload} />
              </UploadButton>
            </AvatarUpload>
          </AvatarContainer>

          <UserName>{profile?.name}</UserName>
          <UserEmail>{profile?.email}</UserEmail>

          <UserStats>
            <StatItem>
              <StatValue>{readingStats?.books_started || 0}</StatValue>
              <StatLabel>Books Started</StatLabel>
            </StatItem>
            <StatItem>
              <StatValue>{readingStats?.books_completed || 0}</StatValue>
              <StatLabel>Books Completed</StatLabel>
            </StatItem>
            <StatItem>
              <StatValue>{profile?.level || 1}</StatValue>
              <StatLabel>Level</StatLabel>
            </StatItem>
          </UserStats>
        </ProfileCard>

        <ProfileCard>
          <NavMenu>
            <NavItem>
              <NavLink
                active={activeTab === ProfileTab.PROFILE}
                onClick={() => setActiveTab(ProfileTab.PROFILE)}
              >
                Profile
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                active={activeTab === ProfileTab.READING_STATS}
                onClick={() => setActiveTab(ProfileTab.READING_STATS)}
              >
                Reading Statistics
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                active={activeTab === ProfileTab.BOOKMARKS}
                onClick={() => setActiveTab(ProfileTab.BOOKMARKS)}
              >
                Bookmarks
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                active={activeTab === ProfileTab.ACTIVITIES}
                onClick={() => setActiveTab(ProfileTab.ACTIVITIES)}
              >
                Activities
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                active={activeTab === ProfileTab.SETTINGS}
                onClick={() => setActiveTab(ProfileTab.SETTINGS)}
              >
                Settings
              </NavLink>
            </NavItem>
          </NavMenu>
        </ProfileCard>
      </Sidebar>

      <ContentArea>
        {activeTab === ProfileTab.PROFILE && (
          <ContentCard>
            <SectionTitle>Profile Information</SectionTitle>

            {successMessage && <SuccessMessage>{successMessage}</SuccessMessage>}
            {error && <ErrorMessage>{error}</ErrorMessage>}

            {editMode ? (
              <Form>
                <FormGroup>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">Email</Label>
                  <Input type="email" id="email" value={profile?.email || ''} disabled />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="bio">Bio</Label>
                  <TextArea id="bio" value={bio} onChange={(e) => setBio(e.target.value)} />
                </FormGroup>

                <div style={{ display: 'flex', gap: '1rem' }}>
                  <Button type="button" onClick={handleSaveProfile}>
                    Save Changes
                  </Button>
                  <Button
                    type="button"
                    onClick={handleCancelEdit}
                    style={{ backgroundColor: '#ccc' }}
                  >
                    Cancel
                  </Button>
                </div>
              </Form>
            ) : (
              <>
                <div style={{ marginBottom: '1.5rem' }}>
                  <Label>Full Name</Label>
                  <p>{profile?.name}</p>
                </div>

                <div style={{ marginBottom: '1.5rem' }}>
                  <Label>Email</Label>
                  <p>{profile?.email}</p>
                </div>

                <div style={{ marginBottom: '1.5rem' }}>
                  <Label>Bio</Label>
                  <p>{profile?.bio || 'No bio provided'}</p>
                </div>

                <div style={{ marginBottom: '1.5rem' }}>
                  <Label>Member Since</Label>
                  <p>{profile?.createdAt ? formatDate(profile.createdAt) : 'Unknown'}</p>
                </div>

                <Button type="button" onClick={handleEditProfile}>
                  Edit Profile
                </Button>
              </>
            )}
          </ContentCard>
        )}

        {activeTab === ProfileTab.READING_STATS && (
          <ContentCard>
            <SectionTitle>Reading Statistics</SectionTitle>

            <ReadingStatsGrid>
              <StatCard>
                <StatCardValue>{readingStats?.books_started || 0}</StatCardValue>
                <StatCardLabel>Books Started</StatCardLabel>
              </StatCard>

              <StatCard>
                <StatCardValue>{readingStats?.books_completed || 0}</StatCardValue>
                <StatCardLabel>Books Completed</StatCardLabel>
              </StatCard>

              <StatCard>
                <StatCardValue>{readingStats?.total_pages_read || 0}</StatCardValue>
                <StatCardLabel>Pages Read</StatCardLabel>
              </StatCard>

              <StatCard>
                <StatCardValue>{readingStats?.reading_streak || 0}</StatCardValue>
                <StatCardLabel>Day Streak</StatCardLabel>
              </StatCard>

              <StatCard>
                <StatCardValue>{readingStats?.average_reading_time || 0}</StatCardValue>
                <StatCardLabel>Avg. Minutes/Day</StatCardLabel>
              </StatCard>
            </ReadingStatsGrid>

            <ReadingHistoryTitle>Reading History (Last 7 Days)</ReadingHistoryTitle>
            <ReadingHistoryChart>
              {readingStats?.reading_history
                ?.slice(0, 7)
                .map((item, index) => {
                  const maxPages = Math.max(
                    ...readingStats.reading_history.map((h) => h.pages_read)
                  );
                  const height = (item.pages_read / maxPages) * 100 || 0;

                  return <HistoryBar key={index} height={height} />;
                })}
            </ReadingHistoryChart>
          </ContentCard>
        )}

        {activeTab === ProfileTab.BOOKMARKS && (
          <ContentCard>
            <SectionTitle>Bookmarks</SectionTitle>

            {bookmarks.length === 0 ? (
              <p>You haven't bookmarked any sections yet.</p>
            ) : (
              <BookmarksList>
                {bookmarks.map((bookmark) => (
                  <BookmarkCard key={bookmark.id}>
                    <BookmarkTitle>{bookmark.section_title}</BookmarkTitle>
                    <BookmarkInfo>
                      From: {bookmark.book_title}
                      <br />
                      Added: {formatDate(bookmark.created_at)}
                      {bookmark.note && (
                        <>
                          <br />
                          Note: {bookmark.note}
                        </>
                      )}
                    </BookmarkInfo>
                    <BookmarkActions>
                      <BookmarkLink to={`/book-viewer?book=${bookmark.book_id}&section=${bookmark.section_id}`}>
                        Go to Section
                      </BookmarkLink>
                      <BookmarkDelete>Remove</BookmarkDelete>
                    </BookmarkActions>
                  </BookmarkCard>
                ))}
              </BookmarksList>
            )}
          </ContentCard>
        )}

        {activeTab === ProfileTab.ACTIVITIES && (
          <ContentCard>
            <SectionTitle>Recent Activities</SectionTitle>

            {activities.length === 0 ? (
              <p>No recent activities.</p>
            ) : (
              <ActivityList>
                {activities.map((activity) => (
                  <ActivityItem key={activity.id}>
                    <ActivityIcon type={activity.type}>{getActivityIcon(activity.type)}</ActivityIcon>
                    <ActivityContent>
                      <ActivityDescription>{activity.description}</ActivityDescription>
                      <ActivityDate>{formatDate(activity.created_at)}</ActivityDate>
                    </ActivityContent>
                    {activity.book_id && (
                      <ActivityLink to={`/book-viewer?book=${activity.book_id}`}>
                        View Book
                      </ActivityLink>
                    )}
                    {activity.topic_id && (
                      <ActivityLink to={`/community/topic/${activity.topic_id}`}>
                        View Topic
                      </ActivityLink>
                    )}
                  </ActivityItem>
                ))}
              </ActivityList>
            )}
          </ContentCard>
        )}

        {activeTab === ProfileTab.SETTINGS && (
          <ContentCard>
            <SectionTitle>Account Settings</SectionTitle>

            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{ marginBottom: '1rem' }}>Change Password</h3>

              {passwordError && <ErrorMessage>{passwordError}</ErrorMessage>}
              {passwordSuccess && <SuccessMessage>{passwordSuccess}</SuccessMessage>}

              <FormGroup>
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                  type="password"
                  id="currentPassword"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                  type="password"
                  id="newPassword"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </FormGroup>

              <Button type="button" onClick={handleChangePassword}>
                Change Password
              </Button>
            </div>

            <div>
              <h3 style={{ marginBottom: '1rem' }}>Notification Settings</h3>

              <FormGroup>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <input type="checkbox" id="emailNotifications" defaultChecked />
                  <Label htmlFor="emailNotifications" style={{ marginLeft: '0.5rem', marginBottom: 0 }}>
                    Email Notifications
                  </Label>
                </div>
              </FormGroup>

              <FormGroup>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <input type="checkbox" id="forumNotifications" defaultChecked />
                  <Label htmlFor="forumNotifications" style={{ marginLeft: '0.5rem', marginBottom: 0 }}>
                    Forum Reply Notifications
                  </Label>
                </div>
              </FormGroup>

              <FormGroup>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <input type="checkbox" id="newContentNotifications" defaultChecked />
                  <Label
                    htmlFor="newContentNotifications"
                    style={{ marginLeft: '0.5rem', marginBottom: 0 }}
                  >
                    New Content Notifications
                  </Label>
                </div>
              </FormGroup>

              <Button type="button">Save Notification Settings</Button>
            </div>
          </ContentCard>
        )}
      </ContentArea>
    </ProfileContainer>
  );
};

export default ProfilePage;
