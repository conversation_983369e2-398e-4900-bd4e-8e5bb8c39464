# Great Nigeria Library - React Frontend

This repository contains the React TypeScript frontend for the Great Nigeria Library project.

## Project Overview

The Great Nigeria Library is a platform for Nigerian citizens to access educational resources, engage in community discussions, and celebrate Nigerian excellence. This frontend application provides a modern, responsive user interface for interacting with the Great Nigeria Library platform.

## Technology Stack

- **Frontend Framework**: React 18+
- **Type System**: TypeScript
- **State Management**: Redux Toolkit
- **Routing**: React Router
- **API Client**: Axios
- **Styling**: Styled Components
- **Testing**: Jest and React Testing Library

## Getting Started

### Prerequisites

- Node.js 16+ and npm

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/great-nigeria-frontend.git
   cd great-nigeria-frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with the following content:
   ```
   REACT_APP_API_BASE_URL=http://localhost:5000/api
   ```

4. Start the development server:
   ```bash
   npm start
   ```

The application will be available at http://localhost:3000.

## Project Structure

```
src/
├── api/              # API client and services
├── assets/           # Static assets
├── components/       # Reusable UI components
├── features/         # Feature-specific components
│   ├── auth/         # Authentication
│   ├── books/        # Book viewer
│   ├── celebrate/    # Celebrate Nigeria
│   ├── forum/        # Forum
│   ├── profile/      # User profile
│   └── resources/    # Resources
├── hooks/            # Custom React hooks
├── layouts/          # Page layouts
├── pages/            # Page components
├── store/            # Redux store
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## Available Scripts

- `npm start`: Starts the development server
- `npm test`: Runs tests
- `npm run build`: Builds the app for production
- `npm run eject`: Ejects from Create React App

## Backend Integration

This frontend application communicates with the Great Nigeria Library Go backend API. Make sure the backend server is running and accessible at the URL specified in the `.env` file.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Open a pull request

## License

[MIT License](LICENSE)
