import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { RootState } from '../store';
import { fetchEntryByTypeAndSlug, voteForEntry } from '../features/celebrate/celebrateSlice';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Breadcrumbs = styled.div`
  margin-bottom: 1.5rem;
  color: #666;
  
  a {
    color: #16213e;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  span {
    margin: 0 0.5rem;
  }
`;

const EntryHeader = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
  
  @media (min-width: 768px) {
    flex-direction: row;
    gap: 2rem;
  }
`;

const EntryImageContainer = styled.div`
  flex-shrink: 0;
  margin-bottom: 1.5rem;
  
  @media (min-width: 768px) {
    width: 300px;
    margin-bottom: 0;
  }
`;

const EntryImage = styled.img`
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`;

const EntryInfo = styled.div`
  flex: 1;
`;

const EntryType = styled.div<{ type: string }>`
  display: inline-block;
  background-color: ${(props) => {
    switch (props.type) {
      case 'person':
        return '#16213e';
      case 'place':
        return '#4caf50';
      case 'event':
        return '#ff9800';
      default:
        return '#16213e';
    }
  }};
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: capitalize;
  margin-bottom: 0.5rem;
`;

const EntryTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const EntrySummary = styled.p`
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #666;
  font-style: italic;
`;

const EntryMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 1.5rem;
`;

const MetaItem = styled.div``;

const MetaLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
`;

const MetaValue = styled.div`
  font-weight: bold;
  color: #16213e;
`;

const EntryActions = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #e0e0e0;
  }
`;

const VoteButton = styled(ActionButton)`
  background-color: #16213e;
  color: white;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const ShareButton = styled(ActionButton)``;

const EntryContent = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  
  @media (min-width: 992px) {
    grid-template-columns: 2fr 1fr;
  }
`;

const MainContent = styled.div``;

const Description = styled.div`
  margin-bottom: 2rem;
  line-height: 1.8;
  
  p {
    margin-bottom: 1rem;
  }
  
  h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #16213e;
  }
  
  h3 {
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    color: #16213e;
  }
  
  ul, ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }
  
  li {
    margin-bottom: 0.5rem;
  }
  
  a {
    color: #16213e;
    text-decoration: underline;
  }
  
  blockquote {
    border-left: 3px solid #16213e;
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    color: #666;
  }
`;

const AchievementsList = styled.div`
  margin-bottom: 2rem;
`;

const AchievementsTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const AchievementItem = styled.div`
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
`;

const MediaGallery = styled.div`
  margin-bottom: 2rem;
`;

const MediaTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const MediaGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
`;

const MediaItem = styled.div`
  border-radius: 8px;
  overflow: hidden;
`;

const MediaImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`;

const MediaVideo = styled.div`
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  
  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
`;

const MediaCaption = styled.div`
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  text-align: center;
`;

const Sidebar = styled.div``;

const SidebarSection = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SidebarTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #16213e;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
`;

const FactsList = styled.div``;

const FactItem = styled.div`
  margin-bottom: 1rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const FactTitle = styled.div`
  font-weight: bold;
  margin-bottom: 0.25rem;
  color: #16213e;
`;

const FactContent = styled.div`
  color: #666;
`;

const RelatedEntries = styled.div``;

const RelatedEntry = styled(Link)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
  text-decoration: none;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: #f8f9fa;
  }
`;

const RelatedEntryType = styled.div<{ type: string }>`
  background-color: ${(props) => {
    switch (props.type) {
      case 'person':
        return '#16213e';
      case 'place':
        return '#4caf50';
      case 'event':
        return '#ff9800';
      default:
        return '#16213e';
    }
  }};
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  text-transform: capitalize;
`;

const RelatedEntryName = styled.div`
  flex: 1;
  color: #333;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  &:after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #16213e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  padding: 1rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
`;

const CelebrateDetailPage: React.FC = () => {
  const { type, slug } = useParams<{ type: 'person' | 'place' | 'event'; slug: string }>();
  
  const dispatch = useDispatch();
  
  const { currentEntry, isLoading, error } = useSelector((state: RootState) => state.celebrate);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  useEffect(() => {
    if (type && slug) {
      dispatch(fetchEntryByTypeAndSlug({ type, slug }));
    }
  }, [dispatch, type, slug]);
  
  const handleVote = () => {
    if (currentEntry && isAuthenticated) {
      dispatch(voteForEntry(currentEntry.id));
    } else if (!isAuthenticated) {
      // Redirect to login
      window.location.href = `/login?redirect=/celebrate/${type}/${slug}`;
    }
  };
  
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: currentEntry?.name || 'Celebrate Nigeria',
        text: currentEntry?.summary || 'Check out this entry on Celebrate Nigeria',
        url: window.location.href,
      });
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };
  
  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  
  if (isLoading && !currentEntry) {
    return <LoadingSpinner />;
  }
  
  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }
  
  if (!currentEntry) {
    return <ErrorMessage>Entry not found</ErrorMessage>;
  }
  
  return (
    <Container>
      <Breadcrumbs>
        <Link to="/celebrate">Celebrate Nigeria</Link>
        <span>›</span>
        <Link to={`/celebrate?type=${currentEntry.type}`}>
          {currentEntry.type === 'person'
            ? 'People'
            : currentEntry.type === 'place'
            ? 'Places'
            : 'Events'}
        </Link>
        <span>›</span>
        <span>{currentEntry.name}</span>
      </Breadcrumbs>
      
      <EntryHeader>
        <EntryImageContainer>
          <EntryImage src={currentEntry.image_url} alt={currentEntry.name} />
        </EntryImageContainer>
        
        <EntryInfo>
          <EntryType type={currentEntry.type}>{currentEntry.type}</EntryType>
          <EntryTitle>{currentEntry.name}</EntryTitle>
          <EntrySummary>{currentEntry.summary}</EntrySummary>
          
          <EntryMeta>
            {currentEntry.type === 'person' && (
              <>
                {currentEntry.birth_date && (
                  <MetaItem>
                    <MetaLabel>Born</MetaLabel>
                    <MetaValue>{formatDate(currentEntry.birth_date)}</MetaValue>
                  </MetaItem>
                )}
                
                {currentEntry.death_date && (
                  <MetaItem>
                    <MetaLabel>Died</MetaLabel>
                    <MetaValue>{formatDate(currentEntry.death_date)}</MetaValue>
                  </MetaItem>
                )}
              </>
            )}
            
            {currentEntry.type === 'event' && (
              <MetaItem>
                <MetaLabel>Date</MetaLabel>
                <MetaValue>{formatDate(currentEntry.birth_date)}</MetaValue>
              </MetaItem>
            )}
          </EntryMeta>
          
          <EntryActions>
            <VoteButton onClick={handleVote}>
              <span>👍</span> Upvote
            </VoteButton>
            
            <ShareButton onClick={handleShare}>
              <span>🔗</span> Share
            </ShareButton>
          </EntryActions>
        </EntryInfo>
      </EntryHeader>
      
      <EntryContent>
        <MainContent>
          {currentEntry.description && (
            <Description dangerouslySetInnerHTML={{ __html: currentEntry.description }} />
          )}
          
          {currentEntry.achievements && currentEntry.achievements.length > 0 && (
            <AchievementsList>
              <AchievementsTitle>Achievements</AchievementsTitle>
              {currentEntry.achievements.map((achievement, index) => (
                <AchievementItem key={index}>{achievement}</AchievementItem>
              ))}
            </AchievementsList>
          )}
          
          {currentEntry.media && currentEntry.media.length > 0 && (
            <MediaGallery>
              <MediaTitle>Gallery</MediaTitle>
              <MediaGrid>
                {currentEntry.media.map((media, index) => (
                  <MediaItem key={index}>
                    {media.type === 'image' ? (
                      <MediaImage src={media.url} alt={media.caption} />
                    ) : (
                      <MediaVideo>
                        <iframe
                          src={media.url}
                          title={media.caption}
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        ></iframe>
                      </MediaVideo>
                    )}
                    <MediaCaption>{media.caption}</MediaCaption>
                  </MediaItem>
                ))}
              </MediaGrid>
            </MediaGallery>
          )}
        </MainContent>
        
        <Sidebar>
          {currentEntry.facts && currentEntry.facts.length > 0 && (
            <SidebarSection>
              <SidebarTitle>Quick Facts</SidebarTitle>
              <FactsList>
                {currentEntry.facts.map((fact, index) => (
                  <FactItem key={index}>
                    <FactTitle>{fact.title}</FactTitle>
                    <FactContent>{fact.content}</FactContent>
                  </FactItem>
                ))}
              </FactsList>
            </SidebarSection>
          )}
          
          {currentEntry.related_entries && currentEntry.related_entries.length > 0 && (
            <SidebarSection>
              <SidebarTitle>Related Entries</SidebarTitle>
              <RelatedEntries>
                {currentEntry.related_entries.map((entry) => (
                  <RelatedEntry key={entry.id} to={`/celebrate/${entry.type}/${entry.slug}`}>
                    <RelatedEntryType type={entry.type}>{entry.type}</RelatedEntryType>
                    <RelatedEntryName>{entry.name}</RelatedEntryName>
                  </RelatedEntry>
                ))}
              </RelatedEntries>
            </SidebarSection>
          )}
        </Sidebar>
      </EntryContent>
    </Container>
  );
};

export default CelebrateDetailPage;
