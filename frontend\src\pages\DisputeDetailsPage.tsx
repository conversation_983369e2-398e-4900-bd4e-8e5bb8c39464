import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  useTheme
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Gavel as DisputeIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { RootState } from '../../store';
import {
  fetchDisputeById,
  fetchDisputeEvidence,
  fetchDisputeMessages,
  addDisputeEvidence,
  addDisputeMessage,
  resolveDispute,
  closeDispute
} from '../../features/escrow/escrowSlice';
import DisputeDetails from '../../components/escrow/DisputeDetails';

const DisputeDetailsPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { 
    currentDispute,
    operations
  } = useSelector((state: RootState) => state.escrow);
  
  // Fetch dispute data when component mounts
  useEffect(() => {
    if (id) {
      dispatch(fetchDisputeById(id) as any);
      dispatch(fetchDisputeEvidence(id) as any);
      dispatch(fetchDisputeMessages(id) as any);
    }
  }, [dispatch, id]);
  
  // Handle add evidence
  const handleAddEvidence = (evidence: { type: string; content: string; fileUrl?: string }) => {
    if (!id) return;
    
    dispatch(addDisputeEvidence({
      disputeId: id,
      evidence: {
        type: evidence.type as any,
        content: evidence.content,
        fileUrl: evidence.fileUrl
      }
    }) as any);
  };
  
  // Handle add message
  const handleAddMessage = (message: string) => {
    if (!id) return;
    
    dispatch(addDisputeMessage({
      disputeId: id,
      message: { message }
    }) as any);
  };
  
  // Handle resolve dispute
  const handleResolveDispute = (resolution: any) => {
    if (!id) return;
    
    dispatch(resolveDispute({
      disputeId: id,
      resolution
    }) as any);
  };
  
  // Handle close dispute
  const handleCloseDispute = () => {
    if (!id) return;
    
    dispatch(closeDispute(id) as any);
  };
  
  // Check if user is admin
  const isAdmin = user?.role === 'admin';
  
  // Loading state
  const isLoading = currentDispute.loading || !currentDispute.item;
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link 
          underline="hover" 
          color="inherit" 
          href="/" 
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Home
        </Link>
        <Link
          underline="hover"
          color="inherit"
          href="/escrow"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <DisputeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Escrow & Disputes
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          Dispute #{id}
        </Typography>
      </Breadcrumbs>
      
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/escrow')}
          sx={{ mr: 2 }}
        >
          Back
        </Button>
        
        <Typography variant="h4" component="h1" fontWeight="bold">
          Dispute Details
        </Typography>
      </Box>
      
      {currentDispute.error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {currentDispute.error}
        </Alert>
      ) : isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : currentDispute.item ? (
        <DisputeDetails
          dispute={currentDispute.item}
          evidence={currentDispute.evidence}
          messages={currentDispute.messages}
          currentUserId={user?.id || ''}
          isAdmin={isAdmin}
          onAddEvidence={handleAddEvidence}
          onAddMessage={handleAddMessage}
          onResolveDispute={isAdmin ? handleResolveDispute : undefined}
          onCloseDispute={isAdmin ? handleCloseDispute : undefined}
          loading={
            operations.addingEvidence || 
            operations.addingMessage || 
            operations.resolving || 
            operations.closing
          }
        />
      ) : (
        <Alert severity="info">
          Dispute not found
        </Alert>
      )}
    </Container>
  );
};

export default DisputeDetailsPage;
