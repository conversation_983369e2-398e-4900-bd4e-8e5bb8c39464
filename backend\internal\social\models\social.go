package models

import (
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/pkg/models"
	"gorm.io/gorm"
)

// UserProfile represents enhanced user profile information
type UserProfile struct {
	gorm.Model
	UserID           uint      `json:"userId" gorm:"uniqueIndex"`
	DisplayName      string    `json:"displayName" gorm:"size:100"`
	Bio              string    `json:"bio" gorm:"type:text"`
	Location         string    `json:"location" gorm:"size:100"`
	Website          string    `json:"website" gorm:"size:255"`
	ProfileTheme     string    `json:"profileTheme" gorm:"size:50;default:'default'"`
	CoverImage       string    `json:"coverImage" gorm:"size:255"`
	AvatarImage      string    `json:"avatarImage" gorm:"size:255"`
	SocialLinks      string    `json:"socialLinks" gorm:"type:json"` // JSON string of social media links
	Interests        string    `json:"interests" gorm:"type:json"`   // JSON array of interests
	ShowReadingStats bool      `json:"showReadingStats" gorm:"default:true"`
	ShowBadges       bool      `json:"showBadges" gorm:"default:true"`
	ShowSkills       bool      `json:"showSkills" gorm:"default:true"`
	ShowActivities   bool      `json:"showActivities" gorm:"default:true"`
	IsPrivate        bool      `json:"isPrivate" gorm:"default:false"`
	LastUpdated      time.Time `json:"lastUpdated" gorm:"autoUpdateTime"`
	
	// Relationships
	Connections []Connection `json:"connections,omitempty" gorm:"foreignKey:FollowerID"`
	Followers   []Connection `json:"followers,omitempty" gorm:"foreignKey:FollowedID"`
	Posts       []Post       `json:"posts,omitempty" gorm:"foreignKey:UserID"`
	Portfolios  []Portfolio  `json:"portfolios,omitempty" gorm:"foreignKey:UserID"`
}

// ConnectionType defines the type of connection between users
type ConnectionType string

const (
	Friend   ConnectionType = "friend"
	Follow   ConnectionType = "follow"
	Blocked  ConnectionType = "blocked"
	Muted    ConnectionType = "muted"
)

// ConnectionStatus defines the status of a connection request
type ConnectionStatus string

const (
	Pending  ConnectionStatus = "pending"
	Accepted ConnectionStatus = "accepted"
	Declined ConnectionStatus = "declined"
)

// Connection represents a relationship between users
type Connection struct {
	gorm.Model
	FollowerID uint            `json:"followerId" gorm:"index:idx_connection,unique:true,priority:1"`
	FollowedID uint            `json:"followedId" gorm:"index:idx_connection,unique:true,priority:2"`
	Type       ConnectionType  `json:"type" gorm:"size:20;default:'follow'"`
	Status     ConnectionStatus `json:"status" gorm:"size:20;default:'pending'"`
	Notes      string          `json:"notes" gorm:"type:text"`
	
	// Relationships
	Follower User `json:"follower,omitempty" gorm:"foreignKey:FollowerID"`
	Followed User `json:"followed,omitempty" gorm:"foreignKey:FollowedID"`
}

// PostType defines the type of post
type PostType string

const (
	TextPost     PostType = "text"
	ImagePost    PostType = "image"
	VideoPost    PostType = "video"
	LinkPost     PostType = "link"
	BookPost     PostType = "book"
	ActivityPost PostType = "activity"
)

// PostVisibility defines who can see a post
type PostVisibility string

const (
	Public    PostVisibility = "public"
	Friends   PostVisibility = "friends"
	Private   PostVisibility = "private"
	Followers PostVisibility = "followers"
)

// Post represents a user's social post
type Post struct {
	gorm.Model
	UserID      uint           `json:"userId" gorm:"index"`
	Type        PostType       `json:"type" gorm:"size:20;default:'text'"`
	Content     string         `json:"content" gorm:"type:text"`
	MediaURLs   string         `json:"mediaUrls" gorm:"type:json"` // JSON array of media URLs
	Visibility  PostVisibility `json:"visibility" gorm:"size:20;default:'public'"`
	Location    string         `json:"location" gorm:"size:100"`
	Feeling     string         `json:"feeling" gorm:"size:50"`
	Activity    string         `json:"activity" gorm:"size:100"`
	BookID      *uint          `json:"bookId" gorm:"index"`
	ChapterID   *uint          `json:"chapterId" gorm:"index"`
	LikesCount  int            `json:"likesCount" gorm:"default:0"`
	SharesCount int            `json:"sharesCount" gorm:"default:0"`
	CommentsCount int          `json:"commentsCount" gorm:"default:0"`
	
	// Relationships
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Comments []Comment `json:"comments,omitempty" gorm:"foreignKey:PostID"`
	Likes    []Like    `json:"likes,omitempty" gorm:"foreignKey:PostID"`
}

// Comment represents a comment on a post
type Comment struct {
	gorm.Model
	PostID      uint   `json:"postId" gorm:"index"`
	UserID      uint   `json:"userId" gorm:"index"`
	Content     string `json:"content" gorm:"type:text"`
	ParentID    *uint  `json:"parentId" gorm:"index"` // For nested comments
	LikesCount  int    `json:"likesCount" gorm:"default:0"`
	
	// Relationships
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Post     Post      `json:"post,omitempty" gorm:"foreignKey:PostID"`
	Parent   *Comment  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Replies  []Comment `json:"replies,omitempty" gorm:"foreignKey:ParentID"`
	Likes    []Like    `json:"likes,omitempty" gorm:"foreignKey:CommentID"`
}

// Like represents a like on a post or comment
type Like struct {
	gorm.Model
	UserID    uint  `json:"userId" gorm:"index"`
	PostID    *uint `json:"postId" gorm:"index"`
	CommentID *uint `json:"commentId" gorm:"index"`
	
	// Relationships
	User    User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Post    *Post    `json:"post,omitempty" gorm:"foreignKey:PostID"`
	Comment *Comment `json:"comment,omitempty" gorm:"foreignKey:CommentID"`
}

// PortfolioType defines the type of portfolio item
type PortfolioType string

const (
	Project     PortfolioType = "project"
	Publication PortfolioType = "publication"
	Award       PortfolioType = "award"
	Education   PortfolioType = "education"
	Experience  PortfolioType = "experience"
	Certification PortfolioType = "certification"
)

// Portfolio represents a portfolio item in a user's profile
type Portfolio struct {
	gorm.Model
	UserID      uint         `json:"userId" gorm:"index"`
	Type        PortfolioType `json:"type" gorm:"size:20"`
	Title       string       `json:"title" gorm:"size:255"`
	Description string       `json:"description" gorm:"type:text"`
	URL         string       `json:"url" gorm:"size:255"`
	ImageURL    string       `json:"imageUrl" gorm:"size:255"`
	StartDate   *time.Time   `json:"startDate"`
	EndDate     *time.Time   `json:"endDate"`
	IsOngoing   bool         `json:"isOngoing" gorm:"default:false"`
	Order       int          `json:"order" gorm:"default:0"`
	
	// Relationships
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// ActivityType defines the type of user activity
type ActivityType string

const (
	BookRead      ActivityType = "book_read"
	BookReviewed  ActivityType = "book_reviewed"
	PostCreated   ActivityType = "post_created"
	CommentAdded  ActivityType = "comment_added"
	BadgeEarned   ActivityType = "badge_earned"
	SkillAdded    ActivityType = "skill_added"
	GroupJoined   ActivityType = "group_joined"
	EventAttended ActivityType = "event_attended"
)

// Activity represents a user activity for the activity feed
type Activity struct {
	gorm.Model
	UserID      uint         `json:"userId" gorm:"index"`
	Type        ActivityType `json:"type" gorm:"size:50;index"`
	Description string       `json:"description" gorm:"type:text"`
	Metadata    string       `json:"metadata" gorm:"type:json"` // JSON string with activity details
	RelatedID   *uint        `json:"relatedId"`                 // ID of the related entity (book, post, etc.)
	RelatedType string       `json:"relatedType" gorm:"size:50"` // Type of the related entity
	IsPublic    bool         `json:"isPublic" gorm:"default:true"`
	
	// Relationships
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// Notification represents a notification for a user
type Notification struct {
	gorm.Model
	UserID      uint   `json:"userId" gorm:"index"`
	SenderID    *uint  `json:"senderId" gorm:"index"`
	Type        string `json:"type" gorm:"size:50;index"`
	Message     string `json:"message" gorm:"type:text"`
	RelatedID   *uint  `json:"relatedId"`                 // ID of the related entity
	RelatedType string `json:"relatedType" gorm:"size:50"` // Type of the related entity
	IsRead      bool   `json:"isRead" gorm:"default:false"`
	
	// Relationships
	User   User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Sender *User `json:"sender,omitempty" gorm:"foreignKey:SenderID"`
}

// User model is now imported from shared models package
// import "github.com/yerenwgventures/GreatNigeriaLibrary/pkg/models"
