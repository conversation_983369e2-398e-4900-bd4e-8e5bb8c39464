-- Create virtual currency tables
CREATE TABLE IF NOT EXISTS virtual_currencies (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE,
    balance DECIMAL(15, 2) NOT NULL DEFAULT 0,
    total_bought DECIMAL(15, 2) NOT NULL DEFAULT 0,
    total_spent DECIMAL(15, 2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS virtual_currency_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    transaction_type VARCHAR(50) NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    balance_before DECIMAL(15, 2) NOT NULL,
    balance_after DECIMAL(15, 2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(100),
    payment_id INTEGER,
    status VARCHAR(50) NOT NULL DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS coin_packages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    coins_amount DECIMAL(15, 2) NOT NULL,
    price_naira DECIMAL(15, 2) NOT NULL,
    bonus_coins DECIMAL(15, 2) NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_promotional BOOLEAN NOT NULL DEFAULT FALSE,
    promotion_ends TIMESTAMP WITH TIME ZONE,
    description TEXT,
    image_url VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create live stream tables
CREATE TABLE IF NOT EXISTS live_streams (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnail_url VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'scheduled',
    scheduled_start TIMESTAMP WITH TIME ZONE NOT NULL,
    actual_start TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    viewer_count INTEGER NOT NULL DEFAULT 0,
    peak_viewer_count INTEGER NOT NULL DEFAULT 0,
    total_gifts_value DECIMAL(15, 2) NOT NULL DEFAULT 0,
    stream_key VARCHAR(100) NOT NULL UNIQUE,
    playback_url VARCHAR(255),
    is_private BOOLEAN NOT NULL DEFAULT FALSE,
    allow_gifting BOOLEAN NOT NULL DEFAULT TRUE,
    categories VARCHAR(255),
    tags VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS live_stream_viewers (
    id SERIAL PRIMARY KEY,
    stream_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    join_time TIMESTAMP WITH TIME ZONE NOT NULL,
    leave_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(stream_id, user_id)
);

-- Create gift tables
CREATE TABLE IF NOT EXISTS live_stream_gifts (
    id SERIAL PRIMARY KEY,
    stream_id INTEGER NOT NULL,
    sender_id INTEGER NOT NULL,
    recipient_id INTEGER NOT NULL,
    gift_id INTEGER NOT NULL,
    gift_name VARCHAR(100) NOT NULL,
    coins_amount DECIMAL(15, 2) NOT NULL,
    naira_value DECIMAL(15, 2) NOT NULL,
    message TEXT,
    is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    is_highlighted BOOLEAN NOT NULL DEFAULT FALSE,
    combo_count INTEGER NOT NULL DEFAULT 1,
    creator_revenue_percent DECIMAL(5, 2) NOT NULL DEFAULT 70.0,
    creator_revenue_amount DECIMAL(15, 2) NOT NULL,
    platform_revenue_amount DECIMAL(15, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create ranking tables
CREATE TABLE IF NOT EXISTS gifter_rankings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    stream_id INTEGER,
    ranking_period VARCHAR(50) NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_gifts INTEGER NOT NULL DEFAULT 0,
    total_coins DECIMAL(15, 2) NOT NULL DEFAULT 0,
    total_naira_value DECIMAL(15, 2) NOT NULL DEFAULT 0,
    rank INTEGER NOT NULL DEFAULT 0,
    previous_rank INTEGER,
    badge_level VARCHAR(50) NOT NULL DEFAULT 'bronze',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, stream_id, ranking_period, period_start)
);

-- Create revenue tables
CREATE TABLE IF NOT EXISTS creator_revenues (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL,
    stream_id INTEGER,
    period VARCHAR(50) NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_gifts INTEGER NOT NULL DEFAULT 0,
    total_coins DECIMAL(15, 2) NOT NULL DEFAULT 0,
    total_naira_value DECIMAL(15, 2) NOT NULL DEFAULT 0,
    platform_fee DECIMAL(15, 2) NOT NULL DEFAULT 0,
    net_revenue DECIMAL(15, 2) NOT NULL DEFAULT 0,
    is_paid BOOLEAN NOT NULL DEFAULT FALSE,
    payment_date TIMESTAMP WITH TIME ZONE,
    payment_reference VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    processed_date TIMESTAMP WITH TIME ZONE,
    processed_by INTEGER,
    notes TEXT,
    transaction_reference VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create fraud detection table
CREATE TABLE IF NOT EXISTS fraud_detection_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    risk_score DECIMAL(5, 2) NOT NULL,
    details TEXT,
    ip_address VARCHAR(50),
    device_info TEXT,
    action_taken VARCHAR(100) NOT NULL DEFAULT 'none',
    reviewed_by INTEGER,
    review_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_virtual_currency_transactions_user_id ON virtual_currency_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_live_streams_creator_id ON live_streams(creator_id);
CREATE INDEX IF NOT EXISTS idx_live_streams_status ON live_streams(status);
CREATE INDEX IF NOT EXISTS idx_live_stream_viewers_stream_id ON live_stream_viewers(stream_id);
CREATE INDEX IF NOT EXISTS idx_live_stream_viewers_user_id ON live_stream_viewers(user_id);
CREATE INDEX IF NOT EXISTS idx_live_stream_gifts_stream_id ON live_stream_gifts(stream_id);
CREATE INDEX IF NOT EXISTS idx_live_stream_gifts_sender_id ON live_stream_gifts(sender_id);
CREATE INDEX IF NOT EXISTS idx_live_stream_gifts_recipient_id ON live_stream_gifts(recipient_id);
CREATE INDEX IF NOT EXISTS idx_gifter_rankings_user_id ON gifter_rankings(user_id);
CREATE INDEX IF NOT EXISTS idx_gifter_rankings_stream_id ON gifter_rankings(stream_id);
CREATE INDEX IF NOT EXISTS idx_creator_revenues_creator_id ON creator_revenues(creator_id);
CREATE INDEX IF NOT EXISTS idx_creator_revenues_stream_id ON creator_revenues(stream_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_creator_id ON withdrawal_requests(creator_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS idx_fraud_detection_logs_user_id ON fraud_detection_logs(user_id);

-- Insert default coin packages
INSERT INTO coin_packages (name, coins_amount, price_naira, bonus_coins, display_order, description, image_url)
VALUES
    ('Starter Pack', 100, 1000, 0, 1, 'Get started with 100 coins', '/images/coin-packages/starter.png'),
    ('Popular Pack', 500, 4500, 50, 2, 'Our most popular package with 500 coins + 50 bonus coins', '/images/coin-packages/popular.png'),
    ('Super Pack', 1000, 8500, 150, 3, 'Super value with 1000 coins + 150 bonus coins', '/images/coin-packages/super.png'),
    ('Premium Pack', 5000, 40000, 1000, 4, 'Premium package with 5000 coins + 1000 bonus coins', '/images/coin-packages/premium.png'),
    ('VIP Pack', 10000, 75000, 2500, 5, 'VIP package with 10000 coins + 2500 bonus coins', '/images/coin-packages/vip.png');
