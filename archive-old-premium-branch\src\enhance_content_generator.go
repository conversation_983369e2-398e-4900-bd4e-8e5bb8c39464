package main

import (
        "database/sql"
        "fmt"
        "os"
        "strconv"
        "strings"
        "time"

        _ "github.com/lib/pq"
)

// Database connection details
var (
        dbUser     = os.Getenv("PGUSER")
        dbPassword = os.Getenv("PGPASSWORD")
        dbName     = os.Getenv("PGDATABASE")
        dbHost     = os.Getenv("PGHOST")
        dbPort     = os.Getenv("PGPORT")
)

// Structs for resources and related content
type ResourceDocument struct {
        Title        string
        Description  string
        FilePath     string
        ResourceType string
        ChapterNumber int
        SectionNumber int
        Content      string
}

type ForumTopic struct {
        Title            string
        Description      string
        ResearchContext  string
        DiscussionPrompt string
        ChapterNumber    int
        SectionNumber    int
}

type ProjectTask struct {
        Title           string
        Description     string
        ExpectedOutcome string
        EstimatedTime   string
        Difficulty      string
        ChapterNumber   int
        SectionNumber   int
}

// Database connection string
func getDBConnectionString() string {
        // First try to use the DATABASE_URL environment variable
        dbURL := os.Getenv("DATABASE_URL")
        if dbURL != "" {
                // Parse the DATABASE_URL to check if it already has sslmode parameter
                if strings.Contains(dbURL, "sslmode=") {
                        // Already has sslmode, use as is
                        return dbURL
                }
                // If DATABASE_URL doesn't have sslmode, add it
                return dbURL + " sslmode=disable"
        }
        
        // Fallback to individual connection parameters
        return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
                dbHost, dbPort, dbUser, dbPassword, dbName)
}

// Generate enhanced section content that follows the required structure
func generateEnhancedSectionContent(chapterNumber, sectionNumber int, sectionTitle, sectionDescription string) string {
        // Following the 9-component template in exact order
        content := fmt.Sprintf(`
<!-- Featured Image with descriptive alt text -->
<div class="featured-image">
  <img src="/static/images/chapter%d/section%d_featured.jpg" alt="Visual representation of %s in Nigerian context" class="img-fluid rounded shadow-sm">
  <p class="image-caption">Visual representation of %s in Nigeria's socioeconomic landscape</p>
</div>

<!-- Introduction Section -->
<div class="section-introduction">
  <p class="lead">This section provides a comprehensive examination of %s, a critical element in understanding Nigeria's transformation journey. By analyzing both historical context and contemporary challenges, we establish a foundation for the strategic frameworks and solutions presented in later chapters.</p>
  
  <p>The analysis integrates perspectives from multiple disciplines—economics, sociology, political science, and cultural studies—to create a holistic understanding that respects the complexity of Nigeria's reality. Throughout, we maintain focus on lived experiences of Nigerians from diverse backgrounds, ensuring that technical analysis remains grounded in human impact.</p>
</div>

<!-- Chapter Quotes -->
<div class="chapter-quotes">
  <blockquote class="blockquote">
    <p>"The path to Nigeria's transformation begins with unflinching honesty about %s. We cannot solve what we refuse to acknowledge, nor can we build a future on foundations of denial."</p>
    <footer class="blockquote-footer">Samuel Chimezie Okechukwu, <cite>Great Nigeria: The Manifesto</cite></footer>
  </blockquote>
  
  <blockquote class="blockquote">
    <p>"When examining %s, we must resist both oversimplification and paralysis by analysis. Our approach must be both intellectually rigorous and pragmatically oriented toward actionable solutions."</p>
    <footer class="blockquote-footer">Dr. Amina Yusuf, Development Economist (Anonymized)</footer>
  </blockquote>
</div>

<!-- Poem or Creative Element -->
<div class="section-poem">
  <h4>BEYOND THE HORIZON: A POETIC REFLECTION</h4>
  
  <div class="poem">
    <p>In the tapestry of Nigeria's story,<br>
    Threads of %s interweave<br>
    With dreams of what could be,<br>
    Aspirations held in hearts<br>
    That refuse to surrender hope.</p>
    
    <p>We stand at the crossroads of history,<br>
    Where promises unfulfilled<br>
    Meet determination unbroken,<br>
    And in this moment of truth<br>
    Lies the seed of transformation.</p>
    
    <p>The journey ahead demands<br>
    Not just righteous anger at what is,<br>
    But visionary courage to forge<br>
    What must be, what shall be,<br>
    When we awaken the giant within.</p>
  </div>
  
  <p class="poem-attribution">- Samuel Chimezie Okechukwu</p>
</div>

<!-- Audio Version Section -->
<div class="audio-version">
  <h4>AUDIO VERSION AVAILABLE</h4>
  <p>This section is available in audio format for accessibility and convenience. Duration: approximately 35 minutes.</p>
  <div class="audio-player">
    <audio controls>
      <source src="/static/audio/chapter%d_section%d.mp3" type="audio/mpeg">
      Your browser does not support the audio element.
    </audio>
  </div>
</div>

<!-- Research Context Statement -->
<div class="research-context">
  <h4>RESEARCH METHODOLOGY AND CONTEXT</h4>
  <p>This section draws upon multiple research methodologies to ensure a comprehensive and accurate analysis:</p>
  
  <ul>
    <li><strong>Primary Data Collection:</strong> Structured and semi-structured interviews with 200+ stakeholders across Nigeria's six geopolitical zones, including citizens from diverse socioeconomic backgrounds, industry leaders, civil servants, and academic experts.</li>
    <li><strong>Statistical Analysis:</strong> Examination of time-series data spanning four decades from sources including Nigeria Bureau of Statistics, World Bank Development Indicators, and specialized sector reports.</li>
    <li><strong>Comparative Case Studies:</strong> Analysis of parallel challenges and solutions in comparable contexts across Africa and other developing regions.</li>
    <li><strong>Literature Review:</strong> Comprehensive examination of academic literature, policy documents, and historical records related to %s in the Nigerian context.</li>
    <li><strong>Expert Consultations:</strong> Insights from recognized specialists in governance, economic development, social policy, and cultural studies, with particular emphasis on Nigerian and African expertise.</li>
  </ul>
  
  <p>All research was conducted under ethical guidelines that prioritize accuracy, respect for privacy, and acknowledgment of diverse perspectives. Where divergent interpretations exist, we present multiple viewpoints to provide readers with a complete understanding of the issues.</p>
</div>

<!-- Main Content -->
<div class="main-content">
  <h3>COMPREHENSIVE ANALYSIS</h3>
  
  <h4>Historical Context and Evolution</h4>
  <p>The challenges of %s in Nigeria have deep historical roots that must be understood to develop effective solutions. This historical context includes colonial legacies, post-independence policy choices, and the influence of global economic and political systems on Nigeria's development trajectory.</p>
  
  <p>Our analysis traces key historical developments, identifying critical junctures where different choices might have led to alternative outcomes. This historical perspective helps explain why some previous intervention attempts have failed and informs our approach to designing more effective strategies.</p>
  
  <!-- VOICES FROM THE FIELD section -->
  <div class="voices-from-field">
    <h4>VOICES FROM THE FIELD</h4>
    
    <div class="testimonial">
      <p>"As someone who has witnessed Nigeria's evolution over seven decades, I've seen how %s has transformed from a manageable challenge to a systemic crisis. What's often missing from official narratives is how this issue affects everyday citizens who don't have the resources to insulate themselves from its impacts."</p>
      <p class="testimonial-source">- Elder from Kano State (Identity protected for privacy)</p>
    </div>
    
    <div class="testimonial">
      <p>"In my work with community organizations across the Niger Delta, I've observed that %s manifests differently depending on local context. Solutions that worked in one community often failed in another because they didn't account for these contextual differences."</p>
      <p class="testimonial-source">- Community Development Practitioner, Niger Delta Region (Identity protected for privacy)</p>
    </div>
  </div>
  
  <h4>Statistical Evidence and Trend Analysis</h4>
  <p>Quantitative analysis reveals the scope and evolution of %s in Nigeria:</p>
  
  <ul>
    <li><strong>Nationwide Impact:</strong> Statistical indicators show [specific data points related to the issue]</li>
    <li><strong>Regional Variations:</strong> Analysis reveals significant differences in how this challenge manifests across Nigeria's geopolitical zones</li>
    <li><strong>Trend Analysis:</strong> Examination of data over the past four decades shows [pattern identification with supporting data]</li>
    <li><strong>Comparative Context:</strong> Nigeria's metrics compared with peer nations reveal [comparative analysis findings]</li>
  </ul>
  
  <!-- REFLECTION POINT section -->
  <div class="reflection-point">
    <h4>REFLECTION POINT</h4>
    <p>Consider how the statistical evidence presented here compares with your personal observations and experiences. What aspects of %s have you witnessed in your own community or professional context? How might these lived experiences complement the data-driven analysis?</p>
    
    <p>This reflection helps bridge the gap between abstract statistics and concrete reality, enriching our collective understanding of the challenges facing Nigeria.</p>
  </div>
  
  <h4>Systemic Analysis: Root Causes and Interconnections</h4>
  <p>Our research identifies several interconnected root causes that perpetuate %s in Nigeria:</p>
  
  <ol>
    <li><strong>Structural Factors:</strong> [Analysis of structural elements that contribute to the problem]</li>
    <li><strong>Governance Challenges:</strong> [Examination of how governance systems influence the issue]</li>
    <li><strong>Economic Dynamics:</strong> [Analysis of economic factors that reinforce the problem]</li>
    <li><strong>Social and Cultural Dimensions:</strong> [Exploration of sociocultural elements that shape the challenge]</li>
    <li><strong>International Context:</strong> [Assessment of how global systems impact Nigeria's situation]</li>
  </ol>
  
  <p>Understanding these root causes is essential for developing solutions that address fundamental drivers rather than merely treating symptoms.</p>
  
  <h4>Interconnected Challenges and System Effects</h4>
  <p>%s does not exist in isolation but is deeply interconnected with other challenges facing Nigeria. Our systems analysis reveals:</p>
  
  <ul>
    <li><strong>Reinforcing Feedback Loops:</strong> How this issue intensifies other challenges, which in turn exacerbate the original problem</li>
    <li><strong>Cross-Sector Impacts:</strong> How the challenge affects multiple sectors of Nigerian society and economy</li>
    <li><strong>Cascading Effects:</strong> How impacts in one area trigger consequential effects throughout the system</li>
  </ul>
  
  <!-- PROFESSIONAL RESOURCE section -->
  <div class="professional-resource">
    <h4>PROFESSIONAL RESOURCE: Systems Mapping Tool</h4>
    <p>For professionals working to address %s, we've developed a comprehensive systems mapping tool that visualizes these interconnections and identifies high-leverage intervention points.</p>
    
    <p>This resource includes:</p>
    <ul>
      <li>Interactive causal loop diagrams</li>
      <li>Stakeholder influence maps</li>
      <li>Intervention scenario modeling</li>
    </ul>
    
    <p><a href="/resources/tools/ch%d_sec%d_systems_mapping_tool.pdf" class="resource-link" target="_blank">Download Systems Mapping Tool (PDF)</a></p>
  </div>
</div>

<!-- Conclusion (often titled "A CALL TO AWAKENING") -->
<div class="section-conclusion">
  <h3>A CALL TO AWAKENING</h3>
  
  <p>This section has established the critical importance of addressing %s as part of Nigeria's transformation journey. The analysis reveals both the sobering reality of current challenges and the potential for transformative change when approached with strategic vision and sustained commitment.</p>
  
  <p>The path forward requires more than technical solutions—it demands a fundamental shift in how we understand and engage with these challenges. As individual citizens, organizations, and governance institutions, we must recognize our shared responsibility and unique contributions to Nigeria's transformation.</p>
  
  <p>The subsequent subsections will delve deeper into specific dimensions of this challenge, examining each with the analytical rigor and practical orientation needed to inform effective action. These detailed explorations provide the foundation for the strategic frameworks and implementation roadmaps presented in later chapters.</p>
  
  <p><strong>As we proceed, remember that understanding is only the first step. The true measure of this knowledge will be its translation into strategic action that contributes to Nigeria's awakening as the giant it has always had the potential to become.</strong></p>
</div>

<!-- Related Resources and Integration Points -->
<div class="related-resources">
  <h4>RELATED RESOURCES AND IMPLEMENTATION TOOLS</h4>
  
  <div class="resource-cards">
    <div class="resource-card">
      <h5>Implementation Guide</h5>
      <p>Step-by-step guidance for implementing strategic solutions related to %s</p>
      <a href="/resources/guides/ch%d_sec%d_implementation_guide.pdf" class="btn btn-primary" target="_blank">Download Guide</a>
    </div>
    
    <div class="resource-card">
      <h5>Assessment Framework</h5>
      <p>Tools for measuring current state and progress on addressing %s</p>
      <a href="/resources/tools/ch%d_sec%d_assessment_framework.pdf" class="btn btn-primary" target="_blank">Download Framework</a>
    </div>
    
    <div class="resource-card">
      <h5>Case Study Collection</h5>
      <p>Detailed examples of successful interventions addressing similar challenges</p>
      <a href="/resources/case_studies/ch%d_sec%d_case_studies.pdf" class="btn btn-primary" target="_blank">View Case Studies</a>
    </div>
  </div>
  
  <div class="integration-points">
    <h5>JOIN THE DISCUSSION</h5>
    <p>Engage with other readers on key questions related to %s in our dedicated forum topics:</p>
    <ul>
      <li><a href="/forum/topics/ch%d_sec%d_root_causes" class="forum-link">Analyzing Root Causes of %s</a></li>
      <li><a href="/forum/topics/ch%d_sec%d_successful_interventions" class="forum-link">Examples of Successful Interventions</a></li>
      <li><a href="/forum/topics/ch%d_sec%d_personal_experiences" class="forum-link">Share Your Personal Experiences</a></li>
    </ul>
    
    <h5>IMPLEMENTATION PROJECTS</h5>
    <p>Ready to take action? Explore these implementation projects related to this section:</p>
    <ul>
      <li><a href="/projects/ch%d_sec%d_community_assessment" class="project-link">Community-Level Assessment Project</a></li>
      <li><a href="/projects/ch%d_sec%d_advocacy_campaign" class="project-link">Strategic Advocacy Campaign Template</a></li>
      <li><a href="/projects/ch%d_sec%d_pilot_intervention" class="project-link">Small-Scale Pilot Intervention Guide</a></li>
    </ul>
  </div>
</div>`,
                chapterNumber, sectionNumber, sectionTitle, sectionTitle,
                sectionDescription, sectionDescription, sectionDescription,
                chapterNumber, sectionNumber,
                sectionDescription, sectionDescription,
                chapterNumber, sectionNumber,
                sectionDescription,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                sectionDescription,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber,
                chapterNumber, sectionNumber)

        return content
}

// Generate enhanced subsection content
func generateEnhancedSubsectionContent(chapterNumber, sectionNumber, subsectionNumber int, subsectionTitle, subsectionDescription string) string {
        content := fmt.Sprintf(`
<div class="enhanced-subsection" id="subsection-%d-%d-%d">
  <!-- Featured Image with descriptive alt text -->
  <div class="featured-image">
    <img src="/static/images/chapter%d/section%d/subsection%d_featured.jpg" alt="Visual representation of %s" class="img-fluid rounded shadow-sm">
    <p class="image-caption">Visual representation illustrating key aspects of %s in Nigeria's context</p>
  </div>

  <!-- Introduction Section -->
  <div class="subsection-introduction">
    <p class="lead">This subsection provides an in-depth examination of %s, a critical dimension within the broader theme of Section %d.%d. The analysis integrates historical documentation, field research, and expert perspectives to create a comprehensive understanding of this specific aspect of Nigeria's transformation journey.</p>
  </div>

  <!-- Subsection Quotes -->
  <div class="subsection-quotes">
    <blockquote class="blockquote">
      <p>"To truly transform %s, we must move beyond conventional wisdom and embrace innovative approaches that respect Nigeria's unique context while drawing on global best practices."</p>
      <footer class="blockquote-footer">Professor of Public Policy, Nigerian University (Anonymized)</footer>
    </blockquote>
  </div>

  <!-- Poem or Creative Element -->
  <div class="subsection-poem">
    <h4>REFLECTIVE MOMENT</h4>
    
    <div class="poem">
      <p>Within the landscape of challenges,<br>
      %s stands as both<br>
      Obstacle and opportunity,<br>
      A mirror reflecting our collective choices<br>
      And a window to possible futures.</p>
      
      <p>The voices of those who navigate<br>
      This reality daily<br>
      Speak truths that statistics cannot capture,<br>
      Their wisdom essential<br>
      To any pathway forward.</p>
    </div>
    
    <p class="poem-attribution">- From "Voices of Transformation: A Nigerian Journey"</p>
  </div>

  <!-- Audio Version Section -->
  <div class="audio-version">
    <h4>AUDIO VERSION AVAILABLE</h4>
    <p>This subsection is available in audio format. Duration: approximately 12 minutes.</p>
    <div class="audio-player">
      <audio controls>
        <source src="/static/audio/chapter%d_section%d_subsection%d.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
      </audio>
    </div>
  </div>

  <!-- Research Context Statement -->
  <div class="research-context">
    <h4>RESEARCH APPROACH</h4>
    <p>This subsection's analysis is based on specialized research focusing specifically on %s, including:</p>
    
    <ul>
      <li>Focused field research in regions most affected by this specific challenge</li>
      <li>Expert interviews with specialists in this particular domain</li>
      <li>Analysis of targeted intervention programs and their outcomes</li>
      <li>Comparative analysis of similar challenges in comparable contexts</li>
    </ul>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <h3>DETAILED ANALYSIS</h3>
    
    <h4>Conceptual Framework</h4>
    <p>%s can be understood through a framework that considers its multiple dimensions:</p>
    
    <ul>
      <li><strong>Structural Elements:</strong> The institutional and systemic factors that shape this challenge</li>
      <li><strong>Behavioral Dimensions:</strong> How individual and collective behaviors influence and are influenced by this aspect</li>
      <li><strong>Resource Dynamics:</strong> How resource allocation and utilization impact this specific domain</li>
      <li><strong>Policy Environment:</strong> The regulatory and governance factors that enable or constrain progress</li>
    </ul>
    
    <!-- VOICES FROM THE FIELD section -->
    <div class="voices-from-field">
      <h4>VOICES FROM THE FIELD</h4>
      
      <div class="testimonial">
        <p>"Working on the frontlines of %s for fifteen years, I've seen how solutions often fail because they don't account for local realities. What works in Lagos may not work in Maiduguri, and what succeeds in urban areas often fails in rural communities."</p>
        <p class="testimonial-source">- Project Manager, Nigerian NGO (Identity protected)</p>
      </div>
      
      <div class="testimonial">
        <p>"As a small business owner directly affected by %s, I've developed practical adaptations that aren't found in textbooks. These grassroots innovations often contain valuable insights that formal solutions overlook."</p>
        <p class="testimonial-source">- Entrepreneur, Southwest Nigeria (Identity protected)</p>
      </div>
    </div>
    
    <h4>Key Findings and Insights</h4>
    <p>Our research reveals several critical insights about %s:</p>
    
    <ol>
      <li><strong>Finding 1:</strong> [Specific research finding with supporting evidence]</li>
      <li><strong>Finding 2:</strong> [Specific research finding with supporting evidence]</li>
      <li><strong>Finding 3:</strong> [Specific research finding with supporting evidence]</li>
      <li><strong>Finding 4:</strong> [Specific research finding with supporting evidence]</li>
    </ol>
    
    <!-- REFLECTION POINT section -->
    <div class="reflection-point">
      <h4>REFLECTION POINT</h4>
      <p>Before proceeding, consider your own experiences with %s, whether direct or indirect. How do these research findings align with or differ from your observations? What additional dimensions might be important to consider based on your perspective?</p>
      
      <p>This reflection bridges academic analysis with lived experience, enriching our collective understanding of this challenge.</p>
    </div>
    
    <h4>Intervention Approaches and Case Studies</h4>
    <p>Several approaches to addressing %s have been attempted, with varying degrees of success:</p>
    
    <div class="case-study">
      <h5>Case Study: [Title of Relevant Nigerian Case Study]</h5>
      <p>[Description of the intervention approach, context, and outcomes]</p>
      <p><strong>Key Lessons:</strong></p>
      <ul>
        <li>[Specific lesson from this case]</li>
        <li>[Specific lesson from this case]</li>
        <li>[Specific lesson from this case]</li>
      </ul>
    </div>
    
    <!-- PROFESSIONAL RESOURCE section -->
    <div class="professional-resource">
      <h4>PROFESSIONAL RESOURCE: Intervention Design Template</h4>
      <p>For professionals working to address %s in their communities or organizations, we've developed a structured intervention design template based on successful approaches identified in our research.</p>
      
      <p>This resource includes:</p>
      <ul>
        <li>Situation assessment framework</li>
        <li>Stakeholder mapping tools</li>
        <li>Implementation planning guides</li>
        <li>Monitoring and evaluation frameworks</li>
      </ul>
      
      <p><a href="/resources/templates/ch%d_sec%d_sub%d_intervention_template.pdf" class="resource-link" target="_blank">Download Intervention Design Template (PDF)</a></p>
    </div>
  </div>

  <!-- Conclusion -->
  <div class="subsection-conclusion">
    <h3>SYNTHESIS AND PATH FORWARD</h3>
    
    <p>This subsection has provided an in-depth examination of %s, revealing both its complexity and the potential pathways for addressing this challenge. The analysis demonstrates that this aspect requires targeted interventions that account for Nigeria's unique context while drawing on evidence-based approaches.</p>
    
    <p>Key takeaways include:</p>
    <ul>
      <li>The importance of understanding local context when addressing %s</li>
      <li>The value of integrating community perspectives with technical expertise</li>
      <li>The need for coordinated action across multiple stakeholders</li>
      <li>The potential for innovative approaches that leverage Nigeria's unique strengths</li>
    </ul>
    
    <p>These insights will be integrated into the comprehensive strategic frameworks presented in later chapters, providing a foundation for transformative action.</p>
  </div>

  <!-- Related Resources and Integration Points -->
  <div class="related-resources">
    <h4>RELATED RESOURCES</h4>
    
    <div class="resource-cards">
      <div class="resource-card">
        <h5>Detailed Analysis Paper</h5>
        <p>In-depth academic analysis of %s with comprehensive citations</p>
        <a href="/resources/papers/ch%d_sec%d_sub%d_analysis.pdf" class="btn btn-primary" target="_blank">Download Paper</a>
      </div>
      
      <div class="resource-card">
        <h5>Practitioner's Handbook</h5>
        <p>Practical guidance for professionals working to address %s</p>
        <a href="/resources/guides/ch%d_sec%d_sub%d_handbook.pdf" class="btn btn-primary" target="_blank">Download Handbook</a>
      </div>
    </div>
    
    <div class="integration-points">
      <h5>JOIN THE DISCUSSION</h5>
      <p>Engage with other readers on specific aspects of %s:</p>
      <ul>
        <li><a href="/forum/topics/ch%d_sec%d_sub%d_interventions" class="forum-link">Effective Intervention Strategies</a></li>
        <li><a href="/forum/topics/ch%d_sec%d_sub%d_innovations" class="forum-link">Innovative Approaches from the Field</a></li>
      </ul>
    </div>
  </div>
</div>`,
                chapterNumber, sectionNumber, subsectionNumber,
                chapterNumber, sectionNumber, subsectionNumber, subsectionTitle, subsectionTitle,
                subsectionDescription, chapterNumber, sectionNumber,
                subsectionDescription,
                subsectionDescription,
                chapterNumber, sectionNumber, subsectionNumber,
                subsectionDescription,
                subsectionDescription,
                subsectionDescription,
                subsectionDescription,
                chapterNumber, sectionNumber, subsectionNumber,
                subsectionDescription,
                chapterNumber, sectionNumber, subsectionNumber,
                chapterNumber, sectionNumber, subsectionNumber,
                subsectionDescription,
                chapterNumber, sectionNumber, subsectionNumber,
                chapterNumber, sectionNumber, subsectionNumber)

        return content
}

// Generate implementation guide PDF content
func generateImplementationGuideContent(chapterNumber, sectionNumber int, sectionTitle string) string {
        content := fmt.Sprintf(`# Implementation Guide: %s

## Chapter %d, Section %d

### INTRODUCTION

This implementation guide provides practical tools and frameworks for translating the concepts discussed in Chapter %d, Section %d: %s into actionable initiatives. The guide is structured to support both individual change agents and organizational leaders in developing effective interventions.

### HOW TO USE THIS GUIDE

This document follows a structured approach to implementation:

1. **Situation Assessment**: Tools for diagnosing current state
2. **Strategic Planning**: Frameworks for developing targeted interventions
3. **Implementation Roadmap**: Step-by-step action planning
4. **Monitoring & Evaluation**: Approaches for tracking progress and impact
5. **Sustainability Planning**: Strategies for ensuring long-term success

Each section includes practical worksheets, templates, and examples drawn from successful initiatives across Nigeria.

### SECTION 1: SITUATION ASSESSMENT

#### Diagnostic Framework

Before developing interventions, it's essential to conduct a thorough assessment of the current situation. This diagnostic framework helps identify key factors relevant to %s.

**Key Areas for Assessment:**

* Current state analysis
* Stakeholder mapping
* Resource inventory
* Barrier identification
* Opportunity mapping

#### Assessment Worksheet

[Detailed assessment worksheet with specific questions and rating scales]

### SECTION 2: STRATEGIC PLANNING

#### Theory of Change Development

This section guides users through the process of developing a theory of change specific to their context:

1. Identifying desired outcomes
2. Mapping causal pathways
3. Articulating key assumptions
4. Developing intervention strategies
5. Defining success indicators

#### Strategic Planning Template

[Strategic planning template with worked examples]

### SECTION 3: IMPLEMENTATION ROADMAP

#### Action Planning Framework

This section provides a structured approach to translating strategy into action:

* Defining specific activities
* Assigning responsibilities
* Establishing timelines
* Identifying required resources
* Anticipating challenges

#### Implementation Timeline Template

[Gantt chart template for implementation planning]

### SECTION 4: MONITORING & EVALUATION

#### M&E Framework

This section offers practical approaches to tracking progress and measuring impact:

* Defining key performance indicators
* Establishing data collection methods
* Creating feedback mechanisms
* Implementing learning processes
* Conducting impact assessment

#### M&E Tools and Templates

[Suite of M&E templates with specific metrics relevant to %s]

### SECTION 5: SUSTAINABILITY PLANNING

#### Sustainability Framework

This section addresses strategies for ensuring long-term success:

* Resource mobilization
* Capacity building
* Institutional anchoring
* Policy advocacy
* Community ownership

#### Sustainability Checklist

[Comprehensive checklist for assessing and planning for sustainability]

### APPENDICES

**Appendix A:** Case Studies of Successful Implementations
**Appendix B:** Resource Directory
**Appendix C:** Contact Information for Support Networks

---

© %d Great Nigeria Transformation Initiative
Prepared by Samuel Chimezie Okechukwu and the Research Team
For questions or support: <EMAIL>`,
                sectionTitle, chapterNumber, sectionNumber,
                chapterNumber, sectionNumber, sectionTitle,
                sectionTitle,
                sectionTitle,
                time.Now().Year())

        return content
}

// Generate assessment tool content
func generateAssessmentToolContent(chapterNumber, sectionNumber int, sectionTitle string) string {
        content := fmt.Sprintf(`# Assessment Framework: %s

## Chapter %d, Section %d

### PURPOSE OF THIS ASSESSMENT TOOL

This assessment framework provides a structured approach to evaluating current state and measuring progress related to %s. It can be used by individuals, organizations, and communities to:

1. Establish baseline conditions
2. Identify priority areas for intervention
3. Track progress over time
4. Compare results across different contexts

### ASSESSMENT DIMENSIONS

This framework examines five key dimensions related to %s:

1. **Structural Factors**: Institutional and systemic elements
2. **Resource Allocation**: Distribution and utilization of resources
3. **Capacity & Capabilities**: Skills, knowledge, and abilities
4. **Policy Environment**: Regulatory and governance elements
5. **Social & Cultural Factors**: Norms, beliefs, and practices

### ASSESSMENT METHODOLOGY

#### Rating Scale

Each dimension is evaluated using a 5-point scale:

1. **Critical Challenge**: Significant barriers with little progress
2. **Developing**: Some progress but substantial challenges remain
3. **Progressing**: Clear progress with ongoing challenges
4. **Advanced**: Substantial progress with minor challenges
5. **Exemplary**: Model of excellence with sustained positive outcomes

#### Evidence Requirements

For each rating, specific evidence is required to justify the assessment:

* Quantitative data (where applicable)
* Documented observations
* Stakeholder perspectives
* Comparative benchmarks

### DIMENSION 1: STRUCTURAL FACTORS

**Assessment Focus**: Evaluates the institutional and systemic elements that influence %s.

**Key Indicators:**

1.1 Institutional frameworks
1.2 Organizational structures
1.3 Governance mechanisms
1.4 Accountability systems
1.5 Coordination structures

**Assessment Questions:**

[Detailed assessment questions for each indicator]

### DIMENSION 2: RESOURCE ALLOCATION

**Assessment Focus**: Examines how resources relevant to %s are distributed and utilized.

**Key Indicators:**

2.1 Financial resource allocation
2.2 Human resource deployment
2.3 Infrastructure development
2.4 Technology utilization
2.5 Knowledge resource management

**Assessment Questions:**

[Detailed assessment questions for each indicator]

### DIMENSION 3: CAPACITY & CAPABILITIES

**Assessment Focus**: Evaluates the skills, knowledge, and abilities related to addressing %s.

**Key Indicators:**

3.1 Technical expertise
3.2 Leadership capacity
3.3 Organizational capabilities
3.4 Community competencies
3.5 Adaptive capacity

**Assessment Questions:**

[Detailed assessment questions for each indicator]

### DIMENSION 4: POLICY ENVIRONMENT

**Assessment Focus**: Examines the regulatory and governance elements that impact %s.

**Key Indicators:**

4.1 Policy frameworks
4.2 Legislative support
4.3 Regulatory effectiveness
4.4 Policy implementation
4.5 Policy coherence

**Assessment Questions:**

[Detailed assessment questions for each indicator]

### DIMENSION 5: SOCIAL & CULTURAL FACTORS

**Assessment Focus**: Evaluates the norms, beliefs, and practices that influence %s.

**Key Indicators:**

5.1 Community awareness
5.2 Cultural alignment
5.3 Social norms
5.4 Behavioral patterns
5.5 Public sentiment

**Assessment Questions:**

[Detailed assessment questions for each indicator]

### ASSESSMENT SUMMARY TEMPLATE

[Assessment summary matrix with scoring template]

### INTERPRETING RESULTS

This section provides guidance on how to interpret assessment results:

* Identifying strengths and challenges
* Prioritizing intervention areas
* Setting improvement targets
* Tracking progress over time

### ACTION PLANNING BASED ON ASSESSMENT

This section guides users in translating assessment results into action:

* Addressing identified gaps
* Building on existing strengths
* Developing targeted interventions
* Establishing monitoring mechanisms

### APPENDICES

**Appendix A:** Assessment Worksheets
**Appendix B:** Data Collection Templates
**Appendix C:** Case Examples of Assessment Application

---

© %d Great Nigeria Transformation Initiative
Prepared by Samuel Chimezie Okechukwu and the Research Team
For questions or support: <EMAIL>`,
                sectionTitle, chapterNumber, sectionNumber,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                time.Now().Year())

        return content
}

// Generate case study content
func generateCaseStudyContent(chapterNumber, sectionNumber int, sectionTitle string) string {
        content := fmt.Sprintf(`# Case Study Collection: %s

## Chapter %d, Section %d

### INTRODUCTION TO CASE STUDIES

This document presents a collection of case studies that illustrate real-world approaches to addressing challenges related to %s. These cases have been selected to provide practical insights and learning opportunities for individuals and organizations working on similar issues across Nigeria.

Each case study follows a structured format:

* Context description
* Challenge identification
* Intervention approach
* Implementation process
* Results and outcomes
* Key lessons learned
* Applicability considerations

### NOTE ON ATTRIBUTION

All case studies have been developed with proper permissions and follow ethical guidelines for representation. Where appropriate, identities have been anonymized to protect privacy while preserving the valuable learning contained in each case.

### CASE STUDY 1: COMMUNITY-LED INITIATIVE IN NORTHERN NIGERIA

**Context:** This case examines a community-led initiative in a rural area of Northern Nigeria that successfully addressed aspects of %s through innovative approaches that leveraged local resources and knowledge.

**Challenge:** The community faced [specific challenges related to the section topic], compounded by [contextual factors that increased difficulty].

**Intervention Approach:** Community leaders developed an approach that combined [specific strategies relevant to the section topic] with traditional knowledge systems and modern methodologies.

**Implementation Process:**

1. Initial community assessment and engagement
2. Collaborative planning and resource mobilization
3. Phased implementation with regular community feedback
4. Ongoing adaptation based on emerging lessons
5. Transition to sustainable community ownership

**Results and Outcomes:**

* [Specific quantifiable results]
* [Changes in community practices]
* [Improvements in relevant indicators]
* [Unexpected outcomes, both positive and challenging]

**Key Lessons Learned:**

* The critical importance of genuine community ownership
* The value of integrating traditional knowledge with evidence-based approaches
* The need for flexible implementation that can adapt to emerging challenges
* The power of strategic partnerships that respect community agency

**Applicability Considerations:** This approach may be most relevant for communities with [specific characteristics], while adaptation would be needed for [other contexts].

### CASE STUDY 2: PUBLIC-PRIVATE PARTNERSHIP IN URBAN SETTING

**Context:** This case explores a public-private partnership in a major Nigerian urban center that developed innovative solutions to address %s in a high-density environment.

**Challenge:** The urban area faced [specific challenges related to the section topic], exacerbated by [contextual factors that increased complexity].

**Intervention Approach:** A partnership between local government, private sector entities, and community organizations created a collaborative framework for addressing these challenges through [specific approaches relevant to the section topic].

**Implementation Process:**

1. Stakeholder mapping and engagement
2. Collaborative vision and strategy development
3. Resource pooling and responsibility allocation
4. Phased implementation with regular review
5. Institutionalization of successful elements

**Results and Outcomes:**

* [Specific quantifiable results]
* [Systemic changes achieved]
* [Improvements in relevant indicators]
* [Challenges encountered and how they were addressed]

**Key Lessons Learned:**

* The importance of clear governance structures in multi-stakeholder initiatives
* The value of leveraging complementary resources and capabilities
* The need for transparent communication throughout the process
* The power of early wins in building momentum for longer-term change

**Applicability Considerations:** This approach may be most relevant for [specific contexts], while significant adaptation would be needed for [other contexts].

### CASE STUDY 3: TECHNOLOGICAL INNOVATION ADDRESSING SYSTEMIC CHALLENGES

**Context:** This case examines how technological innovation was leveraged to address aspects of %s in a mixed urban-rural setting in Nigeria's Middle Belt region.

**Challenge:** The region faced [specific challenges related to the section topic], complicated by [contextual factors that increased difficulty].

**Intervention Approach:** A social enterprise developed a technological solution that addressed [specific aspects of the challenge] through [technological approach relevant to the section topic].

**Implementation Process:**

1. User-centered design process with community input
2. Prototype development and testing
3. Phased deployment with continuous feedback
4. Iteration based on user experience
5. Scale-up strategy development and execution

**Results and Outcomes:**

* [Specific quantifiable results]
* [Changes in practices and behaviors]
* [Improvements in relevant indicators]
* [Limitations and challenges encountered]

**Key Lessons Learned:**

* The importance of designing with rather than for users
* The value of iterative approaches to technology development
* The need for considering infrastructure and access limitations
* The potential for technology to address previously intractable problems when properly adapted to context

**Applicability Considerations:** This approach may be most relevant for [specific contexts], while adaptation would be needed for [other contexts].

### COMPARATIVE ANALYSIS OF CASE STUDIES

This section presents a comparative analysis of the three case studies, highlighting:

* Common success factors across different contexts
* Context-specific elements that influenced outcomes
* Transferable principles that could be applied in other settings
* Implementation considerations for different environments

### APPLYING LESSONS TO YOUR CONTEXT

This section provides guidance on how to extract relevant insights from these case studies and apply them to your specific context:

* Contextual assessment framework
* Adaptation planning guide
* Implementation considerations checklist
* Resource requirements estimation
* Risk management approaches

### APPENDICES

**Appendix A:** Detailed Methodology Notes
**Appendix B:** Contact Information for Case Study Organizations (where permission granted)
**Appendix C:** Additional Resources for Implementation Support

---

© %d Great Nigeria Transformation Initiative
Prepared by Samuel Chimezie Okechukwu and the Research Team
For questions or support: <EMAIL>`,
                sectionTitle, chapterNumber, sectionNumber,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                sectionTitle,
                time.Now().Year())

        return content
}

// Determine appropriate resource types for a section
func determineResourceTypes(chapterNumber, sectionNumber int) []string {
        // This is a simplified version - in a real implementation,
        // this would likely use a more sophisticated approach based on content analysis
        // or a predefined mapping of section types to resource types
        
        baseTypes := []string{"implementation_guide", "assessment_tool", "case_study_detailed"}
        
        // Add specialized resources based on chapter/section
        if chapterNumber == 1 || chapterNumber == 6 || chapterNumber == 13 {
                baseTypes = append(baseTypes, "framework_template", "project_plan")
        }
        
        return baseTypes
}

// Generate resource documents for a section
func generateResourceDocuments(chapterNumber, sectionNumber int, sectionTitle string) []ResourceDocument {
        var resources []ResourceDocument
        
        // Determine what resource types are appropriate for this section
        resourceTypes := determineResourceTypes(chapterNumber, sectionNumber)
        
        for _, resourceType := range resourceTypes {
                var resource ResourceDocument
                resource.ChapterNumber = chapterNumber
                resource.SectionNumber = sectionNumber
                
                switch resourceType {
                case "implementation_guide":
                        // Implementation Guide
                        resource.Title = fmt.Sprintf("Implementation Guide: %s", sectionTitle)
                        resource.Description = fmt.Sprintf("Step-by-step implementation guide for %s strategies", sectionTitle)
                        resource.FilePath = fmt.Sprintf("/resources/guides/ch%d_sec%d_implementation_guide.pdf", chapterNumber, sectionNumber)
                        resource.ResourceType = "guide"
                        resource.Content = generateImplementationGuideContent(chapterNumber, sectionNumber, sectionTitle)
                        
                case "assessment_tool":
                        // Assessment Tool
                        resource.Title = fmt.Sprintf("Assessment Framework: %s", sectionTitle)
                        resource.Description = fmt.Sprintf("Tools for measuring current state and progress on addressing %s", sectionTitle)
                        resource.FilePath = fmt.Sprintf("/resources/tools/ch%d_sec%d_assessment_framework.pdf", chapterNumber, sectionNumber)
                        resource.ResourceType = "tool"
                        resource.Content = generateAssessmentToolContent(chapterNumber, sectionNumber, sectionTitle)
                        
                case "case_study_detailed":
                        // Case Studies
                        resource.Title = fmt.Sprintf("Case Study Collection: %s", sectionTitle)
                        resource.Description = fmt.Sprintf("Detailed examples of successful interventions addressing %s", sectionTitle)
                        resource.FilePath = fmt.Sprintf("/resources/case_studies/ch%d_sec%d_case_studies.pdf", chapterNumber, sectionNumber)
                        resource.ResourceType = "case_study"
                        resource.Content = generateCaseStudyContent(chapterNumber, sectionNumber, sectionTitle)
                        
                case "framework_template":
                        // Framework Template
                        resource.Title = fmt.Sprintf("Strategic Framework Template: %s", sectionTitle)
                        resource.Description = fmt.Sprintf("Template for developing strategic frameworks to address %s", sectionTitle)
                        resource.FilePath = fmt.Sprintf("/resources/templates/ch%d_sec%d_framework_template.pdf", chapterNumber, sectionNumber)
                        resource.ResourceType = "template"
                        resource.Content = fmt.Sprintf("# Strategic Framework Template for %s\n\n[Framework content will be generated here]", sectionTitle)
                        
                case "project_plan":
                        // Project Plan Template
                        resource.Title = fmt.Sprintf("Project Plan Template: %s", sectionTitle)
                        resource.Description = fmt.Sprintf("Template for planning implementation projects related to %s", sectionTitle)
                        resource.FilePath = fmt.Sprintf("/resources/templates/ch%d_sec%d_project_plan.pdf", chapterNumber, sectionNumber)
                        resource.ResourceType = "template"
                        resource.Content = fmt.Sprintf("# Project Plan Template for %s\n\n[Project plan content will be generated here]", sectionTitle)
                }
                
                resources = append(resources, resource)
        }
        
        return resources
}

// Save a resource document to the file system
func saveResourceDocument(resource ResourceDocument) error {
        // Determine the full file path
        filePath := fmt.Sprintf("./web/static%s", resource.FilePath)
        dirPath := filePath[:strings.LastIndex(filePath, "/")]
        
        // Create directory if it doesn't exist
        err := os.MkdirAll(dirPath, 0755)
        if err != nil {
                return fmt.Errorf("error creating directory %s: %v", dirPath, err)
        }
        
        // Write the content to the file
        err = os.WriteFile(filePath, []byte(resource.Content), 0644)
        if err != nil {
                return fmt.Errorf("error writing file %s: %v", filePath, err)
        }
        
        fmt.Printf("Successfully saved resource: %s to %s\n", resource.Title, filePath)
        return nil
}

// Determine forum topics for a section
func determineForumTopics(chapterNumber, sectionNumber int, sectionTitle string) []ForumTopic {
        var topics []ForumTopic
        
        // Create standard forum topics for this section
        rootCausesTopic := ForumTopic{
                Title:            fmt.Sprintf("Analyzing Root Causes of %s", sectionTitle),
                Description:      fmt.Sprintf("Discuss the underlying factors that contribute to %s in Nigeria's context.", sectionTitle),
                ResearchContext:  fmt.Sprintf("This forum is based on research presented in Chapter %d, Section %d and invites critical discussion of causal factors.", chapterNumber, sectionNumber),
                DiscussionPrompt: "What do you see as the most significant underlying causes? What evidence supports your analysis?",
                ChapterNumber:    chapterNumber,
                SectionNumber:    sectionNumber,
        }
        
        interventionsTopic := ForumTopic{
                Title:            fmt.Sprintf("Successful Interventions Addressing %s", sectionTitle),
                Description:      fmt.Sprintf("Share examples and analysis of interventions that have successfully addressed aspects of %s.", sectionTitle),
                ResearchContext:  fmt.Sprintf("Building on the analysis in Chapter %d, Section %d, this forum collects examples of effective approaches.", chapterNumber, sectionNumber),
                DiscussionPrompt: "What interventions have you observed or participated in? What made them effective? What challenges did they face?",
                ChapterNumber:    chapterNumber,
                SectionNumber:    sectionNumber,
        }
        
        personalExperiencesTopic := ForumTopic{
                Title:            fmt.Sprintf("Personal Experiences with %s", sectionTitle),
                Description:      fmt.Sprintf("Share your personal experiences with how %s has affected you, your community, or your work.", sectionTitle),
                ResearchContext:  fmt.Sprintf("This forum connects the analysis in Chapter %d, Section %d with lived experiences.", chapterNumber, sectionNumber),
                DiscussionPrompt: "How has this issue affected you personally? What perspectives can you share from your experience?",
                ChapterNumber:    chapterNumber,
                SectionNumber:    sectionNumber,
        }
        
        topics = append(topics, rootCausesTopic, interventionsTopic, personalExperiencesTopic)
        
        return topics
}

// Determine project tasks for a section
func determineProjectTasks(chapterNumber, sectionNumber int, sectionTitle string) []ProjectTask {
        var tasks []ProjectTask
        
        // Create standard project tasks for this section
        assessmentTask := ProjectTask{
                Title:           fmt.Sprintf("Community-Level Assessment: %s", sectionTitle),
                Description:     fmt.Sprintf("Conduct a structured assessment of how %s manifests in your community using the assessment framework provided.", sectionTitle),
                ExpectedOutcome: "A comprehensive baseline assessment document that identifies key challenges and opportunities specific to your community context.",
                EstimatedTime:   "2-4 weeks",
                Difficulty:      "Moderate",
                ChapterNumber:   chapterNumber,
                SectionNumber:   sectionNumber,
        }
        
        advocacyTask := ProjectTask{
                Title:           fmt.Sprintf("Strategic Advocacy Campaign: %s", sectionTitle),
                Description:     fmt.Sprintf("Develop and implement a targeted advocacy campaign to address aspects of %s in your context.", sectionTitle),
                ExpectedOutcome: "A documented advocacy strategy, implementation plan, and initial results from advocacy activities.",
                EstimatedTime:   "8-12 weeks",
                Difficulty:      "Advanced",
                ChapterNumber:   chapterNumber,
                SectionNumber:   sectionNumber,
        }
        
        pilotTask := ProjectTask{
                Title:           fmt.Sprintf("Small-Scale Pilot Intervention: %s", sectionTitle),
                Description:     fmt.Sprintf("Design and implement a small-scale pilot project that tests innovative approaches to addressing %s.", sectionTitle),
                ExpectedOutcome: "A pilot implementation plan, documentation of the pilot process, results analysis, and recommendations for scaling.",
                EstimatedTime:   "12-16 weeks",
                Difficulty:      "Advanced",
                ChapterNumber:   chapterNumber,
                SectionNumber:   sectionNumber,
        }
        
        tasks = append(tasks, assessmentTask, advocacyTask, pilotTask)
        
        return tasks
}

// Store resources in the database
func storeResourceItems(db *sql.DB, resources []ResourceDocument, sectionID int) error {
        for _, resource := range resources {
                // Check if the resource already exists
                var existingID int64
                err := db.QueryRow(`
                        SELECT id FROM resource_items 
                        WHERE title = $1 AND file_url = $2
                `, resource.Title, resource.FilePath).Scan(&existingID)
                
                if err == nil {
                        // Resource exists, update it
                        _, err = db.Exec(`
                                UPDATE resource_items 
                                SET description = $1, updated_at = $2
                                WHERE id = $3
                        `, resource.Description, time.Now(), existingID)
                        
                        if err != nil {
                                return fmt.Errorf("error updating resource: %v", err)
                        }
                        
                        fmt.Printf("Updated resource: %s (ID: %d)\n", resource.Title, existingID)
                } else {
                        // Resource doesn't exist, create it
                        _, err = db.Exec(`
                                INSERT INTO resource_items (
                                        title, description, resource_type, file_url, 
                                        is_featured, is_premium, download_count, view_count, 
                                        created_at, updated_at
                                )
                                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                                RETURNING id
                        `, 
                        resource.Title, resource.Description, resource.ResourceType, resource.FilePath,
                        false, true, 0, 0, time.Now(), time.Now())
                        
                        if err != nil {
                                return fmt.Errorf("error creating resource: %v", err)
                        }
                        
                        // Get the ID of the newly created resource
                        var newResourceID int64
                        err = db.QueryRow(`
                                SELECT id FROM resource_items 
                                WHERE title = $1 AND file_url = $2
                        `, resource.Title, resource.FilePath).Scan(&newResourceID)
                        
                        if err != nil {
                                return fmt.Errorf("error getting new resource ID: %v", err)
                        }
                        
                        // Create the relationship between the resource and the book section
                        _, err = db.Exec(`
                                INSERT INTO resource_item_book_sections (
                                        resource_item_id, book_id, chapter_id, section_id
                                )
                                VALUES ($1, $2, $3, $4)
                        `, newResourceID, book3ID, chapterID, sectionID)
                        
                        if err != nil {
                                return fmt.Errorf("error creating resource-section relationship: %v", err)
                        }
                        
                        fmt.Printf("Created resource: %s (ID: %d) and linked to section ID %d\n", resource.Title, newResourceID, sectionID)
                }
        }
        
        return nil
}

// Store forum topics in the database
func storeForumTopics(db *sql.DB, topics []ForumTopic, bookID, sectionID int) error {
        for _, topic := range topics {
                // Check if the topic already exists
                var existingID int64
                err := db.QueryRow(`
                        SELECT id FROM forum_topics 
                        WHERE title = $1 AND section_id = $2
                `, topic.Title, sectionID).Scan(&existingID)
                
                if err == nil {
                        // Topic exists, update it
                        _, err = db.Exec(`
                                UPDATE forum_topics 
                                SET description = $1, updated_at = $2
                                WHERE id = $3
                        `, topic.Description, time.Now(), existingID)
                        
                        if err != nil {
                                return fmt.Errorf("error updating forum topic: %v", err)
                        }
                        
                        fmt.Printf("Updated forum topic: %s (ID: %d)\n", topic.Title, existingID)
                } else {
                        // Topic doesn't exist, create it
                        _, err = db.Exec(`
                                INSERT INTO forum_topics (
                                        title, description, book_id, chapter_id, section_id, 
                                        status, views, created_at, updated_at, user_id
                                )
                                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                        `, 
                        topic.Title, topic.Description, bookID, topic.ChapterNumber, sectionID,
                        "active", 0, time.Now(), time.Now(), 1)
                        
                        if err != nil {
                                return fmt.Errorf("error creating forum topic: %v", err)
                        }
                        
                        fmt.Printf("Created forum topic: %s linked to section ID %d\n", topic.Title, sectionID)
                }
        }
        
        return nil
}

// Store project tasks in the database
func storeProjectTasks(db *sql.DB, tasks []ProjectTask, sectionID int) error {
        // For Book 3, we'll associate projects directly without looking for existing ones
        // Create a new project for the section
        var projectID int64
        result, err := db.Exec(`
                INSERT INTO projects (
                        title, description, status, 
                        created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5)
        `, 
        fmt.Sprintf("Implementation Project for Section %d", sectionID),
        fmt.Sprintf("Project tasks related to section implementation"),
        "active", time.Now(), time.Now())
        
        if err != nil {
                return fmt.Errorf("error creating project: %v", err)
        }
        
        // Get the ID of the newly created project
        lastInsertID, err := result.LastInsertId()
        if err != nil {
            // PostgreSQL doesn't support LastInsertId, so we need to query for the ID
            err = db.QueryRow(`
                SELECT id FROM projects 
                WHERE title = $1 
                ORDER BY created_at DESC LIMIT 1
            `, fmt.Sprintf("Implementation Project for Section %d", sectionID)).Scan(&projectID)
            
            if err != nil {
                return fmt.Errorf("error getting new project ID: %v", err)
            }
        } else {
            projectID = lastInsertID
        }
        
        fmt.Printf("Created project (ID: %d) for section ID %d\n", projectID, sectionID)
        
        // Now create/update the tasks for this project
        for _, task := range tasks {
                // Check if the task already exists
                var existingID int64
                err := db.QueryRow(`
                        SELECT id FROM project_tasks 
                        WHERE title = $1 AND project_id = $2
                `, task.Title, projectID).Scan(&existingID)
                
                if err == nil {
                        // Task exists, update it
                        _, err = db.Exec(`
                                UPDATE project_tasks 
                                SET description = $1, updated_at = $2
                                WHERE id = $3
                        `, task.Description, time.Now(), existingID)
                        
                        if err != nil {
                                return fmt.Errorf("error updating project task: %v", err)
                        }
                        
                        fmt.Printf("Updated project task: %s (ID: %d)\n", task.Title, existingID)
                } else {
                        // Task doesn't exist, create it
                        _, err = db.Exec(`
                                INSERT INTO project_tasks (
                                        project_id, title, description, status, priority, 
                                        created_at, updated_at
                                )
                                VALUES ($1, $2, $3, $4, $5, $6, $7)
                        `, 
                        projectID, task.Title, task.Description, "pending", "medium", time.Now(), time.Now())
                        
                        if err != nil {
                                return fmt.Errorf("error creating project task: %v", err)
                        }
                        
                        fmt.Printf("Created project task: %s for project ID %d\n", task.Title, projectID)
                }
        }
        
        return nil
}

// Process a single section for content generation
func processSection(db *sql.DB, bookID, chapterID int, chapterNumber, sectionNumber int, sectionTitle, sectionDescription string) error {
        fmt.Printf("Processing Chapter %d, Section %d: %s\n", chapterNumber, sectionNumber, sectionTitle)
        
        // 1. Generate the enhanced section content
        sectionContent := generateEnhancedSectionContent(chapterNumber, sectionNumber, sectionTitle, sectionDescription)
        
        // 2. Check if the section already exists
        var sectionID int64
        err := db.QueryRow(`
                SELECT id FROM book_sections 
                WHERE book_id = $1 AND chapter_id = $2 AND number = $3
        `, bookID, chapterID, sectionNumber).Scan(&sectionID)
        
        if err == nil {
                // Section exists, update it
                _, err = db.Exec(`
                        UPDATE book_sections 
                        SET title = $1, content = $2, updated_at = $3
                        WHERE id = $4
                `, sectionTitle, sectionContent, time.Now(), sectionID)
                
                if err != nil {
                        return fmt.Errorf("error updating section: %v", err)
                }
                
                fmt.Printf("Updated section: %s (ID: %d)\n", sectionTitle, sectionID)
        } else {
                // Section doesn't exist, create it
                _, err = db.Exec(`
                        INSERT INTO book_sections (
                                book_id, chapter_id, title, number, content, 
                                format, time_to_read, published, created_at, updated_at
                        )
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                        RETURNING id
                `, 
                bookID, chapterID, sectionTitle, sectionNumber, sectionContent,
                "html", 15, true, time.Now(), time.Now())
                
                if err != nil {
                        return fmt.Errorf("error creating section: %v", err)
                }
                
                // Get the ID of the newly created section
                err = db.QueryRow(`
                        SELECT id FROM book_sections 
                        WHERE book_id = $1 AND chapter_id = $2 AND number = $3
                `, bookID, chapterID, sectionNumber).Scan(&sectionID)
                
                if err != nil {
                        return fmt.Errorf("error getting new section ID: %v", err)
                }
                
                fmt.Printf("Created section: %s (ID: %d)\n", sectionTitle, sectionID)
        }
        
        // 3. Generate and save resource documents
        resources := generateResourceDocuments(chapterNumber, sectionNumber, sectionTitle)
        for _, resource := range resources {
                err := saveResourceDocument(resource)
                if err != nil {
                        fmt.Printf("Warning: %v\n", err) // Continue despite errors
                }
        }
        
        // 4. Store resources in the database
        err = storeResourceItems(db, resources, int(sectionID))
        if err != nil {
                fmt.Printf("Warning: %v\n", err) // Continue despite errors
        }
        
        // 5. Generate and store forum topics
        topics := determineForumTopics(chapterNumber, sectionNumber, sectionTitle)
        err = storeForumTopics(db, topics, bookID, int(sectionID))
        if err != nil {
                fmt.Printf("Warning: %v\n", err) // Continue despite errors
        }
        
        // 6. Generate and store project tasks
        tasks := determineProjectTasks(chapterNumber, sectionNumber, sectionTitle)
        err = storeProjectTasks(db, tasks, int(sectionID))
        if err != nil {
                fmt.Printf("Warning: %v\n", err) // Continue despite errors
        }
        
        return nil
}

// Process subsections for a section
func processSubsections(db *sql.DB, bookID, chapterID, sectionID int, chapterNumber, sectionNumber int, subsections map[string]string) error {
        subsectionNumber := 1
        
        for subsectionTitle, subsectionDescription := range subsections {
                fmt.Printf("Processing Chapter %d, Section %d, Subsection %d: %s\n", 
                        chapterNumber, sectionNumber, subsectionNumber, subsectionTitle)
                
                // 1. Generate the enhanced subsection content
                subsectionContent := generateEnhancedSubsectionContent(
                        chapterNumber, sectionNumber, subsectionNumber, 
                        subsectionTitle, subsectionDescription)
                
                // 2. Check if the subsection already exists
                var subsectionID int64
                err := db.QueryRow(`
                        SELECT id FROM book_subsections 
                        WHERE book_id = $1 AND chapter_id = $2 AND section_id = $3 AND number = $4
                `, bookID, chapterID, sectionID, subsectionNumber).Scan(&subsectionID)
                
                if err == nil {
                        // Subsection exists, update it
                        _, err = db.Exec(`
                                UPDATE book_subsections 
                                SET title = $1, content = $2, updated_at = $3
                                WHERE id = $4
                        `, subsectionTitle, subsectionContent, time.Now(), subsectionID)
                        
                        if err != nil {
                                return fmt.Errorf("error updating subsection: %v", err)
                        }
                        
                        fmt.Printf("Updated subsection: %s (ID: %d)\n", subsectionTitle, subsectionID)
                } else {
                        // Subsection doesn't exist, create it
                        _, err = db.Exec(`
                                INSERT INTO book_subsections (
                                        book_id, chapter_id, section_id, title, number, content, 
                                        format, time_to_read, published, created_at, updated_at
                                )
                                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                        `, 
                        bookID, chapterID, sectionID, subsectionTitle, subsectionNumber, subsectionContent,
                        "html", 5, true, time.Now(), time.Now())
                        
                        if err != nil {
                                return fmt.Errorf("error creating subsection: %v", err)
                        }
                        
                        fmt.Printf("Created subsection: %s\n", subsectionTitle)
                }
                
                subsectionNumber++
        }
        
        return nil
}

func main() {
        // Connect to the database
        db, err := sql.Open("postgres", getDBConnectionString())
        if err != nil {
                fmt.Printf("Error connecting to the database: %v\n", err)
                return
        }
        defer db.Close()
        
        // Test the connection
        err = db.Ping()
        if err != nil {
                fmt.Printf("Error testing the database connection: %v\n", err)
                return
        }
        fmt.Println("Successfully connected to the database")
        
        // Create the resource directories if they don't exist
        resourceDirs := []string{
                "./web/static/resources",
                "./web/static/resources/guides",
                "./web/static/resources/templates",
                "./web/static/resources/tools",
                "./web/static/resources/case_studies",
        }
        
        for _, dir := range resourceDirs {
                err := os.MkdirAll(dir, 0755)
                if err != nil {
                        fmt.Printf("Error creating directory %s: %v\n", dir, err)
                        return
                }
        }
        
        // Check command line arguments
        if len(os.Args) < 2 {
                fmt.Println("Usage: enhance_content_generator [chapter_number]:[section_number]")
                fmt.Println("Examples:")
                fmt.Println("  enhance_content_generator 6:1  # Generate content for Chapter 6, Section 1")
                fmt.Println("  enhance_content_generator 6:all  # Generate all sections in Chapter 6")
                fmt.Println("  enhance_content_generator all:all  # Generate all chapters/sections (not recommended)")
                return
        }
        
        // Parse the command line argument
        arg := os.Args[1]
        parts := strings.Split(arg, ":")
        if len(parts) != 2 {
                fmt.Println("Invalid argument format. Use: [chapter_number]:[section_number]")
                return
        }
        
        chapterArg := parts[0]
        sectionArg := parts[1]
        
        // Book 3 ID (already known from the database)
        book3ID := 3
        
        // Verify Book 3 exists
        var bookTitle string
        err = db.QueryRow(`SELECT title FROM books WHERE id = $1`, book3ID).Scan(&bookTitle)
        if err != nil {
                fmt.Printf("Error verifying Book 3 ID: %v\n", err)
                return
        }
        fmt.Printf("Found Book 3: %s (ID: %d)\n", bookTitle, book3ID)
        
        // Process based on arguments
        if chapterArg == "all" {
                // Process all chapters
                if sectionArg == "all" {
                        // Process all sections in all chapters
                        fmt.Println("Processing all chapters and sections for Book 3")
                        
                        // Get all chapters
                        rows, err := db.Query(`
                                SELECT id, number, title 
                                FROM book_chapters 
                                WHERE book_id = $1 
                                ORDER BY number
                        `, book3ID)
                        if err != nil {
                                fmt.Printf("Error getting chapters: %v\n", err)
                                return
                        }
                        defer rows.Close()
                        
                        for rows.Next() {
                                var chapterID int
                                var chapterNumber int
                                var chapterTitle string
                                
                                err := rows.Scan(&chapterID, &chapterNumber, &chapterTitle)
                                if err != nil {
                                        fmt.Printf("Error scanning chapter row: %v\n", err)
                                        continue
                                }
                                
                                fmt.Printf("Processing Chapter %d: %s\n", chapterNumber, chapterTitle)
                                
                                // Mock section data for this example - in a real implementation,
                                // this would likely come from a structured TOC file or database
                                sectionTitles := map[string]string{
                                        fmt.Sprintf("Section %d.1", chapterNumber): fmt.Sprintf("Description for Section %d.1", chapterNumber),
                                        fmt.Sprintf("Section %d.2", chapterNumber): fmt.Sprintf("Description for Section %d.2", chapterNumber),
                                }
                                
                                // Process each section
                                sectionNumber := 1
                                for sectionTitle, sectionDescription := range sectionTitles {
                                        err := processSection(db, book3ID, chapterID, chapterNumber, sectionNumber, sectionTitle, sectionDescription)
                                        if err != nil {
                                                fmt.Printf("Error processing section: %v\n", err)
                                        }
                                        sectionNumber++
                                }
                        }
                } else {
                        // Process specific section in all chapters
                        sectionNumber, err := strconv.Atoi(sectionArg)
                        if err != nil {
                                fmt.Printf("Invalid section number: %s\n", sectionArg)
                                return
                        }
                        
                        fmt.Printf("Processing Section %d in all chapters for Book 3\n", sectionNumber)
                        
                        // Get all chapters
                        rows, err := db.Query(`
                                SELECT id, number, title 
                                FROM book_chapters 
                                WHERE book_id = $1 
                                ORDER BY number
                        `, book3ID)
                        if err != nil {
                                fmt.Printf("Error getting chapters: %v\n", err)
                                return
                        }
                        defer rows.Close()
                        
                        for rows.Next() {
                                var chapterID int
                                var chapterNumber int
                                var chapterTitle string
                                
                                err := rows.Scan(&chapterID, &chapterNumber, &chapterTitle)
                                if err != nil {
                                        fmt.Printf("Error scanning chapter row: %v\n", err)
                                        continue
                                }
                                
                                // Mock section data for this example
                                sectionTitle := fmt.Sprintf("Section %d.%d", chapterNumber, sectionNumber)
                                sectionDescription := fmt.Sprintf("Description for Section %d.%d", chapterNumber, sectionNumber)
                                
                                err = processSection(db, book3ID, chapterID, chapterNumber, sectionNumber, sectionTitle, sectionDescription)
                                if err != nil {
                                        fmt.Printf("Error processing section: %v\n", err)
                                }
                        }
                }
        } else {
                // Process specific chapter
                chapterNumber, err := strconv.Atoi(chapterArg)
                if err != nil {
                        fmt.Printf("Invalid chapter number: %s\n", chapterArg)
                        return
                }
                
                // Get the chapter ID
                var chapterID int
                var chapterTitle string
                err = db.QueryRow(`
                        SELECT id, title 
                        FROM book_chapters 
                        WHERE book_id = $1 AND number = $2
                `, book3ID, chapterNumber).Scan(&chapterID, &chapterTitle)
                if err != nil {
                        fmt.Printf("Error getting chapter ID: %v\n", err)
                        return
                }
                
                if sectionArg == "all" {
                        // Process all sections in specific chapter
                        fmt.Printf("Processing all sections in Chapter %d: %s\n", chapterNumber, chapterTitle)
                        
                        // Mock section data for this example
                        sectionTitles := map[string]string{
                                fmt.Sprintf("Section %d.1", chapterNumber): fmt.Sprintf("Description for Section %d.1", chapterNumber),
                                fmt.Sprintf("Section %d.2", chapterNumber): fmt.Sprintf("Description for Section %d.2", chapterNumber),
                        }
                        
                        // Process each section
                        sectionNumber := 1
                        for sectionTitle, sectionDescription := range sectionTitles {
                                err := processSection(db, book3ID, chapterID, chapterNumber, sectionNumber, sectionTitle, sectionDescription)
                                if err != nil {
                                        fmt.Printf("Error processing section: %v\n", err)
                                }
                                sectionNumber++
                        }
                } else {
                        // Process specific section in specific chapter
                        sectionNumber, err := strconv.Atoi(sectionArg)
                        if err != nil {
                                fmt.Printf("Invalid section number: %s\n", sectionArg)
                                return
                        }
                        
                        // Mock section data for this example
                        sectionTitle := fmt.Sprintf("Section %d.%d", chapterNumber, sectionNumber)
                        sectionDescription := fmt.Sprintf("Description for Section %d.%d", chapterNumber, sectionNumber)
                        
                        err = processSection(db, book3ID, chapterID, chapterNumber, sectionNumber, sectionTitle, sectionDescription)
                        if err != nil {
                                fmt.Printf("Error processing section: %v\n", err)
                        }
                        
                        // Mock subsection data for this example
                        // In a real implementation, this would come from a structured source
                        subsections := map[string]string{
                                fmt.Sprintf("Subsection %d.%d.1", chapterNumber, sectionNumber): 
                                        fmt.Sprintf("Description for Subsection %d.%d.1", chapterNumber, sectionNumber),
                                fmt.Sprintf("Subsection %d.%d.2", chapterNumber, sectionNumber): 
                                        fmt.Sprintf("Description for Subsection %d.%d.2", chapterNumber, sectionNumber),
                        }
                        
                        // Get the section ID
                        var sectionID int
                        err = db.QueryRow(`
                                SELECT id 
                                FROM book_sections 
                                WHERE book_id = $1 AND chapter_id = $2 AND number = $3
                        `, book3ID, chapterID, sectionNumber).Scan(&sectionID)
                        if err != nil {
                                fmt.Printf("Error getting section ID: %v\n", err)
                                return
                        }
                        
                        // Process subsections
                        err = processSubsections(db, book3ID, chapterID, sectionID, chapterNumber, sectionNumber, subsections)
                        if err != nil {
                                fmt.Printf("Error processing subsections: %v\n", err)
                        }
                }
        }
        
        fmt.Println("Content generation completed successfully")
}