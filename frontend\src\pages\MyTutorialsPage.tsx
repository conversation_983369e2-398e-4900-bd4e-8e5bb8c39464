import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  IconButton,
  Button,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  MoreVert as MoreVertIcon,
  School as SchoolIcon,
  AccessTime as AccessTimeIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchUserTutorials, deleteTutorial } from '../store/slices/tutorialsSlice';
import { Tutorial } from '../services/tutorialsService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tutorial-tabpanel-${index}`}
      aria-labelledby={`tutorial-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `tutorial-tab-${index}`,
    'aria-controls': `tutorial-tabpanel-${index}`,
  };
}

const MyTutorialsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { items: tutorials, loading, error } = useSelector(
    (state: RootState) => state.tutorials.userTutorials
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [tabValue, setTabValue] = useState(0);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTutorial, setSelectedTutorial] = useState<Tutorial | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  useEffect(() => {
    if (user) {
      dispatch(fetchUserTutorials(user.id));
    }
  }, [dispatch, user]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, tutorial: Tutorial) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedTutorial(tutorial);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };
  
  const handleDeleteConfirm = async () => {
    if (selectedTutorial) {
      try {
        await dispatch(deleteTutorial(selectedTutorial.id)).unwrap();
        setDeleteDialogOpen(false);
        setSelectedTutorial(null);
      } catch (error) {
        console.error('Failed to delete tutorial:', error);
      }
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };
  
  const handleCreateTutorial = () => {
    navigate('/tutorials/create');
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to view your tutorials. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  const publishedTutorials = tutorials.filter((tutorial) => tutorial.isPublished);
  const draftTutorials = tutorials.filter((tutorial) => !tutorial.isPublished);
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          My Tutorials
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateTutorial}
        >
          Create Tutorial
        </Button>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="tutorial tabs">
            <Tab label="All Tutorials" {...a11yProps(0)} />
            <Tab label="Published" {...a11yProps(1)} />
            <Tab label="Drafts" {...a11yProps(2)} />
          </Tabs>
        </Box>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        ) : (
          <>
            <TabPanel value={tabValue} index={0}>
              {tutorials.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You haven't created any tutorials yet.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateTutorial}
                    sx={{ mt: 2 }}
                  >
                    Create Your First Tutorial
                  </Button>
                </Box>
              ) : (
                <List>
                  {tutorials.map((tutorial) => (
                    <React.Fragment key={tutorial.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar
                            alt={tutorial.title}
                            src={tutorial.thumbnailURL || '/images/tutorial-placeholder.jpg'}
                            variant="rounded"
                            sx={{ width: 80, height: 60, mr: 1 }}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="h6" component="span">
                                {tutorial.title}
                              </Typography>
                              <Chip
                                label={tutorial.isPublished ? 'Published' : 'Draft'}
                                color={tutorial.isPublished ? 'success' : 'default'}
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {tutorial.description.length > 120
                                  ? `${tutorial.description.substring(0, 120)}...`
                                  : tutorial.description}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.difficulty}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.estimatedTime} min
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Created: {new Date(tutorial.createdAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, tutorial)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              {publishedTutorials.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You don't have any published tutorials yet.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateTutorial}
                    sx={{ mt: 2 }}
                  >
                    Create a Tutorial
                  </Button>
                </Box>
              ) : (
                <List>
                  {publishedTutorials.map((tutorial) => (
                    <React.Fragment key={tutorial.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar
                            alt={tutorial.title}
                            src={tutorial.thumbnailURL || '/images/tutorial-placeholder.jpg'}
                            variant="rounded"
                            sx={{ width: 80, height: 60, mr: 1 }}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="h6" component="span">
                              {tutorial.title}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {tutorial.description.length > 120
                                  ? `${tutorial.description.substring(0, 120)}...`
                                  : tutorial.description}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.difficulty}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.estimatedTime} min
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Published: {new Date(tutorial.updatedAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, tutorial)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
            
            <TabPanel value={tabValue} index={2}>
              {draftTutorials.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You don't have any draft tutorials.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateTutorial}
                    sx={{ mt: 2 }}
                  >
                    Create a Tutorial
                  </Button>
                </Box>
              ) : (
                <List>
                  {draftTutorials.map((tutorial) => (
                    <React.Fragment key={tutorial.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar
                            alt={tutorial.title}
                            src={tutorial.thumbnailURL || '/images/tutorial-placeholder.jpg'}
                            variant="rounded"
                            sx={{ width: 80, height: 60, mr: 1 }}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="h6" component="span">
                              {tutorial.title}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {tutorial.description.length > 120
                                  ? `${tutorial.description.substring(0, 120)}...`
                                  : tutorial.description}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.difficulty}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {tutorial.estimatedTime} min
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Last updated: {new Date(tutorial.updatedAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, tutorial)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
          </>
        )}
      </Paper>
      
      {/* Tutorial actions menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            navigate(`/tutorials/${selectedTutorial?.id}`);
            handleMenuClose();
          }}
        >
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View
        </MenuItem>
        <MenuItem
          onClick={() => {
            navigate(`/tutorials/edit/${selectedTutorial?.id}`);
            handleMenuClose();
          }}
        >
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
      
      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Tutorial</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the tutorial "{selectedTutorial?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default MyTutorialsPage;
