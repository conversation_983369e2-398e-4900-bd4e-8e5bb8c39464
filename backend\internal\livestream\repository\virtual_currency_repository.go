package repository

import (
	"context"
	"errors"
	"time"

	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/livestream/models"
	"gorm.io/gorm"
)

// VirtualCurrency represents a user's virtual currency balance
type VirtualCurrency struct {
	gorm.Model
	UserID      uint    `json:"userId" gorm:"uniqueIndex"`
	Balance     float64 `json:"balance" gorm:"default:0"`
	TotalBought float64 `json:"totalBought" gorm:"default:0"`
	TotalSpent  float64 `json:"totalSpent" gorm:"default:0"`
}

// VirtualCurrencyTransaction represents a transaction in the virtual currency system
type VirtualCurrencyTransaction struct {
	gorm.Model
	UserID          uint    `json:"userId" gorm:"index"`
	TransactionType string  `json:"transactionType" gorm:"type:varchar(50)"` // purchase, gift_sent, gift_received, refund, bonus
	Amount          float64 `json:"amount"`
	BalanceBefore   float64 `json:"balanceBefore"`
	BalanceAfter    float64 `json:"balanceAfter"`
	Description     string  `json:"description" gorm:"type:text"`
	ReferenceID     string  `json:"referenceId" gorm:"type:varchar(100)"`
	PaymentID       *uint   `json:"paymentId"`
	Status          string  `json:"status" gorm:"type:varchar(50);default:'completed'"`
}

// CoinPackage represents a purchasable package of virtual coins
type CoinPackage struct {
	gorm.Model
	Name           string  `json:"name" gorm:"type:varchar(100)"`
	CoinsAmount    float64 `json:"coinsAmount"`
	PriceNaira     float64 `json:"priceNaira"`
	BonusCoins     float64 `json:"bonusCoins" gorm:"default:0"`
	IsActive       bool    `json:"isActive" gorm:"default:true"`
	DisplayOrder   int     `json:"displayOrder" gorm:"default:0"`
	IsPromotional  bool    `json:"isPromotional" gorm:"default:false"`
	PromotionEnds  *time.Time `json:"promotionEnds"`
	Description    string  `json:"description" gorm:"type:text"`
	ImageURL       string  `json:"imageUrl" gorm:"type:varchar(255)"`
}

// VirtualCurrencyRepository defines the interface for virtual currency data access
type VirtualCurrencyRepository interface {
	// User balance operations
	GetUserBalance(ctx context.Context, userID uint) (*VirtualCurrency, error)
	CreateUserBalance(ctx context.Context, userID uint) (*VirtualCurrency, error)
	UpdateUserBalance(ctx context.Context, currency *VirtualCurrency) error
	
	// Transaction operations
	CreateTransaction(ctx context.Context, transaction *VirtualCurrencyTransaction) error
	GetTransactionByID(ctx context.Context, id uint) (*VirtualCurrencyTransaction, error)
	GetUserTransactions(ctx context.Context, userID uint, page, limit int) ([]VirtualCurrencyTransaction, int, error)
	
	// Coin package operations
	GetCoinPackages(ctx context.Context) ([]CoinPackage, error)
	GetCoinPackageByID(ctx context.Context, id uint) (*CoinPackage, error)
	CreateCoinPackage(ctx context.Context, pkg *CoinPackage) error
	UpdateCoinPackage(ctx context.Context, pkg *CoinPackage) error
	DeleteCoinPackage(ctx context.Context, id uint) error
	
	// Balance operations
	AddCoinsToUser(ctx context.Context, userID uint, amount float64, transactionType, description, referenceID string, paymentID *uint) error
	DeductCoinsFromUser(ctx context.Context, userID uint, amount float64, transactionType, description, referenceID string) error
}

// VirtualCurrencyRepositoryImpl implements the VirtualCurrencyRepository interface
type VirtualCurrencyRepositoryImpl struct {
	db *gorm.DB
}

// NewVirtualCurrencyRepository creates a new instance of the virtual currency repository
func NewVirtualCurrencyRepository(db *gorm.DB) VirtualCurrencyRepository {
	return &VirtualCurrencyRepositoryImpl{
		db: db,
	}
}

// GetUserBalance retrieves a user's virtual currency balance
func (r *VirtualCurrencyRepositoryImpl) GetUserBalance(ctx context.Context, userID uint) (*VirtualCurrency, error) {
	var currency VirtualCurrency
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&currency)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Create a new balance record if not found
			return r.CreateUserBalance(ctx, userID)
		}
		return nil, result.Error
	}
	return &currency, nil
}

// CreateUserBalance creates a new virtual currency balance for a user
func (r *VirtualCurrencyRepositoryImpl) CreateUserBalance(ctx context.Context, userID uint) (*VirtualCurrency, error) {
	currency := &VirtualCurrency{
		UserID:  userID,
		Balance: 0,
	}
	
	if err := r.db.WithContext(ctx).Create(currency).Error; err != nil {
		return nil, err
	}
	
	return currency, nil
}

// UpdateUserBalance updates a user's virtual currency balance
func (r *VirtualCurrencyRepositoryImpl) UpdateUserBalance(ctx context.Context, currency *VirtualCurrency) error {
	return r.db.WithContext(ctx).Save(currency).Error
}

// CreateTransaction creates a new virtual currency transaction
func (r *VirtualCurrencyRepositoryImpl) CreateTransaction(ctx context.Context, transaction *VirtualCurrencyTransaction) error {
	return r.db.WithContext(ctx).Create(transaction).Error
}

// GetTransactionByID retrieves a transaction by its ID
func (r *VirtualCurrencyRepositoryImpl) GetTransactionByID(ctx context.Context, id uint) (*VirtualCurrencyTransaction, error) {
	var transaction VirtualCurrencyTransaction
	if err := r.db.WithContext(ctx).First(&transaction, id).Error; err != nil {
		return nil, err
	}
	return &transaction, nil
}

// GetUserTransactions retrieves a user's transactions with pagination
func (r *VirtualCurrencyRepositoryImpl) GetUserTransactions(ctx context.Context, userID uint, page, limit int) ([]VirtualCurrencyTransaction, int, error) {
	var transactions []VirtualCurrencyTransaction
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&VirtualCurrencyTransaction{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated transactions
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transactions).Error; err != nil {
		return nil, 0, err
	}
	
	return transactions, int(count), nil
}

// GetCoinPackages retrieves all available coin packages
func (r *VirtualCurrencyRepositoryImpl) GetCoinPackages(ctx context.Context) ([]CoinPackage, error) {
	var packages []CoinPackage
	
	if err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("display_order ASC").
		Find(&packages).Error; err != nil {
		return nil, err
	}
	
	return packages, nil
}

// GetCoinPackageByID retrieves a coin package by its ID
func (r *VirtualCurrencyRepositoryImpl) GetCoinPackageByID(ctx context.Context, id uint) (*CoinPackage, error) {
	var pkg CoinPackage
	if err := r.db.WithContext(ctx).First(&pkg, id).Error; err != nil {
		return nil, err
	}
	return &pkg, nil
}

// CreateCoinPackage creates a new coin package
func (r *VirtualCurrencyRepositoryImpl) CreateCoinPackage(ctx context.Context, pkg *CoinPackage) error {
	return r.db.WithContext(ctx).Create(pkg).Error
}

// UpdateCoinPackage updates a coin package
func (r *VirtualCurrencyRepositoryImpl) UpdateCoinPackage(ctx context.Context, pkg *CoinPackage) error {
	return r.db.WithContext(ctx).Save(pkg).Error
}

// DeleteCoinPackage deletes a coin package
func (r *VirtualCurrencyRepositoryImpl) DeleteCoinPackage(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&CoinPackage{}, id).Error
}

// AddCoinsToUser adds coins to a user's balance and creates a transaction record
func (r *VirtualCurrencyRepositoryImpl) AddCoinsToUser(ctx context.Context, userID uint, amount float64, transactionType, description, referenceID string, paymentID *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get user's current balance
		var currency VirtualCurrency
		if err := tx.Where("user_id = ?", userID).First(&currency).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create a new balance record if not found
				currency = VirtualCurrency{
					UserID:  userID,
					Balance: 0,
				}
				if err := tx.Create(&currency).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}
		
		// Record balance before update
		balanceBefore := currency.Balance
		
		// Update balance
		currency.Balance += amount
		
		// Update total bought if this is a purchase
		if transactionType == "purchase" {
			currency.TotalBought += amount
		}
		
		if err := tx.Save(&currency).Error; err != nil {
			return err
		}
		
		// Create transaction record
		transaction := VirtualCurrencyTransaction{
			UserID:          userID,
			TransactionType: transactionType,
			Amount:          amount,
			BalanceBefore:   balanceBefore,
			BalanceAfter:    currency.Balance,
			Description:     description,
			ReferenceID:     referenceID,
			PaymentID:       paymentID,
			Status:          "completed",
		}
		
		return tx.Create(&transaction).Error
	})
}

// DeductCoinsFromUser deducts coins from a user's balance and creates a transaction record
func (r *VirtualCurrencyRepositoryImpl) DeductCoinsFromUser(ctx context.Context, userID uint, amount float64, transactionType, description, referenceID string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get user's current balance
		var currency VirtualCurrency
		if err := tx.Where("user_id = ?", userID).First(&currency).Error; err != nil {
			return err
		}
		
		// Check if user has sufficient balance
		if currency.Balance < amount {
			return errors.New("insufficient balance")
		}
		
		// Record balance before update
		balanceBefore := currency.Balance
		
		// Update balance
		currency.Balance -= amount
		currency.TotalSpent += amount
		
		if err := tx.Save(&currency).Error; err != nil {
			return err
		}
		
		// Create transaction record
		transaction := VirtualCurrencyTransaction{
			UserID:          userID,
			TransactionType: transactionType,
			Amount:          -amount, // Negative amount for deductions
			BalanceBefore:   balanceBefore,
			BalanceAfter:    currency.Balance,
			Description:     description,
			ReferenceID:     referenceID,
			Status:          "completed",
		}
		
		return tx.Create(&transaction).Error
	})
}
