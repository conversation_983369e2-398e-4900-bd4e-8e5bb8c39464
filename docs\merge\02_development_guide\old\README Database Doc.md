# Great Nigeria Platform - Database Documentation

This directory contains documentation for the database schema and management tools used in the Great Nigeria platform.

## Main Documentation Files

- [DATABASE_SCHEMA_PART1.md](DATABASE_SCHEMA_PART1.md) - Part 1 of the database schema documentation, covering core tables and user engagement
- [DATABASE_SCHEMA_PART2.md](DATABASE_SCHEMA_PART2.md) - Part 2 of the database schema documentation, covering discussion system, points system, and payment system
- [DATABASE_SCHEMA_PART3.md](DATABASE_SCHEMA_PART3.md) - Part 3 of the database schema documentation, covering citation system, database management, and backup/restoration

## Overview

The Great Nigeria platform uses PostgreSQL as its primary database, with GORM as the ORM layer. The database schema is designed to support all aspects of the platform, including:

- User management and authentication
- Book content organization and access control
- User progress tracking
- Discussion forums and comments
- Points and activities tracking
- Payment processing
- Citation and bibliography management

## Database Schema

The database schema is organized into several logical groups:

### Core Tables
- **Users**: User accounts and profiles
- **Books**: Book metadata and access control
- **Chapters**: Book chapter organization
- **Sections**: Book content sections

### User Progress and Engagement
- **UserProgress**: Tracks reading progress
- **Bookmarks**: User-saved locations in books

### Discussion System
- **Discussions**: Forum topics and discussions
- **Comments**: User responses to discussions
- **UserLikes**: User reactions to content

### Points and Rewards System
- **UserActivities**: Records of point-earning actions
- **TopicCompletions**: Tracks completed sections
- **MembershipLevels**: Defines membership tiers

### Payment System
- **Purchases**: Financial transactions
- **Plans**: Subscription plan definitions
- **Subscriptions**: User subscriptions

### Citation System
- **Citations**: Bibliographic references
- **CitationUsages**: Tracks citation usage
- **Bibliographies**: Book bibliography metadata
- **CitationCategories**: Citation type categories

## Database Management

The platform includes several tools for database management:

- **Connection Management**: Configurable connection pooling
- **Auto-Migration**: Automatic schema updates
- **Transaction Support**: Simplified transaction handling
- **Backup and Restoration**: Comprehensive backup tools

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Details of the system architecture
- [Code Analysis Documentation](../code/) - Analysis of the codebase
- [Implementation Documentation](../implementation/) - Implementation plans
