import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  DragIndicator as DragIndicatorIcon,
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { AppDispatch, RootState } from '../store';
import {
  fetchQuizById,
  createQuiz,
  updateQuiz,
  fetchQuestions,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  reorderQuestions,
  publishQuiz,
  unpublishQuiz,
} from '../store/slices/quizzesSlice';
import { Quiz, Question } from '../services/quizzesService';

// Step 1: Basic Information
const BasicInformationStep: React.FC<{
  quizData: Partial<Quiz>;
  setQuizData: React.Dispatch<React.SetStateAction<Partial<Quiz>>>;
}> = ({ quizData, setQuizData }) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setQuizData({
      ...quizData,
      [name]: value,
    });
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setQuizData({
      ...quizData,
      [name]: value,
    });
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setQuizData({
      ...quizData,
      [name]: parseInt(value) || 0,
    });
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          required
          fullWidth
          label="Quiz Title"
          name="title"
          value={quizData.title || ''}
          onChange={handleInputChange}
          variant="outlined"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          required
          fullWidth
          label="Description"
          name="description"
          value={quizData.description || ''}
          onChange={handleInputChange}
          variant="outlined"
          multiline
          rows={4}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Time Limit (minutes)"
          name="timeLimit"
          type="number"
          value={quizData.timeLimit || 0}
          onChange={handleNumberChange}
          variant="outlined"
          helperText="Set to 0 for no time limit"
          inputProps={{ min: 0 }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          required
          fullWidth
          label="Passing Score (%)"
          name="passingScore"
          type="number"
          value={quizData.passingScore || 70}
          onChange={handleNumberChange}
          variant="outlined"
          inputProps={{ min: 0, max: 100 }}
        />
      </Grid>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <FormControlLabel
            control={
              <Switch
                checked={quizData.isPublished || false}
                onChange={(e) => setQuizData({ ...quizData, isPublished: e.target.checked })}
                name="isPublished"
                color="primary"
              />
            }
            label="Publish Quiz"
          />
          <FormHelperText>
            {quizData.isPublished
              ? 'Quiz will be visible to users'
              : 'Quiz will be saved as a draft'}
          </FormHelperText>
        </FormControl>
      </Grid>
    </Grid>
  );
};

// Step 2: Questions Management
const QuestionsStep: React.FC<{
  quizId: number | null;
  questions: Question[];
  setQuestions: React.Dispatch<React.SetStateAction<Question[]>>;
  loading: boolean;
  error: string | null;
}> = ({ quizId, questions, setQuestions, loading, error }) => {
  const dispatch = useDispatch<AppDispatch>();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<Partial<Question> | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null);

  const handleOpenDialog = (question?: Question) => {
    if (question) {
      setCurrentQuestion(question);
      setIsEditing(true);
    } else {
      setCurrentQuestion({
        quizId: quizId || 0,
        questionText: '',
        questionType: 'multiple_choice',
        options: ['', '', '', ''],
        correctAnswer: '',
        points: 1,
        order: questions.length + 1,
      });
      setIsEditing(false);
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setCurrentQuestion(null);
  };

  const handleOpenDeleteDialog = (questionId: number) => {
    setQuestionToDelete(questionId);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setQuestionToDelete(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentQuestion((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;

    if (name === 'questionType') {
      // Reset options and correctAnswer based on question type
      let newOptions: string[] = [];
      let newCorrectAnswer: string | string[] = '';

      switch (value) {
        case 'multiple_choice':
          newOptions = ['', '', '', ''];
          newCorrectAnswer = '';
          break;
        case 'true_false':
          newOptions = ['True', 'False'];
          newCorrectAnswer = '';
          break;
        case 'matching':
          newOptions = ['', ''];
          newCorrectAnswer = ['', ''];
          break;
        case 'short_answer':
        case 'essay':
          newOptions = [];
          newCorrectAnswer = '';
          break;
      }

      setCurrentQuestion((prev) => ({
        ...prev,
        [name]: value,
        options: newOptions,
        correctAnswer: newCorrectAnswer,
      }));
    } else {
      setCurrentQuestion((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentQuestion((prev) => ({
      ...prev,
      [name]: parseInt(value) || 0,
    }));
  };

  const handleOptionChange = (index: number, value: string) => {
    if (!currentQuestion) return;

    const newOptions = [...currentQuestion.options];
    newOptions[index] = value;

    setCurrentQuestion({
      ...currentQuestion,
      options: newOptions,
    });
  };

  const handleAddOption = () => {
    if (!currentQuestion) return;

    setCurrentQuestion({
      ...currentQuestion,
      options: [...currentQuestion.options, ''],
    });
  };

  const handleRemoveOption = (index: number) => {
    if (!currentQuestion) return;

    const newOptions = [...currentQuestion.options];
    newOptions.splice(index, 1);

    // If the correct answer is the removed option, reset it
    let newCorrectAnswer = currentQuestion.correctAnswer;
    if (typeof newCorrectAnswer === 'string' && parseInt(newCorrectAnswer) === index) {
      newCorrectAnswer = '';
    }

    setCurrentQuestion({
      ...currentQuestion,
      options: newOptions,
      correctAnswer: newCorrectAnswer,
    });
  };

  const handleCorrectAnswerChange = (value: string) => {
    if (!currentQuestion) return;

    setCurrentQuestion({
      ...currentQuestion,
      correctAnswer: value,
    });
  };

  const handleSaveQuestion = async () => {
    if (!currentQuestion || !quizId) return;

    try {
      if (isEditing && currentQuestion.id) {
        await dispatch(updateQuestion({ id: currentQuestion.id, question: currentQuestion })).unwrap();

        // Update local state
        setQuestions((prevQuestions) =>
          prevQuestions.map((q) => (q.id === currentQuestion.id ? { ...q, ...currentQuestion } : q))
        );
      } else {
        const newQuestion = await dispatch(
          createQuestion({ quizId, question: currentQuestion })
        ).unwrap();

        // Update local state
        setQuestions((prevQuestions) => [...prevQuestions, newQuestion]);
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Failed to save question:', error);
    }
  };

  const handleDeleteQuestion = async () => {
    if (!questionToDelete) return;

    try {
      await dispatch(deleteQuestion(questionToDelete)).unwrap();

      // Update local state
      setQuestions((prevQuestions) => prevQuestions.filter((q) => q.id !== questionToDelete));

      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Failed to delete question:', error);
    }
  };

  const handleMoveQuestion = (questionId: number, direction: 'up' | 'down') => {
    const questionIndex = questions.findIndex((q) => q.id === questionId);
    if (
      (direction === 'up' && questionIndex === 0) ||
      (direction === 'down' && questionIndex === questions.length - 1)
    ) {
      return;
    }

    const newQuestions = [...questions];
    const targetIndex = direction === 'up' ? questionIndex - 1 : questionIndex + 1;

    // Swap questions
    [newQuestions[questionIndex], newQuestions[targetIndex]] = [
      newQuestions[targetIndex],
      newQuestions[questionIndex],
    ];

    // Update order property
    newQuestions[questionIndex].order = questionIndex + 1;
    newQuestions[targetIndex].order = targetIndex + 1;

    setQuestions(newQuestions);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    const newQuestions = [...questions];
    const [removed] = newQuestions.splice(sourceIndex, 1);
    newQuestions.splice(destinationIndex, 0, removed);

    // Update order property for all questions
    const updatedQuestions = newQuestions.map((question, index) => ({
      ...question,
      order: index + 1,
    }));

    setQuestions(updatedQuestions);

    // Save the new order to the backend
    if (quizId) {
      dispatch(reorderQuestions({ quizId, questionIds: updatedQuestions.map((q) => q.id) }));
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'Multiple Choice';
      case 'true_false':
        return 'True/False';
      case 'matching':
        return 'Matching';
      case 'short_answer':
        return 'Short Answer';
      case 'essay':
        return 'Essay';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Quiz Questions</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Question
        </Button>
      </Box>

      {questions.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            No questions added yet. Click "Add Question" to create your first question.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{ mt: 2 }}
          >
            Add Question
          </Button>
        </Paper>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="questions">
            {(provided) => (
              <List
                {...provided.droppableProps}
                ref={provided.innerRef}
                sx={{ bgcolor: 'background.paper' }}
              >
                {questions.map((question, index) => (
                  <Draggable key={question.id} draggableId={`question-${question.id}`} index={index}>
                    {(provided) => (
                      <ListItem
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        sx={{
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          mb: 2,
                        }}
                      >
                        <Box {...provided.dragHandleProps} sx={{ mr: 2 }}>
                          <DragIndicatorIcon />
                        </Box>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1">
                              {index + 1}. {question.questionText}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ mt: 1 }}>
                              <Chip
                                label={getQuestionTypeLabel(question.questionType)}
                                size="small"
                                sx={{ mr: 1 }}
                              />
                              <Chip
                                label={`${question.points} ${question.points === 1 ? 'point' : 'points'}`}
                                size="small"
                                variant="outlined"
                              />
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleMoveQuestion(question.id, 'up')}
                            disabled={index === 0}
                            sx={{ mr: 1 }}
                          >
                            <ArrowUpwardIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleMoveQuestion(question.id, 'down')}
                            disabled={index === questions.length - 1}
                            sx={{ mr: 1 }}
                          >
                            <ArrowDownwardIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleOpenDialog(question)}
                            sx={{ mr: 1 }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            onClick={() => handleOpenDeleteDialog(question.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </List>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* Question Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Question' : 'Add Question'}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  autoFocus
                  required
                  fullWidth
                  label="Question Text"
                  name="questionText"
                  value={currentQuestion?.questionText || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Question Type</InputLabel>
                  <Select
                    name="questionType"
                    value={currentQuestion?.questionType || 'multiple_choice'}
                    onChange={handleSelectChange}
                    label="Question Type"
                  >
                    <MenuItem value="multiple_choice">Multiple Choice</MenuItem>
                    <MenuItem value="true_false">True/False</MenuItem>
                    <MenuItem value="matching">Matching</MenuItem>
                    <MenuItem value="short_answer">Short Answer</MenuItem>
                    <MenuItem value="essay">Essay</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Points"
                  name="points"
                  type="number"
                  value={currentQuestion?.points || 1}
                  onChange={handleNumberChange}
                  variant="outlined"
                  inputProps={{ min: 1 }}
                />
              </Grid>

              {/* Options for Multiple Choice and True/False */}
              {(currentQuestion?.questionType === 'multiple_choice' ||
                currentQuestion?.questionType === 'true_false') && (
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Options
                  </Typography>
                  {currentQuestion.options.map((option, index) => (
                    <Box key={index} sx={{ display: 'flex', mb: 2 }}>
                      <TextField
                        fullWidth
                        label={`Option ${index + 1}`}
                        value={option}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        variant="outlined"
                        disabled={currentQuestion.questionType === 'true_false'}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={currentQuestion.correctAnswer === index.toString()}
                            onChange={() => handleCorrectAnswerChange(index.toString())}
                            color="primary"
                          />
                        }
                        label="Correct"
                        sx={{ ml: 2 }}
                      />
                      {currentQuestion.questionType === 'multiple_choice' && (
                        <IconButton
                          onClick={() => handleRemoveOption(index)}
                          disabled={currentQuestion.options.length <= 2}
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                  ))}
                  {currentQuestion.questionType === 'multiple_choice' && (
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleAddOption}
                      sx={{ mt: 1 }}
                    >
                      Add Option
                    </Button>
                  )}
                </Grid>
              )}

              {/* Short Answer */}
              {currentQuestion?.questionType === 'short_answer' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Correct Answer"
                    name="correctAnswer"
                    value={currentQuestion.correctAnswer || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    helperText="Enter the correct answer for this question"
                  />
                </Grid>
              )}

              {/* Essay */}
              {currentQuestion?.questionType === 'essay' && (
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Essay questions require manual grading. No correct answer is needed.
                  </Typography>
                </Grid>
              )}

              {/* Matching */}
              {currentQuestion?.questionType === 'matching' && (
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Matching Pairs
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Matching functionality will be implemented in a future update.
                  </Typography>
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Explanation (Optional)"
                  name="explanation"
                  value={currentQuestion?.explanation || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={2}
                  helperText="Explanation will be shown after the user answers the question"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSaveQuestion}
            variant="contained"
            color="primary"
            disabled={
              !currentQuestion?.questionText ||
              (currentQuestion?.questionType === 'multiple_choice' &&
                currentQuestion.correctAnswer === '')
            }
          >
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Question</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this question? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteQuestion} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Step 3: Preview & Settings
const PreviewSettingsStep: React.FC<{
  quizData: Partial<Quiz>;
  questions: Question[];
  onPublish: () => void;
  onUnpublish: () => void;
  loading: boolean;
}> = ({ quizData, questions, onPublish, onUnpublish, loading }) => {
  return (
    <Box>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">{quizData.title}</Typography>
          <Chip
            label={quizData.isPublished ? 'Published' : 'Draft'}
            color={quizData.isPublished ? 'success' : 'default'}
          />
        </Box>
        <Typography variant="body1" paragraph>
          {quizData.description}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
          <Chip
            label={`Time Limit: ${quizData.timeLimit ? `${quizData.timeLimit} minutes` : 'No limit'}`}
            variant="outlined"
            size="small"
          />
          <Chip
            label={`Passing Score: ${quizData.passingScore}%`}
            variant="outlined"
            size="small"
          />
          <Chip
            label={`Questions: ${questions.length}`}
            variant="outlined"
            size="small"
          />
          <Chip
            label={`Total Points: ${questions.reduce((sum, q) => sum + q.points, 0)}`}
            variant="outlined"
            size="small"
          />
        </Box>
      </Paper>

      <Typography variant="h6" gutterBottom>
        Quiz Questions Preview
      </Typography>

      {questions.length === 0 ? (
        <Alert severity="warning" sx={{ mb: 3 }}>
          No questions have been added to this quiz yet. Go back to the previous step to add questions.
        </Alert>
      ) : (
        questions
          .sort((a, b) => a.order - b.order)
          .map((question, index) => (
            <Paper key={question.id} sx={{ p: 3, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                {index + 1}. {question.questionText}
              </Typography>
              <Box sx={{ ml: 3, mt: 2 }}>
                {question.questionType === 'multiple_choice' || question.questionType === 'true_false' ? (
                  <Box>
                    {question.options.map((option, optIndex) => (
                      <Box
                        key={optIndex}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 1,
                          p: 1,
                          borderRadius: 1,
                          bgcolor:
                            question.correctAnswer === optIndex.toString()
                              ? 'success.light'
                              : 'background.paper',
                        }}
                      >
                        <Typography variant="body1">
                          {String.fromCharCode(65 + optIndex)}. {option}
                        </Typography>
                        {question.correctAnswer === optIndex.toString() && (
                          <Chip
                            label="Correct"
                            color="success"
                            size="small"
                            sx={{ ml: 2 }}
                          />
                        )}
                      </Box>
                    ))}
                  </Box>
                ) : question.questionType === 'short_answer' ? (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Correct Answer:
                    </Typography>
                    <Typography variant="body1">{question.correctAnswer}</Typography>
                  </Box>
                ) : question.questionType === 'essay' ? (
                  <Typography variant="body2" color="text.secondary">
                    Essay question - requires manual grading
                  </Typography>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Matching question - pairs will be displayed to users
                  </Typography>
                )}

                {question.explanation && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Explanation:
                    </Typography>
                    <Typography variant="body2">{question.explanation}</Typography>
                  </Box>
                )}

                <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                  <Chip
                    label={`${question.points} ${question.points === 1 ? 'point' : 'points'}`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </Box>
            </Paper>
          ))
      )}

      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Publish Settings
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={quizData.isPublished || false}
              disabled={true}
              color="primary"
            />
          }
          label="Publish this quiz"
        />
        <FormHelperText>
          {quizData.isPublished
            ? 'This quiz is currently published and visible to users.'
            : 'This quiz is currently saved as a draft and only visible to you.'}
        </FormHelperText>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          {quizData.isPublished ? (
            <Button
              variant="outlined"
              color="primary"
              onClick={onUnpublish}
              disabled={loading || questions.length === 0}
              startIcon={loading ? <CircularProgress size={20} /> : null}
              sx={{ mr: 2 }}
            >
              Unpublish
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={onPublish}
              disabled={loading || questions.length === 0}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              Publish Quiz
            </Button>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

// Main Quiz Builder Component
const QuizBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useSelector((state: RootState) => state.auth);
  const { data: currentQuiz, loading: quizLoading, error: quizError } = useSelector(
    (state: RootState) => state.quizzes.currentQuiz
  );
  const { items: questions, loading: questionsLoading, error: questionsError } = useSelector(
    (state: RootState) => state.quizzes.questions
  );

  const [activeStep, setActiveStep] = useState(0);
  const [quizData, setQuizData] = useState<Partial<Quiz>>({
    title: '',
    description: '',
    timeLimit: 0,
    passingScore: 70,
    isPublished: false,
  });
  const [quizQuestions, setQuizQuestions] = useState<Question[]>([]);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      dispatch(fetchQuizById(parseInt(id)));
      dispatch(fetchQuestions(parseInt(id)));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (currentQuiz) {
      setQuizData(currentQuiz);
    }
  }, [currentQuiz]);

  useEffect(() => {
    if (questions) {
      setQuizQuestions(questions);
    }
  }, [questions]);

  const handleNext = async () => {
    if (activeStep === 0) {
      // Save basic information
      try {
        setSaving(true);
        setError(null);

        const quizToSave = {
          ...quizData,
          createdBy: user?.id,
        };

        let savedQuiz;

        if (id) {
          // Update existing quiz
          savedQuiz = await dispatch(
            updateQuiz({ id: parseInt(id), quiz: quizToSave })
          ).unwrap();
        } else {
          // Create new quiz
          savedQuiz = await dispatch(createQuiz(quizToSave)).unwrap();

          // Redirect to the edit page with the new ID
          navigate(`/quizzes/edit/${savedQuiz.id}`, { replace: true });
        }

        setQuizData(savedQuiz);
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      } catch (err: any) {
        setError(err.message || 'Failed to save quiz information');
      } finally {
        setSaving(false);
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handlePublish = async () => {
    try {
      setSaving(true);
      setError(null);

      // Publish the quiz
      const updatedQuiz = await dispatch(publishQuiz(parseInt(id!))).unwrap();

      setQuizData(updatedQuiz);
    } catch (err: any) {
      setError(err.message || 'Failed to publish quiz');
    } finally {
      setSaving(false);
    }
  };

  const handleUnpublish = async () => {
    try {
      setSaving(true);
      setError(null);

      // Unpublish the quiz
      const updatedQuiz = await dispatch(unpublishQuiz(parseInt(id!))).unwrap();

      setQuizData(updatedQuiz);
    } catch (err: any) {
      setError(err.message || 'Failed to unpublish quiz');
    } finally {
      setSaving(false);
    }
  };

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to create or edit quizzes. Please log in and try again.
        </Alert>
      </Container>
    );
  }

  const steps = ['Basic Information', 'Questions', 'Preview & Settings'];

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <BasicInformationStep
            quizData={quizData}
            setQuizData={setQuizData}
          />
        );
      case 1:
        return (
          <QuestionsStep
            quizId={id ? parseInt(id) : null}
            questions={quizQuestions}
            setQuestions={setQuizQuestions}
            loading={questionsLoading}
            error={questionsError}
          />
        );
      case 2:
        return (
          <PreviewSettingsStep
            quizData={quizData}
            questions={quizQuestions}
            onPublish={handlePublish}
            onUnpublish={handleUnpublish}
            loading={saving}
          />
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {id ? 'Edit Quiz' : 'Create New Quiz'}
        </Typography>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {(quizLoading || questionsLoading) && !error ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error || quizError ? (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error || quizError}
          </Alert>
        ) : (
          <>
            <Box sx={{ mb: 3 }}>{getStepContent(activeStep)}</Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                variant="outlined"
              >
                Back
              </Button>
              <Box>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/quizzes/my-quizzes')}
                  sx={{ mr: 1 }}
                >
                  Cancel
                </Button>
                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => navigate('/quizzes/my-quizzes')}
                  >
                    Finish
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    disabled={
                      saving ||
                      (activeStep === 0 && (!quizData.title || !quizData.description))
                    }
                  >
                    {saving ? <CircularProgress size={24} /> : 'Next'}
                  </Button>
                )}
              </Box>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default QuizBuilder;