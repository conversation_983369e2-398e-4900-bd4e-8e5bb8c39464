import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Section = styled.section`
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const Subtitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const Paragraph = styled.p`
  margin-bottom: 1.5rem;
  line-height: 1.8;
`;

const MissionBox = styled.div`
  background-color: #f8f9fa;
  border-left: 4px solid #16213e;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-radius: 0 8px 8px 0;
`;

const MissionStatement = styled.p`
  font-size: 1.2rem;
  font-style: italic;
  margin-bottom: 0;
`;

const TeamGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const TeamMember = styled.div`
  text-align: center;
`;

const TeamMemberImage = styled.img`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
  border: 3px solid #16213e;
`;

const TeamMemberName = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
`;

const TeamMemberRole = styled.p`
  color: #666;
  font-size: 0.9rem;
`;

const CTASection = styled.div`
  background-color: #16213e;
  color: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  margin-top: 3rem;
`;

const CTATitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  margin-bottom: 1.5rem;
`;

const CTAButton = styled(Link)`
  display: inline-block;
  background-color: #e94560;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  text-decoration: none;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #d63553;
    text-decoration: none;
  }
`;

const AboutPage: React.FC = () => {
  return (
    <Container>
      <Section>
        <Title>About Great Nigeria</Title>
        
        <MissionBox>
          <MissionStatement>
            Our mission is to transform Nigeria through citizen education, community building, and
            coordinated action, empowering Nigerians to create a more prosperous, just, and united
            nation.
          </MissionStatement>
        </MissionBox>
        
        <Paragraph>
          Great Nigeria is a platform dedicated to providing educational resources, fostering
          community engagement, and celebrating Nigerian excellence. We believe that by equipping
          citizens with knowledge, building strong communities, and highlighting our achievements, we
          can create positive change throughout Nigeria.
        </Paragraph>
        
        <Paragraph>
          Founded in 2023, Great Nigeria has grown into a vibrant community of change-makers
          committed to addressing Nigeria's challenges through practical, grassroots solutions. Our
          platform offers a comprehensive library of educational resources, a forum for community
          discussion, and a showcase of Nigerian excellence.
        </Paragraph>
      </Section>
      
      <Section>
        <Subtitle>Our Approach</Subtitle>
        
        <Paragraph>
          We believe in a three-pronged approach to transforming Nigeria:
        </Paragraph>
        
        <div style={{ marginBottom: '2rem' }}>
          <h3>1. Citizen Education</h3>
          <Paragraph>
            We provide accessible, practical educational resources that empower Nigerians with the
            knowledge and skills needed to address local and national challenges. Our eBooks cover
            topics ranging from civic responsibility to community development, leadership, and more.
          </Paragraph>
        </div>
        
        <div style={{ marginBottom: '2rem' }}>
          <h3>2. Community Building</h3>
          <Paragraph>
            We foster connections between like-minded Nigerians through our community forum, where
            members can share ideas, collaborate on initiatives, and support each other's efforts.
            Our platform enables Nigerians from all walks of life to work together toward common
            goals.
          </Paragraph>
        </div>
        
        <div>
          <h3>3. Celebrating Excellence</h3>
          <Paragraph>
            Through our Celebrate Nigeria feature, we highlight the achievements of Nigerian people,
            places, and events that inspire pride and progress. By showcasing our successes, we aim
            to inspire future generations and change the narrative about Nigeria.
          </Paragraph>
        </div>
      </Section>
      
      <Section>
        <Subtitle>Our Team</Subtitle>
        
        <Paragraph>
          Great Nigeria is powered by a dedicated team of professionals committed to making a
          positive impact in Nigeria. Our team brings together expertise in education, technology,
          community development, and more.
        </Paragraph>
        
        <TeamGrid>
          <TeamMember>
            <TeamMemberImage src="https://via.placeholder.com/150" alt="Team Member" />
            <TeamMemberName>Adebayo Johnson</TeamMemberName>
            <TeamMemberRole>Founder & CEO</TeamMemberRole>
          </TeamMember>
          
          <TeamMember>
            <TeamMemberImage src="https://via.placeholder.com/150" alt="Team Member" />
            <TeamMemberName>Ngozi Okafor</TeamMemberName>
            <TeamMemberRole>Education Director</TeamMemberRole>
          </TeamMember>
          
          <TeamMember>
            <TeamMemberImage src="https://via.placeholder.com/150" alt="Team Member" />
            <TeamMemberName>Emeka Nwachukwu</TeamMemberName>
            <TeamMemberRole>Technology Lead</TeamMemberRole>
          </TeamMember>
          
          <TeamMember>
            <TeamMemberImage src="https://via.placeholder.com/150" alt="Team Member" />
            <TeamMemberName>Amina Ibrahim</TeamMemberName>
            <TeamMemberRole>Community Manager</TeamMemberRole>
          </TeamMember>
        </TeamGrid>
      </Section>
      
      <CTASection>
        <CTATitle>Join Our Mission</CTATitle>
        <CTAText>
          Ready to be part of the movement to transform Nigeria? Join our community today and start
          making a difference.
        </CTAText>
        <CTAButton to="/register">Get Started Now</CTAButton>
      </CTASection>
    </Container>
  );
};

export default AboutPage;
