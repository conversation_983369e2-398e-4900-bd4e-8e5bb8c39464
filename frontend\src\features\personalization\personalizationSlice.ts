import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import personalizationService, {
  LearningStyle,
  LearningPreference,
  PersonalizedPath,
  PathItem,
  AssessmentQuestion,
  AssessmentResponse,
  ContentRecommendation,
  UserPerformance,
  AssessmentResult,
  PersonalizationRequest,
} from '../../api/personalizationService';

// Define types
interface PersonalizationState {
  learningStyle: LearningStyle | null;
  learningPreference: LearningPreference | null;
  assessmentQuestions: AssessmentQuestion[];
  assessmentResult: AssessmentResult | null;
  personalizedPaths: PersonalizedPath[];
  currentPath: PersonalizedPath | null;
  currentPathItems: PathItem[];
  recommendations: ContentRecommendation[];
  userPerformance: UserPerformance | null;
  isLoading: boolean;
  error: string | null;
  hasMoreRecommendations: boolean;
  userHasStyle: boolean;
}

// Initial state
const initialState: PersonalizationState = {
  learningStyle: null,
  learningPreference: null,
  assessmentQuestions: [],
  assessmentResult: null,
  personalizedPaths: [],
  currentPath: null,
  currentPathItems: [],
  recommendations: [],
  userPerformance: null,
  isLoading: false,
  error: null,
  hasMoreRecommendations: false,
  userHasStyle: false,
};

// Async thunks
export const fetchLearningStyle = createAsyncThunk(
  'personalization/fetchLearningStyle',
  async (_, { rejectWithValue }) => {
    try {
      return await personalizationService.getLearningStyle();
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        return null; // User doesn't have a learning style yet
      }
      return rejectWithValue(error.message || 'Failed to fetch learning style');
    }
  }
);

export const saveLearningStyle = createAsyncThunk(
  'personalization/saveLearningStyle',
  async (style: LearningStyle, { rejectWithValue }) => {
    try {
      return await personalizationService.saveLearningStyle(style);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to save learning style');
    }
  }
);

export const fetchLearningPreference = createAsyncThunk(
  'personalization/fetchLearningPreference',
  async (_, { rejectWithValue }) => {
    try {
      return await personalizationService.getLearningPreference();
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        return null; // User doesn't have preferences yet
      }
      return rejectWithValue(error.message || 'Failed to fetch learning preference');
    }
  }
);

export const saveLearningPreference = createAsyncThunk(
  'personalization/saveLearningPreference',
  async (pref: LearningPreference, { rejectWithValue }) => {
    try {
      return await personalizationService.saveLearningPreference(pref);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to save learning preference');
    }
  }
);

export const fetchAssessmentQuestions = createAsyncThunk(
  'personalization/fetchAssessmentQuestions',
  async (_, { rejectWithValue }) => {
    try {
      return await personalizationService.getAssessmentQuestions();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch assessment questions');
    }
  }
);

export const submitAssessment = createAsyncThunk(
  'personalization/submitAssessment',
  async (responses: AssessmentResponse[], { rejectWithValue }) => {
    try {
      return await personalizationService.submitAssessment(responses);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to submit assessment');
    }
  }
);

export const fetchPersonalizedPaths = createAsyncThunk(
  'personalization/fetchPersonalizedPaths',
  async (_, { rejectWithValue }) => {
    try {
      return await personalizationService.getPersonalizedPaths();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch personalized paths');
    }
  }
);

export const fetchPersonalizedPathWithItems = createAsyncThunk(
  'personalization/fetchPersonalizedPathWithItems',
  async (pathId: number, { rejectWithValue }) => {
    try {
      return await personalizationService.getPersonalizedPathWithItems(pathId);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch personalized path');
    }
  }
);

export const createPersonalizedPath = createAsyncThunk(
  'personalization/createPersonalizedPath',
  async ({ path, items }: { path: PersonalizedPath; items: PathItem[] }, { rejectWithValue }) => {
    try {
      return await personalizationService.createPersonalizedPath(path, items);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create personalized path');
    }
  }
);

export const updatePersonalizedPath = createAsyncThunk(
  'personalization/updatePersonalizedPath',
  async ({ pathId, path }: { pathId: number; path: Partial<PersonalizedPath> }, { rejectWithValue }) => {
    try {
      return await personalizationService.updatePersonalizedPath(pathId, path);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update personalized path');
    }
  }
);

export const deletePersonalizedPath = createAsyncThunk(
  'personalization/deletePersonalizedPath',
  async (pathId: number, { rejectWithValue }) => {
    try {
      await personalizationService.deletePersonalizedPath(pathId);
      return pathId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete personalized path');
    }
  }
);

export const addPathItem = createAsyncThunk(
  'personalization/addPathItem',
  async ({ pathId, item }: { pathId: number; item: Omit<PathItem, 'pathId'> }, { rejectWithValue }) => {
    try {
      return await personalizationService.addPathItem(pathId, item);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add path item');
    }
  }
);

export const updatePathItem = createAsyncThunk(
  'personalization/updatePathItem',
  async ({ itemId, item }: { itemId: number; item: Partial<PathItem> }, { rejectWithValue }) => {
    try {
      return await personalizationService.updatePathItem(itemId, item);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update path item');
    }
  }
);

export const deletePathItem = createAsyncThunk(
  'personalization/deletePathItem',
  async (itemId: number, { rejectWithValue }) => {
    try {
      await personalizationService.deletePathItem(itemId);
      return itemId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete path item');
    }
  }
);

export const markPathItemComplete = createAsyncThunk(
  'personalization/markPathItemComplete',
  async ({ itemId, completed }: { itemId: number; completed: boolean }, { rejectWithValue }) => {
    try {
      await personalizationService.markPathItemComplete(itemId, completed);
      return { itemId, completed };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to mark path item as complete');
    }
  }
);

export const fetchRecommendations = createAsyncThunk(
  'personalization/fetchRecommendations',
  async (request: PersonalizationRequest, { rejectWithValue }) => {
    try {
      return await personalizationService.getRecommendations(request);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch recommendations');
    }
  }
);

export const updateRecommendationStatus = createAsyncThunk(
  'personalization/updateRecommendationStatus',
  async (
    { recId, viewed, saved, rejected }: { recId: number; viewed: boolean; saved: boolean; rejected: boolean },
    { rejectWithValue }
  ) => {
    try {
      await personalizationService.updateRecommendationStatus(recId, viewed, saved, rejected);
      return { recId, viewed, saved, rejected };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update recommendation status');
    }
  }
);

export const fetchUserPerformance = createAsyncThunk(
  'personalization/fetchUserPerformance',
  async (_, { rejectWithValue }) => {
    try {
      return await personalizationService.getUserPerformance();
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        return null; // User doesn't have performance metrics yet
      }
      return rejectWithValue(error.message || 'Failed to fetch user performance');
    }
  }
);

export const updateUserPerformance = createAsyncThunk(
  'personalization/updateUserPerformance',
  async (performance: UserPerformance, { rejectWithValue }) => {
    try {
      return await personalizationService.updateUserPerformance(performance);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update user performance');
    }
  }
);

// Create slice
const personalizationSlice = createSlice({
  name: 'personalization',
  initialState,
  reducers: {
    clearPersonalizationError: (state) => {
      state.error = null;
    },
    clearAssessmentResult: (state) => {
      state.assessmentResult = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchLearningStyle
      .addCase(fetchLearningStyle.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLearningStyle.fulfilled, (state, action) => {
        state.isLoading = false;
        state.learningStyle = action.payload;
        state.userHasStyle = !!action.payload && action.payload.assessmentTaken;
      })
      .addCase(fetchLearningStyle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // saveLearningStyle
      .addCase(saveLearningStyle.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveLearningStyle.fulfilled, (state, action) => {
        state.isLoading = false;
        state.learningStyle = action.payload;
        state.userHasStyle = true;
      })
      .addCase(saveLearningStyle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchLearningPreference
      .addCase(fetchLearningPreference.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLearningPreference.fulfilled, (state, action) => {
        state.isLoading = false;
        state.learningPreference = action.payload;
      })
      .addCase(fetchLearningPreference.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // saveLearningPreference
      .addCase(saveLearningPreference.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveLearningPreference.fulfilled, (state, action) => {
        state.isLoading = false;
        state.learningPreference = action.payload;
      })
      .addCase(saveLearningPreference.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchAssessmentQuestions
      .addCase(fetchAssessmentQuestions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAssessmentQuestions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.assessmentQuestions = action.payload;
      })
      .addCase(fetchAssessmentQuestions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // submitAssessment
      .addCase(submitAssessment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(submitAssessment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.assessmentResult = action.payload;
        state.learningStyle = action.payload.learningStyle;
        state.userHasStyle = true;
        state.personalizedPaths = action.payload.suggestedPaths;
        state.recommendations = action.payload.recommendations;
      })
      .addCase(submitAssessment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchPersonalizedPaths
      .addCase(fetchPersonalizedPaths.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPersonalizedPaths.fulfilled, (state, action) => {
        state.isLoading = false;
        state.personalizedPaths = action.payload;
      })
      .addCase(fetchPersonalizedPaths.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchPersonalizedPathWithItems
      .addCase(fetchPersonalizedPathWithItems.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPersonalizedPathWithItems.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPath = action.payload.path;
        state.currentPathItems = action.payload.items;
      })
      .addCase(fetchPersonalizedPathWithItems.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // createPersonalizedPath
      .addCase(createPersonalizedPath.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPersonalizedPath.fulfilled, (state, action) => {
        state.isLoading = false;
        state.personalizedPaths.push(action.payload);
      })
      .addCase(createPersonalizedPath.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // updatePersonalizedPath
      .addCase(updatePersonalizedPath.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePersonalizedPath.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.personalizedPaths.findIndex((path) => path.id === action.payload.id);
        if (index !== -1) {
          state.personalizedPaths[index] = action.payload;
        }
        if (state.currentPath && state.currentPath.id === action.payload.id) {
          state.currentPath = action.payload;
        }
      })
      .addCase(updatePersonalizedPath.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // deletePersonalizedPath
      .addCase(deletePersonalizedPath.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePersonalizedPath.fulfilled, (state, action) => {
        state.isLoading = false;
        state.personalizedPaths = state.personalizedPaths.filter((path) => path.id !== action.payload);
        if (state.currentPath && state.currentPath.id === action.payload) {
          state.currentPath = null;
          state.currentPathItems = [];
        }
      })
      .addCase(deletePersonalizedPath.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // addPathItem
      .addCase(addPathItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addPathItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPathItems.push(action.payload);
        // Sort items by order
        state.currentPathItems.sort((a, b) => a.order - b.order);
      })
      .addCase(addPathItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // updatePathItem
      .addCase(updatePathItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePathItem.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.currentPathItems.findIndex((item) => item.id === action.payload.id);
        if (index !== -1) {
          state.currentPathItems[index] = action.payload;
        }
      })
      .addCase(updatePathItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // deletePathItem
      .addCase(deletePathItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePathItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPathItems = state.currentPathItems.filter((item) => item.id !== action.payload);
      })
      .addCase(deletePathItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // markPathItemComplete
      .addCase(markPathItemComplete.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(markPathItemComplete.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.currentPathItems.findIndex((item) => item.id === action.payload.itemId);
        if (index !== -1) {
          state.currentPathItems[index].isCompleted = action.payload.completed;
          if (action.payload.completed) {
            state.currentPathItems[index].completedAt = new Date().toISOString();
          } else {
            state.currentPathItems[index].completedAt = undefined;
          }
        }
        
        // Update path completion rate
        if (state.currentPath && state.currentPathItems.length > 0) {
          const completedItems = state.currentPathItems.filter((item) => item.isCompleted).length;
          state.currentPath.completionRate = (completedItems / state.currentPathItems.length) * 100;
        }
      })
      .addCase(markPathItemComplete.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchRecommendations
      .addCase(fetchRecommendations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.recommendations = action.payload.recommendations;
        state.hasMoreRecommendations = action.payload.hasMoreResults;
        state.userHasStyle = action.payload.userHasStyle;
      })
      .addCase(fetchRecommendations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // updateRecommendationStatus
      .addCase(updateRecommendationStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateRecommendationStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.recommendations.findIndex((rec) => rec.id === action.payload.recId);
        if (index !== -1) {
          state.recommendations[index].isViewed = action.payload.viewed;
          state.recommendations[index].isSaved = action.payload.saved;
          state.recommendations[index].isRejected = action.payload.rejected;
        }
      })
      .addCase(updateRecommendationStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // fetchUserPerformance
      .addCase(fetchUserPerformance.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserPerformance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userPerformance = action.payload;
      })
      .addCase(fetchUserPerformance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // updateUserPerformance
      .addCase(updateUserPerformance.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserPerformance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userPerformance = action.payload;
      })
      .addCase(updateUserPerformance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearPersonalizationError, clearAssessmentResult } = personalizationSlice.actions;

// Export selectors
export const selectLearningStyle = (state: RootState) => state.personalization.learningStyle;
export const selectLearningPreference = (state: RootState) => state.personalization.learningPreference;
export const selectAssessmentQuestions = (state: RootState) => state.personalization.assessmentQuestions;
export const selectAssessmentResult = (state: RootState) => state.personalization.assessmentResult;
export const selectPersonalizedPaths = (state: RootState) => state.personalization.personalizedPaths;
export const selectCurrentPath = (state: RootState) => state.personalization.currentPath;
export const selectCurrentPathItems = (state: RootState) => state.personalization.currentPathItems;
export const selectRecommendations = (state: RootState) => state.personalization.recommendations;
export const selectUserPerformance = (state: RootState) => state.personalization.userPerformance;
export const selectPersonalizationLoading = (state: RootState) => state.personalization.isLoading;
export const selectPersonalizationError = (state: RootState) => state.personalization.error;
export const selectHasMoreRecommendations = (state: RootState) => state.personalization.hasMoreRecommendations;
export const selectUserHasStyle = (state: RootState) => state.personalization.userHasStyle;

// Export reducer
export default personalizationSlice.reducer;
