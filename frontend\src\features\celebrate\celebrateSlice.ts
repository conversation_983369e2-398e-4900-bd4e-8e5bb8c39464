import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { CelebrateService } from '../../api';
import { CelebrateState, NewCelebrationEntry } from '../../types';

// Initial state
const initialState: CelebrateState = {
  featuredEntries: [],
  currentEntry: null,
  searchResults: [],
  categories: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchFeaturedEntries = createAsyncThunk(
  'celebrate/fetchFeaturedEntries',
  async (_, { rejectWithValue }) => {
    try {
      return await CelebrateService.getFeaturedEntries();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch featured entries');
    }
  }
);

export const fetchEntryByTypeAndSlug = createAsyncThunk(
  'celebrate/fetchEntryByTypeAndSlug',
  async (
    { type, slug }: { type: 'person' | 'place' | 'event'; slug: string },
    { rejectWithValue }
  ) => {
    try {
      return await CelebrateService.getEntryByTypeAndSlug(type, slug);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch entry');
    }
  }
);

export const searchEntries = createAsyncThunk(
  'celebrate/searchEntries',
  async (
    {
      query,
      type,
      categoryId,
    }: { query: string; type?: 'person' | 'place' | 'event'; categoryId?: string },
    { rejectWithValue }
  ) => {
    try {
      return await CelebrateService.searchEntries(query, type, categoryId);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search entries');
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'celebrate/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await CelebrateService.getCategories();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

export const submitEntry = createAsyncThunk(
  'celebrate/submitEntry',
  async (entry: NewCelebrationEntry, { rejectWithValue }) => {
    try {
      return await CelebrateService.submitEntry(entry);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to submit entry');
    }
  }
);

export const voteForEntry = createAsyncThunk(
  'celebrate/voteForEntry',
  async (entryId: string, { rejectWithValue }) => {
    try {
      const response = await CelebrateService.voteForEntry(entryId);
      return { entryId, votes: response.votes };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to vote for entry');
    }
  }
);

export const commentOnEntry = createAsyncThunk(
  'celebrate/commentOnEntry',
  async (
    { entryId, comment }: { entryId: string; comment: string },
    { rejectWithValue }
  ) => {
    try {
      return await CelebrateService.commentOnEntry(entryId, comment);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to comment on entry');
    }
  }
);

export const getRandomEntry = createAsyncThunk(
  'celebrate/getRandomEntry',
  async (_, { rejectWithValue }) => {
    try {
      return await CelebrateService.getRandomEntry();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to get random entry');
    }
  }
);

// Slice
const celebrateSlice = createSlice({
  name: 'celebrate',
  initialState,
  reducers: {
    clearCurrentEntry: (state) => {
      state.currentEntry = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch featured entries
      .addCase(fetchFeaturedEntries.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFeaturedEntries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.featuredEntries = action.payload;
      })
      .addCase(fetchFeaturedEntries.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch entry by type and slug
      .addCase(fetchEntryByTypeAndSlug.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchEntryByTypeAndSlug.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentEntry = action.payload;
      })
      .addCase(fetchEntryByTypeAndSlug.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Search entries
      .addCase(searchEntries.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchEntries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchEntries.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Submit entry
      .addCase(submitEntry.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(submitEntry.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(submitEntry.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Vote for entry
      .addCase(voteForEntry.fulfilled, (state, action) => {
        if (state.currentEntry && state.currentEntry.id === action.payload.entryId) {
          // Update votes in current entry
        }
      })
      // Get random entry
      .addCase(getRandomEntry.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getRandomEntry.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentEntry = action.payload;
      })
      .addCase(getRandomEntry.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearCurrentEntry, clearSearchResults, clearError } = celebrateSlice.actions;
export default celebrateSlice.reducer;
