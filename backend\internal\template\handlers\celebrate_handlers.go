package handlers

import (
	"fmt"
	"html/template"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/celebration/models"
	"github.com/yerenwgventures/GreatNigeriaLibrary/internal/celebration/service"
)

// CelebrateTemplateHandlers manages the rendering of Celebrate Nigeria templates
type CelebrateTemplateHandlers struct {
	templates          *template.Template
	celebrationService *service.CelebrationService
}

// NewCelebrateTemplateHandlers creates a new instance of CelebrateTemplateHandlers
func NewCelebrateTemplateHandlers(celebrationService *service.CelebrationService) (*CelebrateTemplateHandlers, error) {
	// Load templates
	tmpl := template.New("").Funcs(template.FuncMap{
		"safeHTML": func(s string) template.HTML {
			return template.HTML(s)
		},
		"add": func(a, b int) int {
			return a + b
		},
	})

	// First, load the partials
	_, err := tmpl.ParseGlob("web/templates/partials/*.html")
	if err != nil {
		return nil, fmt.Errorf("failed to parse partials: %w", err)
	}

	// Then load the main templates
	_, err = tmpl.ParseGlob("web/templates/celebrate-*.html")
	if err != nil {
		return nil, fmt.Errorf("failed to parse templates: %w", err)
	}

	return &CelebrateTemplateHandlers{
		templates:          tmpl,
		celebrationService: celebrationService,
	}, nil
}

// RenderHomePage renders the Celebrate Nigeria home page
func (h *CelebrateTemplateHandlers) RenderHomePage(c *gin.Context) {
	// Get featured entries
	featuredEntries, err := h.celebrationService.ListFeaturedEntries(c.Request.Context(), "", 6)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to load featured entries",
		})
		return
	}

	// Get categories
	categories, err := h.celebrationService.ListCategories(c.Request.Context(), "")
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to load categories",
		})
		return
	}

	// Render the template
	c.HTML(http.StatusOK, "celebrate-home.html", gin.H{
		"Title":           "Celebrate Nigeria - Showcasing Nigerian Excellence",
		"Description":     "Explore and celebrate Nigerian people, places, events, and cultural elements in our engaging, educational directory.",
		"FeaturedEntries": featuredEntries,
		"Categories":      categories,
		"CurrentYear":     time.Now().Year(),
	})
}

// RenderDetailPage renders the detail page for a specific entry
func (h *CelebrateTemplateHandlers) RenderDetailPage(c *gin.Context) {
	// The type parameter is used in the route but we'll use it from the entry
	slug := c.Param("slug")

	// Get the entry
	entry, err := h.celebrationService.GetEntryBySlug(c.Request.Context(), slug)
	if err != nil {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"error": "Entry not found",
		})
		return
	}

	// Get related entries - for now, just get some entries of the same type
	relatedEntries, _, err := h.celebrationService.ListEntries(c.Request.Context(), entry.EntryType, 1, 3)
	if err != nil {
		// Log the error but continue
		fmt.Printf("Error getting related entries: %v\n", err)
	}

	// Get comments
	comments, _, err := h.celebrationService.ListEntryComments(c.Request.Context(), entry.ID, 1, 10)
	if err != nil {
		// Log the error but continue
		fmt.Printf("Error getting comments: %v\n", err)
	}

	// In a real implementation, we would increment the view count
	// This would be implemented in the service layer

	// Render the template
	c.HTML(http.StatusOK, "celebrate-detail.html", gin.H{
		"Title":           entry.Title + " - Celebrate Nigeria",
		"Description":     entry.ShortDesc,
		"Entry":           entry,
		"RelatedEntries":  relatedEntries,
		"Comments":        comments,
		"HasMoreComments": len(comments) >= 10,
		"PageURL":         c.Request.URL.String(),
		"CurrentYear":     time.Now().Year(),
	})
}

// RenderSearchPage renders the search page
func (h *CelebrateTemplateHandlers) RenderSearchPage(c *gin.Context) {
	query := c.Query("q")
	entryType := c.Query("type")
	category := c.Query("category")
	sort := c.Query("sort")
	pageStr := c.DefaultQuery("page", "1")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	// Get categories for the filter
	categories, err := h.celebrationService.ListCategories(c.Request.Context(), "")
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to load categories",
		})
		return
	}

	var results []models.CelebrationEntry
	var total int64

	// If there's a search query, perform the search
	if query != "" || entryType != "" || category != "" {
		// For now, just get entries of the specified type
		// In a real implementation, we would have a proper search function
		if entryType == "" {
			entryType = "person" // Default to person if not specified
		}

		// In a real implementation, we would use the category to filter results
		// For now, we'll just log if we found the category
		if category != "" {
			cat, err := h.celebrationService.GetCategoryBySlug(c.Request.Context(), category)
			if err == nil && cat != nil {
				fmt.Printf("Found category: %s (ID: %d)\n", cat.Name, cat.ID)
			}
		}

		results, total, err = h.celebrationService.ListEntries(c.Request.Context(), entryType, page, 12)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": "Search failed",
			})
			return
		}
	}

	// Render the template
	c.HTML(http.StatusOK, "celebrate-search.html", gin.H{
		"Title":       "Search - Celebrate Nigeria",
		"Description": "Search for Nigerian people, places, events, and cultural elements in our directory.",
		"Query":       query,
		"EntryType":   entryType,
		"Category":    category,
		"Sort":        sort,
		"Results":     results,
		"Total":       total,
		"Page":        page,
		"Categories":  categories,
		"CurrentYear": time.Now().Year(),
	})
}

// RenderSubmissionPage renders the submission form
func (h *CelebrateTemplateHandlers) RenderSubmissionPage(c *gin.Context) {
	// Get categories for the form
	categories, err := h.celebrationService.ListCategories(c.Request.Context(), "")
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to load categories",
		})
		return
	}

	// Render the template
	c.HTML(http.StatusOK, "celebrate-submission.html", gin.H{
		"Title":       "Submit Entry - Celebrate Nigeria",
		"Description": "Submit a new entry to the Celebrate Nigeria directory.",
		"Categories":  categories,
		"CurrentYear": time.Now().Year(),
	})
}

// RenderModerationDashboard renders the moderation dashboard
func (h *CelebrateTemplateHandlers) RenderModerationDashboard(c *gin.Context) {
	// In a real implementation, we would check if the user has moderator privileges
	// For now, we'll just render the dashboard with placeholder data

	// Placeholder data for flags
	flags := []models.EntryFlag{
		{
			ID:        1,
			EntryID:   1,
			UserID:    2,
			Reason:    "Inappropriate content",
			Status:    "pending",
			CreatedAt: time.Now().Add(-24 * time.Hour),
		},
		{
			ID:        2,
			EntryID:   3,
			UserID:    4,
			Reason:    "Inaccurate information",
			Status:    "pending",
			CreatedAt: time.Now().Add(-48 * time.Hour),
		},
	}

	// Placeholder data for moderation queue items
	queueItems := []models.EntryModerationQueue{
		{
			ID:        1,
			EntryID:   5,
			UserID:    6,
			Reason:    "New submission",
			Status:    "pending",
			Priority:  3,
			CreatedAt: time.Now().Add(-12 * time.Hour),
		},
		{
			ID:        2,
			EntryID:   7,
			UserID:    8,
			Reason:    "Content update",
			Status:    "pending",
			Priority:  2,
			CreatedAt: time.Now().Add(-36 * time.Hour),
		},
	}

	// Render the template
	c.HTML(http.StatusOK, "celebrate-moderation-dashboard.html", gin.H{
		"Title":       "Moderation Dashboard - Celebrate Nigeria",
		"Description": "Moderate entries in the Celebrate Nigeria directory.",
		"Flags":       flags,
		"QueueItems":  queueItems,
		"CurrentYear": time.Now().Year(),
	})
}

// RegisterRoutes registers the Celebrate Nigeria template handler routes
func (h *CelebrateTemplateHandlers) RegisterRoutes(router *gin.Engine) {
	// Main Celebrate Nigeria page
	router.GET("/celebrate", h.RenderHomePage)

	// Detail pages
	router.GET("/celebrate/:type/:slug", h.RenderDetailPage)

	// Search page
	router.GET("/celebrate/search", h.RenderSearchPage)

	// Submission form
	router.GET("/celebrate/submit", h.RenderSubmissionPage)

	// Moderation dashboard
	router.GET("/celebrate/moderation", h.RenderModerationDashboard)
}
