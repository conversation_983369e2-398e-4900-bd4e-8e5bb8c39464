import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { RootState } from '../store';
import {
  fetchFeaturedEntries,
  fetchCategories,
  searchEntries,
  getRandomEntry,
} from '../features/celebrate/celebrateSlice';
import { CelebrationEntry } from '../types';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Hero = styled.div`
  background-color: #16213e;
  color: white;
  padding: 3rem 2rem;
  border-radius: 8px;
  margin-bottom: 3rem;
  text-align: center;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const SearchContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const SearchForm = styled.form`
  display: flex;
  margin-bottom: 1rem;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
  }
`;

const SearchButton = styled.button`
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0 1.5rem;
  cursor: pointer;
  font-weight: bold;
  
  &:hover {
    background-color: #d63553;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
`;

const FilterButton = styled.button<{ active: boolean }>`
  background-color: ${(props) => (props.active ? '#e94560' : 'transparent')};
  color: ${(props) => (props.active ? 'white' : 'white')};
  border: 1px solid ${(props) => (props.active ? '#e94560' : 'rgba(255, 255, 255, 0.3)')};
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  
  &:hover {
    background-color: ${(props) => (props.active ? '#d63553' : 'rgba(255, 255, 255, 0.1)')};
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #16213e;
  text-align: center;
`;

const FeaturedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const EntryCard = styled.div`
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const EntryImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
`;

const EntryInfo = styled.div`
  padding: 1.5rem;
`;

const EntryType = styled.div<{ type: string }>`
  display: inline-block;
  background-color: ${(props) => {
    switch (props.type) {
      case 'person':
        return '#16213e';
      case 'place':
        return '#4caf50';
      case 'event':
        return '#ff9800';
      default:
        return '#16213e';
    }
  }};
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: capitalize;
  margin-bottom: 0.5rem;
`;

const EntryTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #16213e;
`;

const EntrySummary = styled.p`
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const EntryLink = styled(Link)`
  display: inline-block;
  color: #16213e;
  font-weight: bold;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
`;

const CategoriesSection = styled.div`
  margin-bottom: 3rem;
`;

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
`;

const CategoryCard = styled(Link)`
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const CategoryTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #16213e;
`;

const CategoryCount = styled.div`
  color: #666;
  font-size: 0.9rem;
`;

const RandomEntrySection = styled.div`
  margin-bottom: 3rem;
  text-align: center;
`;

const RandomEntryButton = styled.button`
  background-color: #16213e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const SubmitSection = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 3rem;
`;

const SubmitTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #16213e;
`;

const SubmitDescription = styled.p`
  margin-bottom: 1.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const SubmitButton = styled(Link)`
  display: inline-block;
  background-color: #16213e;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #0f3460;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  &:after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #16213e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: #e94560;
  padding: 1rem;
  background-color: rgba(233, 69, 96, 0.1);
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
`;

const SearchResults = styled.div`
  margin-bottom: 3rem;
`;

const SearchResultsTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #16213e;
`;

const NoResults = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #666;
`;

const CelebratePage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const searchQuery = searchParams.get('q') || '';
  const typeFilter = searchParams.get('type') as 'person' | 'place' | 'event' | null;
  
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [activeFilter, setActiveFilter] = useState<'person' | 'place' | 'event' | 'all'>(
    typeFilter || 'all'
  );
  
  const dispatch = useDispatch();
  
  const { featuredEntries, searchResults, categories, isLoading, error } = useSelector(
    (state: RootState) => state.celebrate
  );
  
  useEffect(() => {
    dispatch(fetchFeaturedEntries());
    dispatch(fetchCategories());
  }, [dispatch]);
  
  useEffect(() => {
    if (searchQuery) {
      dispatch(
        searchEntries({
          query: searchQuery,
          type: typeFilter as 'person' | 'place' | 'event' | undefined,
        })
      );
    }
  }, [dispatch, searchQuery, typeFilter]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchTerm.trim()) {
      const params: Record<string, string> = { q: searchTerm };
      if (activeFilter !== 'all') {
        params.type = activeFilter;
      }
      setSearchParams(params);
    }
  };
  
  const handleFilterChange = (filter: 'person' | 'place' | 'event' | 'all') => {
    setActiveFilter(filter);
    
    if (searchTerm.trim()) {
      const params: Record<string, string> = { q: searchTerm };
      if (filter !== 'all') {
        params.type = filter;
      }
      setSearchParams(params);
    }
  };
  
  const handleRandomEntry = () => {
    dispatch(getRandomEntry())
      .unwrap()
      .then((entry: CelebrationEntry) => {
        window.location.href = `/celebrate/${entry.type}/${entry.slug}`;
      });
  };
  
  return (
    <Container>
      <Hero>
        <HeroTitle>Celebrate Nigeria</HeroTitle>
        <HeroSubtitle>
          Discover and celebrate the people, places, and events that make Nigeria great. From
          historical figures to natural wonders, explore the richness of Nigerian excellence.
        </HeroSubtitle>
        
        <SearchContainer>
          <SearchForm onSubmit={handleSearch}>
            <SearchInput
              type="text"
              placeholder="Search people, places, or events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <SearchButton type="submit">Search</SearchButton>
          </SearchForm>
          
          <FilterContainer>
            <FilterButton
              active={activeFilter === 'all'}
              onClick={() => handleFilterChange('all')}
            >
              All
            </FilterButton>
            <FilterButton
              active={activeFilter === 'person'}
              onClick={() => handleFilterChange('person')}
            >
              People
            </FilterButton>
            <FilterButton
              active={activeFilter === 'place'}
              onClick={() => handleFilterChange('place')}
            >
              Places
            </FilterButton>
            <FilterButton
              active={activeFilter === 'event'}
              onClick={() => handleFilterChange('event')}
            >
              Events
            </FilterButton>
          </FilterContainer>
        </SearchContainer>
      </Hero>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      {searchQuery ? (
        <SearchResults>
          <SearchResultsTitle>
            Search Results for "{searchQuery}"
            {typeFilter && ` (${typeFilter}s)`}
          </SearchResultsTitle>
          
          {isLoading ? (
            <LoadingSpinner />
          ) : searchResults.length === 0 ? (
            <NoResults>
              <p>No results found for your search.</p>
              <p>Try different keywords or remove filters.</p>
            </NoResults>
          ) : (
            <FeaturedGrid>
              {searchResults.map((entry) => (
                <EntryCard key={entry.id}>
                  <EntryImage src={entry.image_url} alt={entry.name} />
                  <EntryInfo>
                    <EntryType type={entry.type}>{entry.type}</EntryType>
                    <EntryTitle>{entry.name}</EntryTitle>
                    <EntrySummary>{entry.summary}</EntrySummary>
                    <EntryLink to={`/celebrate/${entry.type}/${entry.slug}`}>
                      Read More
                    </EntryLink>
                  </EntryInfo>
                </EntryCard>
              ))}
            </FeaturedGrid>
          )}
        </SearchResults>
      ) : (
        <>
          <section>
            <SectionTitle>Featured</SectionTitle>
            
            {isLoading ? (
              <LoadingSpinner />
            ) : (
              <FeaturedGrid>
                {featuredEntries.map((entry) => (
                  <EntryCard key={entry.id}>
                    <EntryImage src={entry.image_url} alt={entry.name} />
                    <EntryInfo>
                      <EntryType type={entry.type}>{entry.type}</EntryType>
                      <EntryTitle>{entry.name}</EntryTitle>
                      <EntrySummary>{entry.summary}</EntrySummary>
                      <EntryLink to={`/celebrate/${entry.type}/${entry.slug}`}>
                        Read More
                      </EntryLink>
                    </EntryInfo>
                  </EntryCard>
                ))}
              </FeaturedGrid>
            )}
          </section>
          
          <CategoriesSection>
            <SectionTitle>Categories</SectionTitle>
            
            {isLoading ? (
              <LoadingSpinner />
            ) : (
              <CategoriesGrid>
                {categories.map((category) => (
                  <CategoryCard
                    key={category.id}
                    to={`/celebrate/category/${category.id}`}
                  >
                    <CategoryTitle>{category.name}</CategoryTitle>
                    <CategoryCount>{category.entries_count} entries</CategoryCount>
                  </CategoryCard>
                ))}
              </CategoriesGrid>
            )}
          </CategoriesSection>
          
          <RandomEntrySection>
            <SectionTitle>Discover Something New</SectionTitle>
            <RandomEntryButton onClick={handleRandomEntry}>
              Random Entry
            </RandomEntryButton>
          </RandomEntrySection>
          
          <SubmitSection>
            <SubmitTitle>Know Something We Don't?</SubmitTitle>
            <SubmitDescription>
              Help us celebrate Nigerian excellence by submitting your own entry. Share stories of
              remarkable people, breathtaking places, or significant events that deserve
              recognition.
            </SubmitDescription>
            <SubmitButton to="/celebrate/submit">Submit an Entry</SubmitButton>
          </SubmitSection>
        </>
      )}
    </Container>
  );
};

export default CelebratePage;
