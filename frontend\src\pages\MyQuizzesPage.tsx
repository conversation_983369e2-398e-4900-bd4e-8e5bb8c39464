import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Timer as TimerIcon,
  School as SchoolIcon,
  QuestionAnswer as QuestionIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchUserCreatedQuizzes, fetchUserAttemptedQuizzes, deleteQuiz } from '../store/slices/quizzesSlice';
import { Quiz } from '../services/quizzesService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`quiz-tabpanel-${index}`}
      aria-labelledby={`quiz-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `quiz-tab-${index}`,
    'aria-controls': `quiz-tabpanel-${index}`,
  };
}

const MyQuizzesPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { items: createdQuizzes, loading: createdLoading, error: createdError } = useSelector(
    (state: RootState) => state.quizzes.userCreatedQuizzes
  );
  const { items: attemptedQuizzes, loading: attemptedLoading, error: attemptedError } = useSelector(
    (state: RootState) => state.quizzes.userAttemptedQuizzes
  );
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [tabValue, setTabValue] = useState(0);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  useEffect(() => {
    if (user) {
      dispatch(fetchUserCreatedQuizzes(user.id));
      dispatch(fetchUserAttemptedQuizzes(user.id));
    }
  }, [dispatch, user]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, quiz: Quiz) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedQuiz(quiz);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };
  
  const handleDeleteConfirm = async () => {
    if (selectedQuiz) {
      try {
        await dispatch(deleteQuiz(selectedQuiz.id)).unwrap();
        setDeleteDialogOpen(false);
        setSelectedQuiz(null);
      } catch (error) {
        console.error('Failed to delete quiz:', error);
      }
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };
  
  const handleCreateQuiz = () => {
    navigate('/quizzes/create');
  };
  
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to view your quizzes. Please log in and try again.
        </Alert>
      </Container>
    );
  }
  
  const publishedQuizzes = createdQuizzes.filter((quiz) => quiz.isPublished);
  const draftQuizzes = createdQuizzes.filter((quiz) => !quiz.isPublished);
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          My Quizzes
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateQuiz}
        >
          Create Quiz
        </Button>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="quiz tabs">
            <Tab label="Created Quizzes" {...a11yProps(0)} />
            <Tab label="Published" {...a11yProps(1)} />
            <Tab label="Drafts" {...a11yProps(2)} />
            <Tab label="Attempted Quizzes" {...a11yProps(3)} />
          </Tabs>
        </Box>
        
        {createdLoading || attemptedLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : createdError || attemptedError ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{createdError || attemptedError}</Alert>
          </Box>
        ) : (
          <>
            {/* All Created Quizzes */}
            <TabPanel value={tabValue} index={0}>
              {createdQuizzes.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You haven't created any quizzes yet.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateQuiz}
                    sx={{ mt: 2 }}
                  >
                    Create Your First Quiz
                  </Button>
                </Box>
              ) : (
                <List>
                  {createdQuizzes.map((quiz) => (
                    <React.Fragment key={quiz.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="h6" component="span">
                                {quiz.title}
                              </Typography>
                              <Chip
                                label={quiz.isPublished ? 'Published' : 'Draft'}
                                color={quiz.isPublished ? 'success' : 'default'}
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {quiz.description.length > 120
                                  ? `${quiz.description.substring(0, 120)}...`
                                  : quiz.description}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <TimerIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {quiz.timeLimit ? `${quiz.timeLimit} minutes` : 'No time limit'}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Passing Score: {quiz.passingScore}%
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <QuestionIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Questions: {quiz.questionCount || 'N/A'}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Created: {new Date(quiz.createdAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, quiz)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
            
            {/* Published Quizzes */}
            <TabPanel value={tabValue} index={1}>
              {publishedQuizzes.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You don't have any published quizzes yet.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateQuiz}
                    sx={{ mt: 2 }}
                  >
                    Create a Quiz
                  </Button>
                </Box>
              ) : (
                <List>
                  {publishedQuizzes.map((quiz) => (
                    <React.Fragment key={quiz.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemText
                          primary={
                            <Typography variant="h6" component="span">
                              {quiz.title}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {quiz.description.length > 120
                                  ? `${quiz.description.substring(0, 120)}...`
                                  : quiz.description}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <TimerIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {quiz.timeLimit ? `${quiz.timeLimit} minutes` : 'No time limit'}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Passing Score: {quiz.passingScore}%
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <QuestionIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Questions: {quiz.questionCount || 'N/A'}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Published: {new Date(quiz.updatedAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, quiz)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
            
            {/* Draft Quizzes */}
            <TabPanel value={tabValue} index={2}>
              {draftQuizzes.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You don't have any draft quizzes.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateQuiz}
                    sx={{ mt: 2 }}
                  >
                    Create a Quiz
                  </Button>
                </Box>
              ) : (
                <List>
                  {draftQuizzes.map((quiz) => (
                    <React.Fragment key={quiz.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemText
                          primary={
                            <Typography variant="h6" component="span">
                              {quiz.title}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {quiz.description.length > 120
                                  ? `${quiz.description.substring(0, 120)}...`
                                  : quiz.description}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <TimerIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {quiz.timeLimit ? `${quiz.timeLimit} minutes` : 'No time limit'}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Passing Score: {quiz.passingScore}%
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <QuestionIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Questions: {quiz.questionCount || 'N/A'}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Last updated: {new Date(quiz.updatedAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="more"
                            onClick={(e) => handleMenuOpen(e, quiz)}
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
            
            {/* Attempted Quizzes */}
            <TabPanel value={tabValue} index={3}>
              {attemptedQuizzes.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    You haven't attempted any quizzes yet.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => navigate('/quizzes')}
                    sx={{ mt: 2 }}
                  >
                    Browse Quizzes
                  </Button>
                </Box>
              ) : (
                <List>
                  {attemptedQuizzes.map((quiz) => (
                    <React.Fragment key={quiz.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemText
                          primary={
                            <Typography variant="h6" component="span">
                              {quiz.title}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {quiz.description.length > 120
                                  ? `${quiz.description.substring(0, 120)}...`
                                  : quiz.description}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Score: {quiz.lastScore || 'N/A'}%
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <QuestionIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    Questions: {quiz.questionCount || 'N/A'}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  Last attempt: {quiz.lastAttemptDate ? new Date(quiz.lastAttemptDate).toLocaleDateString() : 'N/A'}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Button
                            variant="outlined"
                            size="small"
                            component={Link}
                            to={`/quizzes/${quiz.id}`}
                            startIcon={<VisibilityIcon />}
                          >
                            Retake
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </TabPanel>
          </>
        )}
      </Paper>
      
      {/* Quiz actions menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        {selectedQuiz?.isPublished && (
          <MenuItem
            onClick={() => {
              navigate(`/quizzes/${selectedQuiz?.id}`);
              handleMenuClose();
            }}
          >
            <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
            View
          </MenuItem>
        )}
        <MenuItem
          onClick={() => {
            navigate(`/quizzes/edit/${selectedQuiz?.id}`);
            handleMenuClose();
          }}
        >
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
      
      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Quiz</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the quiz "{selectedQuiz?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default MyQuizzesPage;
