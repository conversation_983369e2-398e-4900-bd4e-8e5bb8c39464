import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  useTheme,
  alpha
} from '@mui/material';
import {
  AccountBalanceWallet as WalletIcon,
  ArrowUpward as SendIcon,
  ArrowDownward as ReceiveIcon,
  Add as AddIcon,
  CreditCard as CardIcon,
  AccountBalance as BankIcon,
  Phone as PhoneIcon,
  Receipt as ReceiptIcon,
  History as HistoryIcon,
  Payment as PaymentIcon,
  Close as CloseIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { RootState } from '../../store';
import {
  fetchWallet,
  fetchTransactions,
  initiateDeposit,
  requestWithdrawal,
  transferFunds,
  fetchPaymentMethods,
  resetDepositState,
  resetWithdrawalState,
  resetTransferState
} from '../../features/wallet/walletSlice';
import { Transaction, DepositRequest, WithdrawalRequest, TransferRequest } from '../../api/walletService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`wallet-tabpanel-${index}`}
      aria-labelledby={`wallet-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const WalletPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    wallet,
    transactions,
    paymentMethods,
    deposit,
    withdrawal,
    transfer
  } = useSelector((state: RootState) => state.wallet);
  
  const [tabValue, setTabValue] = useState(0);
  const [showBalance, setShowBalance] = useState(true);
  
  // Dialog states
  const [depositDialogOpen, setDepositDialogOpen] = useState(false);
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  
  // Form states
  const [depositForm, setDepositForm] = useState<DepositRequest>({
    amount: 0,
    paymentMethod: 'card'
  });
  
  const [withdrawalForm, setWithdrawalForm] = useState<WithdrawalRequest>({
    amount: 0,
    withdrawalMethod: 'bank_transfer',
    accountDetails: {}
  });
  
  const [transferForm, setTransferForm] = useState<TransferRequest>({
    amount: 0,
    recipientUserId: '',
    description: ''
  });
  
  // Fetch data when component mounts
  useEffect(() => {
    if (user?.id) {
      dispatch(fetchWallet() as any);
      dispatch(fetchTransactions({}) as any);
      dispatch(fetchPaymentMethods() as any);
    }
  }, [dispatch, user]);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Format currency
  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Get transaction icon
  const getTransactionIcon = (transaction: Transaction) => {
    switch (transaction.type) {
      case 'deposit':
        return <ReceiveIcon color="success" />;
      case 'withdrawal':
        return <SendIcon color="error" />;
      case 'payment':
        return <PaymentIcon color="error" />;
      case 'refund':
        return <ReceiveIcon color="success" />;
      case 'transfer':
        return transaction.amount > 0 ? <ReceiveIcon color="success" /> : <SendIcon color="error" />;
      default:
        return <HistoryIcon />;
    }
  };
  
  // Get transaction color
  const getTransactionColor = (transaction: Transaction) => {
    if (transaction.amount > 0) {
      return theme.palette.success.main;
    } else {
      return theme.palette.error.main;
    }
  };
  
  // Get transaction status chip
  const getStatusChip = (status: string) => {
    switch (status) {
      case 'completed':
        return <Chip size="small" label="Completed" color="success" />;
      case 'pending':
        return <Chip size="small" label="Pending" color="warning" />;
      case 'failed':
        return <Chip size="small" label="Failed" color="error" />;
      case 'cancelled':
        return <Chip size="small" label="Cancelled" color="default" />;
      default:
        return <Chip size="small" label={status} />;
    }
  };
  
  // Handle deposit dialog
  const handleOpenDepositDialog = () => {
    setDepositDialogOpen(true);
  };
  
  const handleCloseDepositDialog = () => {
    setDepositDialogOpen(false);
    dispatch(resetDepositState() as any);
  };
  
  const handleDepositFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setDepositForm({
      ...depositForm,
      [name as string]: value
    });
  };
  
  const handleSubmitDeposit = () => {
    if (depositForm.amount <= 0) {
      return;
    }
    
    dispatch(initiateDeposit(depositForm) as any);
  };
  
  // Handle withdrawal dialog
  const handleOpenWithdrawDialog = () => {
    setWithdrawDialogOpen(true);
  };
  
  const handleCloseWithdrawDialog = () => {
    setWithdrawDialogOpen(false);
    dispatch(resetWithdrawalState() as any);
  };
  
  const handleWithdrawalFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    
    if (name?.startsWith('accountDetails.')) {
      const field = name.split('.')[1];
      setWithdrawalForm({
        ...withdrawalForm,
        accountDetails: {
          ...withdrawalForm.accountDetails,
          [field]: value
        }
      });
    } else {
      setWithdrawalForm({
        ...withdrawalForm,
        [name as string]: value
      });
    }
  };
  
  const handleSubmitWithdrawal = () => {
    if (withdrawalForm.amount <= 0) {
      return;
    }
    
    dispatch(requestWithdrawal(withdrawalForm) as any);
  };
  
  // Handle transfer dialog
  const handleOpenTransferDialog = () => {
    setTransferDialogOpen(true);
  };
  
  const handleCloseTransferDialog = () => {
    setTransferDialogOpen(false);
    dispatch(resetTransferState() as any);
  };
  
  const handleTransferFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setTransferForm({
      ...transferForm,
      [name as string]: value
    });
  };
  
  const handleSubmitTransfer = () => {
    if (transferForm.amount <= 0 || !transferForm.recipientUserId) {
      return;
    }
    
    dispatch(transferFunds(transferForm) as any);
  };
  
  // Redirect to payment gateway if deposit is successful
  useEffect(() => {
    if (deposit.redirectUrl) {
      window.location.href = deposit.redirectUrl;
    }
  }, [deposit.redirectUrl]);
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Digital Wallet
      </Typography>
      
      <Grid container spacing={3}>
        {/* Wallet Balance Card */}
        <Grid item xs={12} md={4}>
          <Paper 
            elevation={2} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.8)} 0%, ${alpha(theme.palette.primary.dark, 0.9)} 100%)`,
              color: 'white'
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Wallet Balance</Typography>
              <IconButton 
                size="small" 
                onClick={() => setShowBalance(!showBalance)}
                sx={{ color: 'white' }}
              >
                {showBalance ? <VisibilityOffIcon /> : <VisibilityIcon />}
              </IconButton>
            </Box>
            
            {wallet.loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                <CircularProgress size={40} sx={{ color: 'white' }} />
              </Box>
            ) : wallet.error ? (
              <Alert severity="error" sx={{ mb: 2 }}>
                {wallet.error}
              </Alert>
            ) : (
              <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
                {showBalance 
                  ? formatCurrency(wallet.data?.balance || 0, wallet.data?.currency) 
                  : '••••••••'}
              </Typography>
            )}
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                color="secondary"
                startIcon={<AddIcon />}
                fullWidth
                onClick={handleOpenDepositDialog}
                disabled={wallet.loading}
              >
                Deposit
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<SendIcon />}
                fullWidth
                onClick={handleOpenWithdrawDialog}
                disabled={wallet.loading || !(wallet.data?.balance && wallet.data.balance > 0)}
                sx={{ 
                  color: 'white', 
                  borderColor: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: alpha('#ffffff', 0.1)
                  }
                }}
              >
                Withdraw
              </Button>
            </Box>
          </Paper>
          
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            
            <List>
              <ListItem button onClick={handleOpenTransferDialog}>
                <ListItemIcon>
                  <SendIcon />
                </ListItemIcon>
                <ListItemText primary="Transfer Money" />
              </ListItem>
              
              <ListItem button component="a" href="/marketplace">
                <ListItemIcon>
                  <PaymentIcon />
                </ListItemIcon>
                <ListItemText primary="Shop Marketplace" />
              </ListItem>
              
              <ListItem button component="a" href="/wallet/payment-methods">
                <ListItemIcon>
                  <CardIcon />
                </ListItemIcon>
                <ListItemText primary="Payment Methods" />
              </ListItem>
            </List>
          </Paper>
        </Grid>
        
        {/* Transactions */}
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ borderRadius: 2 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab label="All Transactions" />
                <Tab label="Deposits" />
                <Tab label="Withdrawals" />
                <Tab label="Transfers" />
                <Tab label="Payments" />
              </Tabs>
            </Box>
            
            <TabPanel value={tabValue} index={0}>
              {renderTransactions(transactions.items)}
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              {renderTransactions(transactions.items.filter(t => t.type === 'deposit'))}
            </TabPanel>
            
            <TabPanel value={tabValue} index={2}>
              {renderTransactions(transactions.items.filter(t => t.type === 'withdrawal'))}
            </TabPanel>
            
            <TabPanel value={tabValue} index={3}>
              {renderTransactions(transactions.items.filter(t => t.type === 'transfer'))}
            </TabPanel>
            
            <TabPanel value={tabValue} index={4}>
              {renderTransactions(transactions.items.filter(t => t.type === 'payment'))}
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Deposit Dialog */}
      <Dialog
        open={depositDialogOpen}
        onClose={handleCloseDepositDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Deposit Funds
            <IconButton onClick={handleCloseDepositDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {deposit.error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {deposit.error}
            </Alert>
          )}
          
          <TextField
            label="Amount"
            type="number"
            name="amount"
            value={depositForm.amount}
            onChange={handleDepositFormChange}
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">₦</InputAdornment>,
            }}
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="payment-method-label">Payment Method</InputLabel>
            <Select
              labelId="payment-method-label"
              name="paymentMethod"
              value={depositForm.paymentMethod}
              onChange={handleDepositFormChange}
              label="Payment Method"
            >
              <MenuItem value="card">Credit/Debit Card</MenuItem>
              <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
              <MenuItem value="ussd">USSD</MenuItem>
              <MenuItem value="mobile_money">Mobile Money</MenuItem>
            </Select>
          </FormControl>
          
          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              You will be redirected to our secure payment gateway to complete your deposit.
            </Typography>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleCloseDepositDialog}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmitDeposit}
            disabled={depositForm.amount <= 0 || deposit.loading}
          >
            {deposit.loading ? <CircularProgress size={24} /> : 'Proceed to Payment'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Withdrawal Dialog */}
      <Dialog
        open={withdrawDialogOpen}
        onClose={handleCloseWithdrawDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Withdraw Funds
            <IconButton onClick={handleCloseWithdrawDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {withdrawal.error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {withdrawal.error}
            </Alert>
          )}
          
          {wallet.data && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Available balance: {formatCurrency(wallet.data.balance, wallet.data.currency)}
            </Alert>
          )}
          
          <TextField
            label="Amount"
            type="number"
            name="amount"
            value={withdrawalForm.amount}
            onChange={handleWithdrawalFormChange}
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">₦</InputAdornment>,
            }}
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="withdrawal-method-label">Withdrawal Method</InputLabel>
            <Select
              labelId="withdrawal-method-label"
              name="withdrawalMethod"
              value={withdrawalForm.withdrawalMethod}
              onChange={handleWithdrawalFormChange}
              label="Withdrawal Method"
            >
              <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
              <MenuItem value="mobile_money">Mobile Money</MenuItem>
            </Select>
          </FormControl>
          
          {withdrawalForm.withdrawalMethod === 'bank_transfer' && (
            <>
              <TextField
                label="Account Name"
                name="accountDetails.accountName"
                value={withdrawalForm.accountDetails.accountName || ''}
                onChange={handleWithdrawalFormChange}
                fullWidth
                margin="normal"
              />
              
              <TextField
                label="Account Number"
                name="accountDetails.accountNumber"
                value={withdrawalForm.accountDetails.accountNumber || ''}
                onChange={handleWithdrawalFormChange}
                fullWidth
                margin="normal"
              />
              
              <TextField
                label="Bank Name"
                name="accountDetails.bankName"
                value={withdrawalForm.accountDetails.bankName || ''}
                onChange={handleWithdrawalFormChange}
                fullWidth
                margin="normal"
              />
            </>
          )}
          
          {withdrawalForm.withdrawalMethod === 'mobile_money' && (
            <>
              <TextField
                label="Phone Number"
                name="accountDetails.phoneNumber"
                value={withdrawalForm.accountDetails.phoneNumber || ''}
                onChange={handleWithdrawalFormChange}
                fullWidth
                margin="normal"
              />
              
              <TextField
                label="Provider"
                name="accountDetails.provider"
                value={withdrawalForm.accountDetails.provider || ''}
                onChange={handleWithdrawalFormChange}
                fullWidth
                margin="normal"
              />
            </>
          )}
          
          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Withdrawal requests are typically processed within 1-3 business days.
            </Typography>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleCloseWithdrawDialog}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmitWithdrawal}
            disabled={
              withdrawalForm.amount <= 0 || 
              withdrawal.loading || 
              (wallet.data && withdrawalForm.amount > wallet.data.balance)
            }
          >
            {withdrawal.loading ? <CircularProgress size={24} /> : 'Request Withdrawal'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Transfer Dialog */}
      <Dialog
        open={transferDialogOpen}
        onClose={handleCloseTransferDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Transfer Money
            <IconButton onClick={handleCloseTransferDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {transfer.error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {transfer.error}
            </Alert>
          )}
          
          {wallet.data && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Available balance: {formatCurrency(wallet.data.balance, wallet.data.currency)}
            </Alert>
          )}
          
          <TextField
            label="Recipient User ID"
            name="recipientUserId"
            value={transferForm.recipientUserId}
            onChange={handleTransferFormChange}
            fullWidth
            margin="normal"
            helperText="Enter the user ID of the recipient"
          />
          
          <TextField
            label="Amount"
            type="number"
            name="amount"
            value={transferForm.amount}
            onChange={handleTransferFormChange}
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">₦</InputAdornment>,
            }}
          />
          
          <TextField
            label="Description (Optional)"
            name="description"
            value={transferForm.description}
            onChange={handleTransferFormChange}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleCloseTransferDialog}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmitTransfer}
            disabled={
              transferForm.amount <= 0 || 
              !transferForm.recipientUserId || 
              transfer.loading || 
              (wallet.data && transferForm.amount > wallet.data.balance)
            }
          >
            {transfer.loading ? <CircularProgress size={24} /> : 'Transfer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
  
  // Helper function to render transactions
  function renderTransactions(transactionList: Transaction[]) {
    if (transactions.loading && transactionList.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (transactions.error) {
      return (
        <Alert severity="error" sx={{ m: 2 }}>
          {transactions.error}
        </Alert>
      );
    }
    
    if (transactionList.length === 0) {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No transactions found
          </Typography>
        </Box>
      );
    }
    
    return (
      <List sx={{ width: '100%' }}>
        {transactionList.map((transaction) => (
          <React.Fragment key={transaction.id}>
            <ListItem alignItems="flex-start">
              <ListItemIcon>
                {getTransactionIcon(transaction)}
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Typography variant="subtitle1" fontWeight="medium">
                    {transaction.description || transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                  </Typography>
                }
                secondary={
                  <>
                    <Typography variant="body2" color="text.secondary" component="span">
                      {formatDate(transaction.createdAt)}
                    </Typography>
                    <br />
                    <Typography variant="body2" color="text.secondary" component="span">
                      Ref: {transaction.referenceId || transaction.id}
                    </Typography>
                  </>
                }
              />
              
              <ListItemSecondaryAction>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                  <Typography 
                    variant="subtitle1" 
                    fontWeight="bold"
                    sx={{ color: getTransactionColor(transaction) }}
                  >
                    {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount, transaction.currency)}
                  </Typography>
                  
                  <Box sx={{ mt: 1 }}>
                    {getStatusChip(transaction.status)}
                  </Box>
                </Box>
              </ListItemSecondaryAction>
            </ListItem>
            <Divider variant="inset" component="li" />
          </React.Fragment>
        ))}
      </List>
    );
  }
};

export default WalletPage;
