import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Badge, UserBadge, getAllBadges, getUserBadges, markBadgeAsViewed, toggleBadgeVisibility, setFeaturedBadge } from '../api/badgeService';

interface BadgesState {
  badges: Badge[];
  userBadges: UserBadge[];
  featuredBadgeId: string | null;
  loading: boolean;
  error: string | null;
}

const initialState: BadgesState = {
  badges: [],
  userBadges: [],
  featuredBadgeId: null,
  loading: false,
  error: null
};

// Async thunks
export const fetchAllBadges = createAsyncThunk(
  'badges/fetchAllBadges',
  async (_, { rejectWithValue }) => {
    try {
      return await getAllBadges();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch badges');
    }
  }
);

export const fetchUserBadges = createAsyncThunk(
  'badges/fetchUserBadges',
  async (userId: string, { rejectWithValue }) => {
    try {
      return await getUserBadges(userId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user badges');
    }
  }
);

export const markBadgeViewed = createAsyncThunk(
  'badges/markBadgeViewed',
  async ({ userId, badgeId }: { userId: string; badgeId: string }, { rejectWithValue }) => {
    try {
      await markBadgeAsViewed(userId, badgeId);
      return badgeId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark badge as viewed');
    }
  }
);

export const toggleBadgeHidden = createAsyncThunk(
  'badges/toggleBadgeHidden',
  async ({ userId, badgeId, isHidden }: { userId: string; badgeId: string; isHidden: boolean }, { rejectWithValue }) => {
    try {
      await toggleBadgeVisibility(userId, badgeId, isHidden);
      return { badgeId, isHidden };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to toggle badge visibility');
    }
  }
);

export const setFeaturedUserBadge = createAsyncThunk(
  'badges/setFeaturedUserBadge',
  async ({ userId, badgeId }: { userId: string; badgeId: string }, { rejectWithValue }) => {
    try {
      await setFeaturedBadge(userId, badgeId);
      return badgeId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to set featured badge');
    }
  }
);

// Slice
const badgesSlice = createSlice({
  name: 'badges',
  initialState,
  reducers: {
    resetBadgesState: (state) => {
      state.badges = [];
      state.userBadges = [];
      state.featuredBadgeId = null;
      state.loading = false;
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchAllBadges
      .addCase(fetchAllBadges.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllBadges.fulfilled, (state, action: PayloadAction<Badge[]>) => {
        state.badges = action.payload;
        state.loading = false;
      })
      .addCase(fetchAllBadges.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // fetchUserBadges
      .addCase(fetchUserBadges.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserBadges.fulfilled, (state, action: PayloadAction<UserBadge[]>) => {
        state.userBadges = action.payload;
        
        // Find featured badge
        const featuredBadge = action.payload.find(badge => badge.isHidden === false && badge.isPublic === true);
        if (featuredBadge) {
          state.featuredBadgeId = featuredBadge.badgeId;
        }
        
        state.loading = false;
      })
      .addCase(fetchUserBadges.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // markBadgeViewed
      .addCase(markBadgeViewed.fulfilled, (state, action: PayloadAction<string>) => {
        const badgeId = action.payload;
        state.userBadges = state.userBadges.map(badge => 
          badge.badgeId === badgeId ? { ...badge, isNew: false } : badge
        );
      })
      
      // toggleBadgeHidden
      .addCase(toggleBadgeHidden.fulfilled, (state, action: PayloadAction<{ badgeId: string; isHidden: boolean }>) => {
        const { badgeId, isHidden } = action.payload;
        state.userBadges = state.userBadges.map(badge => 
          badge.badgeId === badgeId ? { ...badge, isHidden } : badge
        );
      })
      
      // setFeaturedUserBadge
      .addCase(setFeaturedUserBadge.fulfilled, (state, action: PayloadAction<string>) => {
        state.featuredBadgeId = action.payload;
      });
  }
});

export const { resetBadgesState } = badgesSlice.actions;

export default badgesSlice.reducer;
