import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  Tabs,
  Tab,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Divider,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
  Add as AddIcon,
  Share as ShareIcon,
  MonetizationOn as MoneyIcon,
  Link as LinkIcon,
  Check as CheckIcon,
  AccountBalance as WithdrawIcon,
  CardMembership as MembershipIcon,
  ShoppingCart as ProductIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { RootState } from '../../store';
import {
  fetchReferralCodes,
  createReferralCode,
  updateReferralCode,
  deleteReferralCode,
  fetchReferrals,
  fetchCommissions,
  fetchAffiliateDashboard,
  fetchMembershipPlans,
  fetchProductsWithAffiliatePrograms,
  fetchSellerProducts,
  updateProductAffiliateSettings,
  withdrawCommission,
  clearOperationStatus
} from '../../features/affiliate/affiliateSlice';
import ReferralCodeCard from '../../components/affiliate/ReferralCodeCard';
import CommissionCard from '../../components/affiliate/CommissionCard';
import DualAffiliateStatsCard from '../../components/affiliate/DualAffiliateStatsCard';
import MembershipAffiliateCard from '../../components/affiliate/MembershipAffiliateCard';
import AffiliateProductCard from '../../components/affiliate/AffiliateProductCard';
import SellerAffiliateSettings from '../../components/affiliate/SellerAffiliateSettings';
import { UpdateProductAffiliateSettingsRequest } from '../../api/affiliateService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`affiliate-tabpanel-${index}`}
      aria-labelledby={`affiliate-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AffiliatePage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    referralCodes,
    referrals,
    commissions,
    stats,
    settings,
    referralLink,
    membershipPlans,
    membershipStats,
    marketplaceStats,
    sellerProducts,
    affiliateProducts,
    dashboard,
    operations
  } = useSelector((state: RootState) => state.affiliate);
  
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [newCodeDescription, setNewCodeDescription] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState(0);
  const [withdrawMethod, setWithdrawMethod] = useState('wallet');
  const [referralsPage, setReferralsPage] = useState(1);
  const [commissionsPage, setCommissionsPage] = useState(1);
  const [productsPage, setProductsPage] = useState(1);
  const [referralStatusFilter, setReferralStatusFilter] = useState('');
  const [commissionStatusFilter, setCommissionStatusFilter] = useState('');
  const [commissionTypeFilter, setCommissionTypeFilter] = useState('');
  const [productCategoryFilter, setProductCategoryFilter] = useState('');
  const [copied, setCopied] = useState(false);
  
  // Fetch data when component mounts
  useEffect(() => {
    dispatch(fetchReferralCodes() as any);
    dispatch(fetchAffiliateDashboard() as any);
    dispatch(fetchMembershipPlans() as any);
    dispatch(fetchReferralLink({ codeId: undefined }) as any);
    loadTabData();
  }, [dispatch]);
  
  // Load tab data based on current tab
  useEffect(() => {
    loadTabData();
  }, [tabValue, referralsPage, commissionsPage, productsPage, referralStatusFilter, commissionStatusFilter, commissionTypeFilter, productCategoryFilter]);
  
  const loadTabData = () => {
    if (tabValue === 1) {
      // Dashboard tab - already loaded in initial fetch
    } else if (tabValue === 2) {
      // Referrals tab
      dispatch(fetchReferrals({ 
        page: referralsPage, 
        status: referralStatusFilter 
      }) as any);
    } else if (tabValue === 3) {
      // Commissions tab
      dispatch(fetchCommissions({ 
        page: commissionsPage, 
        status: commissionStatusFilter,
        type: commissionTypeFilter as any
      }) as any);
    } else if (tabValue === 4) {
      // Membership tab - plans already loaded in initial fetch
    } else if (tabValue === 5) {
      // Marketplace tab
      dispatch(fetchProductsWithAffiliatePrograms({
        page: productsPage,
        category: productCategoryFilter
      }) as any);
    } else if (tabValue === 6) {
      // Seller settings tab
      dispatch(fetchSellerProducts() as any);
    }
  };
  
  // Clear operation status when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearOperationStatus());
    };
  }, [dispatch]);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle create referral code
  const handleCreateReferralCode = () => {
    dispatch(createReferralCode({ description: newCodeDescription }) as any);
    setNewCodeDescription('');
    setCreateDialogOpen(false);
  };
  
  // Handle edit referral code
  const handleEditReferralCode = (referralCode: any, description: string) => {
    dispatch(updateReferralCode({ id: referralCode.id, request: { description } }) as any);
  };
  
  // Handle delete referral code
  const handleDeleteReferralCode = (referralCode: any) => {
    dispatch(deleteReferralCode(referralCode.id) as any);
  };
  
  // Handle withdraw commission
  const handleWithdrawCommission = () => {
    dispatch(withdrawCommission({
      amount: withdrawAmount,
      paymentMethod: withdrawMethod as any,
      accountDetails: {}
    }) as any);
    setWithdrawDialogOpen(false);
  };
  
  // Handle copy referral link
  const handleCopyReferralLink = () => {
    navigator.clipboard.writeText(referralLink.link);
    setCopied(true);
    
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };
  
  // Handle share referral link
  const handleShareReferralLink = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Join Great Nigeria with my referral code',
        text: 'Use my referral code to join Great Nigeria and get special benefits!',
        url: referralLink.link
      });
    } else {
      handleCopyReferralLink();
    }
  };
  
  // Handle update product affiliate settings
  const handleUpdateProductAffiliateSettings = (productId: string, settings: UpdateProductAffiliateSettingsRequest) => {
    dispatch(updateProductAffiliateSettings({ productId, settings }) as any);
  };
  
  // Get membership referral link
  const getMembershipReferralLink = (planId: string, referralCode: string) => {
    return `${window.location.origin}/membership/join?plan=${planId}&ref=${referralCode}`;
  };
  
  // Get product referral link
  const getProductReferralLink = (productId: string, referralCode: string) => {
    return `${window.location.origin}/marketplace/product/${productId}?ref=${referralCode}`;
  };
  
  // Format currency
  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  // Calculate total available commission
  const calculateAvailableCommission = () => {
    if (!commissions.items.length) return 0;
    
    return commissions.items
      .filter(commission => commission.status === 'approved')
      .reduce((total, commission) => total + commission.amount, 0);
  };
  
  const availableCommission = calculateAvailableCommission();
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Affiliate Marketing
      </Typography>
      
      <Paper elevation={2} sx={{ borderRadius: 2, mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="affiliate tabs"
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<LinkIcon />} label="Referral Codes" iconPosition="start" />
          <Tab icon={<DashboardIcon />} label="Dashboard" iconPosition="start" />
          <Tab icon={<PeopleIcon />} label="Referrals" iconPosition="start" />
          <Tab icon={<MoneyIcon />} label="Commissions" iconPosition="start" />
          <Tab icon={<MembershipIcon />} label="Membership" iconPosition="start" />
          <Tab icon={<ProductIcon />} label="Marketplace" iconPosition="start" />
          <Tab icon={<SettingsIcon />} label="Seller Settings" iconPosition="start" />
        </Tabs>
        
        {/* Operation status messages */}
        {operations.error && (
          <Alert severity="error" sx={{ mx: 3, mt: 2 }}>
            {operations.error}
          </Alert>
        )}
        
        {operations.success && (
          <Alert severity="success" sx={{ mx: 3, mt: 2 }}>
            {operations.success}
          </Alert>
        )}
        
        {/* Referral Codes Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ px: 3, mb: 3 }}>
            <Grid container spacing={3}>
              {/* Referral Link Card */}
              <Grid item xs={12} md={6}>
                <Paper elevation={2} sx={{ p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="h6" gutterBottom>
                    Your Referral Link
                  </Typography>
                  
                  {referralLink.loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                      <CircularProgress />
                    </Box>
                  ) : referralLink.error ? (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {referralLink.error}
                    </Alert>
                  ) : (
                    <>
                      <Box sx={{ 
                        p: 2, 
                        bgcolor: alpha(theme.palette.primary.main, 0.1), 
                        borderRadius: 2,
                        display: 'flex',
                        alignItems: 'center',
                        mb: 2
                      }}>
                        <LinkIcon color="primary" sx={{ mr: 1 }} />
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            flexGrow: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {referralLink.link}
                        </Typography>
                        
                        <Tooltip title={copied ? 'Copied!' : 'Copy link'}>
                          <IconButton onClick={handleCopyReferralLink} color={copied ? 'success' : 'primary'}>
                            {copied ? <CheckIcon /> : <CopyIcon />}
                          </IconButton>
                        </Tooltip>
                      </Box>
                      
                      <Box sx={{ 
                        p: 2, 
                        bgcolor: alpha(theme.palette.secondary.main, 0.1), 
                        borderRadius: 2,
                        mb: 2
                      }}>
                        <Typography variant="body2" gutterBottom>
                          <strong>Referral Code:</strong> {referralLink.code}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Share this code with friends to earn commissions when they join or make purchases.
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', gap: 2, mt: 'auto' }}>
                        <Button
                          variant="outlined"
                          color="primary"
                          startIcon={<CopyIcon />}
                          onClick={handleCopyReferralLink}
                          fullWidth
                        >
                          Copy
                        </Button>
                        
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<ShareIcon />}
                          onClick={handleShareReferralLink}
                          fullWidth
                        >
                          Share
                        </Button>
                      </Box>
                    </>
                  )}
                </Paper>
              </Grid>
              
              {/* Available Commission Card */}
              <Grid item xs={12} md={6}>
                <Paper 
                  elevation={2} 
                  sx={{ 
                    p: 3, 
                    borderRadius: 2,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.8)} 0%, ${alpha(theme.palette.success.dark, 0.9)} 100%)`,
                    color: 'white',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    height: '100%'
                  }}
                >
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Available Commission
                    </Typography>
                    <Typography variant="h3" fontWeight="bold">
                      {formatCurrency(availableCommission)}
                    </Typography>
                    <Typography variant="body2">
                      You can withdraw your approved commissions at any time.
                    </Typography>
                  </Box>
                  
                  <Button
                    variant="contained"
                    color="secondary"
                    size="large"
                    startIcon={<WithdrawIcon />}
                    onClick={() => setWithdrawDialogOpen(true)}
                    disabled={availableCommission <= 0}
                    sx={{ 
                      px: 4, 
                      py: 1.5,
                      fontSize: '1.1rem',
                      boxShadow: theme.shadows[8],
                      mt: 3
                    }}
                  >
                    Withdraw Funds
                  </Button>
                </Paper>
              </Grid>
            </Grid>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 4, mb: 2 }}>
              <Typography variant="h6">
                Your Referral Codes
              </Typography>
              
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => setCreateDialogOpen(true)}
              >
                New Code
              </Button>
            </Box>
            
            {referralCodes.loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : referralCodes.error ? (
              <Alert severity="error">
                {referralCodes.error}
              </Alert>
            ) : referralCodes.items.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No referral codes yet
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Create your first referral code to start sharing with friends and earning commissions.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => setCreateDialogOpen(true)}
                >
                  Create Referral Code
                </Button>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {referralCodes.items.map((code) => (
                  <Grid item xs={12} sm={6} md={4} key={code.id}>
                    <ReferralCodeCard
                      referralCode={code}
                      onEdit={handleEditReferralCode}
                      onDelete={handleDeleteReferralCode}
                      onCopy={() => {}}
                      onShare={() => {}}
                    />
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </TabPanel>
        
        {/* Dashboard Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ px: 3 }}>
            <DualAffiliateStatsCard
              membershipStats={dashboard.data?.membership || null}
              marketplaceStats={dashboard.data?.marketplace || null}
              combinedStats={dashboard.data?.combined || null}
              loading={dashboard.loading}
            />
          </Box>
        </TabPanel>
        {/* Referrals Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ px: 3 }}>
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <InputLabel id="referral-status-filter-label">Status</InputLabel>
                <Select
                  labelId="referral-status-filter-label"
                  value={referralStatusFilter}
                  label="Status"
                  onChange={(e) => setReferralStatusFilter(e.target.value as string)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="converted">Converted</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                </Select>
              </FormControl>
            </Box>
            
            {referrals.loading && referrals.items.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : referrals.error ? (
              <Alert severity="error">
                {referrals.error}
              </Alert>
            ) : referrals.items.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No referrals yet
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Share your referral link or codes with friends to start earning commissions.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ShareIcon />}
                  onClick={handleShareReferralLink}
                >
                  Share Your Link
                </Button>
              </Box>
            ) : (
              <Box>
                <Paper variant="outlined" sx={{ mb: 3 }}>
                  <Box sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                    <Grid container>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">Referral Code</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">User</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">Type</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">Status</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">Value</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="subtitle2">Date</Typography>
                      </Grid>
                    </Grid>
                  </Box>
                  
                  <Divider />
                  
                  {referrals.items.map((referral, index) => (
                    <React.Fragment key={referral.id}>
                      <Box sx={{ p: 2 }}>
                        <Grid container alignItems="center">
                          <Grid item xs={2}>
                            <Typography variant="body2">{referral.referralCode}</Typography>
                          </Grid>
                          <Grid item xs={2}>
                            <Typography variant="body2">{referral.referredUserId}</Typography>
                          </Grid>
                          <Grid item xs={2}>
                            <Chip 
                              size="small" 
                              label={referral.referralType} 
                              color={
                                referral.referralType === 'membership' ? 'secondary' : 'primary'
                              }
                            />
                          </Grid>
                          <Grid item xs={2}>
                            <Chip 
                              size="small" 
                              label={referral.status} 
                              color={
                                referral.status === 'converted' ? 'success' :
                                referral.status === 'pending' ? 'warning' :
                                'default'
                              }
                            />
                          </Grid>
                          <Grid item xs={2}>
                            <Typography variant="body2">
                              {referral.conversionValue ? formatCurrency(referral.conversionValue) : 'N/A'}
                            </Typography>
                          </Grid>
                          <Grid item xs={2}>
                            <Typography variant="body2">
                              {new Date(referral.createdAt).toLocaleDateString()}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Box>
                      {index < referrals.items.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </Paper>
                
                {referrals.totalPages > 1 && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                    <Pagination
                      count={referrals.totalPages}
                      page={referrals.page}
                      onChange={(_, page) => setReferralsPage(page)}
                      color="primary"
                    />
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </TabPanel>
        
        {/* Commissions Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ px: 3 }}>
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel id="commission-type-filter-label">Type</InputLabel>
                <Select
                  labelId="commission-type-filter-label"
                  value={commissionTypeFilter}
                  label="Type"
                  onChange={(e) => setCommissionTypeFilter(e.target.value as string)}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="membership">Membership</MenuItem>
                  <MenuItem value="product">Product</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel id="commission-status-filter-label">Status</InputLabel>
                <Select
                  labelId="commission-status-filter-label"
                  value={commissionStatusFilter}
                  label="Status"
                  onChange={(e) => setCommissionStatusFilter(e.target.value as string)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="paid">Paid</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Box>
            
            {commissions.loading && commissions.items.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : commissions.error ? (
              <Alert severity="error">
                {commissions.error}
              </Alert>
            ) : commissions.items.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No commissions yet
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  You'll earn commissions when your referrals complete qualifying actions.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ShareIcon />}
                  onClick={handleShareReferralLink}
                >
                  Share Your Link
                </Button>
              </Box>
            ) : (
              <Box>
                <Grid container spacing={3}>
                  {commissions.items.map((commission) => (
                    <Grid item xs={12} sm={6} md={4} key={commission.id}>
                      <CommissionCard
                        commission={commission}
                        onWithdraw={commission.status === 'approved' ? () => setWithdrawDialogOpen(true) : undefined}
                      />
                    </Grid>
                  ))}
                </Grid>
                
                {commissions.totalPages > 1 && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                    <Pagination
                      count={commissions.totalPages}
                      page={commissions.page}
                      onChange={(_, page) => setCommissionsPage(page)}
                      color="primary"
                    />
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </TabPanel>
        {/* Membership Tab */}
        <TabPanel value={tabValue} index={4}>
          <Box sx={{ px: 3 }}>
            <Typography variant="h6" gutterBottom>
              Promote Membership Plans
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Earn commissions by referring new members to join Great Nigeria with these membership plans.
            </Typography>
            
            {membershipPlans.loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : membershipPlans.error ? (
              <Alert severity="error">
                {membershipPlans.error}
              </Alert>
            ) : membershipPlans.items.length === 0 ? (
              <Alert severity="info">
                No membership plans available for promotion at this time.
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {membershipPlans.items.filter(plan => plan.isActive).map((plan) => (
                  <Grid item xs={12} sm={6} md={4} key={plan.id}>
                    <MembershipAffiliateCard
                      plan={plan}
                      referralCode={referralLink.code}
                      onGetLink={getMembershipReferralLink}
                    />
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </TabPanel>
        
        {/* Marketplace Tab */}
        <TabPanel value={tabValue} index={5}>
          <Box sx={{ px: 3 }}>
            <Typography variant="h6" gutterBottom>
              Promote Marketplace Products
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Earn commissions by promoting products from the marketplace. Each product has its own commission rate set by the seller.
            </Typography>
            
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <InputLabel id="product-category-filter-label">Category</InputLabel>
                <Select
                  labelId="product-category-filter-label"
                  value={productCategoryFilter}
                  label="Category"
                  onChange={(e) => setProductCategoryFilter(e.target.value as string)}
                >
                  <MenuItem value="">All Categories</MenuItem>
                  <MenuItem value="books">Books</MenuItem>
                  <MenuItem value="courses">Courses</MenuItem>
                  <MenuItem value="digital">Digital Products</MenuItem>
                  <MenuItem value="physical">Physical Products</MenuItem>
                  <MenuItem value="services">Services</MenuItem>
                </Select>
              </FormControl>
            </Box>
            
            {affiliateProducts.loading && affiliateProducts.items.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : affiliateProducts.error ? (
              <Alert severity="error">
                {affiliateProducts.error}
              </Alert>
            ) : affiliateProducts.items.length === 0 ? (
              <Alert severity="info">
                No products available for promotion at this time.
              </Alert>
            ) : (
              <Box>
                <Grid container spacing={3}>
                  {affiliateProducts.items.map((product) => (
                    <Grid item xs={12} sm={6} md={4} key={product.id}>
                      <AffiliateProductCard
                        product={product}
                        referralCode={referralLink.code}
                        onGetLink={getProductReferralLink}
                      />
                    </Grid>
                  ))}
                </Grid>
                
                {affiliateProducts.totalPages > 1 && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                    <Pagination
                      count={affiliateProducts.totalPages}
                      page={productsPage}
                      onChange={(_, page) => setProductsPage(page)}
                      color="primary"
                    />
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </TabPanel>
        
        {/* Seller Settings Tab */}
        <TabPanel value={tabValue} index={6}>
          <Box sx={{ px: 3 }}>
            <Typography variant="h6" gutterBottom>
              Manage Your Product Affiliate Settings
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Enable and configure affiliate marketing for your products. Set commission rates and terms for affiliates who promote your products.
            </Typography>
            
            {sellerProducts.loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : sellerProducts.error ? (
              <Alert severity="error">
                {sellerProducts.error}
              </Alert>
            ) : sellerProducts.items.length === 0 ? (
              <Alert severity="info">
                You don't have any products listed in the marketplace yet. Add products first to configure affiliate settings.
              </Alert>
            ) : (
              <Box>
                {sellerProducts.items.map((product) => (
                  <SellerAffiliateSettings
                    key={product.id}
                    product={product}
                    onSave={handleUpdateProductAffiliateSettings}
                    loading={operations.updating}
                  />
                ))}
              </Box>
            )}
          </Box>
        </TabPanel>
      </Paper>
      
      {/* Create Referral Code Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)}>
        <DialogTitle>Create New Referral Code</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Create a new referral code to share with friends. You can add a description to help you remember what this code is for.
          </Typography>
          
          <TextField
            autoFocus
            margin="dense"
            label="Description (Optional)"
            fullWidth
            multiline
            rows={3}
            value={newCodeDescription}
            onChange={(e) => setNewCodeDescription(e.target.value)}
            placeholder="e.g., For Facebook friends, For blog readers, etc."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateReferralCode} 
            color="primary" 
            variant="contained"
            disabled={operations.creating}
          >
            {operations.creating ? <CircularProgress size={24} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Withdraw Commission Dialog */}
      <Dialog open={withdrawDialogOpen} onClose={() => setWithdrawDialogOpen(false)}>
        <DialogTitle>Withdraw Commission</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            You have {formatCurrency(availableCommission)} available to withdraw.
          </Typography>
          
          <TextField
            margin="dense"
            label="Amount"
            type="number"
            fullWidth
            value={withdrawAmount}
            onChange={(e) => setWithdrawAmount(Number(e.target.value))}
            InputProps={{
              startAdornment: <Box component="span" sx={{ mr: 1 }}>₦</Box>,
            }}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth margin="dense">
            <InputLabel id="withdraw-method-label">Withdrawal Method</InputLabel>
            <Select
              labelId="withdraw-method-label"
              value={withdrawMethod}
              label="Withdrawal Method"
              onChange={(e) => setWithdrawMethod(e.target.value as string)}
            >
              <MenuItem value="wallet">Digital Wallet</MenuItem>
              <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
              <MenuItem value="mobile_money">Mobile Money</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWithdrawDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleWithdrawCommission} 
            color="primary" 
            variant="contained"
            disabled={withdrawAmount <= 0 || withdrawAmount > availableCommission || operations.withdrawing}
          >
            {operations.withdrawing ? <CircularProgress size={24} /> : 'Withdraw'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AffiliatePage;
