import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip,
  Badge,
  LinearProgress,
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Stars as StarsIcon,
  Redeem as RedeemIcon,
  History as HistoryIcon,
  Leaderboard as LeaderboardIcon,
  Info as InfoIcon,
  ArrowUpward as LevelUpIcon,
  CheckCircle as CheckCircleIcon,
  Lock as LockIcon,
  ShoppingCart as ShoppingCartIcon,
  LocalActivity as ActivityIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import {
  fetchUserRewardSummary,
  fetchUserPointsHistory,
  fetchUserActivities,
  fetchAchievements,
  fetchUserAchievements,
  fetchRewards,
  fetchUserRedemptions,
  fetchTiers,
  fetchLeaderboard,
  redeemReward,
} from '../store/slices/rewardsSlice';
import { Reward, Achievement, UserAchievement, RewardTier, LeaderboardEntry } from '../services/rewardsService';

// Helper components
const PointsSummary: React.FC<{
  availablePoints: number;
  level: number;
  nextLevelPoints: number;
  currentLevelPoints: number;
  tier?: RewardTier;
}> = ({ availablePoints, level, nextLevelPoints, currentLevelPoints, tier }) => {
  const progress = (currentLevelPoints / nextLevelPoints) * 100;

  return (
    <Paper sx={{ p: 3, mb: 3, position: 'relative', overflow: 'hidden' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">Your Rewards</Typography>
        {tier && (
          <Chip
            label={tier.name}
            color="primary"
            sx={{ bgcolor: tier.color, color: 'white' }}
          />
        )}
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <StarsIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
        <Box>
          <Typography variant="h4" component="div">
            {availablePoints}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Available Points
          </Typography>
        </Box>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2">
            Level {level}
          </Typography>
          <Typography variant="body2">
            Level {level + 1}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{ height: 8, borderRadius: 4 }}
        />
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {currentLevelPoints} / {nextLevelPoints} points to next level
          </Typography>
        </Box>
      </Box>

      <Box sx={{ position: 'absolute', bottom: -30, right: -30, opacity: 0.1, transform: 'rotate(-15deg)' }}>
        <TrophyIcon sx={{ fontSize: 150, color: 'primary.main' }} />
      </Box>
    </Paper>
  );
};

const AchievementCard: React.FC<{
  achievement: Achievement;
  userAchievement?: UserAchievement;
  onClick: () => void;
}> = ({ achievement, userAchievement, onClick }) => {
  const isCompleted = userAchievement?.isCompleted || false;
  const progress = userAchievement ? userAchievement.progress : 0;
  const isSecret = achievement.isSecret && !isCompleted;

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        opacity: isCompleted ? 1 : 0.7,
        position: 'relative',
      }}
      onClick={onClick}
    >
      {isCompleted && (
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            right: 10,
            zIndex: 1,
          }}
        >
          <CheckCircleIcon color="success" />
        </Box>
      )}
      <CardContent sx={{ flexGrow: 1, pt: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              width: 60,
              height: 60,
              bgcolor: isCompleted ? 'success.light' : 'action.disabledBackground',
              mb: 1,
            }}
          >
            {isSecret ? <LockIcon /> : <TrophyIcon />}
          </Avatar>
          <Typography variant="h6" component="div" align="center">
            {isSecret ? 'Secret Achievement' : achievement.name}
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
          {isSecret
            ? 'Complete special actions to unlock this achievement'
            : achievement.description}
        </Typography>

        {!isSecret && (
          <Box sx={{ mt: 'auto' }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
              <Chip
                label={`${achievement.points} points`}
                size="small"
                color={isCompleted ? 'success' : 'default'}
              />
            </Box>

            {!isCompleted && (
              <Box sx={{ width: '100%' }}>
                <LinearProgress
                  variant="determinate"
                  value={progress}
                  sx={{ height: 6, borderRadius: 3 }}
                />
                <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block', mt: 0.5 }}>
                  {Math.round(progress)}% Complete
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

const RewardCard: React.FC<{
  reward: Reward;
  userPoints: number;
  onRedeem: () => void;
}> = ({ reward, userPoints, onRedeem }) => {
  const canAfford = userPoints >= reward.pointsCost;
  const isAvailable = reward.isActive && (reward.quantity > 0 || reward.quantity === null);

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
          {reward.image ? (
            <Box
              component="img"
              src={reward.image}
              alt={reward.name}
              sx={{ width: 100, height: 100, objectFit: 'contain', mb: 2 }}
            />
          ) : (
            <RedeemIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          )}
          <Typography variant="h6" component="div" align="center">
            {reward.name}
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
          {reward.description}
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
          <Chip
            label={`${reward.pointsCost} points`}
            size="small"
            color={canAfford ? 'primary' : 'default'}
          />
        </Box>

        {reward.quantity !== null && (
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            {reward.quantity} remaining
          </Typography>
        )}
      </CardContent>
      <CardActions sx={{ justifyContent: 'center', p: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<ShoppingCartIcon />}
          onClick={onRedeem}
          disabled={!canAfford || !isAvailable}
          fullWidth
        >
          {canAfford ? 'Redeem' : 'Not Enough Points'}
        </Button>
      </CardActions>
    </Card>
  );
};

const LeaderboardItem: React.FC<{
  entry: LeaderboardEntry;
  rank: number;
  isCurrentUser: boolean;
}> = ({ entry, rank, isCurrentUser }) => {
  return (
    <ListItem
      sx={{
        bgcolor: isCurrentUser ? 'primary.light' : 'transparent',
        borderRadius: 1,
      }}
    >
      <ListItemAvatar>
        <Avatar
          sx={{
            bgcolor: rank <= 3 ? 'warning.main' : 'primary.main',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          {rank}
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={entry.username}
        secondary={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" component="span">
              Level {entry.level}
            </Typography>
            <Chip
              label={`${entry.achievements} achievements`}
              size="small"
              sx={{ ml: 1 }}
            />
          </Box>
        }
      />
      <ListItemSecondaryAction>
        <Typography variant="h6" color="primary">
          {entry.points}
        </Typography>
      </ListItemSecondaryAction>
    </ListItem>
  );
};

// Main component
const RewardsInterface: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useSelector((state: RootState) => state.auth);
  const { data: userSummary, loading: summaryLoading } = useSelector(
    (state: RootState) => state.rewards.userSummary
  );
  const { items: achievements, loading: achievementsLoading } = useSelector(
    (state: RootState) => state.rewards.achievements
  );
  const { items: userAchievements, loading: userAchievementsLoading } = useSelector(
    (state: RootState) => state.rewards.userAchievements
  );
  const { items: rewards, loading: rewardsLoading } = useSelector(
    (state: RootState) => state.rewards.rewards
  );
  const { items: redemptions, loading: redemptionsLoading } = useSelector(
    (state: RootState) => state.rewards.redemptions
  );
  const { items: tiers, loading: tiersLoading } = useSelector(
    (state: RootState) => state.rewards.tiers
  );
  const { items: leaderboard, loading: leaderboardLoading } = useSelector(
    (state: RootState) => state.rewards.leaderboard
  );
  const { items: activities, loading: activitiesLoading } = useSelector(
    (state: RootState) => state.rewards.activities
  );

  const [tabValue, setTabValue] = useState(0);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  const [achievementDialogOpen, setAchievementDialogOpen] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [redeemDialogOpen, setRedeemDialogOpen] = useState(false);
  const [redeemSuccess, setRedeemSuccess] = useState(false);
  const [redeemError, setRedeemError] = useState<string | null>(null);
  const [leaderboardTimeframe, setLeaderboardTimeframe] = useState<'all' | 'month' | 'week' | 'day'>('all');

  useEffect(() => {
    if (user) {
      dispatch(fetchUserRewardSummary(user.id));
      dispatch(fetchUserAchievements(user.id));
      dispatch(fetchUserRedemptions({ userId: user.id }));
      dispatch(fetchUserActivities({ userId: user.id, params: { limit: 10 } }));
    }
    dispatch(fetchAchievements());
    dispatch(fetchRewards());
    dispatch(fetchTiers());
    dispatch(fetchLeaderboard());
  }, [dispatch, user]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAchievementClick = (achievement: Achievement) => {
    setSelectedAchievement(achievement);
    setAchievementDialogOpen(true);
  };

  const handleCloseAchievementDialog = () => {
    setAchievementDialogOpen(false);
  };

  const handleRewardClick = (reward: Reward) => {
    setSelectedReward(reward);
    setRedeemDialogOpen(true);
    setRedeemSuccess(false);
    setRedeemError(null);
  };

  const handleCloseRedeemDialog = () => {
    setRedeemDialogOpen(false);
  };

  const handleRedeemReward = async () => {
    if (!user || !selectedReward) return;

    try {
      await dispatch(redeemReward({ userId: user.id, rewardId: selectedReward.id })).unwrap();
      setRedeemSuccess(true);
      // Refresh user summary to update points
      dispatch(fetchUserRewardSummary(user.id));
      dispatch(fetchUserRedemptions({ userId: user.id }));
    } catch (error: any) {
      setRedeemError(error.message || 'Failed to redeem reward');
    }
  };

  const handleLeaderboardTimeframeChange = (timeframe: 'all' | 'month' | 'week' | 'day') => {
    setLeaderboardTimeframe(timeframe);
    dispatch(fetchLeaderboard({ timeframe }));
  };

  const getUserTier = () => {
    if (!userSummary || !tiers.length) return null;
    return tiers
      .filter(tier => tier.level <= userSummary.level)
      .sort((a, b) => b.level - a.level)[0];
  };

  const currentTier = getUserTier();

  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          You need to be logged in to view your rewards. Please log in and try again.
        </Alert>
      </Container>
    );
  }

  const loading =
    summaryLoading ||
    achievementsLoading ||
    userAchievementsLoading ||
    rewardsLoading ||
    redemptionsLoading ||
    tiersLoading ||
    leaderboardLoading ||
    activitiesLoading;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Rewards & Achievements
      </Typography>

      {loading && !userSummary ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {userSummary && (
            <PointsSummary
              availablePoints={userSummary.availablePoints}
              level={userSummary.level}
              nextLevelPoints={userSummary.nextLevelPoints}
              currentLevelPoints={userSummary.nextLevelPoints - (userSummary.nextLevelPoints - userSummary.availablePoints)}
              tier={currentTier || undefined}
            />
          )}

          <Paper sx={{ mb: 4 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab icon={<TrophyIcon />} label="Achievements" />
              <Tab icon={<RedeemIcon />} label="Rewards" />
              <Tab icon={<HistoryIcon />} label="History" />
              <Tab icon={<LeaderboardIcon />} label="Leaderboard" />
            </Tabs>
          </Paper>

          {/* Achievements Tab */}
          {tabValue === 0 && (
            <Box>
              <Typography variant="h5" gutterBottom>
                Your Achievements
              </Typography>
              <Typography variant="body1" paragraph>
                Complete activities on the platform to earn achievements and points.
              </Typography>

              <Grid container spacing={3}>
                {achievements.map((achievement) => {
                  const userAchievement = userAchievements.find(
                    (ua) => ua.achievementId === achievement.id
                  );
                  return (
                    <Grid item key={achievement.id} xs={12} sm={6} md={4}>
                      <AchievementCard
                        achievement={achievement}
                        userAchievement={userAchievement}
                        onClick={() => handleAchievementClick(achievement)}
                      />
                    </Grid>
                  );
                })}
              </Grid>

              {achievements.length === 0 && !achievementsLoading && (
                <Alert severity="info">
                  No achievements available yet. Check back soon!
                </Alert>
              )}
            </Box>
          )}

          {/* Rewards Tab */}
          {tabValue === 1 && (
            <Box>
              <Typography variant="h5" gutterBottom>
                Available Rewards
              </Typography>
              <Typography variant="body1" paragraph>
                Redeem your points for these exciting rewards.
              </Typography>

              <Grid container spacing={3}>
                {rewards
                  .filter((reward) => reward.isActive)
                  .map((reward) => (
                    <Grid item key={reward.id} xs={12} sm={6} md={4}>
                      <RewardCard
                        reward={reward}
                        userPoints={userSummary?.availablePoints || 0}
                        onRedeem={() => handleRewardClick(reward)}
                      />
                    </Grid>
                  ))}
              </Grid>

              {rewards.filter((r) => r.isActive).length === 0 && !rewardsLoading && (
                <Alert severity="info">
                  No rewards available yet. Check back soon!
                </Alert>
              )}

              {redemptions.length > 0 && (
                <Box sx={{ mt: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Your Redemptions
                  </Typography>
                  <Paper>
                    <List>
                      {redemptions.slice(0, 5).map((redemption) => (
                        <ListItem key={redemption.id}>
                          <ListItemAvatar>
                            <Avatar>
                              <RedeemIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={redemption.reward.name}
                            secondary={
                              <>
                                <Typography variant="body2" component="span">
                                  {redemption.pointsSpent} points • {new Date(redemption.redeemedAt).toLocaleDateString()}
                                </Typography>
                                <Chip
                                  size="small"
                                  label={redemption.status}
                                  color={
                                    redemption.status === 'fulfilled'
                                      ? 'success'
                                      : redemption.status === 'rejected'
                                      ? 'error'
                                      : 'default'
                                  }
                                  sx={{ ml: 1 }}
                                />
                              </>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                </Box>
              )}
            </Box>
          )}

          {/* History Tab */}
          {tabValue === 2 && (
            <Box>
              <Typography variant="h5" gutterBottom>
                Activity History
              </Typography>

              <Paper>
                <List>
                  {activities.map((activity) => (
                    <ListItem key={activity.id}>
                      <ListItemAvatar>
                        <Avatar>
                          <ActivityIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={activity.description}
                        secondary={new Date(activity.createdAt).toLocaleString()}
                      />
                      <ListItemSecondaryAction>
                        <Chip
                          label={`${activity.points > 0 ? '+' : ''}${activity.points} points`}
                          color={activity.points > 0 ? 'success' : 'error'}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}

                  {activities.length === 0 && !activitiesLoading && (
                    <ListItem>
                      <ListItemText
                        primary="No activity yet"
                        secondary="Complete actions on the platform to earn points and see your history here."
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>
            </Box>
          )}

          {/* Leaderboard Tab */}
          {tabValue === 3 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5">Leaderboard</Typography>
                <Box>
                  <Button
                    size="small"
                    variant={leaderboardTimeframe === 'all' ? 'contained' : 'outlined'}
                    onClick={() => handleLeaderboardTimeframeChange('all')}
                    sx={{ mr: 1 }}
                  >
                    All Time
                  </Button>
                  <Button
                    size="small"
                    variant={leaderboardTimeframe === 'month' ? 'contained' : 'outlined'}
                    onClick={() => handleLeaderboardTimeframeChange('month')}
                    sx={{ mr: 1 }}
                  >
                    Month
                  </Button>
                  <Button
                    size="small"
                    variant={leaderboardTimeframe === 'week' ? 'contained' : 'outlined'}
                    onClick={() => handleLeaderboardTimeframeChange('week')}
                    sx={{ mr: 1 }}
                  >
                    Week
                  </Button>
                  <Button
                    size="small"
                    variant={leaderboardTimeframe === 'day' ? 'contained' : 'outlined'}
                    onClick={() => handleLeaderboardTimeframeChange('day')}
                  >
                    Day
                  </Button>
                </Box>
              </Box>

              <Paper>
                <List>
                  {leaderboard.map((entry, index) => (
                    <LeaderboardItem
                      key={entry.userId}
                      entry={entry}
                      rank={index + 1}
                      isCurrentUser={entry.userId === user.id}
                    />
                  ))}

                  {leaderboard.length === 0 && !leaderboardLoading && (
                    <ListItem>
                      <ListItemText
                        primary="No leaderboard data yet"
                        secondary="Be the first to earn points and appear on the leaderboard!"
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>
            </Box>
          )}
        </>
      )}

      {/* Achievement Dialog */}
      <Dialog open={achievementDialogOpen} onClose={handleCloseAchievementDialog} maxWidth="sm" fullWidth>
        {selectedAchievement && (
          <>
            <DialogTitle>{selectedAchievement.name}</DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: 'primary.main',
                    mb: 2,
                  }}
                >
                  <TrophyIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="body1" paragraph align="center">
                  {selectedAchievement.description}
                </Typography>
                <Chip
                  label={`${selectedAchievement.points} points`}
                  color="primary"
                  sx={{ mt: 1 }}
                />
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Requirements:
              </Typography>
              <List>
                {selectedAchievement.requirements.map((req, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={req.description}
                      secondary={`${req.type}: ${req.value}`}
                    />
                  </ListItem>
                ))}
              </List>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseAchievementDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Redeem Dialog */}
      <Dialog open={redeemDialogOpen} onClose={handleCloseRedeemDialog} maxWidth="sm" fullWidth>
        {selectedReward && (
          <>
            <DialogTitle>Redeem Reward</DialogTitle>
            <DialogContent>
              {redeemSuccess ? (
                <Box sx={{ textAlign: 'center', py: 2 }}>
                  <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Reward Redeemed Successfully!
                  </Typography>
                  <Typography variant="body1" paragraph>
                    You have redeemed {selectedReward.name} for {selectedReward.pointsCost} points.
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Check your redemption history for status updates.
                  </Typography>
                </Box>
              ) : (
                <>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                    {selectedReward.image ? (
                      <Box
                        component="img"
                        src={selectedReward.image}
                        alt={selectedReward.name}
                        sx={{ width: 120, height: 120, objectFit: 'contain', mb: 2 }}
                      />
                    ) : (
                      <RedeemIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                    )}
                    <Typography variant="h6" align="center" gutterBottom>
                      {selectedReward.name}
                    </Typography>
                    <Typography variant="body1" paragraph align="center">
                      {selectedReward.description}
                    </Typography>
                  </Box>

                  <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1, mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Redemption Details:
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Cost:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {selectedReward.pointsCost} points
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Your Points:
                        </Typography>
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          color={
                            (userSummary?.availablePoints || 0) >= selectedReward.pointsCost
                              ? 'success.main'
                              : 'error.main'
                          }
                        >
                          {userSummary?.availablePoints || 0} points
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>

                  {redeemError && (
                    <Alert severity="error" sx={{ mb: 3 }}>
                      {redeemError}
                    </Alert>
                  )}

                  <DialogContentText>
                    Are you sure you want to redeem this reward? This action cannot be undone.
                  </DialogContentText>
                </>
              )}
            </DialogContent>
            <DialogActions>
              {redeemSuccess ? (
                <Button onClick={handleCloseRedeemDialog} color="primary">
                  Close
                </Button>
              ) : (
                <>
                  <Button onClick={handleCloseRedeemDialog}>Cancel</Button>
                  <Button
                    onClick={handleRedeemReward}
                    color="primary"
                    variant="contained"
                    disabled={(userSummary?.availablePoints || 0) < selectedReward.pointsCost}
                  >
                    Confirm Redemption
                  </Button>
                </>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default RewardsInterface;
