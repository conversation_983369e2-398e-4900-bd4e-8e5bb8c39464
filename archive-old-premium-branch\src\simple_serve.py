#!/usr/bin/env python3
import http.server
import socketserver
import os
import socket
import time

PORT = 8080  # Using a different port
MAX_PORT = 8090  # Maximum port to try
DIRECTORY = "./backups"

class Handler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

# Add socket reuse option
socketserver.TCPServer.allow_reuse_address = True

def start_server(port):
    try:
        with socketserver.TCPServer(("0.0.0.0", port), Handler) as httpd:
            print(f"Serving backup files at port {port}")
            print(f"Download URL: http://0.0.0.0:{port}/combined_backups_20250423.tar.gz")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except (OSError, socket.error) as e:
        if port < MAX_PORT:
            print(f"Port {port} is busy, trying {port+1}...")
            time.sleep(1)
            start_server(port+1)
        else:
            print(f"Failed to bind to any port between {PORT} and {MAX_PORT}")
            raise e

# Try to start the server with the initial port
start_server(PORT)