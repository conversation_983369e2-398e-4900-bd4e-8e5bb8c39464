#!/bin/bash

# Script to fix all incorrect import paths in Go files
# Changes github.com/greatnigeria to github.com/yerenwgventures/GreatNigeriaLibrary

echo "🔧 Fixing import paths in Go files..."

# Create backup directory
BACKUP_DIR="import-fixes-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Counter for files processed
count=0

# Find all Go files with incorrect imports
files=$(find . -name "*.go" -exec grep -l "github.com/greatnigeria" {} \;)

for file in $files; do
    echo "Processing: $file"
    
    # Create backup
    cp "$file" "$BACKUP_DIR/$(basename $file).backup"
    
    # Fix the import paths
    sed -i 's|github\.com/greatnigeria|github.com/yerenwgventures/GreatNigeriaLibrary|g' "$file"
    
    count=$((count + 1))
done

echo "✅ Fixed import paths in $count files"
echo "📁 Backups created in: $BACKUP_DIR"

# Verify no more incorrect imports remain
remaining=$(find . -name "*.go" -exec grep -l "github.com/greatnigeria" {} \; | wc -l)
if [ "$remaining" -eq 0 ]; then
    echo "✅ All import paths fixed successfully!"
else
    echo "⚠️  Warning: $remaining files still have incorrect imports"
fi

echo "🧪 Testing go mod tidy..."
go mod tidy

echo "🔨 Testing compilation..."
go build ./cmd/auth-service/main.go

echo "📋 Import fix completed!"
